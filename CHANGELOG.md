### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v1.2.0](https://github.com/xtatistix/frontend/compare/v1.1.0...v1.2.0)

> 22 March 2024

- update version [`3ea0f16`](https://github.com/xtatistix/frontend/commit/3ea0f16b25044980bc972c1f6c3b706f7206255a)
- minor change [`1c36741`](https://github.com/xtatistix/frontend/commit/1c367413d390787197fb4c84347e268f3888bebd)
- update version [`399852a`](https://github.com/xtatistix/frontend/commit/399852a13b93d45937f5c474ce18198e0a928dda)

#### [v1.1.0](https://github.com/xtatistix/frontend/compare/v1.0.2...v1.1.0)

> 22 March 2024

- 1.1.0 minor update [`026233e`](https://github.com/xtatistix/frontend/commit/026233e96b52bf6de228f0b2b9b48488ccbfd2cb)
- deneme minor [`e48ef03`](https://github.com/xtatistix/frontend/commit/e48ef03c72dedeb3d28a5f1396c0a83ec8c63df8)
- update version patch [`d71dcdd`](https://github.com/xtatistix/frontend/commit/d71dcdd77759b2854156be91e39852976422449d)

#### [v1.0.2](https://github.com/xtatistix/frontend/compare/v1.0.1...v1.0.2)

> 22 March 2024

- add version patch [`6b9292f`](https://github.com/xtatistix/frontend/commit/6b9292fd884e044d5ae20e0fe2e60c2532ba9dbd)
- update changelog [`e434b1d`](https://github.com/xtatistix/frontend/commit/e434b1dfd7772e4421fe0153bbd614b40fc62cd7)
- remove version [`914f191`](https://github.com/xtatistix/frontend/commit/914f1917943d9fba948ad0eabf8ae70ac7d99115)

#### [v1.0.1](https://github.com/xtatistix/frontend/compare/v1.0.0...v1.0.1)

> 22 March 2024

- Add version v1.0.0 [`8a10509`](https://github.com/xtatistix/frontend/commit/8a1050901d8353f66ef71fa3b036fe3f70cdaa15)

#### v1.0.0

> 21 March 2024

- Notification Module [`#33`](https://github.com/xtatistix/frontend/pull/33)
- Ng select [`#28`](https://github.com/xtatistix/frontend/pull/28)
- Responsive LGTM! [`#10`](https://github.com/xtatistix/frontend/pull/10)
- i18n: OK [`#6`](https://github.com/xtatistix/frontend/pull/6)
- Update Download All Reports: Fix #41 [`#41`](https://github.com/xtatistix/frontend/issues/41)
- Fix #37 [`#37`](https://github.com/xtatistix/frontend/issues/37)
- Fix #35 [`#35`](https://github.com/xtatistix/frontend/issues/35)
- Fix #36 [`#36`](https://github.com/xtatistix/frontend/issues/36)
- Fix #38 [`#38`](https://github.com/xtatistix/frontend/issues/38)
- Resolve #31 [`#31`](https://github.com/xtatistix/frontend/issues/31)
- resolved #26 [`#26`](https://github.com/xtatistix/frontend/issues/26)
- Fix #29 [`#29`](https://github.com/xtatistix/frontend/issues/29)
- Fix #17 [`#17`](https://github.com/xtatistix/frontend/issues/17)
- Fix #18 [`#18`](https://github.com/xtatistix/frontend/issues/18)
- Fix #16 [`#16`](https://github.com/xtatistix/frontend/issues/16)
- Fix #15 [`#15`](https://github.com/xtatistix/frontend/issues/15)
- Fix #12 [`#12`](https://github.com/xtatistix/frontend/issues/12)
- Fix #13 [`#13`](https://github.com/xtatistix/frontend/issues/13)
- Fix #11 [`#11`](https://github.com/xtatistix/frontend/issues/11)
- initial commit [`cda5160`](https://github.com/xtatistix/frontend/commit/cda51606163f171354ede41676c5c4830bdeb276)
- format code [`11c9de4`](https://github.com/xtatistix/frontend/commit/11c9de473311eef01e2f8e5bba7c70d6e4e2d49e)
- Responsive son commit [`4bd743e`](https://github.com/xtatistix/frontend/commit/4bd743ef071bd169103daf2f89d34510d97ed57b)

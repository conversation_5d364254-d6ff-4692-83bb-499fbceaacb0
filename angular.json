{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/istabot.com", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/assets"], "styles": ["src/styles.scss", "node_modules/intl-tel-input/build/css/intlTelInput.css"], "allowedCommonJsDependencies": ["google-libphonenumber", "juice", "handsontable", "core-js/modules/es.error.cause.js", "core-js/modules/es.array.unscopables.flat.js", "core-js/modules/es.json.stringify.js", "core-js/modules/es.set.difference.v2.js", "core-js/modules/es.set.intersection.v2.js", "core-js/modules/es.set.is-disjoint-from.v2.js", "core-js/modules/es.set.is-subset-of.v2.js", "core-js/modules/es.set.is-superset-of.v2.js", "core-js/modules/es.set.symmetric-difference.v2.js", "core-js/modules/es.set.union.v2.js", "core-js/modules/es.object.from-entries.js", "core-js/modules/es.array.at.js", "core-js/modules/es.string.at-alternative.js", "core-js/modules/web.immediate.js", "core-js/modules/es.array.push.js", "moment", "<PERSON>ro"], "scripts": []}, "configurations": {"production": {"optimization": true, "budgets": [{"type": "initial", "maximumWarning": "6mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environment/environment.ts", "with": "src/environment/environment.prod.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "frontend:build:production"}, "development": {"browserTarget": "frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}
{"name": "frontend", "version": "1.1.0", "scripts": {"stats": "webpack-bundle-analyzer ./dist/istabot.com/stats.json", "ng": "ng", "start": "ng serve", "build": "ng build", "prebuild.prod": "ts-node -O '{\"module\": \"commonjs\"}' git.version.ts", "watch": "ng build --watch --configuration development", "test": "ng test", "dev": "lite-server"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.9", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "~16.2.14", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@emailjs/browser": "^4.3.3", "@handsontable/angular": "^14.6.1", "@justin-s/ngx-intl-tel-input": "^16.0.5", "@modelcontextprotocol/sdk": "^1.12.3", "@ng-icons/bootstrap-icons": "^25.3.1", "@ng-icons/core": "^25.3.1", "@ng-icons/feather-icons": "^25.3.1", "@ng-icons/heroicons": "^29.5.2", "@ng-icons/huge-icons": "^29.5.1", "@ng-icons/ionicons": "^25.3.1", "@ng-icons/jam-icons": "^25.3.1", "@ng-icons/lucide": "^29.10.0", "@ng-icons/material-icons": "^25.3.1", "@ng-icons/phosphor-icons": "^29.5.0", "@ng-icons/remixicon": "^25.3.1", "@ng-icons/tabler-icons": "^25.3.1", "@ng-icons/tdesign-icons": "^29.5.0", "@ng-icons/ux-aspects": "^25.3.1", "@ng-select/ng-select": "^11.2.0", "@ngneat/transloco": "^6.0.4", "@openreplay/tracker": "^14.0.3", "@openreplay/tracker-assist": "^9.0.1", "@smithery/sdk": "^1.5.2", "@tailwindcss/forms": "^0.5.6", "alpinejs": "^3.13.0", "angular-mentions": "^1.5.0", "date-fns": "^2.30.0", "gsap": "^3.12.2", "handsontable": "^14.6.1", "hyperformula": "^2.7.1", "juice": "^9.1.0", "matter-js": "^0.20.0", "ng-alt-snotify": "^16.0.0", "ngx-date-fns": "^10.0.1", "ngx-device-detector": "^6.0.2", "ngx-scrollbar": "^13.0.3", "rxjs": "~7.8.0", "ts-node": "^10.9.2", "tslib": "^2.3.0", "tslint-angular": "^3.0.3", "util": "^0.12.5", "xlsx": "^0.18.5", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.0", "@angular/cli": "~16.2.0", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "@types/matter-js": "^0.19.8", "autoprefixer": "^10.4.16", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.30", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.4.1", "typescript": "~5.1.3"}}
import { NgModule } from '@angular/core';
import { PreloadAllModules, provideRouter, RouterModule, Routes } from '@angular/router';
import { MainpageComponent } from '@layout/mainpage/mainpage.component';
import { ProjectsModule } from './modules/projects/projects.module';
import { AuthGuard } from './modules/auth/auth.guard';
import { AuthComponent } from './modules/auth/auth.component';
import { PagenotfoundComponent } from './shared/components/pagenotfound/pagenotfound.component';
import { SettingsModule } from './modules/settings/settings.module';
import { AuthModule } from '@modules/auth/auth.module';
import { LandingComponent } from './modules/landing/pages/landing/landing.component';
import { LandingModule } from '@modules/landing/landing.module';
import { CreditCardResultComponent } from './modules/payment/pages/credit-card-result/credit-card-result.component';
import { TitleResolver } from './shared/pipes/pageTitle.resolver';
import { AdminGuard } from './modules/admin/admin.guard';
import { ReportsModule } from '@modules/reports/reports.module';
import { ChangelogComponent } from './modules/landing/pages/changelog/changelog.component';
import { CareersComponent } from './modules/landing/pages/careers/careers.component';

const routes: Routes = [
  {
    path: '',
    component: LandingComponent,
    loadChildren: () =>
      import('@modules/landing/landing.module').then(
        (m): typeof LandingModule => m.LandingModule,
      ),
    resolve: {
      title: TitleResolver
    },
  },
  {
    path: 'changelog',
    component: ChangelogComponent,
  },
  { path: 'careers', component: CareersComponent },
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full',
  },
  {
    path: '',
    component: AuthComponent,
    children: [
      {
        path: '',
        loadChildren: () =>
          import('@modules/auth/auth.module').then(
            (m): typeof AuthModule => m.AuthModule,
          ),
      },
    ],
  },
  {
    path: '',
    component: MainpageComponent,
    canActivate: [AuthGuard],
    canLoad: [AuthGuard],
    children: [
      {
        path: 'projects',
        loadChildren: () =>
          import('@modules/projects/projects.module').then(
            (m): typeof ProjectsModule => m.ProjectsModule,
          ),
      },
      {
        path: 'reports',
        loadChildren: () =>
          import('@modules/reports/reports.module').then(
            (m): typeof ReportsModule => m.ReportsModule,
          ),
      },
      {
        path: 'explore',
        loadChildren: () =>
          import('@modules/explore/explore.module').then(
            (m) => m.ExploreModule,
          ),
      },
      {
        path: 'analyses',
        loadChildren: () =>
          import('@modules/analyses/analyses.module').then(
            (m) => m.AnalysesModule,
          ),
      },
    ],
  },
  {
    path: 'admin',
    loadChildren: () =>
      import('@modules/admin/admin.module').then(m => m.AdminModule),
    canActivate: [AdminGuard],
    canLoad: [AdminGuard]
  },
  {
    path: 'payment/result',
    component: CreditCardResultComponent,
    canActivate: [AuthGuard],
    canLoad: [AuthGuard],
  },
  { path: '404', component: PagenotfoundComponent },
  { path: '**', redirectTo: '/404' },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      paramsInheritanceStrategy: 'always',
    }),
  ],
  exports: [RouterModule],
  providers: [
    provideRouter(routes),
    TitleResolver
  ]
})
export class AppRoutingModule { }

import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, NavigationStart, Params, Router } from '@angular/router';
import { TranslocoService } from '@ngneat/transloco';
import { PageTitleService } from './shared/services/page-title.service';
import { filter } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from '@env/environment';
import { getInitialLang } from './transloco-root.module';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  title = 'frontend';

  constructor(
    private transloco: TranslocoService,
    private router: Router,
    private pageTitleService: PageTitleService,
    private http: HttpClient
  ) {
    // The initial language is now set by the TranslocoRootModule using getInitialLang()

    // If user is authenticated, fetch their language preference from the backend
    // This ensures we're using the most up-to-date language preference from the server
    if (localStorage.getItem('token') && localStorage.getItem('user_id')) {
      this.fetchUserLanguagePreference();
    }

    if (!(window as any).Tawk_API) {
      this.initTawkTo();
    }
  }

  /**
   * Fetches the user's language preference from the backend
   * This ensures that the language setting persists across page refreshes
   */
  private fetchUserLanguagePreference(): void {
    const userId = localStorage.getItem('user_id');
    if (!userId) return;

    this.http.get(`${environment.apiUrl}/users/${userId}`).subscribe({
      next: (user: any) => {
        if (user && user.locale && (user.locale === 'tr' || user.locale === 'en')) {
          // Always update the language to match the backend preference
          // This ensures consistency between the backend and frontend
          this.transloco.setActiveLang(user.locale);
          localStorage.setItem('activeLang', user.locale);
        }
      },
      error: (error) => {
        console.error('Error fetching user language preference:', error);
      }
    });
  }

  ngOnInit(): void {
    // NavigationStart olaylarını dinle - sayfa değişmeden önce başlığı sıfırla
    this.router.events
      .pipe(filter(event => event instanceof NavigationStart))
      .subscribe((event: NavigationStart) => {
        // Sayfa değişmeden önce başlığı sıfırla

        // Proje detay sayfasından başka bir sayfaya geçiyorsa başlığı sıfırla
        if (this.router.url.includes('/projects/') && !event.url.includes('/projects/')) {
          document.title = 'istabot';

          // Raporlar sayfasına geçiyorsa, başlığı hemen ayarla
          if (event.url === '/reports') {
            // Raporlar sayfasına geçiş
            try {
              // Çevirilerin yüklenip yüklenmediğini kontrol et
              const activeLang = this.transloco.getActiveLang();
              const translations = this.transloco.getTranslation(activeLang);

              if (translations && Object.keys(translations).length > 0) {
                // Çeviriler yüklendi, başlığı ayarla
                this.transloco.selectTranslate('report_list.title').subscribe(title => {
                  // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
                  document.title = `${title} | istabot`;

                  setTimeout(() => {
                    document.title = `${title} | istabot`;
                  }, 50);
                });
              } else {
                // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
                document.title = 'istabot';
              }
            } catch (error) {
              console.error('Error setting title for reports page:', error);
              document.title = 'istabot';
            }
          }
        }
      });

    // NavigationEnd olaylarını dinle - sayfa değiştikten sonra başlığı ayarla
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        // Her sayfa değişikliğinde başlığı yeniden ayarla

        // Özel durumlar
        if (event.url === '/projects') {
          this.transloco.selectTranslate('project_list.my_projects').subscribe(title => {
            document.title = `${title} | istabot`;
          });
        }
        // Proje detay sayfasında değilse ve raporlar sayfasında ise, başlığı zorla ayarla
        else if (event.url === '/reports') {
          try {
            // Çevirilerin yüklenip yüklenmediğini kontrol et
            const activeLang = this.transloco.getActiveLang();
            const translations = this.transloco.getTranslation(activeLang);

            if (translations && Object.keys(translations).length > 0) {
              // Çeviriler yüklendi, başlığı ayarla
              this.transloco.selectTranslate('report_list.title').subscribe(title => {
                // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
                document.title = `${title} | istabot`;

                setTimeout(() => {
                  document.title = `${title} | istabot`;
                }, 50);

              });
            } else {
              // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
              document.title = 'istabot';
            }
          } catch (error) {
            console.error('Error setting title for reports page:', error);
            document.title = 'istabot';
          }
        }
        // Admin sayfalarında ise, başlığı zorla ayarla
        else if (event.url.startsWith('/admin/')) {
          try {
            // Çevirilerin yüklenip yüklenmediğini kontrol et
            const activeLang = this.transloco.getActiveLang();
            const translations = this.transloco.getTranslation(activeLang);

            if (translations && Object.keys(translations).length > 0) {
              // Çeviriler yüklendi, başlığı ayarla
              let titleKey = 'tabs.admin.dashboard';

              if (event.url.includes('/users')) {
                titleKey = 'tabs.admin.users';
              } else if (event.url.includes('/projects')) {
                titleKey = 'tabs.admin.projects';
              } else if (event.url.includes('/reports')) {
                titleKey = 'tabs.admin.reports';
              } else if (event.url.includes('/credit-card-transactions')) {
                titleKey = 'tabs.admin.transactions';
              } else if (event.url.includes('/settings')) {
                titleKey = 'tabs.admin.settings';
              } else if (event.url.includes('/eft-confirm')) {
                titleKey = 'tabs.admin.eft-confirm';
              }

              this.transloco.selectTranslate(titleKey).subscribe(title => {
                // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
                document.title = `${title} | istabot`;

                setTimeout(() => {
                  document.title = `${title} | istabot`;
                }, 50);
              });
            } else {
              // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
              document.title = 'Admin Panel | istabot';
            }
          } catch (error) {
            console.error('Error setting title for admin page:', error);
            document.title = 'Admin Panel | istabot';
          }
        }
        // Proje detay sayfasında değilse, başlığı PageTitleService ile ayarla
        else if (!event.url.includes('/projects/')) {
          // Proje detay sayfasında değiliz, başlığı güncelle
          this.pageTitleService.setTitle(event.url);
        }
        // Proje detay sayfasında ise, ProjectDetailComponent başlığı ayarlayacak
      });

    // Dil değişikliklerini dinle
    this.transloco.langChanges$.subscribe(() => {
      // Dil değiştiğinde sayfa başlığını güncelle
      const currentUrl = this.router.url;

      if (currentUrl === '/projects') {
        this.transloco.selectTranslate('project_list.my_projects').subscribe(title => {
          document.title = `${title} | istabot`;
        });
      }
      // Raporlar sayfasında ise, başlığı zorla ayarla
      else if (currentUrl === '/reports') {
        try {
          // Çevirilerin yüklenip yüklenmediğini kontrol et
          const activeLang = this.transloco.getActiveLang();
          const translations = this.transloco.getTranslation(activeLang);

          if (translations && Object.keys(translations).length > 0) {
            // Çeviriler yüklendi, başlığı ayarla
            this.transloco.selectTranslate('report_list.title').subscribe(title => {
              // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
              document.title = `${title} | istabot`;

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 50);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 150);
            });
          } else {
            // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
            document.title = 'istabot';
          }
        } catch (error) {
          console.error('Error setting title for reports page:', error);
          document.title = 'istabot';
        }
      }
      // Admin sayfalarında ise, başlığı zorla ayarla
      else if (currentUrl.startsWith('/admin/')) {
        try {
          // Çevirilerin yüklenip yüklenmediğini kontrol et
          const activeLang = this.transloco.getActiveLang();
          const translations = this.transloco.getTranslation(activeLang);

          if (translations && Object.keys(translations).length > 0) {
            // Çeviriler yüklendi, başlığı ayarla
            let titleKey = 'tabs.admin.dashboard';

            if (currentUrl.includes('/users')) {
              titleKey = 'tabs.admin.users';
            } else if (currentUrl.includes('/projects')) {
              titleKey = 'tabs.admin.projects';
            } else if (currentUrl.includes('/reports')) {
              titleKey = 'tabs.admin.reports';
            } else if (currentUrl.includes('/credit-card-transactions')) {
              titleKey = 'tabs.admin.transactions';
            } else if (currentUrl.includes('/settings')) {
              titleKey = 'tabs.admin.settings';
            } else if (currentUrl.includes('/eft-confirm')) {
              titleKey = 'tabs.admin.eft-confirm';
            }

            this.transloco.selectTranslate(titleKey).subscribe(title => {
              // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
              document.title = `${title} | istabot`;

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 50);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 150);
            });
          } else {
            // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
            document.title = 'Admin Panel | istabot';
          }
        } catch (error) {
          console.error('Error setting title for admin page:', error);
          document.title = 'Admin Panel | istabot';
        }
      }
      // Kurumsal panel sayfalarında ise, başlığı zorla ayarla
      else if (currentUrl.startsWith('/corporate/')) {
        try {
          // Çevirilerin yüklenip yüklenmediğini kontrol et
          const activeLang = this.transloco.getActiveLang();
          const translations = this.transloco.getTranslation(activeLang);

          if (translations && Object.keys(translations).length > 0) {
            // Çeviriler yüklendi, başlığı ayarla
            let titleKey = 'tabs.corporate.users';

            if (currentUrl.includes('/users')) {
              titleKey = 'tabs.corporate.users';
            } else if (currentUrl.includes('/projects')) {
              titleKey = 'tabs.corporate.projects';
            } else if (currentUrl.includes('/activities')) {
              titleKey = 'tabs.corporate.activities';
            }

            this.transloco.selectTranslate(titleKey).subscribe(title => {
              // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
              document.title = `${title} | istabot`;

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 50);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 150);
            });
          } else {
            // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
            document.title = 'Kurumsal Yönetim | istabot';
          }
        } catch (error) {
          console.error('Error setting title for corporate page:', error);
          document.title = 'Kurumsal Yönetim | istabot';
        }
      }
      // Proje detay sayfasında değilse, başlığı PageTitleService ile ayarla
      else if (!currentUrl.includes('/projects/')) {
        this.pageTitleService.setTitle(currentUrl);
      }
      // Proje detay sayfasında ise, ProjectDetailComponent başlığı güncelleyecek
    });

    // Sayfa yüklenirken başlığı ayarla (sayfa yenilemeleri için)
    setTimeout(() => {
      const currentUrl = this.router.url;

      if (currentUrl === '/projects') {
        this.transloco.selectTranslate('project_list.my_projects').subscribe(title => {
          document.title = `${title} | istabot`;
        });
      }
      // Raporlar sayfasında ise, başlığı zorla ayarla
      else if (currentUrl === '/reports') {
        try {
          // Çevirilerin yüklenip yüklenmediğini kontrol et
          const activeLang = this.transloco.getActiveLang();
          const translations = this.transloco.getTranslation(activeLang);

          if (translations && Object.keys(translations).length > 0) {
            // Çeviriler yüklendi, başlığı ayarla
            this.transloco.selectTranslate('report_list.title').subscribe(title => {
              // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
              document.title = `${title} | istabot`;

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 50);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 150);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 300);
            });
          } else {
            // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
            document.title = 'istabot';
          }
        } catch (error) {
          console.error('Error setting title for reports page:', error);
          document.title = 'istabot';
        }
      }
      // Admin sayfalarında ise, başlığı zorla ayarla
      else if (currentUrl.startsWith('/admin/')) {
        try {
          // Çevirilerin yüklenip yüklenmediğini kontrol et
          const activeLang = this.transloco.getActiveLang();
          const translations = this.transloco.getTranslation(activeLang);

          if (translations && Object.keys(translations).length > 0) {
            // Çeviriler yüklendi, başlığı ayarla
            let titleKey = 'tabs.admin.dashboard';

            if (currentUrl.includes('/users')) {
              titleKey = 'tabs.admin.users';
            } else if (currentUrl.includes('/projects')) {
              titleKey = 'tabs.admin.projects';
            } else if (currentUrl.includes('/reports')) {
              titleKey = 'tabs.admin.reports';
            } else if (currentUrl.includes('/credit-card-transactions')) {
              titleKey = 'tabs.admin.transactions';
            } else if (currentUrl.includes('/settings')) {
              titleKey = 'tabs.admin.settings';
            } else if (currentUrl.includes('/eft-confirm')) {
              titleKey = 'tabs.admin.eft-confirm';
            }

            this.transloco.selectTranslate(titleKey).subscribe(title => {
              // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
              document.title = `${title} | istabot`;

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 50);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 150);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 300);
            });
          } else {
            // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
            document.title = 'Admin Panel | istabot';
          }
        } catch (error) {
          console.error('Error setting title for admin page:', error);
          document.title = 'Admin Panel | istabot';
        }
      }
      // Kurumsal panel sayfalarında ise, başlığı zorla ayarla
      else if (currentUrl.startsWith('/corporate/')) {
        try {
          // Çevirilerin yüklenip yüklenmediğini kontrol et
          const activeLang = this.transloco.getActiveLang();
          const translations = this.transloco.getTranslation(activeLang);

          if (translations && Object.keys(translations).length > 0) {
            // Çeviriler yüklendi, başlığı ayarla
            let titleKey = 'tabs.corporate.users';

            if (currentUrl.includes('/users')) {
              titleKey = 'tabs.corporate.users';
            } else if (currentUrl.includes('/projects')) {
              titleKey = 'tabs.corporate.projects';
            } else if (currentUrl.includes('/activities')) {
              titleKey = 'tabs.corporate.activities';
            }

            this.transloco.selectTranslate(titleKey).subscribe(title => {
              // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
              document.title = `${title} | istabot`;

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 50);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 150);

              setTimeout(() => {
                document.title = `${title} | istabot`;
              }, 300);
            });
          } else {
            // Çeviriler henüz yüklenmedi, varsayılan başlığı kullan
            document.title = 'Kurumsal Yönetim | istabot';
          }
        } catch (error) {
          console.error('Error setting title for corporate page:', error);
          document.title = 'Kurumsal Yönetim | istabot';
        }
      }
      // Proje detay sayfasında değilse, başlığı PageTitleService ile ayarla
      else if (!currentUrl.includes('/projects/')) {
        this.pageTitleService.setTitle(currentUrl);
      }
      // Proje detay sayfasında ise, ProjectDetailComponent başlığı ayarlayacak
    }, 200);
  }

  initTawkTo() {
    var Tawk_API: any = Tawk_API || {}, Tawk_LoadStart = new Date();
    (function () {
      // var link = document.createElement('link');
      // link.rel = 'preload';
      // link.as = 'script';
      // link.href = 'https://embed.tawk.to/669a7cc532dca6db2cb22b5e/1i35nep5c';
      // document.head.appendChild(link);
      var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
      s1.async = true;
      s1.src = 'https://embed.tawk.to/669a7cc532dca6db2cb22b5e/1i35nep5c';
      s1.charset = 'UTF-8';
      s1.setAttribute('crossorigin', '*');
      s0.parentNode?.insertBefore(s1, s0);
    })();
    // (window as any).Tawk_API = (window as any).Tawk_API || {};
    setTimeout(() => {
      (window as any).Tawk_API?.showWidget();
      if (localStorage.getItem('username') && localStorage.getItem('email')) {
        (window as any).Tawk_API?.setAttributes({
          name: '#' + localStorage.getItem('user_id') + ': ' + localStorage.getItem('username'),  // Sitenizden alacağınız isim
          email: localStorage.getItem('email') || '<EMAIL>', // Sitenizden alacağınız email
          // İstediğiniz diğer bilgileri ekleyebilirsiniz
        }, function (error: any) {
          if (error) {
            console.error('Tawk.to güncelleme hatası:', error);
            return;
          }
        });
      }
    }, 500);
  }
  accept() {
    localStorage.setItem('cookieAccepted', 'true');
  }
  isAccepted() {
    return localStorage.getItem('cookieAccepted') === 'true';
  }
}

import { NgModule, LOCALE_ID } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { SidebarComponent } from './layout/sidebar/sidebar.component';
import { MainpageComponent } from './layout/mainpage/mainpage.component';
import {
  CommonModule,
  LocationStrategy,
  PathLocationStrategy,
  registerLocaleData,
} from '@angular/common';
import { SharedModule } from './shared/shared.module';
import { MentionModule } from 'angular-mentions';
import { FormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AuthInterceptorProvider } from './modules/auth/auth.interceptor';
import { AuthGuard } from './modules/auth/auth.guard';
import { AuthComponent } from './modules/auth/auth.component';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { TranslocoRootModule } from './transloco-root.module';
import { SnotifyModule, SnotifyService, ToastDefaults } from 'ng-alt-snotify'; 
import { AdminGuard } from './modules/admin/admin.guard';
import { DiagnoseModule } from './modules/diagnose/diagnose.module';
import { PaymentModule } from './modules/payment/payment.module';
import localeTr from '@angular/common/locales/tr';
import { BreadcrumbComponent } from './layout/breadcrumb/breadcrumb.component';
import { CreditUpdateInterceptor } from './shared/interceptors/credit-update.interceptor';

registerLocaleData(localeTr);

@NgModule({
  declarations: [
    AppComponent,
    SidebarComponent,
    MainpageComponent,
    AuthComponent,
    BreadcrumbComponent
  ],
  imports: [
    SnotifyModule,
    MentionModule,
    BrowserModule,
    AppRoutingModule,
    SharedModule,
    CommonModule,
    DiagnoseModule,
    PaymentModule,
    FormsModule,
    BrowserAnimationsModule,
    HttpClientModule,
    TranslocoRootModule
  ],
  providers: [
    { provide: 'SnotifyToastConfig', useValue: ToastDefaults},
    SnotifyService,
    {
      provide: LocationStrategy,
      useClass: PathLocationStrategy,
    },
    AuthInterceptorProvider,
    AuthGuard,
    AdminGuard,
    { provide: LOCALE_ID, useValue: 'tr-TR' },
        {
      provide: HTTP_INTERCEPTORS,
      useClass: CreditUpdateInterceptor,
      multi: true
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule { 
  constructor() {
    // Web worker'lar için polyfill
    if (typeof (Window as any).global === 'undefined') {
      (Window as any).global = window;
    }
  }
}

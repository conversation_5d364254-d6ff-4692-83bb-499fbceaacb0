import { Injectable } from "@angular/core";
import { AdminService } from "../services/admin.service";
import { map, Observable, of } from "rxjs";
import { AuthService } from "@app/modules/auth/auth.service";

interface UserRole {
  id: number;
  name: string;
  resource_type: string | null;
  resource_id: number | null;
  created_at: string;
  updated_at: string;
}

@Injectable({
    providedIn: 'root'
})
export class AdminHelperService {
    constructor(
        private adminService: AdminService,
        private authService: AuthService
    ) {
    }
    isRole(roleName: string): boolean {
        try {
            const rolesStr = localStorage.getItem('roles');
            if (!rolesStr) return false;
            
            const roles: UserRole[] = JSON.parse(rolesStr);
            
            if (roleName === 'unit_manager') {
                // Special case for unit_manager: check for specific role type
                return roles.some(role => 
                    role.name === 'unit_manager' && 
                    role.resource_type === 'Unit' && 
                    role.resource_id !== null
                );
            }
            
            // General case for other roles
            return roles.some(role => role.name === roleName);
        } catch (error) {
            console.error('Error checking for role:', error);
            return false;
        }
    }
    getEftPayments(): Observable<any> {
        return this.adminService.getEftPayments().pipe(
            map((response) => {
                const eftPayments = response ? response.map((item) => {
                    return {
                        ...item,
                        created_at_full: item.created_at,
                        order: {
                            ...item.order,
                            price: Number(item.order.price),
                            total_price: Number(item.order.total_price),
                            discount: Number(item.order.discount),
                            user: {
                                ...item.order.user,
                                full_name: item.order.user.name + ' ' + item.order.user.surname
                            }
                        }
                    }
                }
                ) : [];
                return eftPayments;
            })
        );
    }
    getUsers(): Observable<any> {
        return this.adminService.getUsers().pipe(
            map((response) => {
                const users = response ? response.map((item) => {
                    return {
                        ...item,
                        full_name: item.name + ' ' + item.surname
                    }
                }) : [];
                return users;
            })
        );
    }
}

import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import { ExcelService } from '../services/excel.service';
import { VariableValidationService } from '@app/modules/diagnose/services/variable-validation.service';
import { DatasetService } from '../services/dataset.service';
import { AnalysisRequirementsService } from '../services/analysis-requirements.service';
import { Observable, map } from 'rxjs';

export interface MissingValueStats {
  variableName: string;
  totalCount: number;
  missingCount: number;
  missingPercentage: number;
  pattern: string;
}
export interface ImportSettings {
  validateBeforeImport: boolean;
  autoDisableInvalid: boolean;
  skipEmptyRows: boolean;
}

export interface ImportResult {
  importedCount: number;
  skippedCount: number;
  invalidCount: number;
  details: {
    variableId: string;
    status: 'imported' | 'skipped' | 'invalid';
    reason?: string;
  }[];
}
@Injectable({
  providedIn: 'root'
})
export class DiagnoseHelperService {
  constructor(
    private excelService: ExcelService,
    private validateService: VariableValidationService,
    private datasetService: DatasetService,
    private analysisReqs: AnalysisRequirementsService,
  ) { }

  getExcelFile(s3Url: string): Promise<Blob> {

    return fetch(s3Url).then(response => response.blob());
  }

  // Excel export için yeni metod
  async exportToExcel(data: { name: string; variables_json: any }): Promise<Blob> {
    try {
      if (!data?.name || !data?.variables_json) {
        throw new Error('Invalid data for excel export');
      }

      return await this.excelService.writeExcelFile({
        variables_json: data.variables_json
      });
    } catch (error) {
      console.error('Error exporting to excel:', error);
      throw error;
    }
  }
  async exportDataviewToExcel(data: any): Promise<Blob> {
    try {
      if (!data?.name || !data?.variables_json) {
        throw new Error('Invalid data for excel export');
      }

      return await this.excelService.writeDataToExcelFile({
        variables_json: data.variables_json
      });
    } catch (error) {
      console.error('Error exporting to excel:', error);
      throw error;
    }
  }
  // Import işlemi için (feature)
  processImport(
    variables: any[],
    data: any[][],
    settings: ImportSettings = {
      validateBeforeImport: true,
      autoDisableInvalid: true,
      skipEmptyRows: true
    }
  ): ImportResult {
    const result: ImportResult = {
      importedCount: 0,
      skippedCount: 0,
      invalidCount: 0,
      details: []
    };

    // Her değişken için import durumunu kontrol et
    variables.forEach(variable => {
      if (!variable.import) {
        result.skippedCount++;
        result.details.push({
          variableId: variable.id,
          status: 'skipped',
          reason: 'Import disabled by user'
        });
        return;
      }

      if (settings.validateBeforeImport) {
        const isValid = this.validateService.validateVariable(variable, data);
        if (!isValid && settings.autoDisableInvalid) {
          variable.import = false;
          result.invalidCount++;
          result.details.push({
            variableId: variable.id,
            status: 'invalid',
            reason: 'Validation failed'
          });
          return;
        }
      }

      // Değişken başarıyla import edildi
      result.importedCount++;
      result.details.push({
        variableId: variable.id,
        status: 'imported'
      });
    });

    return result;
  }




  async prepareDiagnoseData(file: File | Blob,  s3Url?: string, projectId?: string, datasetId?: string) {
    try {
      const actualFile = file instanceof File ? file : new File([file], 'dataset.xlsx');
      const variables = await this.excelService.readExcelFile(actualFile);
      const datasetName = await this.excelService.readDatasetName(actualFile);
      return {
        ...variables,
        s3_url: s3Url,
        project_id: projectId,
        dataset_id: datasetId,
        datasetName
      };
    } catch (error) {
      console.error('Error preparing diagnose data:', error);
      throw error;
    }
  }


  // Missing Value Analizi
  async analyzeMissingValues(variables_json: any): Promise<MissingValueStats[]> {
    const missingStats: MissingValueStats[] = [];

    variables_json.variable_list.forEach((variable: any) => {
      let missingCount = 0;
      const totalCount = variables_json.data.length;
      const columnIndex = variables_json.headers.indexOf(variable.header);

      variables_json.data.forEach((row: any) => {
        // Boş string, null, undefined veya NaN kontrolü
        const value = row[columnIndex];
        if (value === '' || value === null || value === undefined || (typeof value === 'number' && isNaN(value))) {
          missingCount++;
        }
      });

      if (missingCount > 0) {
        missingStats.push({
          variableName: variable.header,
          missingCount,
          totalCount,
          missingPercentage: (missingCount / totalCount) * 100,
          pattern: this.detectMissingPattern(variables_json.data.map(row => row[columnIndex]), variables_json.data)
        });
      }
    });
    return missingStats;
  }

  async analyzeVariableData(variables: any[]): Promise<{
    totalVariables: number;
    totalNullValues: number;
    variablesWithNull: number; // ✅ Null içeren değişken sayısı
  }> {
    if (!variables || variables.length === 0) {
      console.warn("⚠️ Uyarı: Değişkenler dizisi boş!");
      return { totalVariables: 0, totalNullValues: 0, variablesWithNull: 0 };
    }

    let totalNullValues = 0;
    let variablesWithNull = 0;

    variables.forEach((variable) => {
      if (variable.data && Array.isArray(variable.data)) {
        // Değişkendeki null değerleri say
        const nullCount = variable.data.filter(value => value === null).length;
        totalNullValues += nullCount;

        // Eğer bu değişkende en az bir null varsa, sayacı artır
        if (nullCount > 0) {
          variablesWithNull++;
        }
      }
    });

    return {
      totalVariables: variables.length,
      totalNullValues,
      variablesWithNull // ✅ Kaç değişkende null var
    };
  }


  private detectMissingPattern(columnData: any[], fullData: any[][]): string {
    const missingIndices = columnData.map((val, idx) =>
      val === null || val === undefined || val === '' ? idx : -1)
      .filter(idx => idx !== -1);

    if (missingIndices.length === 0) return 'No Missing';

    const gaps = missingIndices.slice(1)
      .map((val, idx) => val - missingIndices[idx]);

    const avgGap = gaps.reduce((a, b) => a + b, 0) / gaps.length;
    const stdDev = Math.sqrt(
      gaps.map(x => Math.pow(x - avgGap, 2))
        .reduce((a, b) => a + b, 0) / gaps.length
    );

    if (stdDev / avgGap < 0.5) return 'MNAR';
    return 'MCAR';
  }

  // Yeni Imputation Metodları
  imputeMean(columnData: any[]): number {
    const validValues = columnData.filter(val =>
      val !== null && val !== undefined && val !== '');
    return parseFloat((validValues.reduce((a, b) => a + Number(b), 0) / validValues.length).toFixed(2));
  }

  imputeMedian(columnData: any[]): number {
    const validValues = columnData
      .filter(val => val !== null && val !== undefined && val !== '')
      .map(Number)
      .sort((a, b) => a - b);

    const mid = Math.floor(validValues.length / 2);

    if (validValues.length % 2 === 0) {
      return (validValues[mid - 1] + validValues[mid]) / 2;
    }
    return validValues[mid];
  }

  imputeMode(columnData: any[]): any {
    const validValues = columnData.filter(val =>
      val !== null && val !== undefined && val !== '');

    const frequency: { [key: string]: number } = {};
    validValues.forEach(val => {
      frequency[val] = (frequency[val] || 0) + 1;
    });

    let maxFreq = 0;
    let mode = validValues[0];

    Object.entries(frequency).forEach(([val, freq]) => {
      if (freq > maxFreq) {
        maxFreq = freq;
        mode = val;
      }
    });

    return mode;
  }

  previewImputation(data: any, method: string, variableName: string): any[] {
    try {
      // Değişkenin index'ini bul
      const columnIndex = data.headers.indexOf(variableName);
      if (columnIndex === -1) {
        console.error('Variable not found:', variableName);
        return [];
      }

      // Kolon verilerini al
      const columnData = data.data.map((row: any[]) => row[columnIndex]);

      // Eksik değerleri bul
      const missingIndices = columnData.map((val: any, index: number) =>
        (val === null || val === '' || val === undefined) ? index : -1
      ).filter(index => index !== -1);

      // İmpute değerini hesapla
      let imputedValue: any;
      switch (method) {
        case 'mean':
          imputedValue = this.imputeMean(columnData);
          break;
        case 'median':
          imputedValue = this.imputeMedian(columnData);
          break;
        case 'mode':
          imputedValue = this.imputeMode(columnData);
          break;
        default:
          return [];
      }

      // Preview data'yı hazırla
      const preview = missingIndices.map(rowIndex => ({
        row: rowIndex + 1,
        originalValue: 'Missing',
        newValue: typeof imputedValue === 'number' ? Number(imputedValue).toFixed(2) : imputedValue
      }));

      return preview;

    } catch (error) {
      console.error('Preview imputation error:', error);
      return [];
    }
  }

  isSystemMissing(value: any): boolean {
    // Explicitly check for 0 and return false
    if (value === 0) return false;
    return value === null || value === undefined || value === '';
  }
  diagnoseAnalysis(datasetId: string): Observable<Record<string, boolean>> {
    return this.datasetService.getDatasetById(datasetId).pipe
    (
      map(dataset => {
        const variables = dataset.variables;
        const analysisTypes = [
          'comean', 'descriptive', 'single', 'multi', 'dependent', 'correlation', 'chisq',
          'logistic_cox', 'survival', 'roc', 'linear'
        ];

        const result: Record<string, boolean> = {};
        analysisTypes.forEach(type => {
          result[type] = this.analysisReqs.checkRequirements(type, variables);
        });

        return result;
      })
    );
  }

  getDatasetInfo(datasetId: string): Observable<any> {
    return this.datasetService.getDatasetById(datasetId);
  }

}
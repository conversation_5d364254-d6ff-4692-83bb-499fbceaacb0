import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { AnalysisService } from '@app/data/services/analysis.service';
import { TranslocoService } from '@ngneat/transloco';
import { tr } from 'date-fns/locale';
import { parse, formatDistanceToNow } from 'date-fns';
import * as juice from 'juice';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '@env/environment';
const codes = {
  tr: {
    "descriptive": "Tanımlayıcı İstatistik",
    "single": "Bağımsız Tek Grup Veri Analizi",
    "multi": "Bağımsız Çok Grup Veri Analizi",
    "correlation": "Korelasyon Analizi",
    "chisq": "Ki Kare Analizi",
    "dependent": "Bağımlı Veri Analizi",
    "comean": "Ortalama Karşılaştırma Analizi",
    "binary": "Lojistik/Cox Regresyon Analizi",
    "survival": "<PERSON>ğkalım Analizi",
    "roc": "ROC Analizi",
    "regression": "Doğrusal Regresyon Analizi"
  },
  en: {
    "descriptive": "Descriptive Statistics",
    "single": "Independent Single Group Data Analysis",
    "multi": "Independent Multiple Group Data Analysis",
    "correlation": "Correlation Analysis",
    "chisq": "Chi Square Analysis",
    "dependent": "Dependent Data Analysis",
    "comean": "Mean Comparison Analysis",
    "binary": "Logistic/Cox Regression Analysis",
    "survival": "Survival Analysis",
    "roc": "ROC Analysis",
    "regression": "Linear Regression Analysis"
  }
}
@Injectable({
  providedIn: 'root'
})
export class ReportHelperService {
  constructor(
    private a: AnalysisService,
    private langService: TranslocoService,
    private sanitizer: DomSanitizer,

  ) { }
  codeCounters = {};
  //Rapor lineleri döner
  getAnalysisReportLines(analysisId: string, limit?: number): Observable<any> {
    return this.a.getAnalysisReportLines(analysisId).pipe(
      map(data => {
        data.reverse();
        const report_lines = data ? data.map((item, index) => {
          return {
            ...item,
            label: `${item.title}`,
            created_at: item.credit_usage.created_at,
          };
        }).sort((a, b) => b.created_at - a.created_at) : [];

        // Eğer limit parametresi verilmişse, sadece o kadarını döndür
        const limitedReports = limit ? report_lines.slice(0, limit) : report_lines;

        return limitedReports;
      }),
      catchError(error => {
        console.error('Error fetching reports', error);
        return of([]);
      })
    );
  }
  getAllReportLines(): Observable<any> {
    return this.a.getReportLines().pipe(
      map(data => {
        const report_lines = data ? data.filter(line => line.credit_usage?.project_id).map((item, index) => {
          return {
            ...item,
            created_at: item.credit_usage.created_at,
            label: `${item.title}`,
          };
        }).sort((a, b) => b.created_at - a.created_at) : [];

        // Get unique project count from credit_usage
        const uniqueProjects = new Set(
          report_lines
            .filter(line => line.credit_usage?.project_id)
            .map(line => line.credit_usage.project_id)
        );

        return {
          report_lines,
          total_projects: uniqueProjects.size
        };
      }),
      catchError(error => {
        console.error('Error fetching reports', error);
        return of({
          report_lines: [],
          total_projects: 0
        });
      })
    );
  }
  // Rapor listesi döner
  private processAcknowledgements(acknowledgements): void {
    ['tr', 'en'].forEach(lang => {
      if (acknowledgements[lang]) {
        const sanitizedAcknowledgements = juice(`${acknowledgements[lang]}`, { extraCss: this.css });
        acknowledgements[lang] = this.sanitizer.bypassSecurityTrustHtml(sanitizedAcknowledgements) as string;
      }
    });
  }
  private processWarnings(warnings): void {
    if (!warnings) return;
    ['tr', 'en'].forEach(lang => {
      if (warnings[lang]) {
        const sanitizedWarnings = juice(`${warnings[lang]}`, { extraCss: this.css });
        warnings[lang] = this.sanitizer.bypassSecurityTrustHtml(sanitizedWarnings) as string;
      }
    });
  }
  getReportContentById(id: string): Observable<any> {
    return this.a.getReportContentById(id).pipe(
      map(data => {
        const report = data.reports[0];
        const created_at = new Date(report.lines[0].credit_usage.created_at);
        const element = {
          ...report,
          id: report.id,
          label: report.lines[0].title,
          analysisId: report.analysis.id,
          icon: this.icons[report.analysis.code],
          contents: report.content,
          created_at: {
            full_time: created_at,
            tr: formatDistanceToNow(created_at, { locale: tr, addSuffix: true }),
            en: formatDistanceToNow(created_at, { addSuffix: true })
          },
          methods_content: report.methods_content,
          lang: this.langService.getActiveLang(),
          code: report.analysis.code,
          credit_usage: this.creditController(report),
          line_id: report.lines[0].id
        };
        this.processContents(element.contents);
        const content = {
          reportLine: element,
          reportContent: {
            ...element,
            showOptions: false,
            lang: this.langService.getActiveLang()
          }
        }
        return content;
      }),
      catchError(error => {
        console.error('Error fetching report content', error);
        return of({});
      })
    );
  }
  // Analiz tiplerine göre icon listesi
  icons = {
    descriptive: 'assets/icons/descriptive.svg',
    single: 'assets/icons/single.svg',
    multi: 'assets/icons/multi.svg',
    dependent: 'assets/icons/dependent.svg',
    correlation: 'assets/icons/correlation.svg',
    chisq: 'assets/icons/chisq.svg',
    comean: 'assets/icons/comean.svg',
    logistic_cox: 'assets/icons/logistic_cox.svg',
    survival: 'assets/icons/survival.svg',
    roc: 'assets/icons/roc.svg',
    linear: 'assets/icons/linear.svg'
  };
  // Analiz tiplerine göre icon döner
  private getIconByAnalysisCode(code: string): string {
    return this.icons[code] || '';
  }
  // Rapor tablosu için styling
  private processContents(contents): void {
    contents.forEach(element => {
      ['tr', 'en'].forEach(lang => {
        const sanitizedTable = juice(`${element[lang].table}`, { extraCss: this.css });
        element[lang].table = this.sanitizer.bypassSecurityTrustHtml(sanitizedTable) as string;
      });
    });
  }
  // Kredi kontrolü
  private creditController(report: any) {
    if (report.content[0] && report.content[0].tr.table.includes('yetersiz bakiye')) {
      report.content[0].tr.table = `<div class="flex justify-center items-center">
      <img src="assets/img/report_tmp.png" class="w-full h-96 blur" />
      <p class="text-center font-semibold text-lg px-3 py-2 rounded-3xl bg-yellow-300/60 flex gap-2  absolute text-zinc-950 items-center" >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-8">
      <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
      </svg>
      Yetersiz bakiye,bu raporu görüntülemek için ${report.lines[0]?.credit_usage?.used_credit ?? 0} kredi gerekli
      </p>
      </div>`;
      report.content[0].en.table = `<div class="flex justify-center items-center">
      <img src="assets/img/report_tmp.png" class="w-full h-96 blur" />
      <p class="text-center font-semibold text-lg px-3 py-2 rounded-3xl bg-yellow-300/60 flex gap-2  absolute text-zinc-950 items-center" >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-8">
      <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
      </svg>
       Insufficient balance, ${report.lines[0]?.credit_usage?.used_credit ?? 0} credits required to view this report
      </p>
      </div>`;
      report.credit_usage = {
        tr: ' ',
        en: ' ',
        credit_usages: []
      }
      return report.credit_usage;
    } else {
      // Check if we have the new credit_usages array
      const creditUsages = report.lines[0]?.credit_usages || [];

      // Calculate total credits used
      let totalCredits = 0;
      if (creditUsages.length > 0) {
        // Sum up all used_credit values from the credit_usages array
        totalCredits = creditUsages.reduce((sum: number, usage: any) => sum + usage.used_credit, 0);
      } else {
        // Fallback to the old method if credit_usages is not available
        totalCredits = report.lines[0]?.credit_usage?.used_credit ?? 0;
      }

      // Format dates for each credit usage
      const formattedCreditUsages = creditUsages.map((usage: any) => {
        const date = new Date(usage.created_at);
        return {
          ...usage,
          formatted_date: {
            tr: date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR'),
            en: date.toLocaleDateString('en-US') + ' ' + date.toLocaleTimeString('en-US')
          }
        };
      });

      report.credit_usage = {
        tr: totalCredits == 0 ? '' : totalCredits + ' kredi kullanıldı',
        en: totalCredits == 0 ? '' : totalCredits + ' credit used',
        total_credits: totalCredits,
        credit_usages: formattedCreditUsages
      }
      return report.credit_usage;
    }
  }
  // Rapor tablosu için css
  css: string = `
    blockquote {
      margin: 1em 0 1em 1.7em;
      padding-left: 1em;
      border-left: 2px solid #e6e6e6;
      color: #606060;
    }
    code {
      font-family: Menlo, Monaco, Consolas, 'Lucida Console',Inter, monospace;
      font-size: 85%;
      margin: 0;
      hyphens: manual;
    }
    pre {
      margin: 1em 0;
      overflow: auto;
    }
    pre code {
      padding: 0;
      overflow: visible;
      overflow-wrap: normal;
    }
    .sourceCode {
      background-color: transparent;
      overflow: visible;
    }
    p span{
      display: none;
    }
    p span.inline{
      display: inline;
    }
    table {
      width: 100%;
      font-variant-numeric: lining-nums tabular-nums;
    }
    div::-webkit-scrollbar-thumb {
      background-image: linear-gradient(180deg, #d0368a 0%, #708ad4 99%);
      box-shadow: inset 2px 2px 5px 0 rgba(#fff, 0.5);
      border-radius: 100px;
    }
    div::-webkit-scrollbar-track
    {
        border-radius: 10px;
        webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    }
      table caption {
      margin-bottom: 0.75em;
      }

      tbody {
        margin-top: 1em;
        border-top: 1px solid #3a3a3a;
        border-bottom: 1px solid #1a1a1a;
      }
      tr td:nth-child(1){
      font-weight: bolder;
      max-width: 400px;
      overflow: hidden;
      text-wrap: wrap;
    }
      table tr:nth-child(even) {
  background-color: #e4e4e7;
}
      table tr:nth-child(odd) {
  background-color: #f4f4f5;
}
      th {
        border-top: 1px solid #1a1a1a;
        padding: 0.5em 0.5em 0.25em 0.5em;
      }
      td {
        padding: 0.125em 0.5em 0.25em 0.5em;
      }
      header {
        margin-bottom: 4em;
        text-align: center;
      }
      #TOC a:not(:hover) {
        text-decoration: none;
      }
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      div.columns{display: flex; gap: min(4vw, 1.5em);}
      div.column{flex: auto; overflow-x: auto;}
      div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}

      .display.math{display: block; text-align: center; margin: 0.5rem auto;}
    `;
}


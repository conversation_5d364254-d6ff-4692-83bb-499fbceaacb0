import { Analysis } from "./analysis.interface";

export interface Dataset {
    id: number;
    name: string;
    s3_url: string;
    project_id: number;
    variables_json: any;
    diagnosed_s3_url: string | null;
    created_at: string;
    updated_at: string;
    analyses: Analysis[];
  }

  export interface DatasetInfo {
    name: string;
    rowCount: number;
    variableCount: number;
    s3_url?: string;
  }
  
  export interface DataColumn {
    id: string;
    name: string;
    type: 'scale' | 'nominal' | 'ordinal';
    typeLabel: string;
    typeColor: string;
  }
  
  export interface ValueLabel {
    value: number;
    labelTR: string;
    labelEN: string;
    count: number;
  }
  
  export interface DataVariable {
    id: string;
    name: string;
    labelTR: string;
    labelEN: string;
    type: 'scale' | 'nominal' | 'ordinal';
    typeLabel: string;
    typeColor: string;
    description: string;
    missingValues: number;
    uniqueValues: number;
    min?: number;
    max?: number;
    mean?: number;
    median?: number;
    stdDev?: number;
    valueLabels?: ValueLabel[];
  }
  
  export interface DataRow {
    [key: string]: any;
  }
  
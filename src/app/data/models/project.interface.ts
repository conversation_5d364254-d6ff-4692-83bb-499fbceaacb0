import { Dataset } from "./dataset.interface";

export interface Project {
    id: number;
    name: string;
    job_id: string | null;
    created_at: string;
    updated_at: string;
    datasets: Dataset[];
    position?: number;
    favorite?: boolean;
    description?: string;
    status?: string;
    used_credits?: number;
    reports?: Report[];
    project_type?: 'normal' | 'demo_template' | 'demo';

    // Owner information - new structure from commit.txt
    owner?: {
        id: number;
        name: string;
        surname: string;
        email: string;
    };

    // Fallback owner fields (for backward compatibility)
    owner_name?: string;
    owner_email?: string;
}

export interface Report {
    id: string;
    label: string;
    created_at: string;
    favorite?: boolean;
    credit_usage?: {
        used_credit?: number;
    };
    credit_usages?: CreditUsage[];

    // New researcher information from commit.txt
    researcher_id?: number;
    researcher_name?: string;
}

export interface CreditUsage {
    created_at: string;
    used_credit: number;

    // New researcher information from commit.txt
    researcher_id?: number;
    researcher_name?: string;
}
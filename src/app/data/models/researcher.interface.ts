export interface Researcher {
    id: number;
    name: string;
    surname: string;
    email: string;
    full_name: string;
}

export interface ShareHistory {
    id: number;
    action_type: 'add_researcher' | 'remove_researcher' | 'transfer_ownership';
    created_at: string;
    actor: {
        id: number;
        full_name: string;
        email: string;
    };
    target_user: {
        id: number;
        full_name: string;
        email: string;
    };
}

export interface AddResearcherRequest {
    researcher: {
        email: string;
    };
}

export interface ApiResponse {
    message: string;
}
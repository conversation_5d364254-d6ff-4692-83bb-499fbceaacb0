// The file path would be something like: src/app/data/models/sidebar.interface.ts
import { Type } from '@angular/core';

export interface MenuItem {
  icon: string;
  name: string;
  route?: string;
  tooltip?: string;
  action?: 'dialog' | 'function'; // Add an action property to define behavior
  component?: Type<any>; // Add component property for dialogs
  function?: () => void; // Optional function property for direct function calls
}

export interface MenuSection {
  title: string;
  items: MenuItem[];
}
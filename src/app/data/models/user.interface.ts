export interface User {
  id: number;
  email: string;
  name: string;
  surname: string;
  phone_number: string;
  locale: string;
  created_at: string;
  roles: Array<{
    id: number;
    name: string;
    resource_type: string | null;
    resource_id: number | null;
  }>;
  gift_acceptance: boolean;
  profile?: {
    id?: number;
    university?: string;
    faculty?: string;
    department?: string;
    interests?: string;
    // Add other profile fields as needed
  };
}

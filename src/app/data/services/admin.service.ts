import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AuthService } from '@app/modules/auth/auth.service';
import { environment } from '@env/environment';
import { Observable, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AdminService {

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  updatePayment(payment_id: number, status: string) {
    return this.http.patch(environment.apiUrl + '/admin/payments/' + payment_id + '/update_status', { status });
  }

  getUsers(): Observable<any> {
    return this.http.get(environment.apiUrl + '/users');
  }

  getUser(): Observable<any> {
    return this.http.get(environment.apiUrl + '/users/' + localStorage.getItem('user_id')).pipe(
      tap((response: HttpResponse<any>) => {
        // if(response.body.roles){
          // localStorage.setItem('roles', JSON.stringify(response.body.roles));
        // }
      })
    );
  }


getUserCredits(userId: number): Observable<any> {

  return this.http.get(environment.apiUrl + `/user_credits`);
}


  getEftPayments(): Observable<any> {
    return this.http.get(environment.apiUrl + '/admin/payments?payment_method=eft');
  }

  getCreditCardPayments(): Observable<any> {
    return this.http.get(environment.apiUrl + '/admin/payments?payment_method=credit_card');
  }

  token: string | null = null;

  switchUser(target_email: string): Observable<any> {
    return this.http.post(environment.apiUrl + '/proxy_sessions/switch_user', { target_email }, { observe: 'response' }).pipe(
      tap((response: HttpResponse<any>) => {
        const token = response.headers.get('Authorization');
        if (token) {
          // Önce proje state'ini temizle
          this.clearProjectState();

          // Sonra token ve kullanıcı bilgilerini güncelle
          this.authService.clearToken();
          this.token = token.split(' ')[1]; // Bearer token'ı ayıklama
          localStorage.setItem('token', this.token); // Token'ı localStorage'a kaydetme
          localStorage.setItem('email', response.body.user.email);
          localStorage.setItem('username', response.body.user.name + ' ' + response.body.user.surname);
          localStorage.setItem('user_id', response.body.user.id);
          localStorage.setItem('roles', JSON.stringify(response.body.user.roles));
          localStorage.setItem('original_user', response.body.original_user.id);
        }
      })
    );
  }

  /**
   * Proje state'ini temizler
   * Kullanıcı değiştiğinde proje bilgilerinin temizlenmesi için kullanılır
   */
  private clearProjectState(): void {
    // ProjectStateService'e erişimimiz olmadığı için localStorage'daki ilgili verileri temizliyoruz
    localStorage.removeItem('currentPage');

    // Diğer proje ile ilgili localStorage verilerini temizle
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('project_') || key.includes('_project'))) {
        keysToRemove.push(key);
      }
    }

    // Bulunan anahtarları temizle
    keysToRemove.forEach(key => localStorage.removeItem(key));

    console.log('Project state cleared for user switch');
  }

  switchBack(): Observable<any> {
    const proxy_user_id = localStorage.getItem('original_user');
    return this.http.post(environment.apiUrl + '/proxy_sessions/switch_back', { proxy_user_id }, { observe: 'response' }).pipe(
      tap((response: HttpResponse<any>) => {
        const token = response.headers.get('Authorization');
        if (token) {
          // Önce proje state'ini temizle
          this.clearProjectState();

          // Sonra token ve kullanıcı bilgilerini güncelle
          this.authService.clearToken();
          this.token = token.split(' ')[1]; // Bearer token'ı ayıklama
          localStorage.setItem('token', this.token); // Token'ı localStorage'a kaydetme
          localStorage.setItem('email', response.body.user.email);
          localStorage.setItem('username', response.body.user.name + ' ' + response.body.user.surname);
          localStorage.setItem('user_id', response.body.user.id);
          localStorage.setItem('roles', JSON.stringify(response.body.user.roles));
        }
      })
    );
  }



  banUser(userId: number): Observable<any> {
    return this.http.post(environment.apiUrl + `/admin/users/${userId}/ban`, {});
  }

  unbanUser(userId: number): Observable<any> {
    return this.http.delete(environment.apiUrl + `/admin/users/${userId}/ban`);
  }

  cloneProject(projectId: number): Observable<any> {
    return this.http.post(environment.apiUrl + `/projects/${projectId}/clone`, {});
  }
  recalculateProject(analyses_id: number): Observable<any> {
    return this.http.post(environment.apiUrl + `/analyses/${analyses_id}/recalculate_all`, {});
  }
  checkAll(analyses_id: number, selected_analyses_id: number): Observable<any> {
    return this.http.post(environment.apiUrl + `/analyses/${analyses_id}/check_all/${selected_analyses_id}`, {});
  }


  /**
   * Kullanıcıya kredi hediye etme işlemi.
   * @param userId - Hediye gönderilecek kullanıcının ID'si
   * @param amount - Gönderilecek kredi miktarı
   * @param recipientEmail - Hediye gönderilecek kullanıcının e-posta adresi
   */
  sendCreditGift(userId: number, amount: number, recipientEmail: string): Observable<any> {
    const payload = {
      credit_gift: {
        recipient_email: recipientEmail,
        amount: amount
      }
    };
    return this.http.post(environment.apiUrl + `/users/${userId}/credit_gifts`, payload);
  }
  updateGiftState(userId: number, giftAcceptance: boolean): Observable<any> {
    const payload = { gift_acceptance: giftAcceptance };
    // environment.apiUrl zaten "http://*************:3000/v1" içeriyor
    return this.http.patch(environment.apiUrl + `/users/${userId}/gift_state`, payload);
  }


  getAllCreditGifts(userId: number): Observable<any> {
    return this.http.get(environment.apiUrl + `/users/${userId}/credit_gifts`);
  }

  totalGiftCredits: number = 0;

}

import { Injectable } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';

interface VariableRequirement {
  minCount?: number;
  types: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AnalysisRequirementsService {
  constructor(
    private transloco: TranslocoService
  ) { }
  private requirements: Record<string, VariableRequirement[]> = {
    'comean': [
      { types: ['nominal', 'ordinal'], minCount: 2 },
      { types: ['scale'], minCount: 1 }
    ],
    'descriptive': [
      { types: ['nominal', 'ordinal', 'scale'], minCount: 1 }
    ],
    'single': [
      { types: ['nominal', 'ordinal'], minCount: 1 }
    ],
    'multi': [
      { types: ['nominal', 'ordinal'], minCount: 2 }
    ],
    'dependent': [
      { types: ['nominal', 'ordinal'], minCount: 2 }
    ],
    'correlation': [
      { types: ['scale'], minCount: 2 }
    ],
    'chisq': [
      { types: ['nominal', 'ordinal'], minCount: 2 }
    ],
    'logistic_cox': [
      { types: ['nominal'], minCount: 1 },
      { types: ['scale', 'ordinal', 'nominal'], minCount: 1 },
      { types: ['scale'], minCount: 1 }
    ],
    'survival': [
      { types: ['nominal'], minCount: 1 },
      { types: ['ordinal', 'nominal'], minCount: 1 },
      { types: ['scale'], minCount: 1 }
    ],
    'roc': [
      { types: ['nominal'], minCount: 1 },
      { types: ['ordinal', 'nominal'], minCount: 1 }
    ],
    'linear': [
      { types: ['scale'], minCount: 1 },
      { types: ['scale', 'ordinal', 'nominal'], minCount: 1 }
    ]
  };

  checkRequirements(analysisType: string, variables: any[]): boolean {
    const requirements = this.requirements[analysisType];
    if (!requirements) return false;
    for (const req of requirements) {
      const matchingVars = variables.filter(v =>
        req.types.includes(v.measure.toLowerCase())
      );

      if (matchingVars.length < (req.minCount || 0)) {
        return false;
      }
    }

    return true;
  }

  getRequirementDescription(analysisType: string): string {
    const lang = this.transloco.getActiveLang();
    const reqs = this.requirements[analysisType];
    if (!reqs) return '';

    return reqs.map(req => {
      const types = req.types.join(lang === 'tr' ? ' veya ' : ' or ');
      return `${req.minCount || 0} ${this.transloco.translate('dataset_view.variables')} ${types}`;
    }).join(lang === 'tr' ? ' ve ' : ' and ');
  }
}

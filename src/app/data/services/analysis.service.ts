import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { retry, catchError, map } from 'rxjs/operators';
import { environment } from '@env/environment';
import { TranslocoService } from '@ngneat/transloco';
import { CacheService } from './cache.service';
@Injectable({
    providedIn: 'root',
})
export class AnalysisService {
    constructor(
        private http: HttpClient,
        private cacheService: CacheService
    ) { }
    createDescriptiveAnalysis(descriptive: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/descriptive',
                descriptive,
            )
            .pipe(retry(1), catchError(this.handleError));
    }
    createIndependentSingleAnalysis(single: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/independent/single',
                single,
            )
            .pipe(retry(1), catchError(this.handleError));
    }

    createIndependentMultiAnalysis(multi: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/independent/multi',
                multi,
            )
            .pipe(retry(1), catchError(this.handleError));
    }
    createDependentAnalysis(dependent: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/dependent',
                dependent,
            )
            .pipe(retry(1), catchError(this.handleError));
    }
    createCorrelationAnalysis(correlation: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/correlation',
                correlation,
            )
            .pipe(retry(1), catchError(this.handleError));
    }
    createChiSquareAnalysis(chiSquare: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/chisq',
                chiSquare,
            )
            .pipe(retry(1), catchError(this.handleError));
    }
    createMeanComparisonAnalysis(meanComparison: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/comean',
                meanComparison,
            )
            .pipe(retry(1), catchError(this.handleError));
    }

    createLogisticCoxAnalysis(logisticCox: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/binary',
                logisticCox,
            )
            .pipe(retry(1), catchError(this.handleError));
    }

    createSurvivalAnalysis(survival: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/survival',
                survival,
            )
            .pipe(retry(1), catchError(this.handleError));
    }

    createRocAnalysis(roc: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/roc',
                roc,
            )
            .pipe(retry(1), catchError(this.handleError));
    }

    createLinearAnalysis(linear: any): Observable<any> {
        return this.http
            .post<any>(
                environment.apiUrl + '/analyses/regression',
                linear,
            )
            .pipe(retry(1), catchError(this.handleError));
    }

    getAnalysisById(analysesId: string): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/analyses/' + analysesId, {})
            .pipe(retry(1), catchError(this.handleError));
    }

    getAnalysisRequests(): Observable<any> {
        return this.http
            .get<any>(
                environment.apiUrl + '/analysis_requests', {})
            .pipe(retry(1), catchError(this.handleError));
    }

    getAnalyses() {
        return this.http
            .get<any>(environment.apiUrl + '/analyses', {})
            .pipe(retry(1), catchError(this.handleError));
    }

    getReportLines(): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/report_lines', {})
            .pipe(retry(1), catchError(this.handleError));
    }
    updateReportLine(id: string, payload: any): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/report_lines/' + id + '/sync_translations', payload)
            .pipe(retry(1), catchError(this.handleError));
    }
    getAnalysisReportLines(analysisId: string): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/report_lines/analysis_report_lines?analysis_id=' + analysisId, {})
            .pipe(retry(1), catchError(this.handleError));
    }
    deleteReportContent(id: string): Observable<any> {
        return this.http
            .delete<any>(environment.apiUrl + '/report_contents/' + id, {})
            .pipe(retry(1), catchError(this.handleError));
    }
    getReportContentById(id: string): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/report_contents/' + id, {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .pipe(retry(1), catchError(this.handleError));
    }
    getReportContentPdfById(id: string, lang: string): Observable<any> {
        const cachedFile = this.cacheService.getCache(id + lang + '.pdf');
        if (cachedFile) {
            return of(cachedFile);
        }
        else {
            return this.http.get<any>(
                environment.apiUrl + '/report_contents/' + id + '.pdf' + '?locale=' + lang,
                { responseType: 'blob' as 'json' },
            ).pipe(
                map((data) => {
                    this.cacheService.setCache(id + lang + '.pdf', data);
                    return data;
                }
                ),
            )
        }
    }

    getReportContentDocxById(id: string, lang: string): Observable<any> {
        return this.http.get<any>(
            environment.apiUrl + '/report_contents/' + id + '.docx' + '?locale=' + lang,
            { responseType: 'blob' as 'json' },
        ).pipe(
            retry(1),
            catchError(this.handleError)
        );
    }
    getAllReportContentPdfById(id: string, lang: string): Observable<any> {
        return this.http.get<any>(
            environment.apiUrl + '/report_contents/' + id + '.allpdf' + '?locale=' + lang,
            { responseType: 'blob' as 'json' },
        );
    }
    getAllReportContentDocxById(id: string, lang: string): Observable<any> {
        return this.http.get<any>(
            environment.apiUrl + '/report_contents/' + id + '.alldocx' + '?locale=' + lang,
            { responseType: 'blob' as 'json' },
        );
    }

    getFavoriteReportsDocx(analysisId: string, lang: string): Observable<any> {
        return this.http.get<any>(
            environment.apiUrl + '/report_contents/' + analysisId + '.favorites' + '?locale=' + lang,
            { responseType: 'blob' as 'json' },
        );
    }

    setAnalysisConfigurations(payload: any, id: number): Observable<any> {
        return this.http
            .patch<any>(
                environment.apiUrl + '/analysis_configurations/' + id,
                payload,
            )
            .pipe(retry(1), catchError(this.handleError));
    }

    getCalculatedAnalysisCredits(analysis: any): Observable<any> {
        return this.http
            .post<any>(environment.apiUrl + '/credits/calculate_credits', analysis)
            .pipe(retry(1), catchError(this.handleError));
    }
    getCalculatedCredts(payload: any): Observable<any> {
        return this.http
            .post<any>(environment.apiUrl + '/credits/calculate_credits', payload)
            .pipe(retry(1), catchError(this.handleError));
    }
    getReportsAnalysisRequest(id: string): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/analysis_requests/' + id + '/request_clone_params', {})
            .pipe(retry(1), catchError(this.handleError));
    }
    updateAnalysisRequest(id: string, payload: any): Observable<any> {
        return this.http
            .post<any>(environment.apiUrl + '/analysis_requests/' + id + '/save_and_update', payload)
            .pipe(retry(1), catchError(this.handleError));
    }
    handleError(error: any) {
        let errorMessage = '';
        if (error.error instanceof ErrorEvent) {
            // Get client-side error
            errorMessage = ' Client Error ' + error.error.message;
        } else {
            // Get server-side error
            errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
        }
        console.warn(errorMessage);
        return throwError(() => {
            console.log(error.status);
            return errorMessage;
        });
    }

    updateReportDescription(id: string, payload: any): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/report_contents/' + id, payload)
            .pipe(retry(1), catchError(this.handleError));
    }

    toggleReportFavorite(id: string): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/report_contents/' + id + '/toggle_favorite', {})
            .pipe(retry(1), catchError(this.handleError));
    }

    updateReportPosition(id: string, position: number): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/report_contents/' + id + '/update_positions', { position })
            .pipe(retry(1), catchError(this.handleError));
    }

    getFavoriteReportsContent(analysisId: string, lang: string): Observable<any> {
        return this.http.get<any>(
            environment.apiUrl + '/report_contents/' + analysisId + '.favorites' + '?locale=' + lang,
            { responseType: 'blob' as 'json' },
        ).pipe(
            retry(1),
            catchError(this.handleError)
        );
    }
}
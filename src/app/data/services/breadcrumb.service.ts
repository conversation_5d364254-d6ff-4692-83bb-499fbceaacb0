// services/breadcrumb.service.ts
import { Injectable, OnDestroy } from '@angular/core';
import { NavigationEnd, NavigationStart, Router } from '@angular/router';
import { BehaviorSubject, Subject, filter, takeUntil } from 'rxjs';
import { Breadcrumb } from '../models/breadcrumb.interface';

interface BreadcrumbRoute {
  path: string;
  breadcrumb?: string | ((data: any) => string);
  icon?: string;
}

@Injectable({
  providedIn: 'root'
})
export class BreadcrumbService implements OnDestroy {
  private breadcrumbsSubject = new BehaviorSubject<Breadcrumb[]>([]);
  breadcrumbs$ = this.breadcrumbsSubject.asObservable();
  private destroy$ = new Subject<void>();
  private isMobile = false;

  // Rota yapılandırması - burada tüm rotalar için breadcrumb bilgilerini tanımlıyoruz
  private routeConfig: { [key: string]: BreadcrumbRoute } = {
    'projects': {
      path: '/projects',
      breadcrumb: 'navigation.projects',
      icon: 'lucideLayers'  // Sidebar ile uyumlu ikon
    },
    'reports': {
      path: '/reports',
      breadcrumb: 'navigation.reports',
      icon: 'lucideNotepadText'  // Sidebar ile uyumlu ikon
    },
    'project-detail': {
      path: '/projects/:pid',
      breadcrumb: (data: any) => data?.project?.name || 'Project',
      icon: 'lucideFolderOpen'
    },
    'project-overview': {
      path: '/projects/:pid/overview',
      breadcrumb: 'navigation.project.overview',
      icon: 'lucideLayoutDashboard'
    },
    'project-dataset': {
      path: '/projects/:pid/dataset',
      breadcrumb: 'navigation.project.dataset',
      icon: 'lucideDatabase'
    },
    'project-analysis': {
      path: '/projects/:pid/analysis',
      breadcrumb: 'navigation.project.analysis',
      icon: 'lucideChartBar'
    },
    'project-history': {
      path: '/projects/:pid/analysis/history',
      breadcrumb: 'navigation.project.history',
      icon: 'lucideHistory'
    },
    'project-settings': {
      path: '/projects/:pid/settings',
      breadcrumb: 'navigation.project.settings',
      icon: 'lucideSettings'
    },
    'report-detail': {
      path: '/reports/:id',
      breadcrumb: (data: any) => data?.report?.title_with_code || 'Report',
      icon: 'lucideFileText'
    }
  };

  // Sayfa geçişlerinde breadcrumb'ların korunması için
  private forcedBreadcrumbs: boolean = false;

  constructor(
    private router: Router
  ) {
    // Ekran boyutunu kontrol et
    this.checkIfMobile();
    window.addEventListener('resize', this.checkIfMobile.bind(this));

    // NavigationStart olaylarını dinle - sayfa değişmeden önce
    this.router.events.pipe(
      filter(event => event instanceof NavigationStart),
      takeUntil(this.destroy$)
    ).subscribe((event: NavigationStart) => {


      // Önceki sayfayı kontrol et
      const currentPage = localStorage.getItem('currentPage');


      // Özel rotalar için breadcrumb'ları hemen ayarla
      if (event.url === '/reports') {


        // Raporlar sayfasına geçiyoruz, breadcrumb'ları zorla ayarla
        const breadcrumbs: Breadcrumb[] = [
          {
            label: 'navigation.reports',
            link: '/reports',
            icon: 'lucideNotepadText'  // Sidebar ile uyumlu ikon
          }
        ];
        this.setBreadcrumbs(breadcrumbs);
        this.forcedBreadcrumbs = true;

        // Mevcut sayfa bilgisini güncelle
        localStorage.setItem('currentPage', 'reports');
      }
      else if (event.url === '/projects') {


        const breadcrumbs: Breadcrumb[] = [
          {
            label: 'navigation.projects',
            link: '/projects',
            icon: 'lucideLayers'  // Sidebar ile uyumlu ikon
          }
        ];
        this.setBreadcrumbs(breadcrumbs);
        this.forcedBreadcrumbs = true;

        // Mevcut sayfa bilgisini güncelle
        localStorage.setItem('currentPage', 'projects');
      }
      // Rapor detay sayfasına gidiyorsak, breadcrumb'ları korumak için işaretleyelim
      else if (event.url.includes('/reports/')) {


        // Rapor detay sayfasına geçiyoruz, breadcrumb'ları korumak için işaretle
        this.forcedBreadcrumbs = true;

        // Mevcut sayfa bilgisini güncelle
        localStorage.setItem('currentPage', 'report-detail');
      }
      // Proje sayfasından başka bir sayfaya geçiyorsak, breadcrumb'ları temizle
      else if (currentPage === 'project' && !event.url.includes('/projects/')) {
 

        // Proje sayfasından başka bir sayfaya geçiyoruz, breadcrumb'ları temizle
        const breadcrumbs: Breadcrumb[] = [];
        this.setBreadcrumbs(breadcrumbs);
        this.forcedBreadcrumbs = true;
      }
    });

    // NavigationEnd olaylarını dinle - sayfa değiştikten sonra
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe((event: NavigationEnd) => {

      // Mevcut sayfa bilgisini kontrol et
      const currentPage = localStorage.getItem('currentPage');

      // Eğer breadcrumb'lar zorla ayarlanmışsa, generateBreadcrumbs'ı çağırma
      if (this.forcedBreadcrumbs) {

        // Zorla ayarlanan breadcrumb'ları koru
        if (event.url === '/reports') {

          // Raporlar sayfasında olduğumuzu doğrula
          const breadcrumbs: Breadcrumb[] = [
            {
              label: 'navigation.reports',
              link: '/reports',
              icon: 'lucideNotepadText'  // Sidebar ile uyumlu ikon
            }
          ];
          this.setBreadcrumbs(breadcrumbs);
        }
        // Rapor detay sayfasında olduğumuzu doğrula
        else if (event.url.includes('/reports/') && currentPage === 'report-detail') {

          // Rapor detay bileşeni breadcrumb'ları ayarlayacak, burada bir şey yapmıyoruz
          // Sadece zorla ayarlama bayrağını koruyoruz
          return;
        }

        // Zorla ayarlama bayrağını sıfırla
        this.forcedBreadcrumbs = false;
      } else {
        // Normal breadcrumb oluşturma
        this.generateBreadcrumbs();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    window.removeEventListener('resize', this.checkIfMobile.bind(this));
  }

  /**
   * Mevcut rotaya göre otomatik olarak breadcrumb'ları oluşturur
   */
  private generateBreadcrumbs(): void {
    // Mevcut rotayı al
    const currentUrl = this.router.url;

    // Mevcut sayfa bilgisini kontrol et
    const currentPage = localStorage.getItem('currentPage');

    // Özel rotalar için breadcrumb'ları ayarla
    if (currentUrl === '/reports') {

      const breadcrumbs: Breadcrumb[] = [
        {
          label: 'navigation.reports',
          link: '/reports',
          icon: 'lucideNotepadText'  // Sidebar ile uyumlu ikon
        }
      ];
      this.setBreadcrumbs(breadcrumbs);

      // Mevcut sayfa bilgisini güncelle
      localStorage.setItem('currentPage', 'reports');
    }
    else if (currentUrl === '/projects') {

      const breadcrumbs: Breadcrumb[] = [
        {
          label: 'navigation.projects',
          link: '/projects',
          icon: 'lucideLayers'  // Sidebar ile uyumlu ikon
        }
      ];
      this.setBreadcrumbs(breadcrumbs);

      // Mevcut sayfa bilgisini güncelle
      localStorage.setItem('currentPage', 'projects');
    }
    // Diğer rotalar için mevcut breadcrumb'ları koru
    // Bu, bileşenlerin kendi breadcrumb'larını ayarlamasına izin verir
    else {
    }
  }

  /**
   * Breadcrumb'ları manuel olarak ayarlar
   */
  setBreadcrumbs(breadcrumbs: Breadcrumb[]) {

    // Mevcut URL'yi kontrol et
    const currentUrl = this.router.url;

    // Mevcut sayfa bilgisini kontrol et
    const currentPage = localStorage.getItem('currentPage');

    // Raporlar sayfasında olduğumuzu doğrula
    if (currentUrl === '/reports' && breadcrumbs.length > 0 && breadcrumbs[0].link !== '/reports') {

      // Raporlar sayfasındayız ama breadcrumb'lar farklı, zorla değiştir
      breadcrumbs = [
        {
          label: 'navigation.reports',
          link: '/reports',
          icon: 'lucideNotepadText'  // Sidebar ile uyumlu ikon
        }
      ];
    }

    // Rapor detay sayfasında olduğumuzu doğrula
    if (currentUrl.includes('/reports/') && currentPage === 'report-detail') {

      // Eğer breadcrumb'lar rapor detay sayfası için doğru değilse, değiştirme
      if (breadcrumbs.length >= 4 &&
          breadcrumbs[0].link === '/projects' &&
          breadcrumbs[2].label === 'navigation.project.history' &&
          breadcrumbs[3].link?.includes('/reports/')) {
      } else {
        console.warn('WARNING: Breadcrumbs do not match expected format for report detail page');
      }
    }

    // Mobil görünüm için breadcrumb'ları optimize et
    if (this.isMobile && breadcrumbs.length > 3) {
      // Mobil görünümde uzun breadcrumb yollarını kısalt
      const optimizedBreadcrumbs = [
        breadcrumbs[0], // İlk öğe (genellikle ana sayfa)
        {
          label: '...',
          link: undefined,
          icon: 'lucideMoveHorizontal',
          tooltip: 'Daha fazla'
        },
        ...breadcrumbs.slice(breadcrumbs.length - 2) // Son iki öğe
      ];
      this.breadcrumbsSubject.next(optimizedBreadcrumbs);
    } else {
      this.breadcrumbsSubject.next(breadcrumbs);
    }
  }

  /**
   * Belirli bir indeksteki breadcrumb'ı günceller
   */
  updateBreadcrumb(index: number, newLabel: string) {
    const currentBreadcrumbs = this.breadcrumbsSubject.getValue();
    if (currentBreadcrumbs[index]) {
      currentBreadcrumbs[index].label = newLabel;
      this.breadcrumbsSubject.next([...currentBreadcrumbs]);
    }
  }

  /**
   * Mevcut breadcrumb'ları döndürür
   */
  getCurrentBreadcrumbs(): Breadcrumb[] {
    return this.breadcrumbsSubject.getValue();
  }

  /**
   * Rota yapılandırmasına göre breadcrumb'ları oluşturur
   * @param route Rota
   * @param data Veri
   */
  createBreadcrumbsFromRoute(route: string, data?: any): Breadcrumb[] {
    const breadcrumbs: Breadcrumb[] = [];
    const routeParts = route.split('/').filter(part => part);

    let currentPath = '';

    // Her zaman ana sayfayı ekle
    breadcrumbs.push({
      label: 'navigation.home',
      link: '/',
      icon: 'lucideHome'
    });

    // Rota parçalarını dolaş
    for (let i = 0; i < routeParts.length; i++) {
      currentPath += `/${routeParts[i]}`;

      // Rota yapılandırmasında bu yol için bir giriş var mı kontrol et
      const configKey = Object.keys(this.routeConfig).find(key => {
        const configPath = this.routeConfig[key].path;
        // Parametreli yolları kontrol et
        if (configPath.includes(':')) {
          const pathPattern = configPath.replace(/:[^\/]+/g, '[^\/]+');
          const regex = new RegExp(`^${pathPattern}$`);
          return regex.test(currentPath);
        }
        return configPath === currentPath;
      });

      if (configKey) {
        const config = this.routeConfig[configKey];
        let label: string;

        // Breadcrumb bir fonksiyon mu yoksa string mi?
        if (typeof config.breadcrumb === 'function' && data) {
          label = config.breadcrumb(data);
        } else {
          label = config.breadcrumb as string || routeParts[i];
        }

        breadcrumbs.push({
          label,
          link: this.replaceRouteParams(config.path, route),
          icon: config.icon
        });
      } else {
        // Yapılandırmada bulunmayan rotalar için varsayılan breadcrumb
        breadcrumbs.push({
          label: this.capitalizeFirstLetter(routeParts[i].replace(/-/g, ' ')),
          link: currentPath
        });
      }
    }

    return breadcrumbs;
  }

  /**
   * Rota parametrelerini değiştirir
   * @param template Şablon
   * @param actualRoute Gerçek rota
   */
  private replaceRouteParams(template: string, actualRoute: string): string {
    const templateParts = template.split('/');
    const actualParts = actualRoute.split('/');

    return templateParts.map((part, index) => {
      if (part.startsWith(':')) {
        return actualParts[index] || part;
      }
      return part;
    }).join('/');
  }

  /**
   * İlk harfi büyük yapar
   * @param str Metin
   */
  private capitalizeFirstLetter(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Mobil görünümü kontrol eder
   */
  private checkIfMobile(): void {
    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth < 768;

    // Mobil durumu değiştiyse ve breadcrumb'lar varsa, yeniden optimize et
    if (wasMobile !== this.isMobile) {
      const currentBreadcrumbs = this.getCurrentBreadcrumbs();
      if (currentBreadcrumbs.length > 0) {
        this.setBreadcrumbs(currentBreadcrumbs);
      }
    }
  }
}
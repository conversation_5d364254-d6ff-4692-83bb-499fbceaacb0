import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CacheService {
  private cache = new Map<string, Blob>();

  constructor() {}

  setCache(key: string, value: Blob): void {
    this.cache.set(key, value);
  }

  getCache(key: string): Blob | null {
    return this.cache.get(key) || null;
  }

  hasCache(key: string): boolean {
    return this.cache.has(key);
  }
}

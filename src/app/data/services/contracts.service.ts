import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BYPASS_LOG } from '@app/modules/auth/auth.interceptor';
import { environment } from '@env/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ContractsService {

  constructor(
    private http: HttpClient
  ) { }
  getDistanceSalesContracts(payload: any) {
    return this.http.get(environment.apiUrl + '/generate_contract/distance_sale_agreement', { params: payload });
  }
  getCookiePolicy() : Observable<any> {
    return this.http.get('https://drive.google.com/uc?export=download&id=1iBK4-1Qgy2X-mnuFsslo0uYzala_bATq', { responseType: 'blob',  context: new HttpContext().set(BYPASS_LOG, true) });
  }
}

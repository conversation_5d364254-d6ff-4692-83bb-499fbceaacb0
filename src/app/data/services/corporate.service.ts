import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { environment } from '@env/environment';
import { AdminService } from './admin.service';

// New interface for project information
export interface UnitProject {
  id: number;
  name: string;
  status: '' | 'paid' | 'unpaid' | null;
  owner?: {
    id: number;
    email: string;
    name?: string;
  };
}

// New interfaces for unit activities
export interface UnitActivity {
  id: number;
  atype: 'credit_send' | 'credit_refund' | 'user_add' | 'user_remove' | 'manager_add' | 'manager_remove'; 
  status: 'done' | 'cancelled' | 'pending';
  detail: string | null;
  credit: number | null;
  email: string | null;
  reason: string | null;
  created_at: string;
  updated_at: string;
  unit: {
    id: number;
    name: string;
    description: string;
    created_at: string;
    updated_at: string;
    available_credits: number;
    managers: any[];
    members: {
      id: number;
      name: string;
      email: string;
      projects?: UnitProject[];
    }[];
  };
  user: {
    id: number;
    email: string;
    uid: string | null;
    name: string;
    surname: string;
    phone_number: string;
    locale: string;
    banned: boolean;
    banned_at: string | null;
    created_at: string;
    updated_at: string;
    gift_acceptance: boolean;
  } | null;
  unit_manager: {
    id: number;
    email: string;
    uid: string | null;
    name: string;
    surname: string;
    phone_number: string;
    locale: string;
    banned: boolean;
    banned_at: string | null;
    created_at: string;
    updated_at: string;
    gift_acceptance: boolean;
  } | null;
}

export interface UserRole {
  id: number;
  name: string;
  resource_type: string | null;
  resource_id: number | null;
  created_at: string;
  updated_at: string;
}

export interface UnitUser {
  id: number;
  email: string;
  uid: string | null;
  name: string;
  surname: string;
  phone_number?: string;
  locale: string;
  banned: boolean;
  banned_at: string | null;
  created_at: string;
  updated_at: string;
  gift_acceptance: boolean;
  roles: UserRole[];
  // Credit information may be added by the API
  credits?: {
    total: number;
    used: number;
    remaining: number;
  };
  // Unit-specific fields
  is_manager?: boolean;
  unit_name?: string;
  projects?: UnitProject[]; // Add projects field for unit members
}

export interface UnitCreditSummary {
  total: number;
  transferred: number;
  remaining: number;
  used: number;
  unused: number;
}

export interface UserCreditSummary {
  email: string;
  status: 'user_registered' | 'user_not_registered' | 'user_not_confirmed'; // Account status
  reason: string | null;
  role: Array<{
    id: number;
    name: string;
    resource_type: string;
    resource_id: number;
    created_at: string;
  }> | null;
  credit: {
    total: number;
    used: number;
    unused: number;
  };
  id?: number;
  projects?: UnitProject[]; // Add projects field to user summary
}

export interface UserCreditSummaryResponse {
  users: UserCreditSummary[];
}

@Injectable({
  providedIn: 'root'
})
export class CorporateService {
  private apiUrl = `${environment.apiUrl}/units`;
  private unitId: string = '';
  
  constructor(private http: HttpClient,
    private adminService: AdminService
  ) { }
  
  // Set active unit ID from selection in UI
  setUnitId(unitId: string): void {
    this.unitId = unitId;
  }
  
  // Get current unit ID
  getUnitId(): string {
    return this.unitId;
  }
  
  // USER MANAGEMENT
  
  // Get list of users in the unit
  getUnitUsers(): Observable<UnitUser[]> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.get<UnitUser[]>(`${this.apiUrl}/${this.unitId}/members`);
  }
  
  // Add user to unit
  addUserToUnit(email: string, reason?: string): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    
    const payload: any = {
      member: {
        email: email
      }
    };
    
    if (reason) {
      payload.member.reason = reason;
    }
    
    return this.http.post(`${this.apiUrl}/${this.unitId}/members`, payload);
  }
  
  // Remove user from unit
  removeUserFromUnit(email: string): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.delete(`${this.apiUrl}/${this.unitId}/members/${email}`);
  }
  
  // MANAGER MANAGEMENT
  
  // Add manager to unit
  addManagerToUnit(userId: number): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.post(`${this.apiUrl}/${this.unitId}/add_manager`, {
      user_id: userId
    });
  }
  
  // Remove manager from unit
  removeManagerFromUnit(userId: number): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.delete(`${this.apiUrl}/${this.unitId}/remove_manager/${userId}`);
  }
  
  // CREDIT MANAGEMENT
  
  // Get unit credits summary
  getUnitCreditSummary(userId?: number): Observable<UnitCreditSummary> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    let url = `${this.apiUrl}/${this.unitId}/credit_summary`;
    if (userId) {
      url += `?user_id=${userId}`;
    }
    return this.http.get<UnitCreditSummary>(url);
  }
  
  // List unit credits (full details)
  getUnitCredits(): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.get(`${this.apiUrl}/${this.unitId}/credits`);
  }
  
  // Send credits to user
  sendCreditToUser(userId: number, amount: number, reason?: string): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    
    const payload: any = {
      user_id: userId,
      unit_credit: {
        amount: amount
      }
    };
    
    if (reason) {
      payload.unit_credit.reason = reason;
    }
    
    return this.http.post(`${this.apiUrl}/${this.unitId}/unit_credits/send_credit`, payload);
  }
  
  // Refund credits from user
  refundCreditFromUser(userId: number, amount: number, reason?: string): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    
    const payload: any = {
      user_id: userId,
      unit_credit: {
        amount: amount
      }
    };
    
    if (reason) {
      payload.unit_credit.reason = reason;
    }
    
    return this.http.post(`${this.apiUrl}/${this.unitId}/unit_credits/refund_credit`, payload);
  }

  // Get user credit summary
  getUserCreditSummary(): Observable<UserCreditSummaryResponse> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.get<UserCreditSummaryResponse>(`${this.apiUrl}/${this.unitId}/user_credit_summary`);
  }
  
  // Get all units for selection
  getUnits(): Observable<any> {
    return this.http.get(`${this.apiUrl}`);
  }
  
  // Get specific unit details
  getUnit(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}`);
  }
  
  // New method to update project status
  updateProjectStatus(projectId: number, status: 'paid' | 'unpaid'): Observable<any> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.patch(`${environment.apiUrl}/projects/${projectId}/status`, { status });
  }
  
  // New method to get unit activities
  getUnitActivities(): Observable<UnitActivity[]> {
    if (!this.unitId) {
      return throwError(() => new Error('No unit ID set'));
    }
    return this.http.get<UnitActivity[]>(`${this.apiUrl}/${this.unitId}/actions`);
  }
}

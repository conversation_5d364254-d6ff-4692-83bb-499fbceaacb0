import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@env/environment';

@Injectable({
  providedIn: 'root'
})
export class CouponService {

  constructor(
    private http: HttpClient
  ) { }
  getCoupons() {
    return this.http.get(environment.apiUrl + '/coupons');
  }
  /**
   * 
   * @param order 
   * {
    "order": {
      "dataset_id": 3,
      "coupon_code": "USER_PERCENTbc1f59e2",
      "price": 3417.0,
      "order_items_data": [
        {
          "product_id": 31,
          "price": 1675.0
        },
        {
          "product_id": 32,
          "price": 1742.0
        }
      ]
    }
  }
   * @returns 
  {
      "original_price": 3417,
      "discount_amount": 512.55,
      "total_price": 2904.45
  }
   */
  applyCoupon(order: any) {
    return this.http.post(environment.apiUrl + '/orders/apply_coupon', order);
  }
}

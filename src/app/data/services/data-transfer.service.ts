import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DataTransferService {
  private datasetSource = new BehaviorSubject<any>(null);
  dataset$ = this.datasetSource.asObservable();

  private totalVariablesSource = new BehaviorSubject<number | null>(null);
  totalVariables$ = this.totalVariablesSource.asObservable();

  private totalNullValuesSource = new BehaviorSubject<number | null>(null);
  totalNullValues$ = this.totalNullValuesSource.asObservable();

  private notImportedVariablesSource = new BehaviorSubject<number | null>(null);
  notImportedVariables$ = this.notImportedVariablesSource.asObservable();

  setDataset(dataset: any) {
    this.datasetSource.next(dataset);
  }

  setTotalVariables(value: number) {
    this.totalVariablesSource.next(value);
  }

  setTotalNullValues(value: number) {
    this.totalNullValuesSource.next(value);
  }

  setNotImportedVariables(value: number) {
    this.notImportedVariablesSource.next(value);
  }

  /**
   * Alınan variableList üzerinden:
   * - dataset olarak aktarır,
   * - toplam değişken sayısını hesaplar,
   * - her değişkene ait varsa "nullCount" değeri toplamını hesaplar,
   * - imported property'si false olan değişkenlerin sayısını belirler.
   *
   * @param variableList Değişken listesinin yer aldığı dizi
   */
  setAllVariables(variableList: any[]) {
    // Dataset olarak aktar
    this.setDataset(variableList);

    // Toplam değişken sayısını ayarla
    const totalVariables = variableList ? variableList.length : 0;
    this.setTotalVariables(totalVariables);

    // Varsayalım her değişkende varsa "nullCount" property'si null değer sayısını tutuyor.
    const totalNullValues = variableList 
      ? variableList.reduce((acc, variable) => acc + (typeof variable.nullCount === 'number' ? variable.nullCount : 0), 0)
      : 0;
    this.setTotalNullValues(totalNullValues);

    // "imported" property'si false olan değişkenlerin sayısını hesapla
    const notImportedCount = variableList 
      ? variableList.filter(variable => variable.imported === false).length 
      : 0;
    this.setNotImportedVariables(notImportedCount);
  }
}

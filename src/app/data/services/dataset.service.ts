import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpHeaders } from '@angular/common/http';
import { from, Observable, throwError } from 'rxjs';
import { retry, catchError, map } from 'rxjs/operators';
import * as XLSX from 'xlsx';
import { environment } from '@env/environment';
import { BYPASS_LOG } from '@app/modules/auth/auth.interceptor';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
export interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
}

export interface ValidationError {
    code: string;
    message: string;
    details?: any;
}

export interface ValidationRule {
    check: (data: any) => boolean;
    errorCode: string;
    errorMessage: string;
}
@Injectable({
    providedIn: 'root',
})
export class DatasetService {
    private readonly ALLOWED_EXTENSIONS = ['.xlsx', '.xls'];
    private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private readonly MAX_COLUMNS = 500;
    private readonly MAX_ROWS = 100000;
    private readonly MIN_COLUMNS = 1;
    private readonly MIN_ROWS = 3;

    constructor(
        private http: HttpClient,
        private transloco: TranslocoService,
        private snotifyService: SnotifyService
    ) {
    }
    updateMeasure(variable_id: number, dataset_id: number) {
        return this.http.post(environment.apiUrl + '/datasets/' + dataset_id + '/change_variable_measure',
            {
                variable_id
            }
        )
    }
    createDiagnosedDataset(dataset_id: any, diagnosed_s3_url: string): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/datasets/' + dataset_id + '/create_diagnosed_dataset', { diagnosed_s3_url })
            .pipe(retry(1), catchError(this.handleError));
    }
    updateDatasetVariables(variables_json: any): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/datasets/' + variables_json.dataset_params, { variables_json })
            .pipe(retry(1), catchError(this.handleError));
    }

    // method: POST
    // url: /v1/presigned_url
    // params: filename, content_type
    getUploadPresignedUrl(filename: string, content_type: string): Observable<any> {
        return this.http.post(environment.apiUrl + '/presigned_url', { filename, content_type });
    }
    uploadToS3(presignedUrl: string, file: File) {
        const headers = new HttpHeaders({
            'Content-Type': file.type // AWS'e dosyanın content type'ını gönderiyoruz
        });

        return this.http.put(presignedUrl, file, { headers, context: new HttpContext().set(BYPASS_LOG, true) });
    }
    getDeletePresignedUrl(filename: string): Observable<any> {
        return this.http.delete(environment.apiUrl + '/presigned_url', { params: { filename } });
    }
    getGetPresignedUrl(filename: string): Observable<any> {
        return this.http.get(environment.apiUrl + '/presigned_url', { params: { filename } });
    }
    deleteToS3(presignedUrl: string) {
        return this.http.delete(presignedUrl, { context: new HttpContext().set(BYPASS_LOG, true) });
    }

    async uploadDataset(blob: Blob, filename: string, oldUrl?: string): Promise<string> {
        try {
            // Filename kontrolü
            if (!filename) {
                throw new Error('Filename is required for upload');
            }

            // Eğer eski URL varsa, o dosyayı sil
            if (oldUrl) {
                try {
                    const oldFilename = decodeURIComponent(oldUrl.split('/').pop() || ''); // URL'den dosya adını al ve decode et

                    const deleteUrlResponse = await this.getDeletePresignedUrl(oldFilename).toPromise();
                    // if (deleteUrlResponse?.url) {
                    await this.deleteToS3(deleteUrlResponse).toPromise();
                    // }
                } catch (deleteError) {
                    console.warn('Error deleting old file:', deleteError);
                    // Silme hatası kritik değil, devam et
                }
            }

            // Yeni dosya için presigned URL al
            const response = await this.getUploadPresignedUrl(
                filename,
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ).toPromise();

            if (!response?.url) {
                throw new Error('Failed to get upload URL');
            }

            // S3'e yükle
            const file = new File([blob], filename, {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });

            await this.uploadToS3(response.url, file).toPromise();

            // S3'deki tam URL'i döndür
            return `https://istabot-development.s3.eu-central-1.amazonaws.com/${filename}`;
        } catch (error) {
            console.error('Upload dataset error:', error);
            throw error;
        }
    }
    async getDataset(fileName) {
        try {
            const response = await this.getGetPresignedUrl(fileName).toPromise();
            if (!response?.url) {
                throw new Error('Failed to get upload URL');
            }
            return fetch(response?.url).then(response => response.blob());

        } catch (error) {
            console.error('Upload dataset error:', error);
            throw error;
        }
    }
    // Dataset URL'ini güncelle
    async updateDatasetUrl(dataset_id: string, newUrl: string, dataset_name?: string): Promise<void> {
        try {

            const updateData: any = {
                s3_url: newUrl
            };
            if (dataset_name) {
                updateData.name = dataset_name;
            }

            await this.http.patch(
                `${environment.apiUrl}/datasets/${dataset_id}`,
                updateData
            ).toPromise();

        } catch (error) {
            console.error('Error updating dataset URL:', error);
            throw error;
        }
    }
    async updateDiagnoseUrl(dataset_id: string, newUrl: string,): Promise<void> {
        try {
            const updateData: any = {
                diagnosed_s3_url: newUrl
            };

            await this.http.patch(
                `${environment.apiUrl}/datasets/${dataset_id}`,
                updateData
            ).toPromise();

        } catch (error) {
            console.error('Error updating dataset URL:', error);
            throw error;
        }
    }

    addDatasetToProject(s3_url: string, name: string, project_id: string): Observable<any> {
        return this.http
            .post<any>(environment.apiUrl + '/datasets', { project_id, s3_url, name }
            );
    }
    getDatasetAll(): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/datasets', {})
            .pipe(retry(1), catchError(this.handleError));
    }

    getDatasetById(id: string,): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/datasets/' + id, {})
            .pipe(retry(1), catchError(this.handleError));
    }

    getExcelDataViewByUrl(url: string): Promise<any[]> {
        return this.http.get(url, { responseType: 'blob', context: new HttpContext().set(BYPASS_LOG, true) }).toPromise().then(blob => {
            return this.parseExcelDataView(blob as any);
        }).then(data => {
            return this.trimNestedArrays(data);
        }
        );
    }
    getExcelVariableViewByUrl(url: string): Promise<any[]> {
        return this.http.get(url, { responseType: 'blob', context: new HttpContext().set(BYPASS_LOG, true) }).toPromise().then(blob => {
            return this.parseExcelVariableView(blob as any);
        });
    }

    parseExcelDataView(blob: Blob): Promise<any[]> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e: any) => {
                const bstr = e.target.result;
                const wb = XLSX.read(bstr, { type: 'binary' });
                const wsname = wb.SheetNames[0];
                const ws = wb.Sheets[wsname];
                const data = XLSX.utils.sheet_to_json(ws, { header: 1, defval: "---", raw: false });
                // Clean up the data by removing empty rows and columns
                const cleanedData = this.cleanExcelData(data);
                resolve(cleanedData);
            };
            reader.onerror = error => reject(error);
            reader.readAsBinaryString(blob);
        });
    }

    private cleanExcelData(data: any[]): any[][] {
        // Remove empty rows (rows with all "---")
        const nonEmptyRows = data.filter(row => !row.every(cell => cell === "---"));

        if (nonEmptyRows.length === 0) return [];

        // Find the last non-empty column index
        const maxColIndex = Math.max(...nonEmptyRows.map(row => {
            let lastIndex = -1;
            for (let i = row.length - 1; i >= 0; i--) {
                if (row[i] !== "---") {
                    lastIndex = i;
                    break;
                }
            }
            return lastIndex;
        }));

        // Trim each row to remove trailing "---" cells
        return nonEmptyRows.map(row => row.slice(0, maxColIndex + 1));
    }

    trimArray(arr: any[]): any[] {
        if (arr.every(val => val === "---")) {
            return [];
        }
        let lastIndex = arr.length - 1;
        for (let i = lastIndex; i >= 0; i--) {
            if (arr[i] !== "---") {
                for (let j = i + 1; j <= lastIndex; j++) {
                    if (arr[j] === "---" && arr.slice(j, lastIndex + 1).every(x => x === "---")) {
                        arr.splice(j);
                        break;
                    }
                }
                break;
            }
        }
        return arr;
    }

    trimNestedArrays(nestedArr: any[][]): any[][] {
        return nestedArr.map(innerArr => this.trimArray(innerArr));
    }

    private parseExcelVariableView(blob: Blob): Promise<any[]> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e: any) => {
                const bstr = e.target.result;
                const wb = XLSX.read(bstr, { type: 'binary' });
                const wsname = wb.SheetNames[1];
                const ws = wb.Sheets[wsname];
                const data = XLSX.utils.sheet_to_json(ws, { header: 1, defval: "---" });
                resolve(data);
            };
            reader.onerror = error => reject(error);
            reader.readAsBinaryString(blob);
        });
    }

    // Error handling
    handleError(error: any) {
        let errorMessage = '';
        if (error.error instanceof ErrorEvent) {
            // Get client-side error
            errorMessage = error.error.message;
        } else {
            // Get server-side error
            errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
        }
        console.warn(errorMessage);
        return throwError(() => {
            console.log(error.status);
            return errorMessage;
        });
    }
    /**
       * Dosya formatı ve boyut validasyonu
       */
    validateFile(file: File): ValidationResult {
        const errors: ValidationError[] = [];

        // Dosya uzantısı kontrolü
        const extension = this.getFileExtension(file.name).toLowerCase();
        if (!this.ALLOWED_EXTENSIONS.includes(extension)) {
            errors.push({
                code: 'INVALID_FILE_TYPE',
                message: this.transloco.translate('validation.file_type', {
                    allowed: this.ALLOWED_EXTENSIONS.join(', ')
                })
            });
        }

        // Dosya boyutu kontrolü
        if (file.size > this.MAX_FILE_SIZE) {
            errors.push({
                code: 'FILE_TOO_LARGE',
                message: this.transloco.translate('validation.file_size', {
                    maxSize: this.formatFileSize(this.MAX_FILE_SIZE)
                })
            });
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Veri seti içeriği validasyonu
     */
    validateDataContent(data: any[]): ValidationResult {
        const errors: ValidationError[] = [];

        // Boş veri seti kontrolü
        if (!Array.isArray(data) || data.length === 0) {
            errors.push({
                code: 'EMPTY_DATASET',
                message: this.transloco.translate('validation.empty_dataset')
            });
            return { isValid: false, errors };
        }

        // Satır sayısı kontrolü
        if (data.length > this.MAX_ROWS) {
            errors.push({
                code: 'TOO_MANY_ROWS',
                message: this.transloco.translate('validation.max_rows', {
                    max: this.MAX_ROWS,
                    current: data.length
                })
            });
        }

        if (data.length < this.MIN_ROWS) {
            errors.push({
                code: 'TOO_FEW_ROWS',
                message: this.transloco.translate('validation.min_rows')
            });
        }

        // Sütun sayısı kontrolü
        const headerRow = data[0];
        if (!Array.isArray(headerRow)) {
            errors.push({
                code: 'INVALID_HEADER_ROW',
                message: this.transloco.translate('validation.invalid_header')
            });
        } else {
            if (headerRow.length > this.MAX_COLUMNS) {
                errors.push({
                    code: 'TOO_MANY_COLUMNS',
                    message: this.transloco.translate('validation.max_columns', {
                        max: this.MAX_COLUMNS,
                        current: headerRow.length
                    })
                });
            }

            if (headerRow.length < this.MIN_COLUMNS) {
                errors.push({
                    code: 'TOO_FEW_COLUMNS',
                    message: this.transloco.translate('validation.min_columns')
                });
            }

            // Sütun başlıklarının boş olup olmadığını kontrol et
            const emptyHeaders = headerRow.filter(header =>
                !header || (typeof header === 'string' && header.trim() === '')
            );

            if (emptyHeaders.length > 0) {
                errors.push({
                    code: 'EMPTY_HEADERS',
                    message: this.transloco.translate('validation.empty_headers')
                });
            }

            // Tekrar eden sütun başlıklarını kontrol et
            const uniqueHeaders = new Set(headerRow);
            if (uniqueHeaders.size !== headerRow.length) {
                errors.push({
                    code: 'DUPLICATE_HEADERS',
                    message: this.transloco.translate('validation.duplicate_headers')
                });
            }
        }

        // Veri tutarlılığı kontrolü
        if (data.length > 1) {
            const columnCount = headerRow.length;
            const inconsistentRows = data.slice(1).filter(row =>
                !Array.isArray(row) || row.length !== columnCount
            );

            if (inconsistentRows.length > 0) {
                errors.push({
                    code: 'INCONSISTENT_ROWS',
                    message: this.transloco.translate('validation.inconsistent_rows', {
                        count: inconsistentRows.length
                    })
                });
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Tam validasyon - hem dosya hem içerik için
     */
    async validateDataset(file: File, data: any[]): Promise<ValidationResult> {
        const fileValidation = this.validateFile(file);
        const contentValidation = this.validateDataContent(data);

        return {
            isValid: fileValidation.isValid && contentValidation.isValid,
            errors: [...fileValidation.errors, ...contentValidation.errors]
        };
    }

    /**
     * Yardımcı metodlar
     */
    private getFileExtension(filename: string): string {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 1);
    }

    private formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
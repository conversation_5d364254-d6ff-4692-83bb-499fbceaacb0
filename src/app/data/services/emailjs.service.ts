import emailjs from '@emailjs/browser';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class EmailJsService {

  private serviceId = "service_v1wfhxn";
  private templateId = "template_48a59cm";
  private publicKey = "o9TqDVlONwom86VF2";

  sendReportDownloadNotification(reportId: number) {
    const userId = localStorage.getItem('user_id') || 'unknown_user';
    const username = localStorage.getItem('username') || 'unknown_user';
    const timeStamp = new Date().toLocaleString();

    emailjs.send(
      this.serviceId,
      this.templateId,
      {
        user_id: userId,
        dataset_id: 'from_report_list',
        username: username,
        format: 'docx',
        report_id: reportId,
        time_stamp: timeStamp
      },
      {
        publicKey: this.publicKey
      }
    )
  }
}

import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import { DiagnoseHelperService } from '../helper/diagnose.helper.service';

export interface ExcelData {
  variables_json: {
    headers: string[];
    data: any[][];
    variable_list: Variable[];
  };
}

export interface Variable {
  id: string;
  header: string;
  label: {
    en: string;
    tr: string;
  };
  measure: 'Scale' | 'Nominal' | 'Ordinal';
  import: boolean;
  value_labels?: {
    en: { [key: string]: string };
    tr: { [key: string]: string };
  };
  // Computed variable için temel alanlar
  computed?: boolean;
  formula?: string;
  sourceVariables?: string[];
  computationType?: string;
  dependencies?: string[];
  autoUpdate?: boolean;
  validationIssues?: { type: 'error' | 'warning', message: string }[];
  missingCount?: number;  // Add this line
  imputed?: boolean;
  recoded?: boolean;
  imputationMethod?: string;
  recodingMethod?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ExcelService {
  async readDatasetName(file: File): Promise<string> {
    return this.readWorkbook(file).then(workbook => {
      const variableSheet = workbook.Sheets['Variable View'];
      if (!variableSheet) {
        return file.name;
      }

      const variableData = XLSX.utils.sheet_to_json(variableSheet);
      if (variableData.length === 0) {
        return file.name;
      }

      return variableData[0]['fileName'] || file.name;
    });
  }
  async readExcelFile(file: File): Promise<ExcelData> {
    const workbook = await this.readWorkbook(file);

    // Data View sayfasını oku
    const dataSheet = workbook.Sheets['Data View'] || workbook.Sheets[workbook.SheetNames[0]];
    const rawJsonData = XLSX.utils.sheet_to_json(dataSheet, { header: 1, raw: false, defval: null }) as any[][];

    // Boş satır ve sütunları filtrele
    const jsonData = rawJsonData.filter(row => {
      return row.some(cell => cell !== null && cell !== undefined && cell !== '');
    });

    // Boş sütunların indexlerini bul
    const headers = jsonData[0] as string[];
    const nonEmptyColumnIndexes = headers.reduce((acc, header, index) => {
      const columnHasData = jsonData.slice(1).some(row =>
        row[index] !== null && row[index] !== undefined && row[index] !== ''
      );
      if (header && columnHasData) {
        acc.push(index);
      }
      return acc;
    }, [] as number[]);

    // Sadece dolu sütunları al
    const filteredHeaders = nonEmptyColumnIndexes.map(i => headers[i]);
    const filteredData = jsonData.slice(1).map(row =>
      nonEmptyColumnIndexes.map(i => row[i])
    );

    // Sayısal dönüşüm için kontrol
    for (let colIndex = 0; colIndex < filteredHeaders.length; colIndex++) {
      const column = filteredData.map(row => row[colIndex]);

      // Önce tüm "boş gibi görünen" değerleri temizle
      for (let rowIndex = 0; rowIndex < filteredData.length; rowIndex++) {
        const value = filteredData[rowIndex][colIndex];
        if (this.isEmptyLikeValue(value)) {
          filteredData[rowIndex][colIndex] = '';
        }
      }

      // Temizlendikten sonra sayısal dönüşüm kontrolü
      const cleanedColumn = filteredData.map(row => row[colIndex]);
      const canConvertToNumber = cleanedColumn.every(value =>
        value === null ||
        value === undefined ||
        value === '' ||
        !isNaN(Number(value))
      );

      if (canConvertToNumber) {
        for (let rowIndex = 0; rowIndex < filteredData.length; rowIndex++) {
          const value = filteredData[rowIndex][colIndex];
          if (value !== null && value !== undefined && value !== '') {
            filteredData[rowIndex][colIndex] = Number(value);
          }
        }
      }
    }

    // Calculate missing values for each column
    const missingCounts = new Map<string, number>();
    filteredHeaders.forEach((header, colIndex) => {
      const missingCount = filteredData.filter(row =>
        row[colIndex] === null ||
        row[colIndex] === undefined ||
        row[colIndex] === ''
      ).length;
      missingCounts.set(header, missingCount);
    });

    // Variable View işlemleri
    const variableSheet = workbook.Sheets['Variable View'];
    let variables: Variable[] = [];
    let fileName = file.name;

    if (variableSheet) {
      const variableData = XLSX.utils.sheet_to_json(variableSheet, { defval: "" });
      if (variableData.length > 0 && typeof variableData[0] === 'object' && variableData[0] && 'fileName' in variableData[0]) {
        fileName = variableData[0]['fileName'] as string;
      }

      variables = this.processVariableSheet(variableData, missingCounts)
        .filter(v => filteredHeaders.includes(v.header)); // Sadece var olan headerlar için
      if (variables.length === 0) {
        variables = this.generateVariableList([filteredHeaders, ...filteredData], missingCounts
        );
      }
    } else {
      variables = this.generateVariableList([filteredHeaders, ...filteredData], missingCounts);
    }

    return {

      variables_json: {
        headers: filteredHeaders,
        data: filteredData,
        variable_list: variables
      }
    };
  }

  private isEmptyLikeValue(value: any): boolean {
    if (value === null || value === undefined) return false;

    if (typeof value === 'string') {
      const trimmed = value.trim();
      // Boş string, sadece boşluk/tab, veya tire kombinasyonları
      return trimmed === '' ||
        trimmed === '-' ||
        trimmed === '--' ||
        trimmed === '---' ||
        /^[\s\t]+$/.test(value); // Sadece boşluk/tab karakterleri
    }

    return false;
  }

  async writeExcelFile(data: ExcelData, fileName?: string): Promise<Blob> {
    if (!data?.variables_json?.headers || !data?.variables_json?.data || !data?.variables_json?.variable_list) {
      throw new Error('Invalid data structure for Excel file');
    }

    // Boş sütunları filtrele
    const nonEmptyColumnIndexes = data.variables_json.headers.reduce((acc, header, index) => {
      const columnHasData = data.variables_json.data.some(row =>
        row[index] !== null && row[index] !== undefined && row[index] !== ''
      );
      if (columnHasData) {
        acc.push(index);
      }
      return acc;
    }, [] as number[]);

    const filteredHeaders = nonEmptyColumnIndexes.map(i => data.variables_json.headers[i]);
    const filteredData = data.variables_json.data.map(row =>
      nonEmptyColumnIndexes.map(i => row[i])
    );

    // Sayısal dönüşüm kontrolü ve dönüştürme
    for (let colIndex = 0; colIndex < filteredHeaders.length; colIndex++) {
      const column = filteredData.map(row => row[colIndex]);
      const canConvertToNumber = column.every(value =>
        value === null ||
        value === undefined ||
        value === '' ||
        !isNaN(Number(value))
      );

      if (canConvertToNumber) {
        for (let rowIndex = 0; rowIndex < filteredData.length; rowIndex++) {
          const value = filteredData[rowIndex][colIndex];
          if (value !== null && value !== undefined && value !== '') {
            filteredData[rowIndex][colIndex] = Number(value);
          }
        }
      }
    }
    const workbook = XLSX.utils.book_new();

    // Data View sayfası
    const dataSheet = XLSX.utils.aoa_to_sheet([
      filteredHeaders,
      ...filteredData
    ]);
    XLSX.utils.book_append_sheet(workbook, dataSheet, 'Data View');

    // Variable View sayfası
    const filteredVariables = data.variables_json.variable_list
      .filter(v => filteredHeaders.includes(v.header));

    // ...existing Variable View code...
    const variableData = filteredVariables.map(variable => {
      const shouldImport = variable.import && (!variable.validationIssues || !variable.validationIssues.some(issue => issue.type === 'error'));

      return {
        'Header': variable.header,
        'Label': variable.label?.tr || variable.header,
        'Label_en': variable.label?.en || variable.header,
        'Type': 'Numeric',
        'Value Labels': this.formatBackendValueLabels(variable.value_labels?.tr),
        'Value Labels_en': this.formatBackendValueLabels(variable.value_labels?.en),
        'Measure': variable.measure || 'Scale',
        'Import': shouldImport ? 1 : 0,
        'Is Computed': variable.computed ? 1 : 0,
        'Formula': variable.formula || '',
        'Source Variables': variable.sourceVariables?.join(', ') || '',
        'Computation Type': variable.computationType || '',
        'Dependencies': variable.dependencies?.join(', ') || '',
        'Auto Update': variable.autoUpdate ? 1 : 0,
        // Add new properties
        'Is Imputed': variable.imputed ? 1 : 0,
        'Imputation Method': variable.imputationMethod || '',
        'Is Recoded': variable.recoded ? 1 : 0,
        'Recoding Method': variable.recodingMethod || '',
        'fileName': fileName
      };
    });

    const variableSheet = XLSX.utils.json_to_sheet(variableData);
    XLSX.utils.book_append_sheet(workbook, variableSheet, 'Variable View');

    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    return new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
  }

  async writeDataToExcelFile(data: ExcelData): Promise<Blob> {
    if (!data?.variables_json?.headers || !data?.variables_json?.data || !data?.variables_json?.variable_list) {
      throw new Error('Invalid data structure for Excel file');
    }

    // Create a copy of the data to avoid modifying the original
    const processedData = [...data.variables_json.data];

    // Check each column for numeric conversion
    for (let colIndex = 0; colIndex < data.variables_json.headers.length; colIndex++) {
      const column = processedData.map(row => row[colIndex]);
      const canConvertToNumber = column.every(value =>
        value === null ||
        value === undefined ||
        value === '' ||
        !isNaN(Number(value))
      );

      if (canConvertToNumber) {
        for (let rowIndex = 0; rowIndex < processedData.length; rowIndex++) {
          const value = processedData[rowIndex][colIndex];
          if (value !== null && value !== undefined && value !== '') {
            processedData[rowIndex][colIndex] = Number(value);
          }
        }
      }
    }

    const workbook = XLSX.utils.book_new();

    // Data View sayfası
    const dataSheet = XLSX.utils.aoa_to_sheet([
      data.variables_json.headers,
      ...processedData
    ]);
    XLSX.utils.book_append_sheet(workbook, dataSheet, 'Data View');

    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    return new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
  }

  private async readWorkbook(file: File): Promise<XLSX.WorkBook> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          resolve(workbook);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = error => reject(error);
      reader.readAsArrayBuffer(file);
    });
  }
  private formatBackendValueLabels(labels?: { [key: string]: string }): string {
    if (!labels) return '';

    // Backend formatı: {1,asdf},{2,asdf},{3,asdf} 
    return Object.entries(labels)
      .map(([value, label]) => `{${value},${label ? label : ' '}}`)
      .join(',');
  }

  private processVariableSheet(variableData: any[], missingCounts: Map<string, number>): Variable[] {
    const requiredHeaders = [
      'Header',
      'Label',
      'Label_en',
      'Measure',
      'Value Labels',
      'Value Labels_en',
      'Import',
      'Is Computed',
      'Formula',
      'Source Variables',
      'Computation Type',
      'Dependencies',
      'Auto Update',
      // Add new headers
      'Is Imputed',
      'Imputation Method',
      'Is Recoded',
      'Recoding Method'
    ];

    if (!variableData || variableData.length === 0) {
      return [];
    }
    const actualHeaders = Object.keys(variableData[0]);
    const missingBasicHeaders = requiredHeaders
      .slice(0, 6)
      .filter(header => !actualHeaders.includes(header));
    if (missingBasicHeaders.length > 0) {
      return [];
    }
    return variableData.map(row => ({
      id: row['Header'],
      expanded: false,
      header: row['Header'],
      missingCount: missingCounts.get(row['Header']) || 0,
      label: {
        en: row['Label_en'] || row['Header'],
        tr: row['Label'] || row['Header']
      },
      measure: row['Measure'] || 'Scale',
      import: row['Import'] === undefined ? true : row['Import'] === 1,
      value_labels: this.parseValueLabelsFromRow(row),

      // Computed variable temel bilgileri
      computed: row['Is Computed'] === 1,
      formula: row['Formula'] || '',
      sourceVariables: row['Source Variables'] ?
        row['Source Variables'].split(',').map(s => s.trim()) : [],
      computationType: row['Computation Type'] || undefined,
      dependencies: row['Dependencies'] ?
        row['Dependencies'].split(',').map(s => s.trim()) : undefined,
      autoUpdate: row['Auto Update'] === 1,

      // New properties for imputed and recoded
      imputed: row['Is Imputed'] === 1,
      imputationMethod: row['Imputation Method'] || undefined,
      recoded: row['Is Recoded'] === 1,
      recodingMethod: row['Recoding Method'] || undefined
    }));
  }

  private parseValueLabelsFromRow(row: any): { en: { [key: string]: string }, tr: { [key: string]: string } } | undefined {
    const trLabels = this.parseBackendValueLabels(row['Value Labels']);

    const enLabels = this.parseBackendValueLabels(row['Value Labels_en']);

    if (!trLabels && !enLabels) return undefined;

    return {
      en: enLabels || {},
      tr: trLabels || {}
    };
  }
  private parseBackendValueLabels(labelString?: string): { [key: string]: string } | undefined {
    if (!labelString) return undefined;

    try {
      // {1,asdf},{2,asdf},{3,metin, virgüllü} formatını parse et
      const result: { [key: string]: string } = {};

      // Düzenli ifade ile süslü parantez içindeki tüm içeriği yakala
      const labels = labelString.match(/\{([^}]+)\}/g) || [];

      labels.forEach((label: string) => {
        // Başındaki '{' ve sonundaki '}' karakterlerini kaldır
        const content = label.slice(1, -1);

        // İlk virgülü bul - bu değer ve metin arasındaki ayırıcıdır
        const firstCommaIndex = content.indexOf(',');

        if (firstCommaIndex !== -1) {
          // İlk virgüle kadar olan kısım değerdir
          const value = content.substring(0, firstCommaIndex).trim();

          // İlk virgülden sonraki kısım metindir (içinde virgül olabilir)
          const text = content.substring(firstCommaIndex + 1).trim();

          if (value && text) {
            result[value] = text;
          }
        }
      });

      return result;
    } catch {
      return undefined;
    }
  }

  private generateVariableList(jsonData: any[][], missingCounts: Map<string, number>): Variable[] {
    const headers = jsonData[0];
    const data = jsonData.slice(1);

    return headers.map((header, index) => {
      const columnData = data.map(row => row[index]);
      const dataType = this.determineDataType(columnData);

      return {
        id: header,
        expanded: false,
        header: header,
        missingCount: missingCounts.get(header) || 0,  // Add this line
        label: {
          en: header,
          tr: header
        },
        measure: dataType,
        import: true,
        value_labels: dataType !== 'Scale' ?
          this.generateValueLabels(columnData) : undefined
      };
    });
  }


  determineDataType(values: any[]): 'Scale' | 'Nominal' | 'Ordinal' {
    // Boş değerleri filtrele ve string değerleri trim et
    const nonEmptyValues = values.filter(val =>
      val !== null && val !== undefined &&
      (typeof val === 'string' ? val.trim() !== '' : val !== ''));

    if (nonEmptyValues.length === 0) {
      return 'Scale'; // Default type
    }

    // String değer kontrolü (trim edilmiş)
    const hasStringValues = nonEmptyValues.some(val =>
      typeof val === 'string' && isNaN(Number(val.trim()))
    );

    if (hasStringValues) {
      return 'Nominal';
    }

    // Unique değerleri al
    const uniqueValues = new Set(nonEmptyValues);
    const uniqueCount = uniqueValues.size;

    // Eğer tüm değerler sayısal görünümlü ve ondalıklı sayılar varsa kesin Scale'dir
    const hasDecimals = nonEmptyValues.some(val =>
      String(val).includes('.') && !isNaN(Number(val)));
    if (hasDecimals) {
      return 'Scale';
    }

    // Tüm değerler 1,2,3 gibi kategorik olabilecek sayılar mı?
    const allSmallIntegers = nonEmptyValues.every(val =>
      Number.isInteger(Number(val)) &&
      Number(val) >= 0 &&
      Number(val) <= 10 &&
      !isNaN(Number(val))
    );

    // Değerler sıralı ve artımsal mı kontrol et
    if (allSmallIntegers) {
      const sortedValues = [...new Set(nonEmptyValues)].map(Number).sort((a, b) => a - b);
      const isSequential = sortedValues.every((val, idx) =>
        idx === 0 || (val === sortedValues[0] + idx)
      );
      const startsWithZeroOrOne = sortedValues[0] === 0 || sortedValues[0] === 1;
      if (isSequential && sortedValues.length <= 3 && startsWithZeroOrOne) {
        return 'Nominal';
      }
    }

    // Tüm değerler sayı mı kontrol et
    const allNumeric = nonEmptyValues.every(val => !isNaN(Number(val)));

    // Eğer sayısal değerlerin unique sayısı az ve sıralı bir yapıdaysa Ordinal olabilir
    if (allNumeric && uniqueCount <= 10) {
      const sortedValues = [...uniqueValues].map(Number).sort((a, b) => a - b);
      const isSequential = sortedValues.every((val, idx, arr) =>
        idx === 0 || (val - arr[idx - 1]) <= 2
      );
      const startsWithZeroOrOne = sortedValues[0] === 0 || sortedValues[0] === 1;
      if (isSequential && startsWithZeroOrOne) {
        return 'Ordinal';
      }
    }

    return 'Scale';
  }

  private generateValueLabels(values: any[]) {
    const uniqueValues = [...new Set(values)]
      .filter(v => v != null && v !== undefined &&
        (typeof v === 'string' ? v.trim() !== '' : v !== ''))
      .map(v => typeof v === 'string' ? v.trim() : v);

    const labels = uniqueValues.reduce((acc, val) => {
      acc[val] = "";
      return acc;
    }, {});

    return { en: labels, tr: labels };
  }
}
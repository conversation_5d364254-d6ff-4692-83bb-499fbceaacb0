import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment, } from '@env/environment';
import { catchError, from, map, Observable, retry, throwError } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
/**
 * Service for handling payment-related operations.
 */
export class PaymentService {
  constructor(
    private http: HttpClient,
  ) {
  }

  getInvoiceAddresses(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/invoice_addresses`);
  }
  addInvoiceAddress(invoice_address: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/invoice_addresses`, { invoice_address });
  }
  updateInvoiceAddress(invoice_address: any): Observable<any> {
    return this.http.patch<any>(`${environment.apiUrl}/invoice_addresses/${invoice_address.id}`, { invoice_address });
  }
  deleteInvoiceAddress(invoice_address_id: string): Observable<any> {
    return this.http.delete<any>(`${environment.apiUrl}/invoice_addresses/${invoice_address_id}`);
  }
  /**
   * 
   * @param order_id 
   * @returns 
   * 
   */
  getOrder(order_id: string): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/orders/${order_id}`);
  }
  /**
   * Creates an order using the provided payload.
   * @param payload The payload for creating the order.
   * @returns An observable that emits the response from the API.
   * {
       "order": {
         "dataset_id": 3,
         "coupon_code": "USER_PERCENTbc1f59e2",
         "discount": 512.55,
         "price": 3417.0,
         "total_price": 2904.45,
         "payment_method": "eft",
         "receipt_s3_url": "https://istabot-test.s3.eu-central-1.amazonaws.com/ornek-dekont.jpg",
         "order_items_data": [
           {
             "product_id": 31,
             "price": 1675.0
           },
           {
             "product_id": 32,
             "price": 1742.0
           }
         ]
       }
      }
   */
  createOrder(order: any): Observable<any> {
    return this.http
      .post<any>(environment.apiUrl + '/orders', { order })
      .pipe(retry(1), catchError(this.handleError));
  }
  getOrders(): Observable<any> {
    return this.http
      .get<any>(environment.apiUrl + '/orders', {})
      .pipe(retry(1), catchError(this.handleError));
  }


  /**
   * Applies a coupon to the order.
   * @param coupon The coupon code to apply.
   * @returns An observable that emits the response from the API.
   * @example paylaod
   *    
    *{
    "order": {
      "dataset_id": 3,
      "coupon_code": "USER_PERCENTbc1f59e2",
      "price": 3417.0,
      "order_items_data": [
        {
          "product_id": 31,
          "price": 1675.0
        },
        {
          "product_id": 32,
          "price": 1742.0
        }
      ]
    }
  }
   * @example response
   * {
    "original_price": 3417,
    "discount_amount": 512.55,
    "total_price": 2904.45
      }
   */
  applyCoupon(order: any): Observable<any> {
    return this.http
      .post<any>(environment.apiUrl + '/orders/coupon', { order })
      .pipe(retry(1), catchError(this.handleError));
  }
  getCoupons(): Observable<any> {
    return this.http
      .get<any>(environment.apiUrl + '/coupons', {})
      .pipe(retry(1), catchError(this.handleError));
  }
  /**
   * Gets the list of products.
   * @returns An observable that emits the response from the API.
   * @example response
   * [
    {
        "id": 31,
        "name": "Bağımsız Tek Grup Veri Analizi",
        "price": "1675.0",
        "code": "single",
        "analysis_group_id": 1,
        "usage_limit_id": 6,
        "product_category_id": 2,
        "product_category": {
            "id": 2,
            "name": "Analizler"
        },
        "analysis_group": {
            "id": 1,
            "name": "Bağımsız Tek Grup Veri Analizi",
            "code": "single"
        },
        "usage_limit": {
            "id": 6,
            "name": "12 Ay 50 Kullanım",
            "quota": 50,
            "validity_period": 12
        }
    },
]
    */
  getProducts(): Observable<any> {
    return this.http
      .get<any>(environment.apiUrl + '/products', {})
      .pipe(retry(1), catchError(this.handleError));
  }
  getOrderInvoice(order_id: string): Observable<any> {
    return this.http
      .get<any>(environment.apiUrl + '/invoices/pdf_url?order_id=' + order_id , {})
      .pipe(retry(1));
  }
  /**
   * 
   * @returns An observable that emits the response from the API.
   * @example response
   * {
    "data": [
        {
            "id": 13,
            "quota": 50,
            "remaining_quota": 50,
            "validity_period": 12,
            "expires_at": "2025-06-01T10:53:13.201Z",
            "dataset_id": 3,
            "product_id": 31,
            "user_id": 2
        },
        {
            "id": 14,
            "quota": 25,
            "remaining_quota": 25,
            "validity_period": 6,
            "expires_at": "2024-12-01T10:53:13.219Z",
            "dataset_id": 3,
            "product_id": 32,
            "user_id": 2
        }
    ],
    "merged": [
        {
            "user_id": 2,
            "dataset_id": 3,
            "product_id": 31,
            "total_quota": 50,
            "total_remaining_quota": 50
        },
        {
            "user_id": 2,
            "dataset_id": 3,
            "product_id": 32,
            "total_quota": 25,
            "total_remaining_quota": 25
        }
    ]
}
   */
  getCreditPackages(): Observable<any> {
    return this.http
      .get<any>(environment.apiUrl + '/credit_packages', {})
      .pipe(retry(1), catchError(this.handleError));
  }
  getUserCredits(): Observable<any> {
    return this.http
      .get<any>(environment.apiUrl + '/user_credits', {})
      .pipe(retry(1), catchError(this.handleError));
  }
  /**
   * Handles errors that occur during API requests.
   * @param error The error object.
   * @returns An observable that emits the error message.
   */
  handleError(error: any) {
    let errorMessage = '';
    if (error.error instanceof ErrorEvent) {
      // Get client-side error
      errorMessage = error.error.message;
    } else {
      // Get server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    console.warn(errorMessage);
    return throwError(() => {
      console.log(error);
      return errorMessage;
    });
  }

  private paymentState: PaymentState = {};

  updateState(newState: Partial<PaymentState>) {
    // Special handling for selectedPackages to avoid overwriting
    if (newState.selectedPackages !== undefined) {
      // If we have new packages, update them
      this.paymentState.selectedPackages = newState.selectedPackages;
      delete newState.selectedPackages; // Remove from the newState to avoid duplicating logic
    } 
    // Otherwise keep the existing packages
    
    // Update other state properties
    this.paymentState = {
      ...this.paymentState,
      ...newState
    };
    
  }

  getState(): PaymentState {
    return this.paymentState;
  }

  resetState() {
    this.paymentState = {};
  }

  startCreditCardPayment(paymentData: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/payments/credit-card`, paymentData)
      .pipe(
        retry(1),
        catchError(this.handleError)
      );
  }

  showBankTransferDialog(paymentData: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/payments/bank-transfer/info`, paymentData)
      .pipe(
        retry(1),
        catchError(this.handleError)
      );
  }

  verifyPayment(paymentId: string): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/payments/${paymentId}/verify`)
      .pipe(
        retry(1),
        catchError(this.handleError)
      );
  }

  uploadBankTransferReceipt(orderId: string, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('receipt', file);

    return this.http.post<any>(`${environment.apiUrl}/orders/${orderId}/upload-receipt`, formData)
      .pipe(
        retry(1),
        catchError(this.handleError)
      );
  }

  completeZeroPayment(paymentData: any): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/orders/zero-payment`, paymentData)
      .pipe(
        retry(1),
        catchError(this.handleError)
      );
  }
}

export interface PaymentState {
  agreed?: boolean;
  discount?: number;
  appliedCoupon?: { code: string } | null;
  currentStep?: number;
  selectedPackages?: any[];
  totalCredits?: number;
  totalPrice?: number;
  billingInfo?: any;
  subTotal?: number;
  tax?: number;
  total?: number;
  paymentMethod?: 'creditCard' | 'bankTransfer';
}

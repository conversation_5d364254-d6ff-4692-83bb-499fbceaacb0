import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { retry, catchError } from 'rxjs/operators';
import { environment } from '@env/environment';
import { <PERSON>er, AddResearcherRequest, ApiResponse, ShareHistory } from '@app/data/models/researcher.interface';

@Injectable({
    providedIn: 'root',
})
export class ProjectService {
    constructor(
        private http: HttpClient,
    ) { }

    // =================== EXISTING PROJECT METHODS ===================

    getProjectAll(): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/projects', {})
            .pipe(retry(1), catchError(this.handleError));
    }

    updateProjectName(id: string, name: string): Observable<any> {
        return this.http
            .put<any>(environment.apiUrl + '/projects/' + id, { name })
            .pipe(retry(1), catchError(this.handleError));
    }

    getProjectById(id: number): Observable<any> {
        return this.http
            .get<any>(environment.apiUrl + '/projects/' + id, {})
            .pipe(retry(1), catchError(this.handleError));
    }

    createProject(data: any): Observable<any> {
        return this.http
            .post<any>(environment.apiUrl + '/projects', data)
            .pipe(retry(1), catchError(this.handleError));
    }

    deleteProject(id: number): Observable<any> {
        return this.http
            .delete<any>(environment.apiUrl + '/projects/' + id)
            .pipe(retry(1), catchError(this.handleError));
    }

    updateProjectDescription(id: string, description: string): Observable<any> {
        return this.http
            .put<any>(environment.apiUrl + '/projects/' + id, { description })
            .pipe(retry(1), catchError(this.handleError));
    }

    toggleProjectFavorite(id: string): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/projects/' + id + '/toggle_favorite', {})
            .pipe(retry(1), catchError(this.handleError));
    }

    updateProjectPosition(id: string, position: number): Observable<any> {
        return this.http
            .patch<any>(environment.apiUrl + '/projects/' + id + '/update_positions', { position })
            .pipe(retry(1), catchError(this.handleError));
    }

    updateProjectType(id: string, projectType: string): Observable<any> {
        return this.http
            .patch<any>(`${environment.apiUrl}/projects/${id}/update_project_type`, {
                project: { project_type: projectType }
            })
            .pipe(retry(1), catchError(this.handleError));
    }

    // =================== NEW RESEARCHER MANAGEMENT METHODS ===================

    /**
     * Projedeki araştırmacıları listeler
     * @param projectId Proje ID'si
     * @returns Araştırmacı listesi
     */
    getResearchers(projectId: number): Observable<Researcher[]> {
        return this.http
            .get<Researcher[]>(`${environment.apiUrl}/projects/${projectId}/researchers`)
            .pipe(retry(1), catchError(this.handleError));
    }

    /**
     * Projeye yeni araştırmacı ekler
     * @param projectId Proje ID'si
     * @param request Araştırmacı ekleme request'i
     * @returns API yanıtı
     */
    addResearcher(projectId: number, request: AddResearcherRequest): Observable<ApiResponse> {
        return this.http
            .post<ApiResponse>(`${environment.apiUrl}/projects/${projectId}/researchers`, request)
            .pipe(retry(1), catchError(this.handleError));
    }

    /**
     * Projeden araştırmacı çıkarır
     * @param projectId Proje ID'si
     * @param userId Kullanıcı ID'si
     * @returns API yanıtı
     */
    removeResearcher(projectId: number, userId: number): Observable<ApiResponse> {
        return this.http
            .delete<ApiResponse>(`${environment.apiUrl}/projects/${projectId}/researchers/${userId}`)
            .pipe(retry(1), catchError(this.handleError));
    }

    /**
     * Proje sahipliğini başka kullanıcıya transfer eder
     * @param projectId Proje ID'si
     * @param userId Yeni sahip kullanıcı ID'si
     * @returns API yanıtı
     */
    transferOwnership(projectId: number, userId: number): Observable<ApiResponse> {
        return this.http
            .put<ApiResponse>(`${environment.apiUrl}/projects/${projectId}/researchers/${userId}/transfer_ownership`, {})
            .pipe(retry(1), catchError(this.handleError));
    }

    /**
     * Proje paylaşım geçmişini listeler
     * @param projectId Proje ID'si
     * @returns Paylaşım geçmişi listesi
     */
    getShareHistories(projectId: number): Observable<ShareHistory[]> {
        return this.http
            .get<ShareHistory[]>(`${environment.apiUrl}/projects/${projectId}/share_histories`)
            .pipe(retry(1), catchError(this.handleError));
    }

    // =================== ERROR HANDLING ===================

    // Error handling - Fixed to properly return the error
    handleError(error: any) {
        let errorMessage = '';
        if (error.error instanceof ErrorEvent) {
            // Get client-side error
            errorMessage = error.error.message;
        } else {
            // Get server-side error
            errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
        }
        console.warn('ProjectService Error:', errorMessage);
        console.warn('Full Error Object:', error);

        // Return the original error object, not just the message string
        // This preserves the status code and other error details
        return throwError(() => error);
    }
}
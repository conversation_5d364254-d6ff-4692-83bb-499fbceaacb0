<nav #breadcrumbNav class="relative flex overflow-x-auto" aria-label="breadcrumb"
  [class.overflow]="isOverflowing">
  <!-- <PERSON><PERSON><PERSON><PERSON><PERSON>ü - <PERSON> -->
  <ol *ngIf="!isMobile" class="flex items-center space-x-1.5">
    <li *ngFor="let item of breadcrumbs$ | async; let last = last; let first = first" class="flex items-center">
      <ng-container *ngIf="!last">
        <a [routerLink]="item.link"
          class="breadcrumb-chip flex items-center text-neutral-600 hover:text-gray-900"
          [title]="item.tooltip || (item.label | transloco)">
          <ng-icon *ngIf="item.icon" [name]="item.icon" class="text-gray-500 flex-shrink-0"></ng-icon>
          <span class="ml-1">{{ item.label | transloco }}</span>
        </a>
        <ng-icon name="lucideChevronRight" class="text-gray-400"></ng-icon>
      </ng-container>
      <span *ngIf="last" class="breadcrumb-chip flex items-center font-medium text-brand-blue-600">
        <ng-icon *ngIf="item.icon" [name]="item.icon" class="text-brand-blue-500 flex-shrink-0"></ng-icon>
        <span class="ml-1">{{ item.label | transloco }}</span>
      </span>
    </li>
  </ol>

  <!-- Mobil Görünümü - Daha Kompakt -->
  <ol *ngIf="isMobile" class="flex items-center space-x-1 whitespace-nowrap">
    <li *ngFor="let item of breadcrumbs$ | async; let last = last; let first = first" class="flex items-center">
      <ng-container *ngIf="!last">
        <a [routerLink]="item.link"
          class="breadcrumb-chip flex items-center text-neutral-600 hover:text-gray-900"
          [title]="item.tooltip || (item.label | transloco)">
          <ng-icon *ngIf="item.icon" [name]="item.icon" class="text-gray-500 flex-shrink-0"></ng-icon>
          <span *ngIf="!first && item.shortLabel" class="ml-1">{{ item.shortLabel | transloco }}</span>
          <span *ngIf="first || !item.shortLabel" class="ml-1">{{ item.label | transloco }}</span>
        </a>
        <ng-icon name="lucideChevronRight" class="text-gray-400"></ng-icon>
      </ng-container>
      <span *ngIf="last" class="breadcrumb-chip flex items-center font-medium text-brand-blue-600">
        <ng-icon *ngIf="item.icon" [name]="item.icon" class="text-brand-blue-500 flex-shrink-0"></ng-icon>
        <span class="ml-1">{{ item.label | transloco }}</span>
      </span>
    </li>
  </ol>

  <!-- Sağa kaydırma göstergesi -->
  <div *ngIf="isOverflowing" class="absolute top-0 right-0 flex items-center h-full pointer-events-none">
    <div class="w-8 h-full bg-gradient-to-r from-transparent to-neutral-100"></div>
    <ng-icon name="lucideChevronRight" class="w-3 h-3 text-gray-400 animate-pulse"></ng-icon>
  </div>
</nav>
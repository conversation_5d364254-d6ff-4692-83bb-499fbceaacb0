// Breadcrumb styles
nav {
  // Ensure breadcrumbs don't overflow on small screens
  overflow-x: auto;
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE and Edge
  min-height: 32px; // Daha kompakt yükseklik

  // Hide scrollbar for Chrome, Safari and Opera
  &::-webkit-scrollbar {
    display: none;
  }

  // Add a subtle fade effect on the right side when content overflows
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 24px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &.overflow::after {
    opacity: 1;
  }
}

// Breadcrumb items
ol {
  white-space: nowrap;
  padding: 0.25rem 0; // Daha az padding
  margin: 0; // Margin'i kaldır

  li {
    transition: all 0.2s ease;
    font-size: 0.8125rem; // <PERSON><PERSON> k<PERSON> font boyutu (13px)

    a, span {
      display: inline-flex;
      align-items: center;
      padding: 0.125rem 0.375rem; // Daha az padding
      border-radius: 999px;
      transition: all 0.2s ease;
      line-height: 1.2; // Daha küçük satır yüksekliği

    }


    // Last item (current page)
    &:last-child span {
      font-weight: 600;
      color: var(--brand-blue-600, #0052F9);
    }

    // Chevron icon'u küçült
    ng-icon[name="lucideChevronRight"] {
      width: 12px !important;
      height: 12px !important;
      margin: 0 0.25rem !important;
    }

    // Item icon'larını küçült
    ng-icon:not([name="lucideChevronRight"]) {
      width: 16px !important;
      height: 16px !important;
      margin-right: 0.375rem !important;
      display: inline-flex !important;
    }
  }
}

// Chip-like design for desktop
.breadcrumb-chip {
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 999px;
  padding: 0.125rem 0.5rem;
  display: inline-flex;
  align-items: center;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  // İkon görünürlüğünü iyileştir
  ng-icon {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
  }
}

// Mobile optimizations
@media (max-width: 767px) {
  ol {
    li {
      a, span {
        padding: 0.125rem 0.25rem;

        // Make icons slightly larger on mobile for better touch targets
        ng-icon {
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }
}
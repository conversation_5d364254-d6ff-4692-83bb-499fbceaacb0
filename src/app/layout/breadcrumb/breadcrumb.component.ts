import { Component, ElementRef, OnDestroy, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { Subject, filter, takeUntil } from 'rxjs';
import { Breadcrumb } from '@app/data/models/breadcrumb.interface';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent implements OnInit, OnDestroy, AfterViewInit {
  breadcrumbs$ = this.breadcrumbService.breadcrumbs$;
  isMobile = false;
  private destroy$ = new Subject<void>();

  constructor(
    private breadcrumbService: BreadcrumbService,
    private router: Router
  ) {}

  @ViewChild('breadcrumbNav') breadcrumbNav: ElementRef;
  isOverflowing = false;

  ngOnInit() {
    // Ekran boyutunu kontrol et
    this.checkIfMobile();
    window.addEventListener('resize', this.checkIfMobile.bind(this));

    // Router olaylarını dinle
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.checkIfMobile();
      // Rota değiştiğinde overflow kontrolü yap
      setTimeout(() => this.checkOverflow(), 100);
    });

    // Breadcrumb değişikliklerini dinle
    this.breadcrumbs$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      // Breadcrumb'lar değiştiğinde overflow kontrolü yap
      setTimeout(() => this.checkOverflow(), 100);
    });
  }

  ngAfterViewInit() {
    // İlk yüklemede overflow kontrolü yap
    setTimeout(() => this.checkOverflow(), 100);

    // Pencere boyutu değiştiğinde overflow kontrolü yap
    window.addEventListener('resize', this.checkOverflow.bind(this));
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    window.removeEventListener('resize', this.checkIfMobile.bind(this));
    window.removeEventListener('resize', this.checkOverflow.bind(this));
  }

  /**
   * Mobil görünümü kontrol eder
   */
  private checkIfMobile(): void {
    this.isMobile = window.innerWidth < 768;
  }

  /**
   * Breadcrumb'ın kısaltılmış etiketini döndürür
   */
  getShortLabel(breadcrumb: Breadcrumb): string {
    return breadcrumb.shortLabel || breadcrumb.label;
  }

  /**
   * Breadcrumb'ın tam etiketini döndürür
   */
  getFullLabel(breadcrumb: Breadcrumb): string {
    return breadcrumb.label;
  }

  /**
   * Breadcrumb navigasyonunun taşıp taşmadığını kontrol eder
   */
  private checkOverflow(): void {
    if (this.breadcrumbNav && this.breadcrumbNav.nativeElement) {
      const nav = this.breadcrumbNav.nativeElement;
      this.isOverflowing = nav.scrollWidth > nav.clientWidth;

      // Taşma durumuna göre CSS sınıfını ekle/kaldır
      if (this.isOverflowing) {
        nav.classList.add('overflow');
      } else {
        nav.classList.remove('overflow');
      }
    }
  }
}
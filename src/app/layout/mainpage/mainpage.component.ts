import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { versions } from '@env/versions';
import { environment } from '@env/environment';

@Component({
  selector: 'app-mainpage',
  templateUrl: './mainpage.component.html',
  styleUrls: ['./mainpage.component.scss'],
})
export class MainpageComponent {
  currentRoute: string = '';
  constructor(private router: Router) {
    
  }
  version = environment.production ? '' :  versions.version + ' ' + versions.revision + ' ' + versions.branch ;

}

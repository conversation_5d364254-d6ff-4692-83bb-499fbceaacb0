<div [ngClass]="{ 'w-[280px]': !isCollapsed, 'w-[76px]': isCollapsed }"
  class="flex flex-col h-full transition-all duration-300 sidebar-container bg-brand-blue-700"
  *transloco="let t;read:'sidebar';">

  <!-- Logo Bölümü -->
  <div (click)="clickIstabot()" class="flex items-center gap-2 px-4 pt-4 pb-2 cursor-pointer"
    [class.justify-center]="isCollapsed">
    <img src="assets/icons/logo-solid.svg" alt="istabot Logo" class="h-9">
    <!-- Sidebar çökükken yalnızca ikonu göster -->
    <span class="text-2xl font-semibold text-white logo-text" [class.collapsed]="isCollapsed" *ngIf="!isCollapsed">{{
      t('titles.istabot') }}</span>
  </div>

  <!-- Divider ve Toggle Buton -->
  <div class="flex items-center justify-center gap-3" [class.pr-2]="!isCollapsed">
    <div class="flex-1 ml-4 border-t-2 opacity-50 section-divider border-brand-blue-400" [class.collapsed]="isCollapsed"
      [class.hidden]="isCollapsed"></div>
    <button (click)="toggleSidebar()"
      class="p-1 transition-all rounded-full menu-item-button text-brand-blue-700 hover:text-brand-blue-200 hover:bg-brand-blue-400 hover:shadow transistion-all"
      [class.bg-white]="!isCollapsed">
      <!-- Buton ikonunu sidebar'ın durumuna göre değiştiriyoruz -->
      <div class="flex items-center justify-center size-5"
        matTooltip="{{ isCollapsed ? 'Kenar çubuğunu genişlet' : 'Kenar çubuğunu daralt' }}" matTooltipPosition="right">
        <ng-icon class="text-xl menu-icon" [ngClass]="!isCollapsed ? '' : 'text-white'"
          [name]="isCollapsed ? 'lucideChevronRight' : 'lucideChevronLeft'">
        </ng-icon>
      </div>

    </button>
  </div>

  <!-- Menu Bölümü -->
  <div class="flex-1 p-4 space-y-5 overflow-hidden text-nowrap" *transloco="let t; read: 'sidebar';">
    <div *ngFor="let section of menuSections; let last = last" class="spac e-y-1">

      <!-- Bölüm başlığı: yalnızca sidebar açıkken göster -->
      <h3
        class="text-xs font-semibold tracking-wider uppercase transition-all section-title text-brand-blue-250 text-nowrap"
        [class.collapsed]="isCollapsed" *ngIf="!isCollapsed">
        {{ t(section.title) }}
      </h3>

      <div class="flex flex-col justify-center pb-3 space-y-1" [class.items-center]="isCollapsed">
        <button *ngFor="let item of section.items" (click)="handleItemClick(item)"
          [routerLink]="item.route ? item.route : null" [matTooltip]="isCollapsed ? ( item.tooltip | transloco) : ''"
          matTooltipPosition="right" routerLinkActive="bg-brand-blue-400 text-brand-blue-500"
          class="flex items-center gap-2 p-3 text-base text-white transition-all rounded-full menu-item-button hover:bg-brand-blue-400"
          [ngClass]="{ 'w-fit justify-center': isCollapsed }">
          <ng-icon [name]="item.icon" class="text-xl transition-all menu-icon text-nowrap"></ng-icon>
          <span class="transition-all menu-item-text text-nowrap" [class.collapsed]="isCollapsed" *ngIf="!isCollapsed">
            {{ t(item.name) }}
          </span>
        </button>

      </div>

      <!-- Bölümler arasında divider: son eleman değilse ve sidebar açıkken göster -->
      <div class="border-t-2 opacity-50 section-divider border-brand-blue-400" [class.collapsed]="isCollapsed"
        *ngIf="!last && !isCollapsed"></div>
    </div>
  </div>

  <!-- Balance and Profile Integration Section -->
  <div class="flex flex-col gap-3 p-4">
    <!-- Balance Section - Hide when profile menu is shown -->
    <div class="flex flex-col gap-1" *ngIf="!showProfileMenu" [@slideIn]>
      <button (click)="openBalanceModal()" [matTooltip]="isCollapsed ? (t('tooltip.credit')) : ''"
        matTooltipPosition="right"
        class="flex flex-col gap-2 p-4 transition-all rounded-sm balance-payment-button-effect bg-brand-blue-800 rounded-t-2xl hover:bg-brand-blue-400 group">

        <!-- Balance container with stable layout -->
        <div class="flex items-center w-full balance-container" [class.collapsed]="isCollapsed">

          <!-- Bakiye texti - sadece expanded durumda, pozisyon absolute ile sabit -->
          <div class="balance-text-wrapper" [class.collapsed]="isCollapsed">
            <span class="text-white balance-text text-nowrap" *ngIf="!isCollapsed">{{ t('balance') }}</span>
          </div>

          <!-- İstacoin container - her zaman aynı pozisyonda -->
          <div class="flex items-center justify-center gap-1 istacoin-container" [class.collapsed]="isCollapsed">
            <img src="assets/icons/istacoin.svg" alt="istacoin Logo" class="flex-shrink-0 h-5">
            <span class="flex-shrink-0 text-white">
              <ng-container *ngIf="totalCredit > 999; else normalCredit">+999</ng-container>
              <ng-template #normalCredit>{{ totalCredit }}</ng-template>
            </span>
          </div>
        </div>

        <div
          class="w-full h-3 overflow-hidden rounded-full progress-bar-container bg-brand-blue-400 group-hover:bg-brand-blue-800"
          [class.collapsed]="isCollapsed" [class.hidden]="isCollapsed">
          <div class="h-full transition-all duration-500 bg-white rounded-full" [style.width.%]="(ratio * 100)"></div>
        </div>
      </button>

      <button (click)="openPaymentModal()" [matTooltip]="isCollapsed ? (t('tooltip.buy_credits')) : ''"
        matTooltipPosition="right"
        class="p-3 transition-all rounded-sm balance-payment-button-effect bg-brand-blue-800 rounded-b-2xl hover:bg-brand-blue-400">
        <div class="flex items-center justify-center gap-2">
          <ng-icon name="lucideShoppingCart" class="text-xl text-white menu-icon"></ng-icon>
          <span class="balance-text" [class.collapsed]="isCollapsed" *ngIf="!isCollapsed">{{ t('buy_credits') }}</span>
        </div>
      </button>
    </div>

    <!-- Profile Menu (Displayed in place of the balance section when open) -->
    <div *ngIf="showProfileMenu" [@slideIn]
      class="p-2 mb-4 shadow-lg profile-menu-fullview rounded-2xl bg-brand-blue-800">

      <!-- Menu Items -->
      <div class="flex flex-col gap-1">
        <!-- Ayarlar Butonu -->
        <button (click)="openSettingsModal(); toggleProfileMenu($event)"
          class="flex items-center w-full gap-2 px-3 py-2 text-left text-white transition-all rounded-lg menu-item-button hover:bg-brand-blue-600">
          <ng-icon name="lucideSettings" class="text-lg menu-icon"></ng-icon>
          <span class="menu-item-text">{{ t('general.settings') || 'Ayarlar' }}</span>
        </button>

        <!-- Admin Panel Butonu - Sadece admin rolüne sahipse göster -->
        <button *ngIf="isRole('admin')" routerLink="/admin" (click)="toggleProfileMenu($event)"
          class="flex items-center w-full gap-2 px-3 py-2 text-left text-white transition-all rounded-lg menu-item-button hover:bg-brand-blue-600">
          <ng-icon name="lucideSquareActivity" class="text-lg menu-icon"></ng-icon>
          <span class="menu-item-text">Admin Paneli</span>
        </button>
        <button *ngIf="isRole('unit_manager')" (click)="openCorporateManagement(); toggleProfileMenu($event)"
          class="flex items-center w-full gap-2 px-3 py-2 text-left text-white transition-all rounded-lg menu-item-button hover:bg-brand-blue-600">
          <ng-icon name="lucideBuilding" class="text-lg menu-icon"></ng-icon>
          <span class="menu-item-text">Kurum Paneli</span>
        </button>
        <!-- Yardım Merkezi Butonu -->
        <button (click)="openHelpCenter(); toggleProfileMenu($event)"
          class="flex items-center w-full gap-2 px-3 py-2 text-left text-white transition-all rounded-lg menu-item-button hover:bg-brand-blue-600">
          <ng-icon name="lucideCircleHelp" class="text-lg menu-icon"></ng-icon>
          <span class="menu-item-text">{{ t('general.help') || 'Yardım' }}</span>
        </button>
        <div *ngIf="isSwitchedAccount" class="h-[1px] rounded-full bg-brand-blue-600"></div>
        <!-- Kendi hesabına dön butonu - Ayrı gösterilir -->
        <button *ngIf="isSwitchedAccount" (click)="switchBack(); toggleProfileMenu($event)"
          class="flex items-center w-full gap-2 px-3 py-2 pt-3 mt-1 text-left text-white transition-all rounded-lg menu-item-button hover:bg-brand-blue-600 ">
          <ng-icon name="lucideUndo2" class="text-lg menu-icon"></ng-icon>
          <span class="menu-item-text">Kendi hesabına dön</span>
        </button>
        <div class="h-[1px] rounded-full bg-brand-blue-600"></div>
        <!-- Çıkış Yap Butonu -->
        <button (click)="logout(); toggleProfileMenu($event)"
          class="flex items-center w-full gap-2 px-3 py-2 pt-3 mt-1 text-left text-white transition-all rounded-lg menu-item-button hover:bg-brand-blue-600 ">
          <ng-icon name="lucideLogOut" class="text-lg menu-icon"></ng-icon>
          <span class="menu-item-text">{{ t('tooltip.logout') || 'Çıkış Yap' }}</span>
        </button>
      </div>
    </div>

    <!-- Profil Bölümü -->
    <div class="flex flex-col gap-1">
      <div class="p-3 rounded-2xl" [class.bg-brand-blue-800]="!isCollapsed">
        <!-- Profil Kartı - Tıklanabilir -->
        <button (click)="toggleProfileMenu($event); isCollapsed ? toggleSidebar() : null"
          [matTooltip]="isCollapsed ? t('tooltip.profile_options') : ''" matTooltipPosition="right"
          class="flex items-center w-full gap-2 p-2 transition-all rounded-lg profile-button-effect profile-button group"
          [class.hover:bg-brand-blue-600]="!isCollapsed" [class.flex-col]="isCollapsed">

          <!-- Profil Avatar: Kullanıcı adı baş harfleri veya avatar ikonu -->
          <div class="flex items-center justify-center rounded-full shrink-0 size-10"
            [ngClass]="!isCollapsed ? 'bg-brand-blue-600 group-hover:bg-brand-blue-800' : 'bg-brand-blue-800 hover:bg-brand-blue-600'">
            <ng-icon name="lucideUser" class="text-lg text-white"></ng-icon>
          </div>

          <!-- Kullanıcı Bilgileri: Sadece sidebar genişken göster -->
          <div class="flex-1 min-w-0 text-left profile-info" [class.collapsed]="isCollapsed" *ngIf="!isCollapsed">
            <p class="text-sm font-medium text-white truncate">
              {{ user.name | titlecase }}
            </p>
            <p class="text-xs truncate text-brand-blue-250">
              {{ user.role }}
            </p>
          </div>

          <!-- Hamburger menü ikonu (Animasyonlu dönüşüm) -->
          <div *ngIf="!isCollapsed" class="relative flex items-center justify-center w-6 h-6">
            <div class="hamburger-menu">
              <!-- 3 çizgi hamburger menü -->
              <span class="block w-5 h-0.5 bg-white rounded-sm transition-all duration-300 absolute"
                [class.top-bar-animate]="showProfileMenu"></span>
              <span class="block w-5 h-0.5 bg-white rounded-sm transition-all duration-300 absolute"
                [class.middle-bar-animate]="showProfileMenu"></span>
              <span class="block w-5 h-0.5 bg-white rounded-sm transition-all duration-300 absolute"
                [class.bottom-bar-animate]="showProfileMenu"></span>
            </div>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>
// Existing hamburger menu styles
.hamburger-menu {
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  span {
    transform-origin: center;
  }

  // Normal hamburger menu positions
  span:nth-child(1) {
    top: 6px;
  }

  span:nth-child(2) {
    top: 12px;
  }

  span:nth-child(3) {
    top: 18px;
  }
}

// Animated state - top bar rotates down
.top-bar-animate {
  transform: translateY(6px) rotate(45deg) !important;
}

// Animated state - middle bar fades out
.middle-bar-animate {
  opacity: 0 !important;
}

// Animated state - bottom bar rotates up
.bottom-bar-animate {
  transform: translateY(-6px) rotate(-45deg) !important;
}

// SMOOTH SIDEBAR EXPANSION ANIMATIONS
// Sidebar genişlerken daha yumuşak geçiş için
.sidebar-container {
  overflow: hidden; // Taşan içerikleri gizle
}

// Logo text animasyonu
.logo-text {
  transition:
    opacity 0.2s ease-out 0.15s,
    transform 0.2s ease-out 0.15s;

  &.collapsed {
    opacity: 0;
    transform: translateX(-10px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Section başlıkları için yumuşak geçiş
.section-title {
  transition:
    opacity 0.2s ease-out 0.2s,
    transform 0.2s ease-out 0.2s;

  &.collapsed {
    opacity: 0;
    transform: translateX(-15px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Menu item text'leri için staggered animation
.menu-item-text {
  transition:
    opacity 0.25s ease-out 0.25s,
    transform 0.25s ease-out 0.25s;
  white-space: nowrap;
  overflow: hidden;

  &.collapsed {
    opacity: 0;
    transform: translateX(-20px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Balance section için gelişmiş layout kontrolü
.balance-container {
  transition: all 0.3s ease-out;

  &.collapsed {
    justify-content: center !important;
  }

  &:not(.collapsed) {
    justify-content: space-between !important;
  }
}

// Balance text wrapper - pozisyon kontrolü için
.balance-text-wrapper {
  transition:
    opacity 0.2s ease-out 0.3s,
    width 0.3s ease-out;
  overflow: hidden;

  &.collapsed {
    opacity: 0;
    width: 0;
    transition:
      opacity 0.1s ease-in,
      width 0.1s ease-in;
  }

  &:not(.collapsed) {
    opacity: 1;
    width: auto;
  }
}

// Balance text'i için
.balance-text {
  white-space: nowrap;
  color: white;
  transition: transform 0.2s ease-out 0.3s;

  &.collapsed {
    transform: translateX(-20px);
    transition: transform 0.1s ease-in;
  }
}

// İstacoin container için stable positioning
.istacoin-container {
  transition: flex-direction 0.3s ease-out;
  flex-shrink: 0;

  &.collapsed {
    flex-direction: column !important;
  }

  &:not(.collapsed) {
    flex-direction: row !important;
  }
}

// Profile bilgileri için
.profile-info {
  transition:
    opacity 0.2s ease-out 0.35s,
    transform 0.2s ease-out 0.35s;

  &.collapsed {
    opacity: 0;
    transform: translateX(-15px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Icon'ların boyut ve pozisyon stabilitesi için
.menu-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  transition: none; // Icon'larda geçiş efekti olmasın
}

// Divider'lar için yumuşak geçiş
.section-divider {
  transition:
    opacity 0.15s ease-out 0.4s,
    transform 0.15s ease-out 0.4s;

  &.collapsed {
    opacity: 0;
    transform: scaleX(0);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Progress bar için
.progress-bar-container {
  transition:
    opacity 0.2s ease-out 0.4s,
    transform 0.2s ease-out 0.4s;

  &.collapsed {
    opacity: 0;
    transform: scaleX(0.8);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Extra: Menu container için smooth width transition
.menu-container {
  transition: width 0.3s ease-out;
}

// Ana sidebar container için overflow kontrol
.sidebar-main {
  transition: all 0.3s ease-out;
  will-change: width;
}

// SMOOTH SIDEBAR EXPANSION ANIMATIONS
// Sidebar genişlerken daha yumuşak geçiş için
.sidebar-container {
  overflow: hidden; // Taşan içerikleri gizle
}

// Logo text animasyonu
.logo-text {
  transition:
    opacity 0.2s ease-out 0.15s,
    transform 0.2s ease-out 0.15s;

  &.collapsed {
    opacity: 0;
    transform: translateX(-10px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Section başlıkları için yumuşak geçiş
.section-title {
  transition:
    opacity 0.2s ease-out 0.2s,
    transform 0.2s ease-out 0.2s;

  &.collapsed {
    opacity: 0;
    transform: translateX(-15px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Menu item text'leri için staggered animation
.menu-item-text {
  transition:
    opacity 0.25s ease-out 0.25s,
    transform 0.25s ease-out 0.25s;
  white-space: nowrap;
  overflow: hidden;

  &.collapsed {
    opacity: 0;
    transform: translateX(-20px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Balance section text'leri için
.balance-text {
  transition:
    opacity 0.2s ease-out 0.3s,
    transform 0.2s ease-out 0.3s;

  &.collapsed {
    opacity: 0;
    transform: translateX(-10px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Profile bilgileri için
.profile-info {
  transition:
    opacity 0.2s ease-out 0.35s,
    transform 0.2s ease-out 0.35s;

  &.collapsed {
    opacity: 0;
    transform: translateX(-15px);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Icon'ların boyut ve pozisyon stabilitesi için
.menu-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  transition: none; // Icon'larda geçiş efekti olmasın
}

// Divider'lar için yumuşak geçiş
.section-divider {
  transition:
    opacity 0.15s ease-out 0.4s,
    transform 0.15s ease-out 0.4s;

  &.collapsed {
    opacity: 0;
    transform: scaleX(0);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// Progress bar için
.progress-bar-container {
  transition:
    opacity 0.2s ease-out 0.4s,
    transform 0.2s ease-out 0.4s;

  &.collapsed {
    opacity: 0;
    transform: scaleX(0.8);
    transition:
      opacity 0.1s ease-in,
      transform 0.1s ease-in;
  }
}

// NEW: Click effects for menu items
.menu-item-button {
  transition: all 0.15s ease-out;

  &:active {
    transform: scale(0.95);
  }
}

// Balance and payment button effects
.balance-payment-button-effect {
  transition: all 0.15s ease-out;

  &:active {
    transform: scale(0.98);
    filter: brightness(1.05);
  }
}

// Profile button specific effects
.profile-button-effect {
  transition: all 0.15s ease-out;

  &:active {
    transform: scale(0.97);
  }
}

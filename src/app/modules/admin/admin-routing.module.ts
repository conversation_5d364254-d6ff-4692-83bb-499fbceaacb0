import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EftConfirmComponent } from './pages/eft-confirm/eft-confirm.component';
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';
import { UsersComponent } from './pages/users/users.component';
import { UserListComponent } from './pages/users/user-list/user-list.component';
import { UserDetailComponent } from './pages/users/dialogs/user-detail/user-detail.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { CreditCardTransactionsComponent } from './pages/credit-card-transactions/credit-card-transactions.component';

const routes: Routes = [
  {
    path: '',
    component: AdminLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },
      {
        path: 'dashboard',
        component: DashboardComponent
      },
      {
        path: 'eft-confirm',
        component: EftConfirmComponent,
      },
      {
        path: 'users',
        component: UserListComponent,
        // children: [
        //   { path: '', component: UserListComponent },
        //   { path: 'details/:uid', component: UserDetailComponent }
        // ]
      },
      {
        path: 'credit-card-transactions',
        component: CreditCardTransactionsComponent,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminRoutingModule { }

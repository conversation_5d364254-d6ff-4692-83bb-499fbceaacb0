import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
  CanLoad,
  Route,
  UrlSegment,
  ActivatedRoute,
  Params,
} from '@angular/router';
import { map, Observable } from 'rxjs';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AdminGuard implements CanLoad {
  constructor(
    private authService: AuthService,
    private router: Router,
    private adminHelperService: AdminHelperService,
  ) { }

  canLoad(
    route: Route,
    segments: UrlSegment[],
  ): | boolean | UrlTree | Observable<boolean | UrlTree> | Promise<boolean | UrlTree> {
    if (this.adminController()) {
      return true;
    } else {
      this.authService.logout().subscribe(
        (response) => {
          this.router.navigate(['/login']);
        },
        (error) => {
          console.error('Error logging out', error);
        }
      );
      return false;
    }
  }
  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): | Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (this.adminController()) {
      return true;
    } else {
      this.authService.logout().subscribe(
        (response) => {
          this.router.navigate(['/login']);
        },
        (error) => {
          console.error('Error logging out', error);
        }
      );
      return false;
    }
  }
  adminController() {
    return this.adminHelperService.isRole('admin');
  }
}

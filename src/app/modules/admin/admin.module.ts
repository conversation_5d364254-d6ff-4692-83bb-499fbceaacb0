import { NgModule } from '@angular/core';
import { AdminRoutingModule } from './admin-routing.module';
import { SharedModule } from '@app/shared/shared.module';
import { EftConfirmComponent } from './pages/eft-confirm/eft-confirm.component';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { AdminLayoutComponent } from './layouts/admin-layout/admin-layout.component';
import { UsersComponent } from './pages/users/users.component';
import { UserDetailComponent } from './pages/users/dialogs/user-detail/user-detail.component';
import { UserListComponent } from './pages/users/user-list/user-list.component';
import { PaymentDetailComponent } from './pages/eft-confirm/dialogs/payment-detail/payment-detail.component';
import { PaymentManagementComponent } from './pages/eft-confirm/dialogs/payment-management/payment-management.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { CreditCardTransactionsComponent } from './pages/credit-card-transactions/credit-card-transactions.component';
import { ReactiveFormsModule } from '@angular/forms';
import { AdminGiftingComponent } from './pages/admin-gifting/admin-gifting.component';

@NgModule({
  declarations: [
    EftConfirmComponent,
    PaymentDetailComponent,
    PaymentManagementComponent,
    SidebarComponent,
    AdminLayoutComponent,
    UsersComponent,
    UserDetailComponent,
    UserListComponent,
    DashboardComponent,
    CreditCardTransactionsComponent,
    AdminGiftingComponent
  ],
  imports: [
    AdminRoutingModule,
    SharedModule,
    ReactiveFormsModule
  ],
})
export class AdminModule { }

<div class="flex flex-col h-full bg-brand-blue-700 w-[280px]">

    <!-- <PERSON><PERSON>ü -->
    <div class="flex items-center gap-2 p-4">
        <img src="assets/icons/logo-solid.svg" alt="istabot Logo" class="h-9">
<div class="flex flex-col h-fit">
    <span class="text-2xl font-semibold text-white text-nowrap">Admin <PERSON></span>
    <p class="-mt-2 text-base font-medium text-white truncate">{{ userName }}</p>
</div>


    </div>

    <!-- Divider -->
    <div class="flex items-center justify-center gap-3 pr-2">
        <div class="flex-1 ml-4 border-t-2 opacity-50 border-brand-blue-400"></div>
    </div>

    <!-- Menu Bölümü -->
    <div class="flex-1 p-4 space-y-5 overflow-hidden text-nowrap">
        <div class="flex flex-col justify-center pb-3 space-y-1">
            <!-- Dashboard Button -->
            <button [disabled]="url=='dashboard'" (click)="goto('dashboard')"
                class="flex items-center gap-2 p-3 font-medium text-white transition-all rounded-full hover:bg-brand-blue-400"
                [ngClass]="{ 'bg-brand-blue-400': url=='dashboard' }">
                <ng-icon [ngClass]="url==='dashboard' ? 'text-white': 'text-white'" name="hugeDashboardSquare02"
                    class="text-xl"></ng-icon>
                <span class="transition-all text-nowrap">Dashboard</span>
            </button>

            <!-- Users Button -->
            <button [disabled]="url=='users'" (click)="goto('users')"
                class="flex items-center gap-2 p-3 font-medium text-white transition-all rounded-full hover:bg-brand-blue-400"
                [ngClass]="{ 'bg-brand-blue-400': url=='users' }">
                <ng-icon [ngClass]="url==='users' ? 'text-white': 'text-white'" name="featherUser"
                    class="text-xl"></ng-icon>
                <span class="transition-all text-nowrap">Kullanıcılar</span>
            </button>

            <!-- EFT/Havale Button -->
            <button [disabled]="url=='eft-confirm'" (click)="goto('eft-confirm')"
                class="flex items-center gap-2 p-3 font-medium text-white transition-all rounded-full hover:bg-brand-blue-400"
                [ngClass]="{ 'bg-brand-blue-400': url=='eft-confirm' }">
                <ng-icon [ngClass]="url==='eft-confirm' ? 'text-white': 'text-white'" name="tdesignUndertakeTransaction"
                    class="text-xl"></ng-icon>
                <span class="transition-all text-nowrap">Eft / Havale Yönetimi</span>
            </button>

            <!-- Credit Card Transactions Button -->
            <button [disabled]="url=='credit-card-transactions'" (click)="goto('credit-card-transactions')"
                class="flex items-center gap-2 p-3 font-medium text-white transition-all rounded-full hover:bg-brand-blue-400"
                [ngClass]="{ 'bg-brand-blue-400': url=='credit-card-transactions' }">
                <ng-icon [ngClass]="url==='credit-card-transactions' ? 'text-white': 'text-white'"
                    name="tablerCreditCard" class="text-xl"></ng-icon>
                <span class="transition-all text-nowrap">Kredi Kartı İşlemleri</span>
            </button>
        </div>
    </div>

    <!-- Back to Projects Link -->
    <div class="flex flex-col gap-3 p-4">
        <a href="/projects" class="flex items-center gap-2 p-3 rounded-full bg-brand-blue-800 hover:bg-brand-blue-400">
            <ng-icon name="heroArrowUturnLeft" class="text-xl text-white"></ng-icon>
            <span class="text-white">Admin Panelinden çık</span>
        </a>
    </div>
</div>
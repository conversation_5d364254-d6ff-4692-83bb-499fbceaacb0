.hamburger-menu {
    position: relative;
    width: 24px;
    height: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    span {
        transform-origin: center;
    }

    // Normal hamburger menu positions
    span:nth-child(1) {
        top: 6px;
    }

    span:nth-child(2) {
        top: 12px;
    }

    span:nth-child(3) {
        top: 18px;
    }
}

// Animated state - top bar rotates down
.top-bar-animate {
    transform: translateY(6px) rotate(45deg) !important;
}

// Animated state - middle bar fades out
.middle-bar-animate {
    opacity: 0 !important;
}

// Animated state - bottom bar rotates up
.bottom-bar-animate {
    transform: translateY(-6px) rotate(-45deg) !important;
}
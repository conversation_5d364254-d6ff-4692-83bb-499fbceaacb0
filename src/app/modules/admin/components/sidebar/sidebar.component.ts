import { Component, HostListener, OnInit } from '@angular/core';
import {
  ActivatedRoute,
  Event as ebe,
  NavigationEnd,
  NavigationError,
  NavigationStart,
  Router,
} from '@angular/router';
import { AuthService } from '@app/modules/auth/auth.service';
import { TranslocoService } from '@ngneat/transloco';
import { TabService } from '@app/shared/services/tab.service';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ opacity: 0, transform: 'translateY(10px)' }))
      ])
    ])
  ]
})
export class SidebarComponent implements OnInit {
  currentRoute: string;
  url: string = '';
  userName = localStorage.getItem('username');
  sidebarState: boolean = false;

  constructor(
    private router: Router,
    private translocoService: TranslocoService,
    private tabService: TabService,
    private auth: AuthService
  ) {
    this.router.events.subscribe((events: ebe) => {
      if (events instanceof NavigationStart) {
        // Show progress spinner or progress bar
      }
      if (events instanceof NavigationEnd) {
        // Hide progress spinner or progress bar
        this.currentRoute = events.url;
        this.changeBarColor(this.currentRoute);
      }
      if (events instanceof NavigationError) {
        // Hide progress spinner or progress bar
        // Present error to user
        console.log(events.error);
      }
    });
  }

  ngOnInit(): void {
    // Başlangıçta aktif tab'ı belirle
    this.initializeActiveTab();
  }

  initializeActiveTab(): void {
    const url = this.router.url;
    if (url.includes('dashboard')) {
      this.tabService.setActiveTab('dashboard');
      this.url = 'dashboard';
    } else if (url.includes('users')) {
      this.tabService.setActiveTab('users');
      this.url = 'users';
    } else if (url.includes('eft-confirm')) {
      this.tabService.setActiveTab('eft-confirm');
      this.url = 'eft-confirm';
    } else if (url.includes('credit-card-transactions')) {
      this.tabService.setActiveTab('transactions');
      this.url = 'credit-card-transactions';
    }
  }

  changeBarColor(route: string) {
    if (route.includes('eft-confirm')) {
      this.url = 'eft-confirm';
    } else if (route.includes('users')) {
      this.url = 'users';
    } else if (route.includes('dashboard') || route === '/admin') {
      this.url = 'dashboard';
    } else if (route.includes('credit-card-transactions')) {
      this.url = 'credit-card-transactions';
    }
  }

  goto(route: string) {
    this.router.navigate(['/admin/' + route]);
  }

  clickIstabot() {
    const url = window.location.origin;
    window.location.href = url;
  }

  // Document click handler for closing the menu when clicking outside
  @HostListener('document:click', ['$event'])
  handleClick(event: Event): void {
    const clickedElement = event.target as HTMLElement;

    // Handle original sidebar state if needed
    if (!clickedElement.classList.contains('sidebar')) {
      this.sidebarState = false;
    }
  }
}
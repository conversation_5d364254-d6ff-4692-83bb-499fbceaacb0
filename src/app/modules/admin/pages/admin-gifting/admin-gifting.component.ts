import { Component, Input } from '@angular/core';
import { AdminService } from '@app/data/services/admin.service';
import { SnotifyService } from 'ng-alt-snotify';
import { CreditUpdateService } from '@app/data/services/credit-update.service';
@Component({
  selector: 'app-admin-gifting',
  templateUrl: './admin-gifting.component.html',
  styleUrls: ['./admin-gifting.component.scss']
})
export class AdminGiftingComponent {
  @Input() user: any;
  // showModal: boolean = true;
  creditAmount: number = 0;
  errorMessage: string;

  constructor(
    private adminService: AdminService,
    private snotifyService: SnotifyService,
    private cu: CreditUpdateService
  ) {}

  setPreset(amount: number) {
    this.creditAmount = amount;
    this.errorMessage = '';
  }

  sendCredit() {
    this.errorMessage = '';

    if (this.creditAmount <= 0) {
      this.errorMessage = 'Lütfen geçerli bir kredi miktarı girin.';
      this.snotifyService.error(this.errorMessage);
      return;
    }
    if (!this.user?.email) {
      this.errorMessage = 'Hediye gönderilecek kullanıcı email bilgisi eksik.';
      this.snotifyService.error(this.errorMessage);
      return;
    }
    
    const loggedInUserEmail = localStorage.getItem('email');
    if (loggedInUserEmail === this.user.email) {
      this.errorMessage = 'Kendinize kredi gönderemezsiniz.';
      this.snotifyService.error(this.errorMessage);
      return;
    }

    const payload = {
      credit_gift: {
        recipient_email: this.user.email,
        amount: this.creditAmount
      }
    };

    this.adminService.sendCreditGift(this.user.id, this.creditAmount, this.user.email).subscribe({
      next: (response) => {
        this.snotifyService.success('Kredi gönderildi.');
        this.creditAmount = 0;
        this.cu.notifyCreditUpdated();
      },
      error: (error) => {
        console.error('Send credit error:', error);
        if (error.error && error.error.error) {
          this.errorMessage = error.error.error;
          this.snotifyService.error(this.errorMessage);
        } else {
          this.errorMessage = 'Kredi gönderilemedi.';
          this.snotifyService.error(this.errorMessage);
        }
      }
    });
  }
  
  cancel() {
    this.creditAmount = 0;
    this.errorMessage = '';
  }
}

<div class="flex flex-col h-full grow bg-blue-50 rounded-3xl">
    <!-- Summary Section -->
    <div class="grid grid-cols-6 gap-3 p-3 ">
        <!-- Total Transactions -->
        <div class="flex items-center justify-between col-span-2 p-3 bg-white shadow-sm rounded-xl">

                <div class="flex items-center gap-2">
                    <div class="flex items-center p-2 bg-blue-100 rounded-lg ">
                        <ng-icon name="tablerCreditCard" class="text-lg text-blue-600"></ng-icon>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-sm font-medium">Toplam İşlem</span>
                        <span class="text-lg font-semibold">{{summary.total}}</span>
                    </div>
                </div>
                <span class="text-xs" [ngClass]="getTodayCount() > 0 ? 'text-green-700' : 'text-yellow-500'">
                    {{getTodayCount() > 0 ? '+' + getTodayCount() : getTodayCount()}} bugün
                </span>
        </div>

        <!-- Total Amount -->
        <div class="flex items-center justify-between col-span-2 p-3 bg-white shadow-sm rounded-xl">

                <div class="flex items-center gap-2">
                    <div class="flex p-2 bg-green-100 rounded-lg">
                        <ng-icon name="tablerCurrencyLira" class="text-lg text-green-600"></ng-icon>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-sm font-medium">Toplam Tutar</span>
                        <span class="text-lg font-semibold">₺{{summary.totalAmount |thousandSeparator}}</span>
                    </div>
                </div>
                <span class="text-xs" [ngClass]="getTodayTotalAmount() > 0 ? 'text-green-700' : 'text-yellow-500'">
                    {{getTodayTotalAmount() > 0 ? '+' : ''}}{{getTodayTotalAmount() |thousandSeparator}} bugün
                </span>

        </div>

        <!-- Status Summary -->
        <div class="flex items-center justify-center col-span-2 p-2 bg-white shadow-sm rounded-xl">
            <div class="grid grid-cols-3 gap-2">
                <div class="flex flex-col items-center">
                    <span class="text-xs text-gray-500">Onaylanan</span>
                    <span class="text-lg font-medium text-green-600">{{summary.approved}}</span>
                </div>
                <div class="flex flex-col items-center">
                    <span class="text-xs text-gray-500">Bekleyen</span>
                    <span class="text-lg font-medium text-yellow-600">{{summary.pending}}</span>
                </div>
                <div class="flex flex-col items-center">
                    <span class="text-xs text-gray-500">Reddedilen</span>
                    <span class="text-lg font-medium text-red-600">{{summary.rejected}}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Search Section -->
    <div class="grid grid-cols-3 gap-4 mx-4 mb-4">
        <!-- Search Section -->
        <div class="col-span-1">
            <div class="flex items-center justify-center w-full h-full gap-2 p-2 bg-white rounded-3xl">
                <ng-icon name="tablerSearch" class="text-xl text-gray-400"></ng-icon>
                <input type="text" (keyup)="applyFilter($event)"
                    class="w-full text-sm bg-transparent border-none outline-none placeholder:text-gray-400"
                    placeholder="İşlem ID, kullanıcı adı veya email ile ara">
            </div>
        </div>
        <!-- Filter Section -->
        <div class="relative col-span-2">
            <mat-accordion class="absolute z-[999] w-full">
                <mat-expansion-panel class="shadow-lg">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <div class="flex items-center gap-2">
                                <ng-icon name="tablerFilter" class="text-xl"></ng-icon>
                                Filtrele
                            </div>
                        </mat-panel-title>
                    </mat-expansion-panel-header>

                    <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <!-- Date Range -->
                            <mat-form-field>
                                <mat-label>Başlangıç Tarihi</mat-label>
                                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                                <mat-datepicker #startPicker></mat-datepicker>
                            </mat-form-field>

                            <mat-form-field>
                                <mat-label>Bitiş Tarihi</mat-label>
                                <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                                <mat-datepicker #endPicker></mat-datepicker>
                            </mat-form-field>

                            <!-- Status -->
                            <mat-form-field>
                                <mat-label>Durum</mat-label>
                                <mat-select formControlName="status">
                                    <mat-option value="">Tümü</mat-option>
                                    <mat-option value="approved">Onaylandı</mat-option>
                                    <mat-option value="pending">Beklemede</mat-option>
                                    <mat-option value="rejected">Reddedildi</mat-option>
                                </mat-select>
                            </mat-form-field>

                            <!-- Amount Range -->
                            <mat-form-field>
                                <mat-label>Min. Tutar</mat-label>
                                <input matInput type="number" formControlName="minAmount">
                            </mat-form-field>

                            <mat-form-field>
                                <mat-label>Max. Tutar</mat-label>
                                <input matInput type="number" formControlName="maxAmount">
                            </mat-form-field>
                        </div>

                        <div class="flex justify-end gap-2 mt-4">
                            <button mat-button type="button" (click)="resetFilter()">Sıfırla</button>
                            <button mat-raised-button color="primary" type="submit">Filtrele</button>
                        </div>
                    </form>
                </mat-expansion-panel>
            </mat-accordion>
        </div>


    </div>

    <div class="table-section">
        <div class="overflow-auto table-container" style="max-height: calc(100vh - 250px)">
            <table mat-table [dataSource]="dataSource" matSort matSortActive="date" matSortDirection="desc" class="w-full">
                <!-- ID Column -->
                <ng-container matColumnDef="id">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header="id"> ID </th>
                    <td mat-cell *matCellDef="let row"> {{row.id}} </td>
                </ng-container>

                <!-- User Column -->
                <ng-container matColumnDef="user">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header="user"> Kullanıcı </th>
                    <td mat-cell *matCellDef="let row">
                        <div class="flex flex-col">
                            <span class="font-medium">{{row.order?.user?.name}} {{row.order?.user?.surname}}</span>
                            <span class="text-sm text-gray-500">{{row.order?.user?.email}}</span>
                        </div>
                    </td>
                </ng-container>

                <!-- Amount Column -->
                <ng-container matColumnDef="amount">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header="amount"> Tutar </th>
                    <td mat-cell *matCellDef="let row">
                        <div class="flex flex-col">
                            <span>{{row.order?.total_price | currency:'TRY':'symbol-narrow':'1.2-2'}}</span>
                            <span *ngIf="row.order?.discount > 0" class="text-sm text-gray-500">
                                İndirim: {{row.order?.discount | currency:'TRY':'symbol-narrow':'1.2-2'}}
                            </span>
                        </div>
                    </td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header="status"> Durum </th>
                    <td mat-cell *matCellDef="let row">
                        <div [class]="'px-2 py-1 rounded-full text-sm inline-block ' + getStatusClass(row.status)">
                            {{getStatusText(row.status)}}
                        </div>
                    </td>
                </ng-container>

                <!-- Date Column -->
                <ng-container matColumnDef="date">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header="date"> Tarih </th>
                    <td mat-cell *matCellDef="let row">
                        {{row.created_at | date:'dd MMMM yyyy HH:mm':'':'tr-TR'}}
                    </td>
                </ng-container>

                <!-- Error Column -->
                <ng-container matColumnDef="error">
                    <th mat-header-cell *matHeaderCellDef> Hata Mesajı </th>
                    <td mat-cell *matCellDef="let row">
                        <span class="text-red-600">{{row.credit_card_payment?.response_error_messages?.ErrMsg}}</span>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <!-- Loading Spinner -->
            <div *ngIf="isLoading" class="flex justify-center p-4">
                <mat-spinner diameter="40"></mat-spinner>
            </div>

            <!-- No Data Message -->
            <div *ngIf="!isLoading && dataSource?.data?.length === 0" class="flex justify-center p-4 text-gray-500">
                Kayıt bulunamadı
            </div>
        </div>
        <mat-paginator [pageSizeOptions]="[10, 25, 50]" showFirstLastButtons></mat-paginator>
    </div>
</div>
import { Component, OnInit, ViewChild, LOCALE_ID, AfterViewInit } from '@angular/core';
import { formatDate } from '@angular/common';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import { AdminService } from '@app/data/services/admin.service';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-credit-card-transactions',
  templateUrl: './credit-card-transactions.component.html',
  providers: [{ provide: LOCALE_ID, useValue: 'tr-TR' }]
})
export class CreditCardTransactionsComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = ['id', 'user', 'amount', 'status', 'date', 'error'];
  dataSource: MatTableDataSource<any>;
  isLoading = false;
  summary = {
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0,
    totalAmount: 0,
    approvedAmount: 0
  };
  filterForm: FormGroup;
  originalData: any[] = [];

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private adminService: AdminService,
    private dialog: MatDialog,
    private fb: FormBuilder
  ) {
    this.initFilterForm();
  }

  ngOnInit() {
    this.loadTransactions();
    this.setupFilter();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  
  }

  initFilterForm() {
    this.filterForm = this.fb.group({
      startDate: [null],
      endDate: [null],
      status: [''],
      minAmount: [null],
      maxAmount: [null]
    });
  }

  setupFilter() {
    if (this.dataSource) {
      this.dataSource.filterPredicate = (data: any, filter: string) => {
        const searchText = filter.toLocaleLowerCase('tr-TR');
        return (
          data.id.toString().includes(searchText) ||
          data.order?.user?.name?.toLocaleLowerCase('tr-TR').includes(searchText) ||
          data.order?.user?.surname?.toLocaleLowerCase('tr-TR').includes(searchText) ||
          data.order?.user?.email?.toLocaleLowerCase('tr-TR').includes(searchText) ||
          data.status?.toLocaleLowerCase('tr-TR').includes(searchText) ||
          data.order?.total_price?.toString().includes(searchText)
        );
      };
    }
  }

  loadTransactions() {
    this.isLoading = true;
    this.adminService.getCreditCardPayments().subscribe({
      next: (data) => {
        this.originalData = data;
        this.dataSource = new MatTableDataSource(data);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sortingDataAccessor = (item: any, property: string) => {
          switch (property) {
            case 'id':
              return Number(item.id);
            case 'user':
              return (item.order?.user?.name + ' ' + item.order?.user?.surname).toLowerCase(); // Convert to lowercase
            case 'amount':
              return Number(item.order?.total_price) || 0; // Convert to number and handle null/undefined
            case 'date':
              return new Date(item.created_at).getTime();
            case 'status':
              return item.status;
            default:
              return item[property];
          }
        };
        this.dataSource.sort = this.sort;
        this.calculateSummary(data);

      },
      error: (error) => {
        console.error('Error loading transactions:', error);
      },
      complete: () => {
        this.isLoading = false;
      }
    });
  }

  calculateSummary(data: any[]) {
    this.summary = {
      total: data.length,
      totalAmount: data.reduce((sum, order) => sum + (order.status === 'approved' ? Number(order.order?.total_price || 0) : 0), 0).toFixed(0),
      pending: data.filter(order => order.status === 'pending').length,
      approved: data.filter(order => order.status === 'approved').length,
      rejected: data.filter(order => order.status === 'rejected').length,
      approvedAmount: data.reduce((sum, order) => sum + (order.status === 'approved' ? Number(order.order?.total_price || 0) : 0), 0).toFixed(0)
    };
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLocaleLowerCase('tr-TR');
  }

  resetFilter() {
    this.filterForm.reset();
    this.dataSource.data = this.originalData;
  }

  applyFilters() {
    let filteredData = [...this.originalData];
    const filters = this.filterForm.value;

    if (filters.startDate || filters.endDate) {
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.created_at);
        if (filters.startDate && filters.endDate) {
          const start = new Date(filters.startDate);
          start.setHours(0, 0, 0, 0);
          const end = new Date(filters.endDate);
          end.setHours(23, 59, 59, 999);
          return itemDate >= start && itemDate <= end;
        } else if (filters.startDate) {
          const start = new Date(filters.startDate);
          start.setHours(0, 0, 0, 0);
          return itemDate >= start;
        } else if (filters.endDate) {
          const end = new Date(filters.endDate);
          end.setHours(23, 59, 59, 999);
          return itemDate <= end;
        }
        return true;
      });
    }

    if (filters.status && filters.status.trim() !== '') {
      filteredData = filteredData.filter(item => 
        item.status?.toLowerCase() === filters.status.toLowerCase().trim()
      );
    }

    if (filters.minAmount !== null && filters.minAmount !== '') {
      filteredData = filteredData.filter(item => {
        const amount = parseFloat(item.order?.total_price || 0);
        return amount >= parseFloat(filters.minAmount);
      });
    }

    if (filters.maxAmount !== null && filters.maxAmount !== '') {
      filteredData = filteredData.filter(item => {
        const amount = parseFloat(item.order?.total_price || 0);
        return amount <= parseFloat(filters.maxAmount);
      });
    }

    this.dataSource.data = filteredData;
    this.calculateSummary(filteredData);
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'approved': return 'Onaylandı';
      case 'rejected': return 'Reddedildi';
      case 'pending': return 'Beklemede';
      default: return 'Bilinmiyor';
    }
  }

  openPaymentDetail(payment: any) {
    // Dialog açma işlemi
  }

  formatDate(date: string): string {
    return formatDate(date, 'dd MMMM yyyy HH:mm', 'tr-TR');
  }

  getTodayCount(): number {
    return this.dataSource?.data?.filter(transaction => {
      const transactionDate = new Date(transaction.created_at);
      const today = new Date();
      return transactionDate.getFullYear() === today.getFullYear() &&
             transactionDate.getMonth() === today.getMonth() &&
             transactionDate.getDate() === today.getDate();
    }).length || 0;
  }

  getTodayTotalAmount(): number {
    return Number(this.dataSource?.data
      ?.filter(transaction => {
        const transactionDate = new Date(transaction.created_at);
        const today = new Date();
        return transactionDate.getFullYear() === today.getFullYear() &&
               transactionDate.getMonth() === today.getMonth() &&
               transactionDate.getDate() === today.getDate() &&
               transaction.status == 'approved';
      })
      .reduce((total, transaction) => total + Number(transaction.order?.total_price || 0), 0)
      .toFixed(0)) || 0;
  }
  sortData(sort: Sort): void {
    const data = [...this.dataSource.data];
    if (!sort.active || sort.direction === '') {
      this.dataSource.data = data;
      return;
    }

    this.dataSource.data = data.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'date':
          return this.compare(new Date(a.created_at).getTime(), new Date(b.created_at).getTime(), isAsc);
        // ...other cases...
        default:
          return 0;
      }
    });
  }

  compare(a: number | string | Date, b: number | string | Date, isAsc: boolean): number {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }
}
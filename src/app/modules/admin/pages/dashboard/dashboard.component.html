<div
    class="flex flex-col items-start justify-start w-full h-full gap-2 p-8 overflow-auto transition-all bg-blue-50 grow rounded-3xl">
    <!--başlık-->
    <div class="flex items-center justify-between w-full mb-2">
        <p class="flex items-center flex-none gap-2 text-3xl font-medium">
            Yönetim Paneli
        </p>
        <button (click)="loadAllData()" [disabled]="isRefreshing" class="refresh-button"
            [class.refreshing]="isRefreshing">
            <ng-container *ngIf="!isRefreshing; else refreshingIcon">
                <ng-icon name="heroArrowPath" class="text-xl"></ng-icon>
                <span>Yenile</span>
            </ng-container>
            <ng-template #refreshingIcon>
                <ng-icon name="heroArrowPath" class="text-xl animate-spin"></ng-icon>
                <span>Yenileniyor...</span>
            </ng-template>
        </button>
    </div>

    <div class="flex w-full h-full gap-2 max-h-40">
        <!--k<PERSON><PERSON><PERSON><PERSON><PERSON>lar-->
        <a routerLink="/admin/users" class="link-button">
            <div class="flex flex-col items-center justify-center w-full">
                <div class="flex flex-col items-center gap-2 pb-2">
                    <ng-icon name="featherUser" class="text-3xl"></ng-icon>
                    <span class="text-2xl">Kullanıcılar</span>
                </div>
                <div class="flex items-center gap-2 mt-2">
                    <ng-container *ngIf="isLoading.users; else usersLoaded">
                        <div class="loader-pulse">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </ng-container>
                    <ng-template #usersLoaded>
                        <span class="font-semibold">Toplam {{userSummary.total}}</span>
                    </ng-template>
                </div>
            </div>
        </a>

        <!-- Eft Yönetimi -->
        <a routerLink="/admin/eft-confirm" class="link-button">
            <div class="flex flex-col items-center justify-center w-full">
                <div class="flex flex-col items-center gap-2 pb-2">
                    <ng-icon name="tdesignUndertakeTransaction" class="text-3xl"></ng-icon>
                    <span class="text-2xl">Eft / Havale Yönetimi</span>
                </div>
                <div class="flex items-center justify-center gap-2 mt-2">
                    <ng-container *ngIf="isLoading.eft; else eftLoaded">
                        <div class="loader-pulse">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </ng-container>
                    <ng-template #eftLoaded>
                        <div
                            class="flex items-center justify-center px-2 py-1 text-sm text-green-800 bg-green-100 rounded-lg">
                            <span>{{eftSummary.approved}} Onaylı</span>
                        </div>
                        <div
                            class="flex items-center justify-center px-2 py-1 text-sm text-yellow-800 bg-yellow-100 rounded-lg">
                            <span>{{eftSummary.pending}} Bekleyen</span>
                        </div>
                        <div
                            class="flex items-center justify-center px-2 py-1 text-sm text-red-800 bg-red-100 rounded-lg">
                            <span>{{eftSummary.rejected}} İptal</span>
                        </div>
                    </ng-template>
                </div>
            </div>
        </a>

        <!-- Kredi Kartı Yönetimi -->
        <a routerLink="/admin/credit-card-transactions" class="link-button">
            <div class="flex flex-col items-center justify-center w-full">
                <div class="flex flex-col items-center gap-2 pb-2">
                    <ng-icon name="tablerCreditCard" class="text-3xl"></ng-icon>
                    <span class="text-2xl">Kredi Kartı Yönetimi</span>
                </div>
                <div class="flex items-center justify-center gap-2 mt-2">
                    <ng-container *ngIf="isLoading.creditCard; else creditCardLoaded">
                        <div class="loader-pulse">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </ng-container>
                    <ng-template #creditCardLoaded>
                        <div
                            class="flex items-center justify-center px-2 py-1 text-sm text-green-800 bg-green-100 rounded-lg">
                            <span>{{creditCardSummary.approved}} Onaylı</span>
                        </div>
                        <div
                            class="flex items-center justify-center px-2 py-1 text-sm text-yellow-800 bg-yellow-100 rounded-lg">
                            <span>{{creditCardSummary.pending}} Bekleyen</span>
                        </div>
                        <div
                            class="flex items-center justify-center px-2 py-1 text-sm text-red-800 bg-red-100 rounded-lg">
                            <span>{{creditCardSummary.rejected}} Reddedilen</span>
                        </div>
                    </ng-template>
                </div>
            </div>
        </a>
    </div>
</div>
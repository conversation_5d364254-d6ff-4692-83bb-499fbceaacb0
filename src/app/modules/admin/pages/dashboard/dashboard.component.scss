.link-button {
  @apply flex w-full h-full items-center justify-center gap-2 px-4 py-3 text-base font-normal transition-all bg-white border shadow border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 rounded-3xl;
}

.refresh-button {
  @apply flex items-center gap-1 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 active:scale-95 transition-all disabled:opacity-70 disabled:pointer-events-none;

  &.refreshing {
    @apply bg-blue-200 text-blue-800;
  }
}

.loader-pulse {
  @apply flex gap-1 items-center justify-center;
}

.loader-pulse div {
  @apply w-2 h-2 bg-blue-600 rounded-full;
  animation: pulse 1.5s ease-in-out infinite;
}

.loader-pulse div:nth-child(2) {
  animation-delay: 0.2s;
}

.loader-pulse div:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

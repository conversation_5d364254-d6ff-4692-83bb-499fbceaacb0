import { Component, OnInit } from '@angular/core';
import { AdminService } from '@app/data/services/admin.service';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  // Loading durumları için değişkenler
  isLoading = {
    users: true,
    eft: true,
    creditCard: true
  };

  // Yenileme butonu için loading durumu
  isRefreshing = false;

  // Kullanıcılar için özet bilgiler
  userSummary = {
    total: 0,
    todayCount: 0
  };

  // EFT/Havale işlemleri için özet bilgiler
  eftSummary = {
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0
  };

  // Kredi Kartı işlemleri için özet bilgiler
  creditCardSummary = {
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0
  };

  constructor(
    private adminService: AdminService,
    private adminHelperService: AdminHelperService
  ) { }

  ngOnInit(): void {
    // Başlangıçta tüm verileri yükle
    this.loadAllData();
  }

  // Tüm verileri yükleme
  loadAllData(): void {
    this.isRefreshing = true; // Yenileme butonunu devre dışı bırak ve spinner göster

    // Tüm veri isteklerini başlat
    this.loadUsersSummary();
    this.loadEftSummary();
    this.loadCreditCardSummary();

    // Tüm veriler yüklendiğinde yenileme durumunu sıfırla
    const checkAllLoaded = () => {
      if (!this.isLoading.users && !this.isLoading.eft && !this.isLoading.creditCard) {
        this.isRefreshing = false;
      } else {
        setTimeout(checkAllLoaded, 200); // 200ms sonra tekrar kontrol et
      }
    };

    setTimeout(checkAllLoaded, 200);
  }

  // Kullanıcı verilerini yükle
  loadUsersSummary(): void {
    this.isLoading.users = true;
    this.adminHelperService.getUsers().subscribe({
      next: (users) => {
        this.userSummary.total = users.length;

        // Bugün eklenen kullanıcıları hesapla
        const today = new Date();
        this.userSummary.todayCount = users.filter(user => {
          const userDate = new Date(user.created_at);
          return userDate.getFullYear() === today.getFullYear() &&
            userDate.getMonth() === today.getMonth() &&
            userDate.getDate() === today.getDate();
        }).length;
        this.isLoading.users = false;
      },
      error: (error) => {
        console.error('Kullanıcı verileri yüklenemedi:', error);
        this.isLoading.users = false;
      }
    });
  }

  // EFT/Havale verilerini yükle
  loadEftSummary(): void {
    this.isLoading.eft = true;
    this.adminHelperService.getEftPayments().subscribe({
      next: (payments) => {
        this.eftSummary.total = payments.length;
        this.eftSummary.approved = payments.filter(payment => payment.status === 'approved').length;
        this.eftSummary.pending = payments.filter(payment => payment.status === 'pending').length;
        this.eftSummary.rejected = payments.filter(payment => payment.status === 'rejected').length;
        this.isLoading.eft = false;
      },
      error: (error) => {
        console.error('EFT/Havale verileri yüklenemedi:', error);
        this.isLoading.eft = false;
      }
    });
  }

  // Kredi Kartı verilerini yükle
  loadCreditCardSummary(): void {
    this.isLoading.creditCard = true;
    this.adminService.getCreditCardPayments().subscribe({
      next: (payments) => {
        this.creditCardSummary.total = payments.length;
        this.creditCardSummary.approved = payments.filter(payment => payment.status === 'approved').length;
        this.creditCardSummary.pending = payments.filter(payment => payment.status === 'pending').length;
        this.creditCardSummary.rejected = payments.filter(payment => payment.status === 'rejected').length;
        this.isLoading.creditCard = false;
      },
      error: (error) => {
        console.error('Kredi Kartı verileri yüklenemedi:', error);
        this.isLoading.creditCard = false;
      }
    });
  }
}
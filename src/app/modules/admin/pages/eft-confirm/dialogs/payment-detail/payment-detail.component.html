<div class="relative flex flex-col gap-2 p-5 bg-white rounded-3xl w-128">
    <button (click)="onNoClick()" class="absolute text-4xl text-red-500 top-2 right-4">
        ×
    </button>
    <h2 class="text-lg font-bold">Ödeme Detayı</h2>
    <div class="flex flex-col gap-2 ">
        <div class="flex items-center justify-between">
            <p class="text-sm">İşlem Kodu:</p>
            <p class="text-sm font-semibold">{{order.id}} </p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm">Ödeme Yapan:</p>
            <p class="text-sm font-semibold">{{ order.order.user.name}} {{
                order.order.user.surname}}({{order.order.user.id}}) </p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm ">Ödeme <PERSON>:</p>
            <p class="text-sm font-semibold ">{{ order.created_at}}</p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm">Tutar:</p>
            <p class="text-sm font-semibold">{{ order.order.price | thousandSeparator}} ₺ </p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm">İndirim Tutarı:</p>
            <p class="text-sm font-semibold">{{ order.order.discount| thousandSeparator }} ₺ </p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm">Ödenecek Tutar:</p>
            <p class="text-sm font-semibold">{{ order.order.total_price | thousandSeparator}} ₺ </p>
        </div>
        <div class="flex items-center justify-between pb-1 border-b-2">
            <p class="text-sm">Son Güncellenme Tarihi:</p>
            <p class="text-sm font-semibold">{{ order.updated_sentence}} </p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm font-semibold">Durum:</p>

            <span [ngClass]="{
              'text-yellow-700 bg-yellow-300/20 p-1 ': order.status === 'pending',
              'text-green-700 bg-green-300/20 p-1 ': order.status === 'approved',
              'text-red-700 bg-red-300/20 p-1 ': order.status === 'rejected',
              'rounded-2xl': true,
            }" class="flex gap-1 px-2 py-1 text-sm text-center">
                <ng-icon
                    [name]="order.status == 'pending' ? 'matHistoryRound' : order.status == 'approved' ? 'matCheckRound' : order.status == 'rejected' ? 'matCloseRound' : 'hugeArrowMoveUpLeft'"
                    class="text-lg"></ng-icon>
                {{ getStatusLabel(order.status) }}
            </span>

        </div>
    </div>
    <h2 class="text-lg font-bold">Dekont</h2>
    <div *ngIf="isLoadingReceipt" class="flex items-center justify-center h-72">
        <ng-icon name="hugeLoading03" class="text-3xl text-blue-600 animate-spin"></ng-icon>
    </div>
    <div *ngIf="!isLoadingReceipt && fileType == 'image' && fileUrl else fileType == 'pdf' && fileUrl ? '' : !isLoadingReceipt ? null_s3 : ''">
        <img [src]="fileUrl" class="w-full max-h-72">
    </div>
    <div *ngIf="!isLoadingReceipt && fileType == 'pdf' && fileUrl">
        <iframe [src]="fileUrl" type="application/pdf" class="w-full h-96">
        </iframe>
    </div>
    <ng-template #null_s3>
        <p>
            Dekont bulunamadı.
        </p>
    </ng-template>
    <div class="flex justify-center flex-1 gap-4">
        <!-- Approve -->
        <button *ngIf="order?.status === 'pending'" type="button" (click)="approveOrder()"
            [disabled]="order?.status !== 'pending' || isSending"
            [ngClass]="{'opacity-50 cursor-not-allowed': order?.status !== 'pending'}"
            class="flex items-center justify-center gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:cursor-not-allowed disabled:opacity-50 disabled:active:scale-100 disabled:hover:border-zinc-200 hover:border-green-600 text-green-950 active:scale-95 group rounded-3xl hover:border-3 ">
            <ng-icon *ngIf="!isSending || action == 'rejected'" name="matCheckRound"
                class="text-xl text-green-600 group-disabled:text-green-950 "></ng-icon>
            <ng-icon *ngIf="isSending && action == 'approved'" name="hugeLoading03"
                class="text-xl text-white animate-spin group-disabled:text-blue-950 "></ng-icon>
            <p>
                <span class="text-sm font-medium" *ngIf="!isSending || action== 'rejected'">
                    Ödemeyi Onayla
                </span>
                <span class="text-sm font-medium" *ngIf="isSending && action == 'approved'">
                    İşlem Gerçekleştiriliyor
                </span>
            </p>
        </button>
        <!-- Reject -->
        <button *ngIf="order?.status === 'pending'" type="button" (click)="rejectOrder()"
            [disabled]="order?.status !== 'pending' || isSending"
            class="flex items-center justify-center gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:cursor-not-allowed disabled:opacity-50 disabled:active:scale-100 hover:border-red-600 disabled:hover:border-zinc-200 text-red-950 active:scale-95 group rounded-3xl hover:border-3 ">
            <ng-icon *ngIf="!isSending || action == 'approved'" name="matCloseRound"
                class="text-xl text-red-600 group-disabled:text-green-950 "></ng-icon>
            <ng-icon *ngIf="isSending && action == 'rejected'" name="hugeLoading03"
                class="text-xl text-white animate-spin group-disabled:text-blue-950 "></ng-icon>
            <p>
                <span class="text-sm font-medium" *ngIf="!isSending || action== 'approved'">
                    Ödemeyi İptal Et
                </span>
                <span class="text-sm font-medium" *ngIf="isSending && action == 'rejected'">
                    İşlem Gerçekleştiriliyor
                </span>
            </p>
        </button>
        <div *ngIf="order?.status === 'approved'" class="w-full">
            <button type="button" (click)="showInvoiceModal()" [disabled]="isGettingInvoice"
                class="flex items-center justify-center w-full gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:active:scale-100 disabled:hover:border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 group rounded-3xl hover:border-3 ">
                <ng-icon *ngIf="!isGettingInvoice" name="hugeInvoice01" class="text-2xl text-blue-600"></ng-icon>
                <ng-icon *ngIf="isGettingInvoice" name="hugeLoading03"
                    class="text-xl text-white animate-spin group-disabled:text-blue-950 "></ng-icon>
                <p>
                    <span class="text-sm font-medium" *ngIf="!isGettingInvoice">
                        Faturayı Göster
                    </span>
                    <span class="text-sm font-medium" *ngIf="isGettingInvoice">
                        İşlem Gerçekleştiriliyor
                    </span>
                </p>
            </button>
        </div>
    </div>
</div>
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { AdminService } from '@app/data/services/admin.service';
import { DatasetService } from '@app/data/services/dataset.service';
import { PaymentService } from '@app/data/services/payment.service';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { formatRelative } from 'date-fns';
import { tr } from 'date-fns/locale';
import { SnotifyService } from 'ng-alt-snotify';
import { map, Observable, of } from 'rxjs';

@Component({
  selector: 'app-payment-detail',
  templateUrl: './payment-detail.component.html',
  styleUrls: ['./payment-detail.component.scss']
})
export class PaymentDetailComponent {
  constructor(
    @Inject(DIALOG_DATA) public order: any,
    private dialogRef: DialogRef<any>,
    private snotifyService: SnotifyService,
    private adminService: AdminService,
    private dialog: Dialog,
    private paymentService: PaymentService,
    private sanitizer: DomSanitizer,
    private datasetService: DatasetService
  ) {
    this.order.updated_sentence = formatRelative(new Date(order.updated_at), new Date(), { locale: tr })
    this.processReceipt();

  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  isPending(order: any): boolean {
    return order.status === 'pending';
  }

  approveOrder(): void {
    const dialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: 'Ödeme Onayı',
        content: 'Ödemeyi onaylamak istediğinize emin misiniz? Bu işlem geri alınamaz.',
        confirm: 'Evet',
        cancel: 'Hayır'
      }
    });
    dialog.closed.subscribe((result) => {
      if (result) {
        if (this.order && this.isPending(this.order)) {
          this.action = 'approved';
          this.isSending = true;
          const request = this.handleOrderApproval(this.order);
          this.processSingleRequest(request, 'approved');
        }
      }
    }
    )
  }
  isSending = false;
  rejectOrder(): void {
    const dialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: 'Ödeme İptali',
        content: 'Ödemeyi iptal etmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
        confirm: 'Evet',
        cancel: 'Hayır'
      }
    });
    dialog.closed.subscribe((result) => {
      if (result) {
        if (this.order && this.isPending(this.order)) {
          this.action = 'rejected';
          this.isSending = true;
          const request = this.handleOrderRejection(this.order);
          this.processSingleRequest(request, 'rejected');
        }
      }
    }
    )
  }
  isGettingInvoice = false;
  showInvoiceModal() {
    this.isGettingInvoice = true;
    this.paymentService.getOrderInvoice(this.order.order.id).subscribe({
      next: (data) => {
        this.isGettingInvoice = false;
        if (data.status == 'ready') {
          window.open(data.url, '_blank');
        }
        else {
          this.snotifyService.error(data.message, {
            position: 'centerBottom',
            timeout: 1000,
            pauseOnHover: false
          });
        }
      },
      error: (error) => {
        this.isGettingInvoice = false;
        this.snotifyService.error(error.error.message, {
          position: 'centerBottom',
          timeout: 1000,
          pauseOnHover: false
        });
      },
      complete: () => {
      }
    });
  }

  private handleOrderApproval(order: any): Observable<any> {
    return this.sendRequest(order.id, 'approved').pipe(
      map(() => {
        order.status = 'approved';
        return of(null);
      })
    );
  }

  private handleOrderRejection(order: any): Observable<any> {
    return this.sendRequest(order.id, 'rejected').pipe(
      map(() => {
        order.status = 'rejected';
        return of(null);
      })
    );
  }
  action = '';
  private processSingleRequest(request: Observable<any>, status: string): void {
    request.subscribe({
      next: () => {
        this.snotifyService.success(`Ödeme ${status === 'approved' ? 'onaylandı' : status === 'refunded' ? 'iade edildi' : 'iptal edildi'}`, '', {
          timeout: 1500,
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: false,
          position: 'centerBottom',
        });
      },
      error: (error) => {
        console.error('Error processing request:', error);
        this.snotifyService.error('İşlem sırasında bir hata oluştu', '', {
          timeout: 1500,
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: false,
          position: 'centerBottom',
        });
      },
      complete: () => {
        this.isSending = false;
        this.action = '';
      }
    });
  }

  sendRequest(orderId: number, status: string): Observable<any> {
    return this.adminService.updatePayment(orderId, status);
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'pending':
        return 'Onay Bekliyor';
      case 'approved':
        return 'Onaylandı';
      case 'rejected':
        return 'İptal Edildi';
      default:
        return status;
    }
  }
  fileType: string;
  fileUrl: string;
  isLoadingReceipt = false;

  processReceipt() {
    const receiptUrl = this.order.eft_payment?.receipt_s3_url;
    if (receiptUrl) {
      this.isLoadingReceipt = true;
      const fileNameDecoded = decodeURIComponent(receiptUrl.replace(/\+/g, " "));
      const fileName = fileNameDecoded.split('/').pop();
      const fileExtension = fileName.split('.').pop().toLowerCase();
      this.fileType = fileExtension === 'pdf' ? 'pdf' : 'image';

      // Get presigned URL for the file
      this.datasetService.getGetPresignedUrl(fileName).subscribe({
        next: (response) => {
          if (response && response.url) {
            this.fileUrl = fileExtension === 'pdf' ?
              this.sanitizePdf(response.url) :
              response.url;
          } else {
            this.snotifyService.error('Dekont yüklenirken bir hata oluştu', '', {
              position: 'centerBottom',
              timeout: 3000,
              pauseOnHover: false
            });
          }
          this.isLoadingReceipt = false;
        },
        error: (error) => {
          console.error('Error getting presigned URL:', error);
          this.snotifyService.error('Dekont yüklenirken bir hata oluştu', '', {
            position: 'centerBottom',
            timeout: 3000,
            pauseOnHover: false
          });
          this.isLoadingReceipt = false;
        }
      });
    }
  }

  sanitizePdf(pdfData: string): any {
    return this.sanitizer.bypassSecurityTrustResourceUrl(pdfData);
  }
}

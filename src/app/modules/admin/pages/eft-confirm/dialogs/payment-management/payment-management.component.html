<!-- approve modal -->
<div class="relative flex flex-col gap-2 p-5 bg-white w-240 max-h-144 rounded-3xl">

    <button (click)="onNoClick()" class="absolute text-4xl text-red-500 top-2 right-4">
        ×
    </button>
    <p class="flex items-center flex-none gap-2 text-2xl font-medium">
        <ng-icon name="hugeCheckList" class="text-2xl text-blue-700"></ng-icon>
        <span class="">Seçilen Ödemeler</span>
    </p>
    <div class="flex flex-col w-full overflow-auto rounded-3xl">
        <div class="flex-none w-full text-center">
            <div matSort (matSortChange)="sortData($event)"
                class="flex items-center flex-none w-full gap-2 px-3 py-2 text-lg font-medium text-blue-900 rounded-b-none bg-slate-200 rounded-3xl ">
                <div class="flex items-center flex-none w-6">

                </div>
                <div mat-sort-header="name" class="flex-1"><PERSON><PERSON><PERSON><PERSON><PERSON> Adı</div>
                <div mat-sort-header="amount" class="flex items-center justify-end flex-1 ">Tutar</div>
                <!-- <div class="flex-1 text-center">Dekont</div>
                <div class="flex-1 text-center">Fatura</div> -->
                <div mat-sort-header="date" class="flex items-center justify-center flex-1 ">Tarih</div>
                <div mat-sort-header="status" class="flex items-center justify-center flex-1 pr-2">Durum</div>
                <div class="flex-none w-40 pl-10 text-start max-w-40 ">Detay</div>
            </div>
        </div>
        <div class="w-full overflow-auto text-center grow">
            <div *ngFor="let order of payments"
                class="flex items-center w-full gap-2 px-3 py-2 border-b hover:bg-slate-50">
                <div class="flex-none w-6">
                    <input *ngIf="order.status === 'pending'" type="checkbox"
                        class="font-medium transition-all duration-300 rounded-full size-6 bg-slate-50 hover:bg-slate-100 checked:bg-blue-500 checked:hover:bg-blue-600 focus:bg-slate-50 focus:ring-transparent"
                        (change)="toggleCheckbox(order)" [checked]="tmpPayments.has(order)" />
                </div>
                <div class="flex-1 text-left truncate max-w-96">
                    <span class="w-full truncate" [title]="order.order.user.name + ' ' + order.order.user.surname">
                        {{ order.order.user.name}} {{ order.order.user.surname}}
                    </span>
                </div>
                <div class="flex items-center justify-end flex-1 ">{{ order.order.total_price }} ₺</div>
                <div class="flex items-center justify-center flex-1 ">{{ order.created_at }}</div>
                <div class="flex items-center justify-center flex-1">
                    <span [ngClass]="{
                  'text-yellow-700 bg-yellow-300/20 p-1 ': order.status === 'pending',
                  'text-green-700 bg-green-300/20 p-1 ': order.status === 'approved',
                  'text-red-700 bg-red-300/20 p-1 ': order.status === 'rejected',
                  'text-purple-700 bg-purple-300/20 p-1 ': order.status === 'refunded',
                  'rounded-2xl': true,
                }" class="flex gap-1 px-2 py-1 text-sm text-center">
                        <ng-icon
                            [name]="order.status == 'pending' ? 'matHistoryRound' : order.status == 'approved' ? 'matCheckRound' : order.status == 'rejected' ? 'matCloseRound' : 'hugeArrowMoveUpLeft'"
                            class="text-lg"></ng-icon>
                        {{ getStatusLabel(order.status) }}
                    </span>
                </div>
                <div class="flex items-center justify-center flex-1 w-40 text-center cursor-pointer max-w-40">
                    <button type="button" (click)="showReceiptModal(order)"
                        class="flex items-center justify-center gap-2 px-3 py-2 text-sm font-normal transition-all bg-white border shadow border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 group rounded-3xl hover:border-3 ">
                        <ng-icon name="matInfoOutline" class="text-xl text-blue-700"></ng-icon>
                        Görüntüle
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!---->

    <div class="flex justify-center flex-1 gap-4">
        <button type="button" (click)="approveSelectedOrders()" [disabled]="isSending || getSelectedCount() === 0"
            class="flex items-center justify-center gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:cursor-not-allowed disabled:opacity-50 disabled:active:scale-100 disabled:hover:border-zinc-200 hover:border-green-600 text-green-950 active:scale-95 group rounded-3xl hover:border-3 ">
            <ng-icon *ngIf="!isSending || action == 'rejected'" name="matCheckRound"
                class="text-xl text-green-600 group-disabled:text-green-950 "></ng-icon>
            <ng-icon *ngIf="isSending && action == 'approved'" name="hugeLoading03"
                class="text-xl text-white animate-spin group-disabled:text-blue-950 "></ng-icon>
            <p>
                <span class="text-sm font-medium" *ngIf="!isSending || action== 'rejected'">
                    Seçili Ödemeleri Onayla
                </span>
                <span class="text-sm font-medium" *ngIf="isSending && action == 'approved'">
                    İşlem Gerçekleştiriliyor
                </span>
            </p>
        </button>
        <button type="button" (click)="rejectSelectedOrders()" [disabled]="isSending || getSelectedCount() === 0"
            class="flex items-center justify-center gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:cursor-not-allowed disabled:opacity-50 disabled:active:scale-100 hover:border-red-600 disabled:hover:border-zinc-200 text-red-950 active:scale-95 group rounded-3xl hover:border-3 ">
            <ng-icon *ngIf="!isSending || action == 'approved'" name="matCloseRound"
                class="text-xl text-red-600 group-disabled:text-green-950 "></ng-icon>
            <ng-icon *ngIf="isSending && action == 'rejected'" name="hugeLoading03"
                class="text-xl text-white animate-spin group-disabled:text-blue-950 "></ng-icon>
            <p>
                <span class="text-sm font-medium" *ngIf="!isSending || action== 'approved'">
                    Seçili Ödemeleri İptal Et
                </span>
                <span class="text-sm font-medium" *ngIf="isSending && action == 'rejected'">
                    İşlem Gerçekleştiriliyor
                </span>
            </p>
        </button>
    </div>
</div>
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AdminService } from '@app/data/services/admin.service';
import { SnotifyService } from 'ng-alt-snotify';
import { catchError, concatMap, from, map, Observable, of } from 'rxjs';
import { PaymentDetailComponent } from '../payment-detail/payment-detail.component';
import { Sort } from '@angular/material/sort';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';

@Component({
  selector: 'app-payment-management',
  templateUrl: './payment-management.component.html',
  styleUrls: ['./payment-management.component.scss']
})
export class PaymentManagementComponent {
  constructor(
    public dialogRef: DialogRef<PaymentManagementComponent>,
    @Inject(DIALOG_DATA) public payments: any,
    private dialog: Dialog,
    private snotifyService: SnotifyService,
    private adminService: AdminService
  ) {
    this.payments.forEach(payment => {
      this.tmpPayments.add(payment);
    });

  }
  tmpPayments: Set<any> = new Set();
  onNoClick(): void {
    this.dialogRef.close();
  }
  isSending = false;

  totalRequests = 0;
  approveSelectedOrders(): void {
    const dialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: 'Ödeme Onayı',
        content: 'Seçilen ödemeleri onaylamak istediğinize emin misiniz? Bu işlem geri alınamaz.',
        confirm: 'Evet',
        cancel: 'Hayır'
      }
    });
    dialog.closed.subscribe((result) => {
      if (result) {
        this.action = 'approved';
        this.isSending = true;
        this.approvedCount = 0;
        this.totalRequests = this.payments.length;
        const requests = this.payments.filter(order => order.status === 'pending' && Array.from(this.tmpPayments).find(payment => payment.id === order.id)).map(order => this.handleOrderApproval(order));
        this.processSequentialRequests(requests, 'approved');
      }
    });
  }
  approvedCount = 0;
  rejectedCount = 0;
  private processSequentialRequests(requests: Observable<any>[], status: string) {
    from(requests).pipe(
      concatMap(request =>
        request.pipe(
          catchError(error => {
            this.snotifyService.error('Bir ödeme işleminde hata oluştu, diğer işlemler devam ediyor.', '');
            return of(null); // Hata olsa da akışa devam etmek için null dönüyoruz
          })
        )
      )
    ).subscribe({
      next: () => {
      },
      complete: () => {
        const message = `Seçilen ${this.action == 'approved' ? this.approvedCount : this.rejectedCount} ödeme ${this.action === 'approved' ? 'onaylandı' : 'iptal edildi'}.`;
        this.snotifyService.success(message, '');
        this.isSending = false;
        this.action = '';
        this.approvedCount = 0;
        this.rejectedCount = 0;
        this.refreshSelectedOrders();
        if (!this.payments.some(payment => payment.status === 'pending')) {
          this.dialogRef.close();
        }
      }
    });
  }
  refreshSelectedOrders(): void {
    this.tmpPayments = new Set(
      Array.from(this.tmpPayments).filter(orderId => {
        const order = this.payments.find(o => o.id === orderId);
        return order && order.status === 'pending';
      })
    );
  }

  
  sendRequest(orderId: number, status: string): Observable<any> {
    return this.adminService.updatePayment(orderId, status);
  }

  showReceiptModal(order: any): void {
    const dialog = this.dialog.open(PaymentDetailComponent, {
      data: order
    });
  }
  private handleOrderApproval(order: any): Observable<any> {
    return this.sendRequest(order.id, 'approved').pipe(
      map(() => {
        order.status = 'approved';
        this.approvedCount++;
        return of(null);
      })
    );
  }

  sortData(sort: Sort): void {
    const data = [...this.payments];

    this.payments = data.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'name':
          return this.compare(a.order.user.name.toLowerCase(), b.order.user.name.toLowerCase(), isAsc);
        case 'amount':
          return this.compare(a.order.total_price, b.order.total_price, isAsc);
        case 'date':
          return this.compare(new Date(a.created_at_full), new Date(b.created_at_full), isAsc);
        case 'status':
          return this.compare(a.status, b.status, isAsc);
        default:
          return 0;
      }
    });
  }
  compare(a: number | string | Date, b: number | string | Date, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }

  rejectSelectedOrders(): void {
    const dialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: 'Ödeme İptali',
        content: 'Seçilen ödemeleri iptal etmek istediğinize emin misiniz? Bu işlem geri alınamaz.',
        confirm: 'Evet',
        cancel: 'Hayır'
      }
    });
    dialog.closed.subscribe((result) => {
      if (result) {
        this.action = 'rejected';
        this.isSending = true;
        this.rejectedCount = 0;
        this.totalRequests = this.payments.length;
        const requests = this.payments.filter(order => order.status === 'pending' && Array.from(this.tmpPayments).find(payment => payment.id === order.id)).map(order => this.handleOrderRejection(order));
        this.processSequentialRequests(requests, 'rejected');
      }
    });
  }

  getSelectedCount(): number {
    return this.payments.filter(order => Array.from(this.tmpPayments).find(payment => payment.id === order.id)).length;
  }
  action = '';
  private handleOrderRejection(order: any): Observable<any> {
    return this.sendRequest(order.id, 'rejected').pipe(
      map(() => {
        order.status = 'rejected';
        this.rejectedCount++;
        return of(null);
      })
    );
  }
  toggleCheckbox(order: any): void {
    if (order && order.status === 'pending') {
      if (Array.from(this.tmpPayments).find(payment => payment.id === order.id)) {
        this.tmpPayments.delete(order);
      } else {
        this.tmpPayments.add(order);
      }
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'pending':
        return 'Onay Bekliyor';
      case 'approved':
        return 'Onaylandı';
      case 'rejected':
        return 'İptal Edildi';
      case 'refunded':
        return 'İade Edildi';
      default:
        return status;
    }
  }
}

<div class="flex flex-col h-full gap-1 pb-1 grow bg-blue-50 rounded-3xl">
    <!-- Summary Section -->
    <div class="grid grid-cols-6 gap-3 p-3 ">
        <!-- Total Transactions -->
        <div class="flex items-center justify-between col-span-2 p-3 bg-white shadow-sm rounded-xl">
            <div class="flex items-center gap-2">
                <div class="flex items-center p-2 bg-blue-100 rounded-lg">
                    <ng-icon name="tdesignUndertakeTransaction" class="text-lg text-blue-600"></ng-icon>
                </div>
                <div class="flex flex-col">
                    <span class="text-sm font-medium">Toplam İşlem</span>
                    <span class="text-lg font-semibold">{{summary.total}}</span>
                </div>
            </div>
            <span class="text-xs" [ngClass]="getTodayCount() > 0 ? 'text-green-700' : 'text-yellow-500'">
                {{getTodayCount() > 0 ? '+' + getTodayCount() : getTodayCount()}} bugün
            </span>
        </div>

        <!-- Total Amount -->
        <div class="flex items-center justify-between col-span-2 p-3 bg-white shadow-sm rounded-xl">
            <div class="flex items-center gap-2">
                <div class="flex p-2 bg-green-100 rounded-lg">
                    <ng-icon name="tablerCurrencyLira" class="text-lg text-green-600"></ng-icon>
                </div>
                <div class="flex flex-col">
                    <span class="text-sm font-medium">Toplam Tutar</span>
                    <span class="text-lg font-semibold"> ₺{{summary.totalAmount | thousandSeparator}}</span>
                </div>
            </div>
            <span class="text-xs" [ngClass]="getTodayTotalAmount() > 0 ? 'text-green-700' : 'text-yellow-500'">
                {{getTodayTotalAmount() > 0 ? '+₺' : ''}}{{getTodayTotalAmount() |thousandSeparator}} bugün
            </span>
        </div>

        <!-- Status Summary -->
        <div class="flex items-center justify-center col-span-2 p-2 bg-white shadow-sm rounded-xl">
            <div class="grid grid-cols-3 gap-2">
                <div class="flex flex-col items-center">
                    <span class="text-xs text-gray-500">Onaylanan</span>
                    <span class="text-lg font-medium text-green-600">{{summary.approved}}</span>
                </div>
                <div class="flex flex-col items-center">
                    <span class="text-xs text-gray-500">Bekleyen</span>
                    <span class="text-lg font-medium text-yellow-600">{{summary.pending}}</span>
                </div>
                <div class="flex flex-col items-center">
                    <span class="text-xs text-gray-500">İptal Edilen</span>
                    <span class="text-lg font-medium text-red-600">{{summary.rejected}}</span>
                </div>
            </div>
        </div>
    </div>
    <!-- Filter and Search Section -->
    <div class="grid grid-cols-3 gap-4 mx-4 mb-4">
        <!-- Search Section -->
        <div class="col-span-1">
            <div class="flex items-center justify-center w-full h-full gap-2 p-2 bg-white rounded-3xl">
                <ng-icon name="tablerSearch" class="text-xl text-gray-400"></ng-icon>
                <input type="text" 
                       (keyup)="applySearchFilter($event)"
                       class="w-full text-sm bg-transparent border-none outline-none placeholder:text-gray-400"
                       placeholder="İşlem ID, kullanıcı adı veya email ile ara">
            </div>
        </div>
        <!-- Filter Section with Material Expansion Panel -->
        <div class="relative col-span-2">
            <mat-accordion class="absolute z-[999] w-full">
                <mat-expansion-panel class="shadow-lg">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <div class="flex items-center gap-2">
                                <ng-icon name="tablerFilter" class="text-xl"></ng-icon>
                                Filtrele
                            </div>
                        </mat-panel-title>
                    </mat-expansion-panel-header>

                    <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <!-- Date Range -->
                            <mat-form-field>
                                <mat-label>Başlangıç Tarihi</mat-label>
                                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                                <mat-datepicker #startPicker></mat-datepicker>
                            </mat-form-field>

                            <mat-form-field>
                                <mat-label>Bitiş Tarihi</mat-label>
                                <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                                <mat-datepicker #endPicker></mat-datepicker>
                            </mat-form-field>

                            <!-- Status -->
                            <mat-form-field>
                                <mat-label>Durum</mat-label>
                                <mat-select formControlName="status">
                                    <mat-option value="">Tümü</mat-option>
                                    <mat-option value="approved">Onaylandı</mat-option>
                                    <mat-option value="pending">Beklemede</mat-option>
                                    <mat-option value="rejected">Reddedildi</mat-option>
                                </mat-select>
                            </mat-form-field>

                            <!-- Amount Range -->
                            <mat-form-field>
                                <mat-label>Min. Tutar</mat-label>
                                <input matInput type="number" formControlName="minAmount">
                            </mat-form-field>

                            <mat-form-field>
                                <mat-label>Max. Tutar</mat-label>
                                <input matInput type="number" formControlName="maxAmount">
                            </mat-form-field>
                        </div>

                        <div class="flex justify-end gap-2 mt-4">
                            <button mat-button type="button" (click)="resetFilter()">Sıfırla</button>
                            <button mat-raised-button color="primary" type="submit">Filtrele</button>
                        </div>
                    </form>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>

    <div class="table-section">
        <div class="overflow-auto table-container" style="max-height: calc(100vh - 320px)">
            <mat-table [dataSource]="dataSource" matSort>
                <!-- Checkbox Column -->
                <ng-container matColumnDef="select">
                    <mat-header-cell *matHeaderCellDef>
                        <input *ngIf="hasPendingOrders()" type="checkbox" [(ngModel)]="isAllChecked"
                            (change)="toggleAllCheckboxes($event)"
                            class="font-medium transition-all duration-300 rounded-full size-6 bg-slate-50 hover:bg-slate-100 checked:bg-blue-500 checked:hover:bg-blue-600 focus:bg-slate-50 focus:ring-transparent" />
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row">
                        <input *ngIf="row.status === 'pending'" type="checkbox" (change)="toggleCheckbox(row.id,$event)"
                            [checked]="selectedOrders.has(row.id)"
                            class="font-medium transition-all duration-300 rounded-full size-6 bg-slate-50 hover:bg-slate-100 checked:bg-blue-500 checked:hover:bg-blue-600 focus:bg-slate-50 focus:ring-transparent" />
                    </mat-cell>
                </ng-container>

                <!-- ID Column -->
                <ng-container matColumnDef="id">
                    <mat-header-cell *matHeaderCellDef mat-sort-header> ID </mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{row.id}} </mat-cell>
                </ng-container>

                <!-- Name Column -->
                <ng-container matColumnDef="name">
                    <mat-header-cell *matHeaderCellDef mat-sort-header> Kullanıcı Adı </mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{row.order.user.name}} {{row.order.user.surname}} </mat-cell>
                </ng-container>

                <!-- Amount Column -->
                <ng-container matColumnDef="amount">
                    <mat-header-cell *matHeaderCellDef mat-sort-header class="justify-end"> Tutar </mat-header-cell>
                    <mat-cell *matCellDef="let row" class="justify-end">
                        <div class="flex flex-col items-end">
                            <span>{{row.order.total_price | thousandSeparator}} ₺</span>
                            <span *ngIf="row.order.discount > 0" class="text-sm text-gray-500">
                                İndirim: {{row.order.discount | thousandSeparator}} ₺
                            </span>
                        </div>
                    </mat-cell>
                </ng-container>

                <!-- Date Column -->
                <ng-container matColumnDef="date">
                    <mat-header-cell *matHeaderCellDef mat-sort-header class="justify-center"> Tarih </mat-header-cell>
                    <mat-cell *matCellDef="let row" class="justify-center">
                        {{row.created_at | date:'dd MMMM yyyy HH:mm':'':'tr-TR'}} </mat-cell>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef mat-sort-header class="justify-center"> Durum </mat-header-cell>
                    <mat-cell *matCellDef="let row" class="justify-center">
                        <span [ngClass]="{
              'text-yellow-700 bg-yellow-300/20': row.status === 'pending',
              'text-green-700 bg-green-300/20': row.status === 'approved',
              'text-red-700 bg-red-300/20': row.status === 'rejected',
              'rounded-2xl p-1': true
            }" class="flex gap-1 px-2 py-1 text-sm text-center">
                            <ng-icon
                                [name]="row.status == 'pending' ? 'matHistoryRound' : row.status == 'approved' ? 'matCheckRound' : 'matCloseRound'"
                                class="text-lg">
                            </ng-icon>
                            {{getStatusLabel(row.status)}}
                        </span>
                    </mat-cell>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                    <mat-header-cell *matHeaderCellDef class="justify-center"> Detay </mat-header-cell>
                    <mat-cell *matCellDef="let row" class="justify-center">
                        <button type="button" (click)="showReceiptModal(row)"
                            class="flex items-center justify-center gap-2 px-3 py-2 text-sm font-normal transition-all bg-white border shadow border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 group rounded-3xl hover:border-3">
                            <ng-icon name="matInfoOutline" class="text-xl text-blue-700"></ng-icon>
                            Görüntüle
                        </button>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;" class="hover:bg-slate-50"></mat-row>
            </mat-table>
        </div>
        <mat-paginator [pageSizeOptions]="[10, 25, 50]" showFirstLastButtons></mat-paginator>
    </div>

    <!--main buttons-->
    <div class="flex items-center justify-center flex-none w-full">
        <button type="button" (click)="showPaymentManagement()" [disabled]="selectedOrders.size === 0"
            class="flex items-center justify-center gap-2 px-4 py-3 text-lg font-normal transition-all bg-white border shadow disabled:cursor-not-allowed disabled:active:scale-100 disabled:opacity-50 disabled:hover:border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 group rounded-3xl hover:border-3">
            <ng-icon name="hugeCheckList" class="text-xl text-blue-700"></ng-icon>
            Seçili Ödemeleri Görüntüle ({{selectedOrders.size}})
        </button>
    </div>
</div>
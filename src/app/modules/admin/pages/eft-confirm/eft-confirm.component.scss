.table-section {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.table-container {
  flex: 1;
  min-height: 0;
  overflow: auto;
  
  .mat-header-row {
    position: sticky;
    top: 0;
    z-index: 100;
    background: rgb(226 232 240);
  }
}

.mat-header-row {
  background: rgb(226 232 240);
  min-height: 48px;
}

.mat-row {
  min-height: 48px;
}

.mat-table {
  width: 100%;
}

// Ensure proper header cell alignment
.mat-header-cell {
  &.justify-end {
    justify-content: flex-end;
  }
  &.justify-center {
    justify-content: center;
  }
}

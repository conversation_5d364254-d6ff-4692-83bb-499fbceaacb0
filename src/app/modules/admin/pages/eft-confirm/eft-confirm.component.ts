import { animate, state, style, transition, trigger } from '@angular/animations';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Sort } from '@angular/material/sort';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';
import { AdminService } from '@app/data/services/admin.service';
import { SnotifyService } from 'ng-alt-snotify';
import { catchError, concatMap, forkJoin, from, map, Observable, of } from 'rxjs';
import { PaymentDetailComponent } from './dialogs/payment-detail/payment-detail.component';
import { Dialog } from '@angular/cdk/dialog';
import { PaymentManagementComponent } from './dialogs/payment-management/payment-management.component';
import { <PERSON><PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';

interface PaymentSummary {
  total: number;
  totalAmount: number;
  pending: number;
  approved: number;
  rejected: number;
}

@Component({
  selector: 'app-eft-confirm',
  templateUrl: './eft-confirm.component.html',
  styleUrls: ['./eft-confirm.component.scss'],
  animations: [
    trigger('opacity', [
      state('void', style({ opacity: '0' })),
      state('*', style({ opacity: '*' })),
      transition('void <=> *', [animate('0.3s ease-in-out')]),
    ]),
  ]
})
export class EftConfirmComponent implements OnInit {
  ordersData: any[] = [];
  filteredOrders = this.ordersData;
  selectedOrders: Set<number> = new Set();
  selectedReceiptOrder: any;
  searchTerm = '';
  selectedOrderData: any[] = [];
  isRingVisible = false;
  filterOn = false;

  minAmount: number | null = null;
  maxAmount: number | null = null;
  startDate: Date | null = null;
  endDate: Date | null = null;
  selectedStatuses: Set<string> = new Set();
  dateRange: { start: Date | null; end: Date | null } = { start: null, end: null };
  status: string = '';

  summary: PaymentSummary = {
    total: 0,
    totalAmount: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  };

  filterForm: FormGroup;
  dataSource: MatTableDataSource<any> = new MatTableDataSource();
  
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private adminHelperService: AdminHelperService,
    private adminService: AdminService,
    private dialog: Dialog,
    private fb: FormBuilder
  ) {
    // Initialize form first
    this.filterForm = this.fb.group({
      startDate: [null],
      endDate: [null],
      status: [''],
      minAmount: [null],
      maxAmount: [null]
    });
    
    // Then initialize other properties
    this.filteredOrders = this.selectedOrderData;
    this.resetFilter();
    this.dataSource = new MatTableDataSource();
  }

  ngOnInit(): void {
    this.getPayments();
    this.setupSearchFilter();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    
    // Varsayılan sıralama için
    setTimeout(() => {
      this.sort.sort({
        id: 'date',
        start: 'desc',
        disableClear: false
      });
    });
  }

  isFilterApplied(): boolean {
    return this.minAmount !== null || this.maxAmount !== null || this.startDate !== null || this.endDate !== null || this.selectedStatuses.size > 0;
  }
  onFilterApplied(filterData: { minAmount: number | null; maxAmount: number | null; startDate: Date | null; endDate: Date | null; selectedStatuses: Set<string> }): void {
    this.minAmount = filterData.minAmount;
    this.maxAmount = filterData.maxAmount;
    this.startDate = filterData.startDate;
    this.endDate = filterData.endDate;
    this.selectedStatuses = filterData.selectedStatuses;
    this.applyFilter();
  }


  applyFilter(): void {
    this.filteredOrders = this.ordersData.filter(order => {
      const amountCondition =
        (this.minAmount === null || order.order.total_price >= this.minAmount) &&
        (this.maxAmount === null || order.order.total_price <= this.maxAmount);
      const dateCondition =
        (this.startDate === null || new Date(order.created_at_full) >= new Date(this.startDate)) &&
        (this.endDate === null || new Date(order.created_at_full) <= new Date(this.endDate));

      const statusCondition =
        this.selectedStatuses.size === 0 || this.selectedStatuses.has(order.status);

      return amountCondition && dateCondition && statusCondition;
    });
    
    // Update summary after applying filters
    this.updateSummary(this.filteredOrders);
    this.dataSource.data = this.filteredOrders;
  }

  resetFilter(): void {
    // Form kontrollerini sıfırla
    this.filterForm.reset();
    
    // Filtered data'yı sıfırla
    this.filteredOrders = [...this.ordersData];
    
    // DataSource'u güncelle
    this.dataSource.data = this.filteredOrders;
    
    // Summary'i güncelle
    this.updateSummary(this.filteredOrders);

    // Paginatoru sıfırla
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  closeFilter(): void {
    this.filterOn = false;
  }

  filterFunc(): void {
    this.filterOn = !this.filterOn;
  }


  isStatusChangeable(order: any): boolean {
    return order.status === 'pending';
  }

  toggleRingOpacity(): void {
    this.isRingVisible = !this.isRingVisible;
    this.selectedOrderData = this.filteredOrders.filter(order => this.selectedOrders.has(order.id));

  }

  sendRequest(orderId: number, status: string): Observable<any> {
    return this.adminService.updatePayment(orderId, status);
  }

  getPayments() {
    return this.adminHelperService.getEftPayments().subscribe({
      next: (res) => {
        this.ordersData = res;
        // Tarihe göre sırala
        this.ordersData.sort((a, b) => 
          new Date(b.created_at_full).getTime() - new Date(a.created_at_full).getTime()
        );
        
        this.filteredOrders = this.ordersData;
        this.selectedOrderData = this.ordersData;
        this.updateSummary(this.filteredOrders);
        this.applyFilter();
        this.dataSource.data = this.filteredOrders;
      },
      error: (error) => {
        console.error('Ödemeler alınamadı:', error);
      }
    });
  }

  updateSummary(orders: any[]): void {
    this.summary = {
      total: orders.length,
      totalAmount: orders.reduce((sum, order) => sum + (order.status === 'approved' ? order.order.total_price : 0), 0).toFixed(0),
      pending: orders.filter(order => order.status === 'pending').length,
      approved: orders.filter(order => order.status === 'approved').length,
      rejected: orders.filter(order => order.status === 'rejected').length
    };
  }

  refreshSelectedOrders(): void {
    this.selectedOrders = new Set(
      Array.from(this.selectedOrders).filter(orderId => {
        const order = this.ordersData.find(o => o.id === orderId);
        return order && order.status === 'pending';
      })
    );
  }


  toggleAllCheckboxes(event: any): void {
    const isChecked = event.target.checked;  // Changed from event.checked
    const pendingOrders = this.filteredOrders.filter(order => order.status === 'pending');
    
    if (isChecked) {
        pendingOrders.forEach(order => this.selectedOrders.add(order.id));
    } else {
        pendingOrders.forEach(order => this.selectedOrders.delete(order.id));
    }
    
    this.isAllChecked = isChecked;
}

toggleCheckbox(orderId: number, event: any): void {
    const isChecked = event.target.checked;  // Changed from event.checked
    
    if (isChecked) {
        this.selectedOrders.add(orderId);
    } else {
        this.selectedOrders.delete(orderId);
    }
    
    // Update isAllChecked status
    const pendingOrders = this.filteredOrders.filter(order => order.status === 'pending');
    this.isAllChecked = pendingOrders.length > 0 && 
                       pendingOrders.every(order => this.selectedOrders.has(order.id));
    
}

  displayedColumns: string[] = ['select', 'id', 'name', 'amount', 'date', 'status', 'actions'];
  isAllChecked = false;
  sortData(sort: Sort): void {
    const data = [...this.filteredOrders];
    if (!sort.active || sort.direction === '') {
      this.filteredOrders = this.ordersData;
      this.applyFilter();
      return;
    }
    this.filteredOrders = data.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'id':
          return this.compare(a.id, b.id, isAsc);
        case 'name':
          return this.compare(a.order.user.name.toLowerCase(), b.order.user.name.toLowerCase(), isAsc);
        case 'amount':
          return this.compare(a.order.total_price, b.order.total_price, isAsc);
        case 'date':
          return this.compare(new Date(a.created_at_full), new Date(b.created_at_full), isAsc);
        case 'status':
          return this.compare(a.status, b.status, isAsc);
        default:
          return 0;
      }
    });
  }

  compare(a: number | string | Date, b: number | string | Date, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }

  getPendingCount(): number {
    return this.selectedOrderData.filter(order => order.status === 'pending').length;
  }
  getRejectedCount(): number {
    return this.selectedOrderData.filter(order => order.status === 'rejected').length;
  }
  getApprovedCount(): number {
    return this.selectedOrderData.filter(order => order.status === 'approved').length
  }

  hasPendingOrders(): boolean {
    return this.filteredOrders.some(order => order.status === 'pending');
  }
  getTodayCount(): number {
    return this.selectedOrderData.filter(order => {
      const orderDate = new Date(order.created_at_full);
      const today = new Date();
      return orderDate.getFullYear() === today.getFullYear() &&
             orderDate.getMonth() === today.getMonth() &&
             orderDate.getDate() === today.getDate();
    }).length;
  }

  filterAndSortOrders(): void {
    this.filteredOrders = this.selectedOrderData.filter(order => {
      const orderDate = new Date(order.date); // Tarihi Date nesnesine çevirin
      return (!this.startDate || orderDate >= this.startDate) && (!this.endDate || orderDate <= this.endDate);
    });

    // Sıralama: Tarihe göre artan sıralama
    this.filteredOrders.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }


  showPaymentManagement(): void {
    if (this.selectedOrders.size > 0) {
        this.selectedOrderData = this.filteredOrders.filter(order => this.selectedOrders.has(order.id));
        
        const dialog = this.dialog.open(PaymentManagementComponent, {
            data: this.selectedOrderData,
            width: '80%',  // Add appropriate sizing
            maxHeight: '90vh'
        });
        
        dialog.closed.subscribe(() => {
            this.getPayments();
            this.refreshSelectedOrders();
            this.selectedOrders.clear();  // Clear selections after dialog closes
            this.isAllChecked = false;
        });
    }
}

  showReceiptModal(order: any): void {
    const dialog = this.dialog.open(PaymentDetailComponent, {
      data: order
    });
  }

  getTotalAmount(): number {
    return this.selectedOrderData.filter(item => item.status != "rejected").reduce((total, order) => total + order.order.total_price, 0).toFixed(0);
  }
  getTodayTotalAmount(): number {
    return Number(this.dataSource?.data
      ?.filter(order => {
        const orderDate = new Date(order.created_at_full);
        const today = new Date();
        return orderDate.getFullYear() === today.getFullYear() &&
               orderDate.getMonth() === today.getMonth() &&
               orderDate.getDate() === today.getDate() &&
               order.status == 'approved';
      })
      .reduce((total, order) => total + Number(order.order?.total_price || 0), 0)
      .toFixed(0)) || 0;
  }
  resetAllCheckboxes(): void {
    this.selectedOrders.clear();
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'pending':
        return 'Bekliyor';
      case 'approved':
        return 'Onaylandı';
      case 'rejected':
        return 'İptal Edildi';
      default:
        return status;
    }
  }

  applyFilters(): void {
    const filters = this.filterForm.value;
    
    this.filteredOrders = this.ordersData.filter(order => {
      // Tarih kontrolü
      const startDate = filters.startDate ? new Date(filters.startDate) : null;
      const endDate = filters.endDate ? new Date(filters.endDate) : null;
      
      // Eğer endDate varsa, günün sonuna ayarla (23:59:59)
      if (endDate) {
        endDate.setHours(23, 59, 59, 999);
      }
      
      const orderDate = new Date(order.created_at);
      const dateMatches = (!startDate || orderDate >= startDate) && 
                         (!endDate || orderDate <= endDate);

      // Durum kontrolü
      const statusMatches = !filters.status || order.status === filters.status;

      // Tutar kontrolü
      const amountMatches = (!filters.minAmount || parseFloat(order.order.total_price) >= filters.minAmount) &&
                           (!filters.maxAmount || parseFloat(order.order.total_price) <= filters.maxAmount);

      return dateMatches && statusMatches && amountMatches;
    });

    // DataSource'u güncelle
    this.dataSource.data = this.filteredOrders;
    
    // Summary'i güncelle
    this.updateSummary(this.filteredOrders);

    // Paginatoru ilk sayfaya döndür
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setupSearchFilter() {
    this.dataSource.filterPredicate = (data: any, filter: string) => {
      const searchStr = filter.toLocaleLowerCase('tr-TR');
      return data.id.toString().includes(searchStr) ||
             data.order.user.name.toLocaleLowerCase('tr-TR').includes(searchStr) ||
             data.order.user.surname.toLocaleLowerCase('tr-TR').includes(searchStr) ||
             data.order.user.email.toLocaleLowerCase('tr-TR').includes(searchStr);
    };
  }

  applySearchFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLocaleLowerCase('tr-TR');

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
}

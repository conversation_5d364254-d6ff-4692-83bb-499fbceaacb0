<div class="relative flex flex-col gap-2 p-5 bg-white rounded-3xl w-128">
    <button (click)="onNoClick()" class="absolute text-4xl text-red-500 top-2 right-4">
        ×
    </button>
    <h2 class="text-lg font-bold"><PERSON><PERSON><PERSON><PERSON><PERSON> Detayı</h2>
    <div class="flex flex-col gap-2 ">
        <div class="flex items-center justify-between">
            <p class="text-sm">Kullanıcı ID:</p>
            <p class="text-sm font-semibold">{{user.id}} </p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm">Kullanıcı Adı:</p>
            <p class="text-sm font-semibold">{{ user.full_name}}</p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm ">Hesap Oluşturma Tarihi:</p>
            <p class="text-sm font-semibold ">{{ user.created_at}}</p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm">Email:</p>
            <p class="text-sm font-semibold">{{ user.email }} </p>
        </div>
        <!-- <div class="flex items-center justify-between">
            <p class="text-sm">Kullanıcı Kredisi:</p>
            <p class="text-sm font-semibold">  </p>
        </div> -->
        <div class="flex items-center justify-between">
            <p class="text-sm">Telefon Numarası:</p>
            <p class="text-sm font-semibold">{{ user.phone_number}} </p>
        </div>
        <div class="flex items-center justify-between">
            <p class="text-sm">Kullanıcı hediye kabul ediyor mu:</p>
            <div class="text-sm font-semibold"> 
                <div>
                    <ng-container *ngIf="user.gift_acceptance; else notAccepting">
                      <span class="text-sm font-medium text-green-600">Evet</span>
                    </ng-container>
                    <ng-template #notAccepting>
                      <span class="text-sm font-medium text-red-600">Hayır</span>
                    </ng-template>
                  </div>
            </div>
        </div>
        
        <div class="flex items-center justify-between pb-1 border-b-2">
            <p class="text-sm">Roller:</p>
            <div class="flex items-center justify-center gap-1">
                <span *ngFor="let role of user.unique_roles" [ngClass]="{
                    'text-yellow-700 bg-yellow-300/20  ': role === 'analyzer',
                    'text-green-700 bg-green-300/20  ': role === 'proxy_user',
                    'text-purple-700 bg-purple-300/20  ': role === 'admin',
                    'rounded-2xl': true,
                }" class="flex gap-1 px-2 py-1 text-sm text-center">
                    {{ role }}
                </span>
            </div>
        </div>

    </div>
    <div class="flex flex-col gap-4">
        <div class="flex justify-center flex-1 gap-4">
            <button type="button" (click)="switchUser()"
                class="flex items-center justify-center w-full gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:active:scale-100 disabled:hover:border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 group rounded-3xl hover:border-3 ">
                <ng-icon name="hugeRocket01" class="text-2xl text-blue-600"></ng-icon>
                <p>
                    <span class="text-sm font-medium">
                        Kullanıcıya Geç
                    </span>
                </p>
            </button>
            <button type="button" *ngIf="!user.banned" (click)="banUser()"
                class="flex items-center justify-center w-full gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:active:scale-100 disabled:hover:border-zinc-200 hover:border-red-600 text-red-950 active:scale-95 group rounded-3xl hover:border-3">
                <ng-icon name="hugeUserMinus02" class="text-2xl text-red-600"></ng-icon>
                <p>
                    <span class="text-sm font-medium">
                        Kullanıcıyı Banla
                    </span>
                </p>
            </button>
            <button type="button" *ngIf="user.banned" (click)="unbanUser()"
                class="flex items-center justify-center w-full gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:active:scale-100 disabled:hover:border-zinc-200 hover:border-green-600 text-green-950 active:scale-95 group rounded-3xl hover:border-3">
                <ng-icon name="hugeUserAdd02" class="text-2xl text-green-600"></ng-icon>
                <p>
                    <span class="text-sm font-medium">
                        Banı Kaldır
                    </span>
                </p>
            </button>
        </div>
        <button *ngIf="!showSendButton" type="button" (click)="sendCredit()"
            class="flex items-center justify-center w-full gap-2 px-3 py-2 font-normal transition-all bg-white border shadow disabled:active:scale-100 disabled:hover:border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 group rounded-3xl hover:border-3 ">
            <ng-icon name="lucideGift" class="text-2xl text-yellow-600"></ng-icon>
            <p>
                <span class="text-sm font-medium">
                    Kullanıcıya Hediye Kredi Gönder
                </span>
            </p>
        </button>
        <div *ngIf="showSendButton" class="flex flex-col w-full gap-4">
            <app-admin-gifting [user]="user"></app-admin-gifting>
            <div class="flex items-center justify-center gap-2">
                <p class="text-sm">Mevcut krediniz:</p>
                <div class="flex items-center justify-center gap-1">
                    <svg width="24" height="24" viewBox="0 0 515 515" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="500" height="500" rx="250" transform="matrix(1 0 0 -1 15 515)" fill="#AD8000"/>
                        <rect width="500" height="500" rx="250" transform="matrix(1 0 0 -1 0 500)" fill="#EEB501"/>
                        <g clip-path="url(#clip0_7246_32)">
                        <rect width="400" height="400" rx="200" transform="matrix(1 0 0 -1 50 450)" fill="#C08E00"/>
                        <rect width="400" height="400" rx="200" transform="matrix(1 0 0 -1 60 460)" fill="#F3C70D"/>
                        <path d="M305.894 312.459L320.455 277.289C321.49 274.79 314.606 311.519 313.536 323.156C313.371 324.944 314.882 325.738 316.455 324.871L338.923 312.488C340.074 311.854 341.567 312.464 342.74 313.056V313.056L377.631 330.663C380.146 331.932 380.146 335.26 377.631 336.53L342.74 354.137C341.977 354.522 341.39 355.146 341.084 355.896L327.247 389.887C326.073 392.772 321.647 392.783 320.455 389.904L306.373 355.889V355.889C305.884 354.707 305.428 353.202 306.2 352.182L320.321 333.54C321.454 332.044 320.815 330.387 318.939 330.338C306.603 330.014 267.387 331.753 269.571 330.658L305.894 312.459Z" fill="#A26300"/>
                        <path d="M169.219 163.993L183.78 128.822C184.852 126.232 180.794 149.992 180.825 156.884C180.829 157.907 181.608 158.612 182.614 158.798L202.795 162.532C203.801 162.718 204.588 163.483 205.374 164.137C205.583 164.311 205.814 164.463 206.064 164.589L240.956 182.197C243.471 183.466 243.471 186.794 240.956 188.063L206.064 205.671C205.302 206.055 204.714 206.68 204.409 207.429L190.572 241.42C189.398 244.305 184.972 244.317 183.78 241.438L169.697 207.422V207.422C169.165 206.138 168.436 204.684 168.953 203.392L178.477 179.56C179.055 178.112 178.321 176.819 176.765 176.905C166.306 177.488 130.677 183.303 132.896 182.192L169.219 163.993Z" fill="#A26300"/>
                        <path d="M152.463 364.937C152.623 364.496 152.879 364.102 153.222 363.755C156.555 360.376 168.127 361.382 180.711 366.399C194.59 371.931 204.444 380.247 202.721 384.972C200.999 389.698 188.352 389.044 174.473 383.511C160.594 377.978 150.74 369.663 152.463 364.937Z" fill="#A26300"/>
                        <path d="M186.874 311.353C222.017 312.318 232.557 328.953 233.434 337.149L329.824 187.059C326.368 168.032 296.079 163.904 281.366 164.219L186.874 311.353Z" fill="#A26300"/>
                        <path d="M180.711 366.399C194.59 371.931 204.444 380.247 202.721 384.972L233.434 337.149C232.557 328.953 222.017 312.318 186.874 311.353L153.222 363.755C156.555 360.376 168.127 361.382 180.711 366.399Z" fill="#A26300"/>
                        <path d="M334.935 126.253C321.056 120.721 308.409 120.066 306.687 124.792L281.366 164.219C296.079 163.904 326.368 168.032 329.824 187.059L356.186 146.01L356.945 144.827C358.668 140.102 348.814 131.786 334.935 126.253Z" fill="#A26300"/>
                        <path d="M300.895 307.46L315.455 272.289C316.647 269.41 321.073 269.421 322.247 272.306L336.084 306.297C336.39 307.047 336.977 307.671 337.74 308.056L372.631 325.664C375.146 326.933 375.146 330.261 372.631 331.53L337.74 349.138C336.977 349.522 336.39 350.147 336.084 350.897L322.247 384.887C321.073 387.773 316.647 387.784 315.455 384.905L301.373 350.89C301.064 350.144 300.476 349.525 299.715 349.143L264.571 331.535C262.043 330.268 262.043 326.925 264.571 325.659L300.895 307.46Z" fill="#D59D00"/>
                        <path d="M164.219 158.993L178.78 123.822C179.972 120.943 184.398 120.955 185.572 123.84L199.409 157.831C199.714 158.581 200.302 159.205 201.064 159.59L235.956 177.197C238.471 178.466 238.471 181.794 235.956 183.063L201.064 200.671C200.302 201.056 199.714 201.68 199.409 202.43L185.572 236.421C184.398 239.306 179.972 239.317 178.78 236.438L164.697 202.423C164.389 201.677 163.801 201.058 163.04 200.676L127.896 183.068C125.368 181.802 125.368 178.459 127.896 177.192L164.219 158.993Z" fill="#D59D00"/>
                        <path d="M147.463 359.937C147.623 359.496 147.879 359.102 148.222 358.755C151.555 355.376 163.127 356.382 175.711 361.399C189.59 366.931 199.444 375.247 197.721 379.972C195.999 384.698 183.352 384.044 169.473 378.511C155.594 372.978 145.74 364.663 147.463 359.937Z" fill="#D59D00"/>
                        <path d="M181.874 306.353C217.017 307.318 227.557 323.953 228.434 332.149L324.824 182.059C321.368 163.032 291.079 158.904 276.366 159.219L181.874 306.353Z" fill="#D59D00"/>
                        <path d="M175.711 361.399C189.59 366.931 199.444 375.247 197.721 379.972L228.434 332.149C227.557 323.953 217.017 307.318 181.874 306.353L148.222 358.755C151.555 355.376 163.127 356.382 175.711 361.399Z" fill="#D59D00"/>
                        <path d="M329.935 121.253C316.056 115.721 303.409 115.066 301.687 119.792L276.366 159.219C291.079 158.904 321.368 163.032 324.824 182.059L351.186 141.01L351.945 139.827C353.668 135.102 343.814 126.786 329.935 121.253Z" fill="#D59D00"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_7246_32">
                        <rect width="400" height="400" rx="200" transform="matrix(1 0 0 -1 50 450)" fill="white"/>
                        </clipPath>
                        </defs>
                    </svg>
                    <p class="text-sm font-semibold">{{ totalRemainingCredits }} </p>
                </div>
            </div>
        </div>
    </div>
</div>
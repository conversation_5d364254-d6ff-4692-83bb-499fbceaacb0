import { Subscription } from 'rxjs';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AdminService } from '@app/data/services/admin.service';
import { SnotifyService } from 'ng-alt-snotify';
import { CreditUpdateService } from '@app/data/services/credit-update.service';

@Component({
  selector: 'app-user-detail',
  templateUrl: './user-detail.component.html',
  styleUrls: ['./user-detail.component.scss']
})
export class UserDetailComponent implements OnInit {
  user: any;
  showSendButton: boolean = false;
  userCredits: any[] = []; // Kullanıcının kredi kayıtlarını tutar
  totalRemainingCredits: number = 0; // Toplam remaining_credit
  private creditUpdateSubscription: Subscription;

  constructor(
    @Inject(DIALOG_DATA) public user_detail: any,
    private dialogRef: DialogRef<any>,
    private snotifyService: SnotifyService,
    private adminService: AdminService,
    private router: Router,
    private cu: CreditUpdateService
  ) {
    // Gelen data içerisinden kullanıcı bilgilerini alıyoruz.
    this.user = user_detail.user;
    // Eğer unique_roles ayrı bir alanda gelmiyorsa, data içerisinden alabilirsiniz.
    this.user['unique_roles'] = user_detail.unique_roles;
  }

  ngOnInit(): void {
    this.fetchUserCredits();
    // Abone ol; kredi gönderimi sonrası bildirimi alıp verileri yenileyelim.
    this.creditUpdateSubscription = this.cu.creditUpdated$.subscribe(() => {
      this.fetchUserCredits();
    });
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  switchUser() {
    this.adminService.switchUser(this.user.email).subscribe({
      next: (response) => {
        this.snotifyService.success('Kullanıcı değiştirildi');
        this.dialogRef.close();

        // ProjectStateService'i temizlemek için AdminService'teki clearProjectState metodu kullanılıyor
        // Ancak yine de localStorage'daki currentPage değerini kontrol edelim
        if (localStorage.getItem('currentPage')) {
          localStorage.removeItem('currentPage');
        }

        // Projeler sayfasına yönlendir
        this.router.navigate(['/projects']);
      },
      error: (error) => {
        this.snotifyService.error('Kullanıcı değiştirilemedi');
      }
    });
  }

  banUser() {
    this.adminService.banUser(this.user.id).subscribe({
      next: () => {
        this.user.banned = true;
        this.snotifyService.success('Kullanıcı banlandı');
      },
      error: () => {
        this.snotifyService.error('Kullanıcı banlanamadı');
      }
    });
  }

  unbanUser() {
    this.adminService.unbanUser(this.user.id).subscribe({
      next: () => {
        this.user.banned = false;
        this.snotifyService.success('Kullanıcı banı kaldırıldı');
      },
      error: () => {
        this.snotifyService.error('Kullanıcı banı kaldırılamadı');
      }
    });
  }

  sendCredit() {
    this.showSendButton = true;
  }

  // Kullanıcının kredi bilgilerini sunucudan çeker ve toplam remaining_credit'i hesaplar.
  fetchUserCredits(): void {
    this.adminService.getUserCredits(this.user.id).subscribe({
      next: (response: any) => {
        // Yanıt yapısına göre; örneğin, response.data varsa onu kullanıyoruz.
        this.userCredits = response.data ? response.data : response;
        this.totalRemainingCredits = this.userCredits.reduce((sum: number, credit: any) => {
          return sum + (Number(credit.remaining_credit) || 0);
        }, 0);
      },
      error: (error) => {
        console.error('Kullanıcı kredi bilgileri alınamadı:', error);
        this.snotifyService.error('Kullanıcı kredi bilgileri alınamadı.');
      }
    });
  }

  // Child (admin-gifting) component'den kredi gönderimi tamamlandığında çağrılacak metod.
  onCreditSent(): void {
    // Kredi gönderimi sonrasında, güncel kredi bilgilerini tekrar yükleyelim.
    this.fetchUserCredits();
    // Gönderim panelini kapatalım.
    this.showSendButton = false;
  }
}

<div class="flex flex-col h-full grow bg-blue-50 rounded-3xl">
    <!-- Summary Section -->
    <div class="grid grid-cols-6 gap-3 p-3">
        <!-- Total Users -->
        <div class="flex items-center justify-between col-span-2 p-3 bg-white shadow-sm rounded-xl">
            <div class="flex items-center gap-2">
                <div class="flex items-center p-2 bg-blue-100 rounded-lg">
                    <ng-icon name="featherUser" class="text-lg text-blue-600"></ng-icon>
                </div>
                <div class="flex flex-col">
                    <span class="text-sm font-medium">Toplam Kullanıcı</span>
                    <span class="text-lg font-semibold">{{users.length}}</span>
                </div>
            </div>
            <span class="text-xs" [ngClass]="getTodayCreatedUsersCount() > 0 ? 'text-green-700' : 'text-yellow-500'">
                {{getTodayCreatedUsersCount() > 0 ? '+' + getTodayCreatedUsersCount() : getTodayCreatedUsersCount()}} bugün
            </span>
        </div>
    
        <!-- toplam hediye edilen kredi -->
        <div class="flex items-center justify-between col-span-2 p-3 bg-white shadow-sm rounded-xl">
            <div class="flex items-center gap-2">
                <div class="flex items-center p-2 bg-yellow-100 rounded-lg">
                    <ng-icon name="lucideGift" class="text-lg text-yellow-600"></ng-icon>
                </div>
                <div class="flex flex-col">
                    <span class="text-sm font-medium">Hediye ettiğin toplam kredi</span>
                    <span class="text-lg font-semibold">{{ totalGiftCredits }}</span>
                </div>
            </div>
        </div>
    </div>
    
    

    <!-- Filter and Search Section -->
    <div class="grid grid-cols-3 gap-4 mx-4 mb-4">
        <!-- Search Section -->
        <div class="col-span-1">
            <div class="flex items-center justify-center w-full h-full gap-2 p-2 bg-white rounded-3xl">
                <ng-icon name="tablerSearch" class="text-xl text-gray-400"></ng-icon>
                <input type="text" 
                       (keyup)="applySearchFilter($event)"
                       class="w-full text-sm bg-transparent border-none outline-none placeholder:text-gray-400"
                       placeholder="ID, kullanıcı adı veya email ile ara">
            </div>
        </div>
        <!-- Filter Section -->
        <div class="relative col-span-2">
            <mat-accordion class="absolute z-[999] w-full">
                <mat-expansion-panel class="shadow-lg">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <div class="flex items-center gap-2">
                                <ng-icon name="tablerFilter" class="text-xl"></ng-icon>
                                Filtrele
                            </div>
                        </mat-panel-title>
                    </mat-expansion-panel-header>

                    <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <!-- Date Range -->
                            <mat-form-field>
                                <mat-label>Başlangıç Tarihi</mat-label>
                                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                                <mat-datepicker #startPicker></mat-datepicker>
                            </mat-form-field>

                            <mat-form-field>
                                <mat-label>Bitiş Tarihi</mat-label>
                                <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                                <mat-datepicker #endPicker></mat-datepicker>
                            </mat-form-field>
                        </div>

                        <div class="flex justify-end gap-2 mt-4">
                            <button mat-button type="button" (click)="resetFilter()">Sıfırla</button>
                            <button mat-raised-button color="primary" type="submit">Filtrele</button>
                        </div>
                    </form>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>

    <div class="table-section">
        <div class="overflow-auto table-container" style="max-height: calc(100vh - 250px)">
            <mat-table [dataSource]="dataSource" matSort>
                <!-- ID Column -->
                <ng-container matColumnDef="id">
                    <mat-header-cell *matHeaderCellDef mat-sort-header> ID </mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{row.id}} </mat-cell>
                </ng-container>

                <!-- Name Column -->
                <ng-container matColumnDef="name">
                    <mat-header-cell *matHeaderCellDef mat-sort-header> Kullanıcı Adı </mat-header-cell>
                    <mat-cell *matCellDef="let row">
                        <div class="flex flex-col">
                            <span class="truncate" [title]="row.full_name">{{row.full_name}}</span>
                            <span *ngIf="row.banned" class="text-xs font-medium text-red-600">Yasaklı Kullanıcı</span>
                        </div>
                    </mat-cell>
                </ng-container>

                <!-- Email Column -->
                <ng-container matColumnDef="email">
                    <mat-header-cell *matHeaderCellDef> E-posta </mat-header-cell>
                    <mat-cell *matCellDef="let row">{{row.email}}</mat-cell>
                </ng-container>

                <!-- Phone Column -->
                <ng-container matColumnDef="phone">
                    <mat-header-cell *matHeaderCellDef> Tel No </mat-header-cell>
                    <mat-cell *matCellDef="let row">{{row.phone_number}}</mat-cell>
                </ng-container>

                <!-- Analyzer Role Column -->
                <ng-container matColumnDef="analyzerRole">
                    <mat-header-cell *matHeaderCellDef> Proje </mat-header-cell>
                    <mat-cell *matCellDef="let row">
                        <div class="flex flex-col">
                            <span *ngIf="getAnalyzerRoleCount(row.roles) > 0" class="text-sm font-medium text-blue-600">
                                {{getAnalyzerRoleCount(row.roles)}} Proje Mevcut
                            </span>
                        </div>
                    </mat-cell>
                </ng-container>

                <!-- Date Column -->
                <ng-container matColumnDef="date">
                    <mat-header-cell *matHeaderCellDef mat-sort-header> Tarih </mat-header-cell>
                    <mat-cell *matCellDef="let row">
                        {{row.created_at | date:'dd MMMM yyyy HH:mm':'':'tr-TR'}}
                    </mat-cell>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                    <mat-header-cell *matHeaderCellDef class="justify-center"> Detay </mat-header-cell>
                    <mat-cell *matCellDef="let row" class="justify-center">
                        <button type="button" (click)="showUserDetail(row)"
                            class="flex items-center justify-center gap-2 px-3 py-2 text-sm font-normal transition-all bg-white border shadow border-zinc-200 hover:border-blue-600 text-blue-950 active:scale-95 group rounded-3xl hover:border-3">
                            <ng-icon name="matInfoOutline" class="text-xl text-blue-700"></ng-icon>
                            Görüntüle
                        </button>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;" class="hover:bg-slate-50"></mat-row>
            </mat-table>
        </div>
        <mat-paginator [pageSizeOptions]="[10, 25, 50]" showFirstLastButtons></mat-paginator>
    </div>
</div>
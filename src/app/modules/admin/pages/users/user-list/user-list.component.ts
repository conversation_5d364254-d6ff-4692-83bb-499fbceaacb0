import { Subscription } from 'rxjs';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';
import { UserDetailComponent } from '../dialogs/user-detail/user-detail.component';
import { Dialog } from '@angular/cdk/dialog';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { AdminService } from '@app/data/services/admin.service';
import { CreditUpdateService } from '@app/data/services/credit-update.service';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {
  constructor(
    private adminService: AdminHelperService,
    private dialog: Dialog,
    private fb: FormBuilder,
    private as: AdminService,
    private cu: CreditUpdateService
  ) {
    this.filterForm = this.fb.group({
      startDate: [null],
      endDate: [null],
      searchTerm: ['']
    });
  }

  private creditUpdateSubscription: Subscription;

  ngOnInit(): void {
    this.getUsers();
    this.setupSearchFilter();
    this.fetchTotalGiftCredits();
    this.creditUpdateSubscription = this.cu.creditUpdated$.subscribe(() => {
      this.fetchTotalGiftCredits();
    });
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  getUsers() {
    this.adminService.getUsers().subscribe((response) => {
      this.users = response;
      this.tmpUsers = response;
      this.dataSource = new MatTableDataSource(response);
      
      // sortingDataAccessor tanımlama
      this.dataSource.sortingDataAccessor = (item: any, property: string) => {
        switch (property) {
          case 'id':
            return Number(item.id);
          case 'name':
            return item.full_name.toLowerCase();
          case 'email':
            return item.email.toLowerCase();
          case 'date':
            return new Date(item.created_at).getTime();
          default:
            return item[property];
        }
      };

      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      
      // Varsayılan sıralama
      setTimeout(() => {
        this.sort.sort({
          id: 'date',
          start: 'desc',
          disableClear: false
        });
      });
    });
  }

  showUserDetail(user: any) {

    const dialog = this.dialog.open(UserDetailComponent, {
      data: {user, unique_roles: this.getRoles(user.roles)},
    });
  }
  users = []
  tmpUsers = []
  searchTerm: string = '';
  dataSource: MatTableDataSource<any> = new MatTableDataSource();
  filterForm: FormGroup;
  
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  applySearchFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLocaleLowerCase('tr-TR');
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  setupSearchFilter() {
    this.dataSource.filterPredicate = (data: any, filter: string) => {
      const searchStr = filter.toLocaleLowerCase('tr-TR');
      return data.id.toString().includes(searchStr) ||
             data.full_name.toLocaleLowerCase('tr-TR').includes(searchStr) ||
             data.email.toLocaleLowerCase('tr-TR').includes(searchStr) ||
             (data.phone_number && data.phone_number.toLocaleLowerCase('tr-TR').includes(searchStr));
    };
  }

  applyFilters(): void {
    const filters = this.filterForm.value;
    
    this.users = this.tmpUsers.filter(user => {
      // Tarih kontrolü
      const startDate = filters.startDate ? new Date(filters.startDate) : null;
      const endDate = filters.endDate ? new Date(filters.endDate) : null;
      
      if (endDate) {
        endDate.setHours(23, 59, 59, 999);
      }
      
      const userDate = new Date(user.created_at);
      const dateMatches = (!startDate || userDate >= startDate) && 
                         (!endDate || userDate <= endDate);

      return dateMatches;
    });

    this.dataSource.data = this.users;

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.users = [...this.tmpUsers];
    this.dataSource.data = this.users;
    
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  getRoles(roles: any) {
    const unique_roles: any[] = [];
    const seen = new Set();
    // Döngü ile `value_labels`'ları kontrol ediyoruz
    for (const role of roles) {
      // Eğer daha önce eklenmemişse
      const stringifiedValue = JSON.stringify(role.name);
      if (!seen.has(stringifiedValue)) {
        // Tekrarını önlemek için `Set` içine ekle
        seen.add(stringifiedValue);
        // `unique_labels`'a ekle
        unique_roles.push(role.name);
      }
    }
    return unique_roles
  }

  getTodayCreatedUsersCount(): number {
    const today = new Date();
    return this.users.filter(user => {
      const userDate = new Date(user.created_at);
      return userDate.getFullYear() === today.getFullYear() &&
             userDate.getMonth() === today.getMonth() &&
             userDate.getDate() === today.getDate();
    }).length;
  }
  displayedColumns: string[] = ['id', 'name', 'email', 'phone', 'date', 'analyzerRole', 'actions'];

  getAnalyzerRoleCount(roles: any[]): number {
    if (!roles) return 0;
    return roles.filter(role => role.name === 'analyzer').length;
  }
  
  totalGiftCredits: number = 0;
  userId: number;

  fetchTotalGiftCredits(): void {
    this.as.getAllCreditGifts(this.userId).subscribe({
      next: (response) => {
        const gifts = Array.isArray(response) ? response : response.data;
        this.totalGiftCredits = gifts.reduce((sum: number, gift: any) => {
          return sum + Number(gift.amount);
        }, 0);
      },
    });
  }
  
}

<div class="px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="mb-2 text-3xl font-bold text-gray-900">Bibliometric Analysis</h1>
        <p class="text-lg text-gray-600">
            Upload your Web of Science data file to generate comprehensive bibliometric insights and visualizations.
        </p>
    </div>

    <!-- Progress Steps -->
    <div class="flex items-center justify-center mb-8" *ngIf="isUploading || isAnalyzing || analysisData">
        <div class="flex items-center space-x-4">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-10 h-10 transition-colors duration-200 border-2 rounded-full"
                    [ngClass]="{
               'bg-blue-600 border-blue-600 text-white': currentStep === 'upload' || isUploading,
               'bg-green-600 border-green-600 text-white': !isUploading && (isAnalyzing || analysisData),
               'bg-gray-200 border-gray-300 text-gray-500': !isUploading && !isAnalyzing && !analysisData
             }">
                    <span class="text-sm font-medium">1</span>
                </div>
                <span class="ml-2 text-sm font-medium text-gray-700">Data Upload</span>
            </div>

            <div class="w-16 h-0.5 transition-colors duration-200" [ngClass]="{
             'bg-green-600': !isUploading && (isAnalyzing || analysisData),
             'bg-gray-300': isUploading || (!isAnalyzing && !analysisData)
           }"></div>

            <div class="flex items-center">
                <div class="flex items-center justify-center w-10 h-10 transition-colors duration-200 border-2 rounded-full"
                    [ngClass]="{
               'bg-blue-600 border-blue-600 text-white': currentStep === 'analysis' || isAnalyzing,
               'bg-green-600 border-green-600 text-white': analysisData,
               'bg-gray-200 border-gray-300 text-gray-500': !isAnalyzing && !analysisData
             }">
                    <span class="text-sm font-medium">2</span>
                </div>
                <span class="ml-2 text-sm font-medium text-gray-700">Configuration</span>
            </div>

            <div class="w-16 h-0.5 transition-colors duration-200" [ngClass]="{
             'bg-green-600': analysisData,
             'bg-gray-300': !analysisData
           }"></div>

            <div class="flex items-center">
                <div class="flex items-center justify-center w-10 h-10 transition-colors duration-200 border-2 rounded-full"
                    [ngClass]="{
               'bg-green-600 border-green-600 text-white': analysisData,
               'bg-gray-200 border-gray-300 text-gray-500': !analysisData
             }">
                    <span class="text-sm font-medium">3</span>
                </div>
                <span class="ml-2 text-sm font-medium text-gray-700">Analysis & Reporting</span>
            </div>
        </div>
    </div>

    <!-- Upload Section -->
    <div class="mb-8" *ngIf="!analysisData">
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
            <!-- File Upload Area -->
            <div class="p-8 text-center transition-colors duration-200 border-2 border-dashed rounded-lg" [ngClass]="{
             'border-blue-300 bg-blue-50': !selectedFile && !isUploading && !isAnalyzing,
             'border-green-300 bg-green-50': selectedFile && !isUploading && !isAnalyzing,
             'border-gray-300 bg-gray-50 opacity-50': isUploading || isAnalyzing
           }" (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onDrop($event)">

                <!-- Upload Icon and Text -->
                <div *ngIf="!selectedFile" class="space-y-4">
                    <div class="flex justify-center">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="mb-2 text-lg font-medium text-gray-900">Upload Web of Science Data</h3>
                        <p class="mb-1 text-gray-600">Drop your WoS data file here or click to browse</p>
                        <p class="text-sm text-gray-500">Supported formats: .txt files exported from Web of Science</p>
                        <p class="text-sm text-gray-500">Maximum file size: 50MB</p>
                    </div>
                </div>

                <!-- Selected File Info -->
                <div *ngIf="selectedFile && fileInfo" class="flex items-center justify-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">{{ fileInfo.name }}</p>
                            <p class="text-sm text-gray-500">{{ fileInfo.size }}</p>
                        </div>
                        <button (click)="resetAnalysis()" [disabled]="isUploading || isAnalyzing"
                            class="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Hidden File Input -->
                <input type="file" #fileInput accept=".txt" (change)="onFileSelected($event)"
                    [disabled]="isUploading || isAnalyzing" class="hidden">
            </div>

            <!-- Browse Button -->
            <div class="flex justify-center mt-4" *ngIf="!selectedFile">
                <button (click)="fileInput.click()" [disabled]="isUploading || isAnalyzing"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                        </path>
                    </svg>
                    Browse Files
                </button>
            </div>

            <!-- Upload Progress -->
            <div class="mt-6" *ngIf="isUploading">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Uploading file...</span>
                    <span class="text-sm text-gray-500">{{ uploadProgress }}%</span>
                </div>
                <div class="w-full h-2 bg-gray-200 rounded-full">
                    <div class="h-2 transition-all duration-300 bg-blue-600 rounded-full"
                        [style.width.%]="uploadProgress"></div>
                </div>
                <p class="mt-2 text-sm text-gray-600">{{ currentMessage }}</p>
            </div>

            <!-- Analysis Progress -->
            <div class="mt-6" *ngIf="isAnalyzing">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Analyzing bibliometric data...</span>
                    <span class="text-sm text-gray-500">{{ analysisProgress }}%</span>
                </div>
                <div class="w-full h-2 bg-gray-200 rounded-full">
                    <div class="h-2 transition-all duration-300 bg-green-600 rounded-full"
                        [style.width.%]="analysisProgress"></div>
                </div>
                <p class="mt-2 text-sm text-gray-600">{{ currentMessage }}</p>
            </div>

            <!-- Start Analysis Button -->
            <div class="flex justify-center mt-6 space-x-3" *ngIf="selectedFile && !isUploading && !isAnalyzing">
                <button (click)="uploadAndAnalyze()"
                    class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Start Analysis
                </button>
                <button (click)="resetAnalysis()"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Cancel
                </button>
            </div>

            <!-- Error Message -->
            <div class="p-4 mt-4 border border-red-200 rounded-md bg-red-50" *ngIf="error">
                <div class="flex">
                    <svg class="w-5 h-5 mr-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-sm text-red-700">{{ error }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div *ngIf="analysisData">
        <!-- Results Header -->
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold text-gray-900">Analysis Results</h2>
            <button (click)="resetAnalysis()"
                class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                    </path>
                </svg>
                New Analysis
            </button>
        </div>

        <!-- Tab Navigation -->
        <div class="mb-6 border-b border-gray-200">
            <nav class="flex space-x-8">
                <button *ngFor="let tab of ['overview', 'authors', 'citations', 'countries', 'sources', 'keywords']"
                    (click)="setActiveTab(tab)" [ngClass]="{
                  'border-blue-500 text-blue-600': activeTab === tab,
                  'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== tab
                }" class="px-1 py-2 text-sm font-medium capitalize border-b-2 whitespace-nowrap">
                    {{ tab }}
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
            <!-- Overview Tab -->
            <div class="p-6" *ngIf="activeTab === 'overview'">
                <div class="grid grid-cols-1 gap-4 mb-8 md:grid-cols-2 lg:grid-cols-3">
                    <div class="p-4 rounded-lg bg-gray-50" *ngFor="let key of getMainInfoKeys()">
                        <div class="mb-1 text-sm font-medium text-gray-500">{{ key }}</div>
                        <div class="text-lg font-semibold text-gray-900">{{ getMainInfoValue(key) }}</div>
                    </div>
                </div>

                <!-- Annual Production -->
                <div *ngIf="analysisData.AnnualProduction?.length" class="pt-6 border-t border-gray-200">
                    <h3 class="mb-4 text-lg font-medium text-gray-900">Annual Scientific Production</h3>
                    <div class="grid grid-cols-2 gap-3 md:grid-cols-4 lg:grid-cols-7">
                        <div class="p-3 text-center rounded-lg bg-blue-50"
                            *ngFor="let item of analysisData.AnnualProduction">
                            <div class="text-sm font-medium text-blue-600">{{ item.year }}</div>
                            <div class="text-lg font-semibold text-gray-900">{{ item.articles }}</div>
                            <div class="text-xs text-gray-500">articles</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Authors Tab -->
            <div class="p-6" *ngIf="activeTab === 'authors'">
                <h3 class="mb-4 text-lg font-medium text-gray-900">Most Productive Authors</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Author</th>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Articles</th>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Fractionalized</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr *ngFor="let author of analysisData.MostProdAuthors">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">{{
                                    author.author }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{ author.articles }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                    author.articles_fractionalized }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Citations Tab -->
            <div class="p-6" *ngIf="activeTab === 'citations'">
                <h3 class="mb-4 text-lg font-medium text-gray-900">Most Cited Papers</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Paper</th>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    DOI</th>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Total Citations</th>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Citations/Year</th>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Normalized TC</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr *ngFor="let paper of analysisData.MostCitedPapers">
                                <td class="max-w-xs px-6 py-4 text-sm text-gray-900 truncate">{{ paper.paper }}</td>
                                <td class="px-6 py-4 text-sm whitespace-nowrap">
                                    <a *ngIf="paper.doi" [href]="'https://doi.org/' + paper.doi" target="_blank"
                                        class="block max-w-xs text-blue-600 truncate hover:text-blue-500">
                                        {{ paper.doi }}
                                    </a>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{ paper.total_citations
                                    }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                    paper.citations_per_year | number:'1.2-2' }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{ paper.normalized_tc |
                                    number:'1.2-2' }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Countries Tab -->
            <div class="p-6" *ngIf="activeTab === 'countries'">
                <div class="space-y-8">
                    <div>
                        <h3 class="mb-4 text-lg font-medium text-gray-900">Most Productive Countries</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Country</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Articles</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Frequency</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            SCP</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            MCP</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            MCP Ratio</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr *ngFor="let country of analysisData.MostProdCountries">
                                        <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">{{
                                            country.country }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            country.articles }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            country.frequency | number:'1.3-3' }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            country.single_country_publications }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            country.multiple_country_publications }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            country.mcp_ratio | number:'1.3-3' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div>
                        <h3 class="mb-4 text-lg font-medium text-gray-900">Total Citations per Country</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Country</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Total Citations</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Average Citations</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr *ngFor="let country of analysisData.TCperCountries">
                                        <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">{{
                                            country.country }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            country.total_citations }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            country.average_article_citations | number:'1.1-1' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sources Tab -->
            <div class="p-6" *ngIf="activeTab === 'sources'">
                <h3 class="mb-4 text-lg font-medium text-gray-900">Most Relevant Sources</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Source</th>
                                <th
                                    class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                    Articles</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr *ngFor="let source of analysisData.MostRelSources">
                                <td class="px-6 py-4 text-sm text-gray-900">{{ source.source }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{ source.articles }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Keywords Tab -->
            <div class="p-6" *ngIf="activeTab === 'keywords'">
                <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
                    <div>
                        <h3 class="mb-4 text-lg font-medium text-gray-900">Author Keywords</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Keyword</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Articles</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr *ngFor="let keyword of analysisData.MostRelKeywords.author_keywords">
                                        <td class="px-6 py-4 text-sm text-gray-900">{{ keyword.keyword }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            keyword.articles }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div>
                        <h3 class="mb-4 text-lg font-medium text-gray-900">Keywords Plus</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Keyword</th>
                                        <th
                                            class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                                            Articles</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr *ngFor="let keyword of analysisData.MostRelKeywords.keywords_plus">
                                        <td class="px-6 py-4 text-sm text-gray-900">{{ keyword.keyword }}</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{{
                                            keyword.articles }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
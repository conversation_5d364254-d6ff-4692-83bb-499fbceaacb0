// Minimal custom styles for bibliometric component
// Most styling is handled by Tailwind classes in the template

.bibliometric-component {
  // Custom animations for progress bars
  .progress-bar-animation {
    transition: width 0.3s ease-in-out;
  }

  // Custom table hover effects
  table tbody tr:hover {
    @apply bg-gray-50;
  }

  // Custom drag and drop styles
  .drag-over {
    @apply border-blue-400 bg-blue-100;
  }

  // Custom scrollbar for tables
  .table-container::-webkit-scrollbar {
    height: 6px;
  }

  .table-container::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }

  .table-container::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded-full;
  }

  .table-container::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}

// Animation for step transitions
@keyframes pulse-green {
  0%,
  100% {
    @apply bg-green-500;
  }
  50% {
    @apply bg-green-600;
  }
}

.step-completed {
  animation: pulse-green 1s ease-in-out;
}

// Loading spinner for analysis
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '@env/environment';
import { BibliometricService } from '../../services/bibliometric.service';

// Bibliometric analysis data interfaces
interface AnnualProduction {
  year: number;
  articles: number;
}

interface MostCitedPaper {
  paper: string;
  doi?: string;
  total_citations: number;
  citations_per_year: number;
}

interface CountryData {
  country: string;
  articles: number;
  frequency: number;
  single_country_publications: number;
  multiple_country_publications: number;
  mcp_ratio: number;
}

interface CountryCitations {
  country: string;
  total_citations: number;
  average_article_citations: number;
}

interface SourceData {
  source: string;
  articles: number;
}

interface KeywordData {
  keyword: string;
  articles: number;
}

interface BibliometricAnalysisData {
  MainInformation: { [key: string]: string | number };
  AnnualProduction?: AnnualProduction[];
  MostCitedPapers?: MostCitedPaper[];
  MostProdCountries?: CountryData[];
  TCperCountries?: CountryCitations[];
  MostRelSources?: SourceData[];
  MostRelKeywords?: {
    keywords_plus: KeywordData[];
  };
}

@Component({
  selector: 'app-bibliometric',
  templateUrl: './bibliometric.component.html',
  styleUrls: ['./bibliometric.component.scss']
})
export class BibliometricComponent implements OnInit {

  // Upload states
  isUploading = false;
  uploadProgress = 0;
  selectedFile: File | null = null;
  s3Url: string | null = null;

  // Analysis states
  isAnalyzing = false;
  analysisProgress = 0;

  // Data states
  analysisData: BibliometricAnalysisData | null = null;
  error: string | null = null;

  // Display states
  activeTab = 'overview';

  constructor(
    private bibliometricService: BibliometricService
  ) {}

  ngOnInit(): void {}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file && file.name.endsWith('.txt')) {
      this.selectedFile = file;
      this.error = null;
    } else {
      this.error = 'Please select a valid .txt file exported from Web of Science';
      this.selectedFile = null;
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file.name.endsWith('.txt')) {
        this.selectedFile = file;
        this.error = null;
      } else {
        this.error = 'Please select a valid .txt file exported from Web of Science';
      }
    }
  }

  async uploadAndAnalyze(): Promise<void> {
    if (!this.selectedFile) {
      this.error = 'Please select a file first';
      return;
    }

    try {
      this.isUploading = true;
      this.uploadProgress = 0;
      this.error = null;
      this.analysisData = null;

      // Use BibliometricService to handle the entire process
      this.bibliometricService.uploadAndAnalyze(this.selectedFile).subscribe({
        next: (result) => {
          if (result.step === 'upload') {
            // Update upload progress
            this.uploadProgress = result.progress;
          } else if (result.step === 'upload_complete') {
            // Upload complete, analysis starting
            this.isUploading = false;
            this.isAnalyzing = true;
            this.analysisProgress = 0;
          } else if (result.step === 'analysis_complete') {
            // Analysis complete
            this.isAnalyzing = false;
            this.analysisProgress = 100;
            this.analysisData = result.data;
            this.s3Url = result.s3_url;
            this.activeTab = 'overview';
          }
        },
        error: (error) => {
          console.error('Analysis failed:', error);
          this.error = error.error?.error || 'Analysis failed. Please try again.';
          this.isUploading = false;
          this.isAnalyzing = false;
        }
      });
    } catch (error) {
      console.error('Analysis failed:', error);
      this.error = error instanceof Error ? error.message : 'Analysis failed. Please try again.';
      this.isUploading = false;
      this.isAnalyzing = false;
    }
  }

  resetAnalysis(): void {
    this.selectedFile = null;
    this.analysisData = null;
    this.error = null;
    this.isUploading = false;
    this.isAnalyzing = false;
    this.uploadProgress = 0;
    this.analysisProgress = 0;
    this.activeTab = 'overview';
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  getMainInfoKeys(): string[] {
    return this.analysisData ? Object.keys(this.analysisData.MainInformation) : [];
  }

  getMainInfoValue(key: string): string { 
    return this.analysisData ? (this.analysisData.MainInformation as any)[key] : '';
  }
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

// AWS SDK import (npm install aws-sdk gerekli)
declare const AWS: any;

interface S3UploadResult {
  success: boolean;
  url: string;
  key: string;
}

interface ApiResponse {
  payload: {
    preliminary: any;
    charts: any;
  };
  methods: {
    normality: any[];
    test: string[];
    sub: any[];
    warnings: any[];
  };
}

@Injectable({
  providedIn: 'root'
})
export class BibliometricService {

  private readonly BIBLIOMETRIC_API_ENDPOINT = 'https://r.sadeapp.com/v1/bibliometric';

  // AWS S3 Configuration
  private s3: any;
  private readonly S3_BUCKET = 'your-wos-bucket'; // Kendi bucket adın
  private readonly S3_REGION = 'eu-central-1'; // Bucket region'ın

  constructor(private http: HttpClient) {
    this.initializeS3();
  }

  private initializeS3(): void {
    // AWS SDK configuration
    AWS.config.update({
      accessKeyId: 'YOUR_ACCESS_KEY_ID', // AWS credentials
      secretAccessKey: 'YOUR_SECRET_ACCESS_KEY',
      region: this.S3_REGION
    });

    this.s3 = new AWS.S3();
  }

  /**
   * Step 1: Upload WoS file directly to S3
   */
  uploadWoSFileToS3(file: File): Observable<any> {
    return new Observable(observer => {
      if (!this.isValidWoSFile(file)) {
        observer.error({
          step: 'validation',
          error: 'Invalid file format. Please upload a .txt file exported from Web of Science.'
        });
        return;
      }

      const fileName = this.generateFileName(file.name);
      const s3Key = `wos-files/${fileName}`;
      const s3Url = `https://${this.S3_BUCKET}.s3.${this.S3_REGION}.amazonaws.com/${s3Key}`;

      const uploadParams = {
        Bucket: this.S3_BUCKET,
        Key: s3Key,
        Body: file,
        ContentType: 'text/plain',
        ACL: 'public-read' // R service'in erişebilmesi için
      };

      const upload = this.s3.upload(uploadParams);

      // Progress tracking
      upload.on('httpUploadProgress', (progress: any) => {
        const percentCompleted = Math.round((progress.loaded / progress.total) * 100);
        observer.next({
          step: 'upload',
          progress: percentCompleted,
          message: `Uploading to S3... ${percentCompleted}%`
        });
      });

      upload.send((err: any, data: any) => {
        if (err) {
          observer.error({
            step: 'upload',
            error: `S3 upload failed: ${err.message}`
          });
        } else {
          observer.next({
            step: 'upload_complete',
            progress: 100,
            message: 'File uploaded to S3 successfully',
            s3_url: s3Url,
            fileName: fileName
          });
          observer.complete();
        }
      });
    });
  }

  /**
   * Generate unique filename
   */
  private generateFileName(originalName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop();
    return `${timestamp}-${randomString}.${extension}`;
  }

  /**
   * Step 2: Call R service with S3 URL
   */
  callRServiceWithS3Url(s3Url: string): Observable<ApiResponse> {
    const apiUrl = `${this.BIBLIOMETRIC_API_ENDPOINT}?s3_url=${encodeURIComponent(s3Url)}`;

    return this.http.get<ApiResponse>(apiUrl).pipe(
      catchError(error => {
        console.error('R service call failed:', error);
        let errorMessage = 'Analysis failed. Please try again.';

        if (error.status === 400) {
          errorMessage = 'Invalid file format or corrupted data.';
        } else if (error.status === 500) {
          errorMessage = 'Server error during analysis. Please try again later.';
        } else if (error.status === 0) {
          errorMessage = 'Network error. Please check your connection.';
        }

        return throwError(() => new Error(errorMessage));
      })
    );
  }

  /**
   * Complete workflow: Upload WoS → S3 → R endpoint (NO BACKEND)
   */
  uploadAndAnalyze(file: File): Observable<any> {
    return new Observable(observer => {
      let s3Url: string;
      let fileName: string;

      // Step 1: Upload directly to S3
      this.uploadWoSFileToS3(file).subscribe({
        next: (result) => {
          if (result.step === 'upload') {
            // Upload progress
            observer.next(result);
          } else if (result.step === 'upload_complete') {
            // Upload complete, start analysis
            s3Url = result.s3_url;
            fileName = result.fileName;

            observer.next({
              step: 'upload_complete',
              progress: 100,
              message: 'File uploaded to S3. Starting analysis...'
            });

            // Step 2: Call R service with S3 URL
            this.callRServiceWithS3Url(s3Url).subscribe({
              next: (analysisResult) => {
                observer.next({
                  step: 'analysis_complete',
                  progress: 100,
                  message: 'Analysis completed successfully!',
                  data: analysisResult.payload.preliminary,
                  s3_url: s3Url,
                  fileName: fileName
                });
                observer.complete();
              },
              error: (error) => {
                observer.error({
                  step: 'analysis',
                  error: error.message,
                  s3_url: s3Url,
                  fileName: fileName
                });
              }
            });
          }
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  /**
   * Validate if file is a valid WoS export
   */
  private isValidWoSFile(file: File): boolean {
    // Check file extension
    if (!file.name.toLowerCase().endsWith('.txt')) {
      return false;
    }

    // Check file size (max 50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return false;
    }

    return true;
  }

  /**
   * Get file info for display
   */
  getFileInfo(file: File): { name: string; size: string; type: string } {
    return {
      name: file.name,
      size: this.formatFileSize(file.size),
      type: file.type || 'text/plain'
    };
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
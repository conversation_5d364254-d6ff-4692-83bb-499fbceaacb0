import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { AuthComponent } from './auth.component';
import { LoginComponent } from './pages/login/login.component';
import { SignupComponent } from './pages/signup/signup.component';
import { VerificationComponent } from './pages/verification/verification.component';
import { VerifiedComponent } from './pages/verified/verified.component';
import { ChangePasswordComponent } from './pages/change-password/change-password.component';
import { MailInputComponent } from './pages/mail-input/mail-input.component';
import { MailSentComponent } from './pages/mail-sent/mail-sent.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path:'login',
    component: LoginComponent
  },
  {
    path:'signup',
    component: SignupComponent
  },
  {
    path:'verification',
    component: VerificationComponent
  },
  {
    path:'verified',
    component: VerifiedComponent
  },
  {
    path:'password/edit',
    component: ChangePasswordComponent
  },
  {
    path: 'password/reset',
    component:MailInputComponent
  },
  {
    path: 'password-info',
    component: MailSentComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuthRoutingModule {}

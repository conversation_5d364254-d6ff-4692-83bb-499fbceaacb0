import { Component } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';
@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
})
export class AuthComponent {
  constructor(
    private transloco: TranslocoService,
  ) {
    const activeLang = localStorage.getItem('activeLang');
    if (activeLang) {
      this.currentLang  = activeLang;
      this.transloco.setActiveLang(activeLang);
    }
  }
  currentLang='tr';
  changeLanguage(lang: string) {
    this.currentLang = lang;
    this.transloco.setActiveLang(lang);
    localStorage.setItem('activeLang', lang);
  }
}

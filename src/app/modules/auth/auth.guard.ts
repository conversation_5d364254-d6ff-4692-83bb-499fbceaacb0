import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
  CanLoad,
  Route,
  UrlSegment,
  ActivatedRoute,
  Params,
} from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanLoad {
  constructor(
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
  ) { }
  referrerController(): boolean {
    const referrer = document.referrer;
    if (referrer.includes('eistatistik.com')) {
      this.activatedRoute.queryParams.subscribe((params: Params) => {
        if (params['access_token']) {
          localStorage.setItem('ACCESS_TOKEN', params['access_token']);
        }
        if (params['refresh_token']) {
          localStorage.setItem('REFRESH_TOKEN', params['refresh_token']);
        }
        if (params[' user_id']) {
          localStorage.setItem('USER_ID', params['user_id']);
        }
        // Refactor
        if (params['expires_in']) {
          localStorage.setItem('EXPIRES_IN', params['expires_in']);
        }
      });
      return true;
    } else {
      return false;
    }
  }

  canLoad(
    route: Route,
    segments: UrlSegment[],
  ):
    | boolean
    | UrlTree
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree> {
    if (this.authService.authenticated || this.referrerController()) {
      return true;
    } else {
      this.router.navigate(['/login']);
      return false;
    }
  }
  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    if (this.authService.authenticated || this.referrerController()) {
      return true;
    } else {
      this.router.navigate(['/login']);
      return false;
    }
  }
}

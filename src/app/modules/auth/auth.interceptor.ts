import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpHeaders,
  HTTP_INTERCEPTORS,
  HttpResponse,
  HttpContextToken,
} from '@angular/common/http';
import { Observable, tap, timeout, catchError } from 'rxjs';
export const BYPASS_LOG = new HttpContextToken(() => false);

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<any> {
    if (req.context.get(BYPASS_LOG) === false) {
      if (localStorage.getItem('token') !== null) {
        const authReq = req.clone({
          headers: req.headers.set(
            'Authorization',
            'Bearer ' + localStorage.getItem('token'),
          ),
          withCredentials: true,
        });
        return next.handle(authReq).pipe(
          timeout(300000), // 5 minutes
          catchError((error) => {
            throw error;
          })
        );
      } else {
        return next.handle(req).pipe(
          timeout(300000),
          catchError((error) => {
            throw error;
          })
        );
      }
    } else {
      return next.handle(req).pipe(
        timeout(300000),
        catchError((error) => {
          throw error;
        })
      );
    }
  }
}
export const AuthInterceptorProvider = {
  provide: HTTP_INTERCEPTORS,
  useClass: AuthInterceptor,
  multi: true,
};

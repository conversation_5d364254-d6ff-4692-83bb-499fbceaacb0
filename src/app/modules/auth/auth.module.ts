import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthComponent } from './auth.component';
import { FormsModule } from '@angular/forms';
import { AuthRoutingModule } from './auth-routing.module';
import { LoginComponent } from './pages/login/login.component';
import { SignupComponent } from './pages/signup/signup.component';
import { VerificationComponent } from './pages/verification/verification.component';
import { ChangePasswordComponent } from './pages/change-password/change-password.component';
import { VerifiedComponent } from './pages/verified/verified.component';
import { SharedModule } from '@app/shared/shared.module';
import { MailInputComponent } from './pages/mail-input/mail-input.component';
import { MailSentComponent } from './pages/mail-sent/mail-sent.component';

@NgModule({
  declarations: [
    LoginComponent,
    SignupComponent,
    VerificationComponent,
    ChangePasswordComponent,
    VerifiedComponent,
    MailInputComponent,
    MailSentComponent
  ],
  imports: [CommonModule, FormsModule, AuthRoutingModule,SharedModule],
  providers: [],
})
export class AuthModule {}

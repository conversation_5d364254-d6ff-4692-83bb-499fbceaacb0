import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { tap, map, catchError } from 'rxjs/operators';
import { environment } from '@env/environment';
import { BYPASS_LOG } from './auth.interceptor';
import { TranslocoService } from '@ngneat/transloco';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  // YENİ: Profile update için BehaviorSubject ekledik
  private profileUpdatedSubject = new BehaviorSubject<boolean>(false);
  public profileUpdated$ = this.profileUpdatedSubject.asObservable();

  // YENİ: User interests değişiklik eventi
  private userInterestsChangedSubject = new BehaviorSubject<string>('');
  public userInterestsChanged$ = this.userInterestsChangedSubject.asObservable();

  constructor(private http: HttpClient,
    private translocoService: TranslocoService,
  ) { }
  private token: string | null = null;

  clearToken() {
    localStorage.removeItem('token');
    localStorage.removeItem('REFRESH_TOKEN');
    localStorage.removeItem('USER_ID');
    localStorage.removeItem('original_user');
  }

  // LOGIN
  login(email: string, password: string, target_email?: string): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    const body = { user: { email, password, } };
    if (target_email != null) {
      body.user['target_email'] = target_email;
    }
    return this.http.post<any>(`${environment.apiUrl}/login`, body, { headers, observe: 'response', context: new HttpContext().set(BYPASS_LOG, true) }).pipe(
      tap((response: HttpResponse<any>) => {
        const token = response.headers.get('Authorization');
        (window as any).Tawk_API.setAttributes({
          name: '#' + response.body.user.id + ': ' + response.body.user.name + ' ' + response.body.user.surname,
          email: response.body.user.email,
          phone: response.body.user.phone_number,
        }, function (error: any) {
          if (error) {
            console.error('Tawk.to güncelleme hatası:', error);
            return;
          }
        });
        if (token) {
          this.token = token.split(' ')[1];
          localStorage.setItem('token', this.token);
          localStorage.setItem('email', email);
          localStorage.setItem('roles', JSON.stringify(response.body.user.roles));

          if (response.body.user.locale && (response.body.user.locale === 'tr' || response.body.user.locale === 'en')) {
            this.translocoService.setActiveLang(response.body.user.locale);
            localStorage.setItem('activeLang', response.body.user.locale);
          }
        }
        if (response.body.original_user) {
          localStorage.setItem('original_user', response.body.original_user.id);
        }
      })
    );
  }

  signup(user: any): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    return this.http.post<any>(`${environment.apiUrl}/signup`, user, { headers, observe: 'response' })
  }

  resendMail(email: string): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    const body = JSON.stringify({ user: { email } });
    return this.http.post<any>(`${environment.apiUrl}/confirmation`, body, { headers, observe: 'response', context: new HttpContext().set(BYPASS_LOG, true) })
  }

  verifyEmail(token: string): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    return this.http.get<any>(`${environment.apiUrl}/confirmation?confirmation_token=${token}`, { headers, observe: 'response', context: new HttpContext().set(BYPASS_LOG, true) })
  }

  sendPasswordResetEmail(body: any): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    const req = JSON.stringify(body);
    return this.http.post<any>(`${environment.apiUrl}/password?locale=` + this.translocoService.getActiveLang(), req, { headers, observe: 'response', context: new HttpContext().set(BYPASS_LOG, true) })
  }

  changePassword(user: any, _resetToken: string): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    const body = JSON.stringify({ user });
    return this.http.put<any>(`${environment.apiUrl}/password`, body, { headers, observe: 'response', context: new HttpContext().set(BYPASS_LOG, true) })
  }

  getReportSettings(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/analysis_preferences/${localStorage.getItem('user_id')}`);
  }

  getUser(): Observable<any> {
    return this.http.get<any>(`${environment.apiUrl}/users/${localStorage.getItem('user_id')}`);
  }

  updateReportSettings(settings: any): Observable<any> {
    return this.http.patch<any>(`${environment.apiUrl}/analysis_preferences/${localStorage.getItem('user_id')}`, settings);
  }

  // PROFILE METHODS
  createProfile(profileData: any): Observable<any> {
    return this.http.post(`${environment.apiUrl}/profiles`, profileData).pipe(
      tap((response) => {
        console.log('Profile created, notifying components...');
        this.profileUpdatedSubject.next(true);

        if (profileData.profile?.interests) {
          this.userInterestsChangedSubject.next(profileData.profile.interests);
          console.log('User interests updated via profile creation:', profileData.profile.interests);
        }
      })
    );
  }

  updateProfile(profileId: number, profileData: any): Observable<any> {
    return this.http.patch(`${environment.apiUrl}/profiles/${profileId}`, profileData).pipe(
      tap((response) => {
        console.log('Profile updated, notifying components...');
        this.profileUpdatedSubject.next(true);

        if (profileData.profile?.interests) {
          this.userInterestsChangedSubject.next(profileData.profile.interests);
          console.log('User interests updated via profile update:', profileData.profile.interests);
        }
      })
    );
  }

  getUserProfile(): Observable<any> {
    const userId = localStorage.getItem('user_id');
    return this.http.get(`${environment.apiUrl}/profiles/${userId}`);
  }

  /**
   * Kullanıcının ilgi alanlarını döndürür
   * @returns Observable<string[]> - Kullanıcının ilgi alanları veya boş array
   */
  getUserInterests(): Observable<string[]> {
    return this.getUser().pipe(
      map(user => {
        try {
          if (user?.profile?.interests && typeof user.profile.interests === 'string') {
            return user.profile.interests
              .split(/[,;\n]+/)
              .map((interest: string) => interest.trim())
              .filter((interest: string) => interest.length > 0);
          }
          return [];
        } catch (error) {
          console.warn('Error parsing user interests:', error);
          return [];
        }
      }),
      catchError(error => {
        console.warn('Error fetching user interests:', error);
        return of([]);
      })
    );
  }

  /**
   * Kullanıcının üniversite bilgisini döndürür
   * @returns Observable<string | null> - Üniversite adı veya null
   */
  getUserUniversity(): Observable<string | null> {
    return this.getUser().pipe(
      map(user => {
        try {
          return user?.profile?.university?.trim() || null;
        } catch (error) {
          console.warn('Error getting user university:', error);
          return null;
        }
      }),
      catchError(error => {
        console.warn('Error fetching user university:', error);
        return of(null);
      })
    );
  }

  /**
   * YENİ: MCP için uygun keyword string'i oluşturur - JSON tabanlı mapping ile
   * @returns Observable<string> - MCP için keyword string'i
   */
  getMCPKeywords(): Observable<string> {
    return this.getUserInterests().pipe(
      map(interests => {
        if (interests.length === 0) {
          return 'academic conference research';
        }

        // JSON tabanlı kategori mapping'i
        const mappedKeywords = this.mapInterestsToMCPKeywords(interests);

        if (mappedKeywords.length === 0) {
          // Eğer mapping bulunamazsa direkt interest'leri kullan
          return interests
            .slice(0, 5)
            .join(' ')
            .toLowerCase();
        }

        return mappedKeywords
          .slice(0, 5)
          .join(' ')
          .toLowerCase();
      })
    );
  }

  /**
   * YENİ: Interest'leri JSON kategorilerine göre MCP keyword'lerine mapping'le
   * Hem İngilizce hem Türkçe destekli
   */
  private mapInterestsToMCPKeywords(interests: string[]): string[] {
    const mappedKeywords: string[] = [];

    // JSON'daki kategori mapping'i - İngilizce ve Türkçe terimler
    const categoryMapping: { [key: string]: string[] } = {
      // Medical & Medicine - İngilizce
      'cardiology': ['cardiology', 'cardiovascular', 'heart disease', 'cardiac surgery'],
      'neurology': ['neurology', 'neuroscience', 'brain', 'nervous system'],
      'emergency medicine': ['emergency medicine', 'critical care', 'trauma'],
      'gynecology': ['gynecology', 'obstetrics', 'pregnancy', 'reproductive health'],
      'internal medicine': ['internal medicine', 'general medicine', 'primary care'],

      // Medical & Medicine - Türkçe 
      'kardiyoloji': ['cardiology', 'cardiovascular', 'heart disease', 'cardiac surgery'],
      'nöroloji': ['neurology', 'neuroscience', 'brain', 'nervous system'],
      'acil tıp': ['emergency medicine', 'critical care', 'trauma'],
      'jinekoloji': ['gynecology', 'obstetrics', 'pregnancy', 'reproductive health'],
      'kadın hastalıkları': ['gynecology', 'obstetrics', 'pregnancy', 'reproductive health'],
      'dahiliye': ['internal medicine', 'general medicine', 'primary care'],
      'iç hastalıkları': ['internal medicine', 'general medicine', 'primary care'],

      // Dentistry - İngilizce
      'pedodontics': ['pediatric dentistry', 'children dental care'],
      'orthodontics': ['orthodontics', 'braces', 'dental alignment'],
      'oral surgery': ['oral surgery', 'maxillofacial surgery', 'dental implants'],
      'periodontics': ['periodontics', 'gum disease', 'periodontitis'],
      'endodontics': ['endodontics', 'root canal', 'dental pulp'],

      // Dentistry - Türkçe
      'pedodonti': ['pediatric dentistry', 'children dental care'],
      'çocuk diş hekimliği': ['pediatric dentistry', 'children dental care'],
      'ortodonti': ['orthodontics', 'braces', 'dental alignment'],
      'ağız cerrahisi': ['oral surgery', 'maxillofacial surgery', 'dental implants'],
      'çene cerrahisi': ['oral surgery', 'maxillofacial surgery', 'dental implants'],
      'periodontoloji': ['periodontics', 'gum disease', 'periodontitis'],
      'diş eti hastalıkları': ['periodontics', 'gum disease', 'periodontitis'],
      'endodonti': ['endodontics', 'root canal', 'dental pulp'],
      'kanal tedavisi': ['endodontics', 'root canal', 'dental pulp'],

      // Health Sciences - İngilizce
      'nursing': ['nursing', 'patient care', 'nursing education'],
      'public health': ['public health', 'epidemiology', 'health promotion'],
      'health management': ['health management', 'healthcare administration'],
      'rehabilitation': ['rehabilitation', 'physical therapy', 'physiotherapy'],
      'nutrition': ['nutrition', 'dietetics', 'clinical nutrition'],

      // Health Sciences - Türkçe
      'hemşirelik': ['nursing', 'patient care', 'nursing education'],
      'halk sağlığı': ['public health', 'epidemiology', 'health promotion'],
      'sağlık yönetimi': ['health management', 'healthcare administration'],
      'rehabilitasyon': ['rehabilitation', 'physical therapy', 'physiotherapy'],
      'fizyoterapi': ['rehabilitation', 'physical therapy', 'physiotherapy'],
      'beslenme': ['nutrition', 'dietetics', 'clinical nutrition'],
      'diyetetik': ['nutrition', 'dietetics', 'clinical nutrition'],

      // Social Sciences - İngilizce
      'psychology': ['psychology', 'clinical psychology', 'mental health'],
      'social work': ['social work', 'social services', 'community intervention'],
      'behavioral sciences': ['behavioral sciences', 'human behavior', 'addiction'],

      // Social Sciences - Türkçe
      'psikoloji': ['psychology', 'clinical psychology', 'mental health'],
      'ruh sağlığı': ['psychology', 'clinical psychology', 'mental health'],
      'sosyal hizmet': ['social work', 'social services', 'community intervention'],
      'davranış bilimleri': ['behavioral sciences', 'human behavior', 'addiction'],

      // Educational Sciences - İngilizce
      'medical education': ['medical education', 'medical training', 'clinical skills'],
      'nursing education': ['nursing education', 'nursing curriculum'],
      'health education': ['health education', 'health literacy', 'patient education'],
      'educational technology': ['educational technology', 'e-learning', 'online education'],

      // Educational Sciences - Türkçe
      'tıp eğitimi': ['medical education', 'medical training', 'clinical skills'],
      'hemşirelik eğitimi': ['nursing education', 'nursing curriculum'],
      'sağlık eğitimi': ['health education', 'health literacy', 'patient education'],
      'eğitim teknolojisi': ['educational technology', 'e-learning', 'online education']
    };

    // Her interest için mapping kontrol et
    interests.forEach(interest => {
      const lowerInterest = interest.toLowerCase().trim();
      let foundMapping = false;

      // Direkt match arıyoruz
      for (const [category, keywords] of Object.entries(categoryMapping)) {
        if (lowerInterest.includes(category) || category.includes(lowerInterest)) {
          mappedKeywords.push(...keywords.slice(0, 3)); // Her kategori için max 3 keyword
          foundMapping = true;
          console.log(`🎯 Mapped "${interest}" to keywords:`, keywords.slice(0, 3));
          break;
        }
      }

      // Eğer mapping bulunamazsa orijinal interest'i ekle
      if (!foundMapping) {
        mappedKeywords.push(lowerInterest);
        console.log(`📝 No mapping found for "${interest}", using original term`);
      }
    });

    // Duplicate'ları temizle
    return Array.from(new Set(mappedKeywords));
  }

  /**
   * Manuel olarak user interests değişikliğini bildir
   * @param interests - Yeni interests string'i
   */
  notifyUserInterestsChanged(interests: string): void {
    console.log('Manually notifying user interests changed:', interests);
    this.userInterestsChangedSubject.next(interests);
  }

  /**
   * Manuel olarak profile değişikliğini bildir
   */
  notifyProfileUpdated(): void {
    console.log('Manually notifying profile updated');
    this.profileUpdatedSubject.next(true);
  }

  get authenticated(): boolean {
    return localStorage.getItem('token') !== null;
  }

  isAuthenticated(): Observable<boolean> {
    return this.http.get(`${environment.apiUrl}/users/logged_in`, { withCredentials: true }).pipe(
      tap((response: any) => {
        return response.logged_in
      }),
    );
  }

  // LOGOUT
  logout(): Observable<any> {
    return this.http
      .delete(`${environment.apiUrl}/logout`, { withCredentials: true })
      .pipe(
        tap((_) => {
          localStorage.removeItem('token');
          localStorage.removeItem('username');
          localStorage.removeItem('user_id');
          localStorage.removeItem('email');
          localStorage.removeItem('roles');
          localStorage.removeItem('original_user');
        }),
      );
  }

  // DELETE USER
  deleteUser(): Observable<any> {
    var user_id = localStorage.getItem('user_id');
    return this.http
      .delete(`${environment.apiUrl}/users/${user_id}`, { withCredentials: true })
      .pipe(
        tap((_) => {
          localStorage.removeItem('token');
        }),
      );
  }

  updateLocale(locale: string): Observable<any> {
    const body = { locale };
    return this.http.patch(`${environment.apiUrl}/users/update_locale`, body, { observe: 'response' });
  }

  // REFRESH TOKEN
  refresh(): Observable<any> {
    const data = { refresh_token: localStorage.getItem('REFRESH_TOKEN') };
    return this.http
      .post(`${environment.apiUrl}/refresh`, data, {
        observe: 'response' as 'response',
      })
      .pipe(
        tap((response: any) => {
          if (response.access_token != undefined) {
            localStorage.removeItem('token');
          }
        }),
      );
  }

  hasRememberedCredentials(): boolean {
    return localStorage.getItem('rememberedUser') !== null;
  }

  getRememberedEmail(): string | null {
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
      try {
        const userData = JSON.parse(rememberedUser);
        return userData.email || null;
      } catch (e) {
        console.error('Error parsing saved credentials', e);
        return null;
      }
    }
    return null;
  }

  clearRememberedCredentials(): void {
    localStorage.removeItem('rememberedUser');
  }

  areRememberedCredentialsValid(maxAge: number = 30 * 24 * 60 * 60 * 1000): boolean {
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
      try {
        const userData = JSON.parse(rememberedUser);
        const timestamp = userData.timestamp || 0;
        const now = new Date().getTime();

        return (now - timestamp) < maxAge;
      } catch (e) {
        console.error('Error parsing saved credentials', e);
        return false;
      }
    }
    return false;
  }
}
<div class="flex items-center justify-center min-h-screen p-4 bg-neutral-100"
    *transloco="let t;read :'auth.change_password'">
    <div class="w-full max-w-md p-8 space-y-8 bg-white rounded-2xl">
        <!-- Success State -->
        <div *ngIf="isSuccess" class="space-y-8">
            <div class="flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-brand-green-100">
                <svg class="w-8 h-8 text-brand-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
            </div>

            <div class="space-y-2 text-center">
                <h1 class="text-2xl font-bold text-gray-900">{{t('success_title')}}</h1>
                <p class="text-gray-600">{{t('success_message')}}</p>
            </div>

            <button [routerLink]="'/login'"
                class="w-full px-6 py-3 font-medium text-white transition-colors rounded-full bg-brand-blue-500 hover:bg-brand-blue-600">
                {{t('login_button')}}
            </button>
        </div>

        <!-- Reset Form -->
        <div *ngIf="!isSuccess" class="space-y-8">
            <div class="flex items-center gap-2">
                <button (click)="goBack()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 12H5M12 19l-7-7 7-7" />
                    </svg>
                </button>
                <h1 class="text-2xl font-bold text-gray-900">{{t('title')}}</h1>
            </div>

            <div class="flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-brand-blue-100">
                <ng-icon name="lucideKey" class="text-3xl text-brand-blue-500"></ng-icon>
            </div>

            <div *ngIf="errorMessage"
                class="flex items-center gap-2 p-4 rounded-lg bg-status-warning-500/15 text-status-warning-500">
                <ng-icon name="lucideTriangleAlert" class="text-lg text-status-warning-500"></ng-icon>
                <span>{{errorMessage}}</span>
            </div>

            <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">{{t('new_password')}}</label>
                    <div class="relative">
                        <input [type]="passType" formControlName="password"
                            class="w-full px-4 py-3 transition-colors border-2 border-gray-200 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
                            placeholder="{{t('new_password_placeholder')}}">
                        <button type="button" (click)="togglePassword('password')"
                            class="absolute text-gray-400 -translate-y-1/2 right-4 top-1/2 hover:text-gray-600">
                            <ng-icon [name]="passType === 'password' ? 'lucideEyeOff' : 'lucideEye'"
                                class="w-5 h-5"></ng-icon>
                        </button>
                    </div>
                </div>

                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">{{t('confirm_password')}}</label>
                    <div class="relative">
                        <input [type]="confirmPassType" formControlName="confirmPassword"
                            class="w-full px-4 py-3 transition-colors border-2 border-gray-200 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
                            placeholder="{{t('confirm_password_placeholder')}}">
                        <button type="button" (click)="togglePassword('confirm')"
                            class="absolute text-gray-400 -translate-y-1/2 right-4 top-1/2 hover:text-gray-600">
                            <ng-icon [name]="confirmPassType === 'password' ? 'lucideEyeOff' : 'lucideEye'"
                                class="w-5 h-5"></ng-icon>
                        </button>
                    </div>
                </div>

                <button type="submit" [disabled]="form.invalid || form.pending" class="text-white bg-brand-blue-500 w-full
                    shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(0,60,189,0.3)] hover:bg-brand-blue-600
                    hover:shadow-[0_6px_12px_-5px_rgba(0,60,189,0.3)] active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.2)]
                    transition-all px-6 py-3 flex items-center gap-2 font-medium rounded-full disabled:opacity-50  justify-center
                    disabled:cursor-not-allowed disabled:hover:bg-brand-blue-500
                    disabled:hover:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
                    disabled:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
                    disabled:active:shadow-[inset_0_2px_4px_rgba(0,0,0,0)]">
                    {{t('update_button')}}
                </button>
            </form>
        </div>
    </div>
</div>
import { Component, OnInit } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';
import { AuthService } from '../../auth.service';
import { SnotifyService } from 'ng-alt-snotify';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {
  constructor(
    private snotifyService: SnotifyService,
    private authService: AuthService,
    private transloco: TranslocoService,
    private router: ActivatedRoute,
    private route: Router,
    private formBuilder: FormBuilder
  ) { }
  token: string;
  isSuccess = false;
  passType = 'password';
  confirmPassType = 'password';
  form: FormGroup;
  submitted = false;
  errorMessage = '';

  ngOnInit(): void {
    this.router.queryParams.subscribe((params: Params) => {
      if (params['reset_password_token']) {
        this.token = params['reset_password_token'];
      } else {
        this.route.navigate(['/login'])
      }
    })
    this.form = this.formBuilder.group(
      {
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            Validators.maxLength(24),
          ],
        ],
        confirmPassword: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            Validators.maxLength(24),
          ],
        ],
      },
      {
        validator: this.passwordMatchValidator
      }
    );
  }
  togglePassword(field: 'password' | 'confirm'): void {
    if (field === 'password') {
      this.passType = this.passType === 'password' ? 'text' : 'password';
    } else {
      this.confirmPassType = this.confirmPassType === 'password' ? 'text' : 'password';
    }
  }
  passwordMatchValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');
    return password && confirmPassword && password.value === confirmPassword.value ? null : { 'passwordMismatch': true };
  };

  changePassword() {
    this.authService.changePassword(
      {
        password: this.form.value.password,
        password_confirmation: this.form.value.confirmPassword,
        reset_password_token: this.token
      },
      this.token
    )
      .subscribe({
        next: () => {
          this.isSuccess = true;
        },
        error: (err) => {
          console.log(err);
          this.snotifyService.error
            (this.transloco.translate('auth.change_password.' + err.error.error), this.transloco.translate('auth.login.error'), {
              timeout: 1500,
              showProgressBar: true,
              closeOnClick: true,
              pauseOnHover: false,
              position: 'centerBottom',
            }
            );
        },
      }

      )
  }
  onSubmit(): void {
    this.submitted = true;
    this.errorMessage = '';

    if (this.form.value.password.length < 8) {
      this.errorMessage = this.transloco.translate('auth.change_password.error.password_short');
      return;
    }

    if (this.form.hasError('passwordMismatch')) {
      this.errorMessage = this.transloco.translate('auth.change_password.error.password_mismatch');
      return;
    }

    if (this.form.valid) {
      this.changePassword();
    }
  }

  goBack(): void {
    window.history.back();
  }
}

<div class="flex items-center justify-center min-h-screen p-4 bg-neutral-100" *transloco="let t; read: 'auth.login'">
    <div class="w-full max-w-md">
        <!-- Logo Section -->
        <div class="mb-8 text-center">
            <img src="assets/icons/istabot-logo.svg" alt="istabot Logo" class="mx-auto size-32">
            <h1 class="text-4xl font-bold text-gray-900">
                istabot
            </h1>
            <p class="mt-2 text-gray-600">{{t('desc')}}</p>
        </div>

        <!-- Main Form Card -->
        <div class="p-8 bg-white shadow-md rounded-3xl">
            <div *ngIf="error" class="p-4 mb-6 text-red-600 rounded-lg bg-red-50">
                {{error}}
            </div>

            <form [formGroup]="form" (submit)="onSubmit()" class="space-y-6">
                <!-- Email Input -->
                <div class="relative flex flex-col w-full">
                    <label class="block mb-1 text-sm font-medium text-gray-700">{{t('email')}}</label>
                    <div class="relative">
                        <input type="email" formControlName="email" placeholder="{{t('email_placeholder')}}"
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                            [ngClass]="{'border-red-500': submitted && f['email'].errors}">
                    </div>
                    <div *ngIf="submitted && f['email'].errors" class="mt-1 text-xs text-red-500">
                        <div *ngIf="f['email'].errors['required']">{{t('email_is_required')}}</div>
                        <div *ngIf="f['email'].errors['email']">{{t('email_is_invalid')}}</div>
                    </div>
                </div>

                <!-- Password Input -->
                <div class="relative flex flex-col w-full">
                    <div class="flex justify-between mb-1">
                        <label class="block text-sm font-medium text-gray-700">{{t('password')}}</label>

                    </div>

                    <div class="relative">
                        <input [type]="passType" formControlName="password" placeholder="••••••••"
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                            [ngClass]="{'border-red-500': submitted && f['password'].errors}">

                        <button type="button" (click)="togglePassword()"
                            class="absolute text-gray-400 -translate-y-1/2 right-4 top-1/2 hover:text-gray-600">
                            <ng-icon [name]="passType === 'password' ? 'lucideEyeOff' : 'lucideEye'"
                                class="text-lg"></ng-icon>
                        </button>
                    </div>

                    <div *ngIf="submitted && f['password'].errors" class="mt-1 text-xs text-red-500">
                        <div *ngIf="f['password'].errors['required']">{{t('password_is_required')}}</div>
                        <div *ngIf="f['password'].errors['minlength']">{{t('password_is_short')}}</div>
                    </div>
                </div>

                <!-- Remember Me Checkbox -->
                <div class="flex items-center justify-between px-1">
                    <div class="flex items-center">
                        <input type="checkbox" formControlName="rememberMe" id="rememberMe"
                            class="w-5 h-5 rounded-full cursor-pointer text-brand-blue-500 focus:ring-0">
                        <label for="rememberMe" class="ml-2 text-sm text-gray-700 cursor-pointer">
                            {{t('remember_me') || 'Beni hatırla'}}
                        </label>
                    </div>

                    <a [routerLink]="'/password/reset'"
                        class="text-sm cursor-pointer text-brand-blue-600 hover:underline">
                        {{ t('forgot_password') }}
                    </a>

                </div>

                <!-- Submit Button -->
                <button type="submit" class="text-white bg-brand-blue-500 w-full
                shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(0,60,189,0.3)] hover:bg-brand-blue-600
                hover:shadow-[0_6px_12px_-5px_rgba(0,60,189,0.3)] active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.2)]
                transition-all px-6 py-3 flex items-center gap-2 font-medium rounded-full disabled:opacity-50 justify-center
                disabled:cursor-not-allowed disabled:hover:bg-brand-blue-500
                disabled:hover:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
                disabled:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
                disabled:active:shadow-[inset_0_2px_4px_rgba(0,0,0,0)]">
                    {{t('login')}}
                </button>
            </form>
        </div>

        <!-- Sign Up Link -->
        <div class="mt-8 text-center">
            <p class="text-gray-600">
                <span class="text-sm">{{t('dont_have_account')}}</span>
                <a [routerLink]="['/signup']" class="text-sm text-brand-blue-500"> {{t('sign_up')}} </a>
            </p>
        </div>
        <p class="mt-6 text-xs text-center text-gray-500">
            © istabot 2025
        </p>
    </div>
</div>
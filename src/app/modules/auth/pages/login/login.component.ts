import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../auth.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'app-signin',
  templateUrl: './login.component.html',
})
export class LoginComponent implements OnInit {
  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
    private route: ActivatedRoute,
  ) { }
  form: FormGroup = new FormGroup({
    email: new FormControl(''),
    password: new FormControl(''),
    rememberMe: new FormControl(false)
  });
  submitted = false;
  error: string = '';
  passType = 'password';

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    this.error = '';
    this.login(this.form.value.email, this.form.value.password, this.form.value.rememberMe);
  }

  ngOnInit(): void {
    this.authService.isAuthenticated().subscribe(
      (response:any) => {
        if (response.logged_in==true) {
          setTimeout(() => {
            this.router.navigate(['/projects']);
            this.snotifyService.info(
              this.transloco.translate('notification.auth.login.info.already_logged_in'),
              this.transloco.translate('notification.auth.login.info.title'),
              {
                timeout: 1500,
                showProgressBar: true,
                closeOnClick: true,
                pauseOnHover: false,
                position: 'centerBottom',
              }
            );
          }, 100);
        } else {
          // Check if we have saved credentials
          this.loadSavedCredentials();
        }
      },
      (error) => {
        console.error('Error checking authentication status', error);
        // Even on error, try to load saved credentials
        this.loadSavedCredentials();
      }
    );
    
    this.form = this.formBuilder.group(
      {
        email: [
          '',
          [
            Validators.required,
            Validators.email
          ]
        ],
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(6),
            Validators.maxLength(24),
          ],
        ],
        rememberMe: [false]
      },
    );

    // Listen for changes to the rememberMe checkbox
    this.form.get('rememberMe')?.valueChanges.subscribe(checked => {
      if (!checked) {
        // If user unchecks "Remember Me", clear the saved credentials
        this.clearSavedCredentials();
      }
    });
  }

  /**
   * Load saved credentials from localStorage if they exist
   */
  loadSavedCredentials(): void {
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
      try {
        const userData = JSON.parse(rememberedUser);
        // Only set rememberMe to true if we successfully load credentials
        const rememberMe = !!userData.email;
        
        this.form.patchValue({
          email: userData.email || '',
          rememberMe: rememberMe
        });
        
        // If we have an encrypted password, decrypt it
        if (userData.encryptedPassword) {
          // In a real application, you'd use a proper encryption/decryption mechanism
          // For demo purposes, we're using a simple encoding
          const decryptedPassword = atob(userData.encryptedPassword);
          this.form.patchValue({
            password: decryptedPassword
          });
        }
      } catch (e) {
        console.error('Error parsing saved credentials', e);
        // Clear potentially corrupted data
        localStorage.removeItem('rememberedUser');
      }
    }
  }

  /**
   * Save user credentials to localStorage if rememberMe is checked
   */
  saveCredentials(email: string, password: string): void {
    // In a real application, you'd use a proper encryption mechanism
    // For demo purposes, we're using a simple encoding
    const encryptedPassword = btoa(password);
    
    const userData = {
      email,
      encryptedPassword,
      timestamp: new Date().getTime()
    };
    
    localStorage.setItem('rememberedUser', JSON.stringify(userData));
  }

  /**
   * Clear saved credentials from localStorage
   */
  clearSavedCredentials(): void {
    localStorage.removeItem('rememberedUser');
  }

  login(email: string, password: string, rememberMe: boolean = false): void {
    var target_email = null;
    localStorage.setItem('email', email);
    
    // Handle the remember me functionality
    if (rememberMe) {
      this.saveCredentials(email, password);
    } else {
      this.clearSavedCredentials();
    }
    
    this.route.queryParams.subscribe((params: Params) => {
      if (params['target_email']) {
        target_email = params['target_email'];
      }
    })
    
    this.authService.login(email, password, target_email).subscribe(
      (response) => {
        localStorage.setItem('username', response.body.user.name + ' ' + response.body.user.surname);
        localStorage.setItem('user_id', response.body.user.id);

        // If the user has a locale preference in their profile, use it
        if (response.body.user.locale && (response.body.user.locale === 'tr' || response.body.user.locale === 'en')) {
          this.transloco.setActiveLang(response.body.user.locale);
          localStorage.setItem('activeLang', response.body.user.locale);
        }

        // Always update the backend with the current language setting
        this.authService.updateLocale(this.transloco.getActiveLang()).subscribe(
          (response) => {
            this.router.navigate(['/projects']);
          },
          (error) => {
            console.error('Error updating locale', error);
            this.router.navigate(['/projects']);
          }
        );
      },
      (error) => {
        if (error.error == 'email_unconfirmed') {
          localStorage.setItem('email', email);
          this.router.navigateByUrl('/verification');
        } else {
          let errorMessage = '';
          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.error?.error) {
            errorMessage = error.error.error;
          } else if (error.error) {
            errorMessage = error.error;
          } else if (error.message) {
            errorMessage = error.message;
          } else {
            errorMessage = 'Unknown error';
          }
          this.snotifyService.error(
            this.transloco.translate('notification.auth.login.error.message.' + errorMessage),
            this.transloco.translate('auth.login.error'),
            {
              timeout: 1500,
              showProgressBar: true,
              closeOnClick: true,
              pauseOnHover: false,
              position: 'centerBottom',
            }
          );
        }
      }
    )
  }

  togglePassword(): void {
    this.passType = this.passType === 'password' ? 'text' : 'password';
  }
}
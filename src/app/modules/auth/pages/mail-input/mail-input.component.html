<div class="flex items-center justify-center min-h-screen p-4 bg-neutral-100" *transloco="let t; read: 'auth.mail-input'">
  <div class="w-full max-w-2xl p-8 bg-white shadow-lg rounded-3xl">
    <!-- <PERSON><PERSON> ve <PERSON>şlık -->
    <div class="mb-8 text-center">
      <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-brand-blue-100">
        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 152 152" fill="none"
        class="text-brand-blue-600">
        <g filter="url(#filter0_f_2620_1426)">
            <circle cx="76" cy="76" r="50" fill="currentColor" />
        </g>
        <defs>
            <filter id="filter0_f_2620_1426" x="0.700001" y="0.700001" width="150.6" height="150.6"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feGaussianBlur stdDeviation="12.65" result="effect1_foregroundBlur_2620_1426" />
            </filter>
        </defs>
    </svg>
    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 68 68" fill="none"
        class="absolute ">
        <g clip-path="url(#clip0_2620_1424)">
            <path
                d="M33.9417 19.799L28.2849 25.4559L31.1133 28.2843L36.7701 22.6274C39.1036 20.294 42.922 20.294 45.2554 22.6274C47.5889 24.9609 47.5889 28.7793 45.2554 31.1127L39.5986 36.7696L42.427 39.598L48.0839 33.9411C51.9871 30.0379 51.9871 23.7022 48.0839 19.799C44.1806 15.8958 37.8449 15.8958 33.9417 19.799ZM36.7701 39.598L31.1133 45.2548C28.7798 47.5883 24.9615 47.5883 22.628 45.2548C20.2946 42.9214 20.2946 39.103 22.628 36.7696L28.2849 31.1127L25.4564 28.2843L19.7996 33.9411C15.8964 37.8444 15.8964 44.18 19.7996 48.0833C23.7028 51.9865 30.0385 51.9865 33.9417 48.0833L39.5986 42.4264L36.7701 39.598ZM26.8707 38.1838L38.1844 26.8701L41.0128 29.6985L29.6991 41.0122L26.8707 38.1838ZM33.9417 19.799L28.2849 25.4559L31.1133 28.2843L36.7701 22.6274C39.1036 20.294 42.922 20.294 45.2554 22.6274C47.5889 24.9609 47.5889 28.7793 45.2554 31.1127L39.5986 36.7696L42.427 39.598L48.0839 33.9411C51.9871 30.0379 51.9871 23.7022 48.0839 19.799C44.1806 15.8958 37.8449 15.8958 33.9417 19.799ZM36.7701 39.598L31.1133 45.2548C28.7798 47.5883 24.9615 47.5883 22.628 45.2548C20.2946 42.9214 20.2946 39.103 22.628 36.7696L28.2849 31.1127L25.4564 28.2843L19.7996 33.9411C15.8964 37.8444 15.8964 44.18 19.7996 48.0833C23.7028 51.9865 30.0385 51.9865 33.9417 48.0833L39.5986 42.4264L36.7701 39.598ZM26.8707 38.1838L38.1844 26.8701L41.0128 29.6985L29.6991 41.0122L26.8707 38.1838Z"
                fill="white" />
        </g>
        <defs>
            <clipPath id="clip0_2620_1424">
                <rect width="48" height="48" fill="white" transform="translate(0 33.9409) rotate(-45)" />
            </clipPath>
        </defs>
    </svg>
      </div>
      <h1 class="mb-2 text-2xl font-bold text-gray-900">
        {{t('title')}}
      </h1>
      <p class="text-gray-600">
        {{t('desc')}}
      </p>
    </div>

    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
      <!-- Email -->
      <div class="space-y-1">
        <label class="block text-sm font-medium text-gray-700">{{t('email')}}</label>
        <div class="relative">
          <input type="email" formControlName="email"
            class="w-full py-3 pr-4 transition-all border-2 border-gray-200 pl-11 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
            [ngClass]="{'border-red-500': submitted && form.get('email').errors}"
            placeholder="{{t('email_placeholder')}}">
          <ng-icon name="lucideMail" class="absolute text-xl left-4 top-4 text-brand-blue-500"></ng-icon>
          <div *ngIf="submitted && form.get('email').errors" class="absolute left-0 text-sm text-red-500 -bottom-5">
            <span *ngIf="form.get('email').errors?.required">{{t('email_required')}}</span>
            <span *ngIf="form.get('email').errors?.email">{{t('email_invalid')}}</span>
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex gap-4">
        <button type="button" routerLink="/login"
          class="w-1/2 px-6 py-3 font-medium transition-all border-2 border-gray-200 rounded-3xl hover:bg-gray-50 active:scale-98">
          {{t('cancel')}}
        </button>
        <button type="submit" [disabled]="isSending"
          class="w-1/2 px-6 py-3 font-medium text-white transition-all rounded-3xl bg-brand-blue-500 hover:bg-brand-blue-600 active:scale-98 disabled:opacity-50 disabled:cursor-not-allowed">
          <ng-container *ngIf="isSending">
            <ng-icon name="lucideLoader" class="inline w-5 h-5 mr-2 animate-spin"></ng-icon>
            {{t('sending')}}
          </ng-container>
          <ng-container *ngIf="!isSending">
            {{t('send_email')}}
          </ng-container>
        </button>
      </div>
    </form>

    <p class="mt-6 text-xs text-center text-gray-500">
      © istabot 2025
    </p>
  </div>
</div>
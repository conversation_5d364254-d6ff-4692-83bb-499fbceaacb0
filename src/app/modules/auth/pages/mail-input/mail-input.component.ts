import { Component } from '@angular/core';
import { SnotifyService } from 'ng-alt-snotify';
import { AuthService } from '../../auth.service';
import { TranslocoService } from '@ngneat/transloco';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-mail-input',
  templateUrl: './mail-input.component.html',
  styleUrls: ['./mail-input.component.scss']
})
export class MailInputComponent {
  constructor(
    private formBuilder: FormBuilder,
    private snotifyService: SnotifyService,
    private authService: AuthService,
    private transloco: TranslocoService,
    private router: Router
  ) { }
  message
  form: FormGroup = new FormGroup({
    email: new FormControl(''),
  });
  submitted = false;
  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }
  onSubmit(): void {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    this.sendMail()
  }
  ngOnInit(): void {
    this.form = this.formBuilder.group(
      {
        email: [
          '',
          [
            Validators.required,
            Validators.email
          ]
        ],
      },
    );
  }
  isSending = false;
  sendMail() {
    this.isSending = true;
    const body = {
      user: {
        email: this.form.value.email,
      }
    };
    const status = Observable.create((observer) => {
      this.authService.sendPasswordResetEmail(body).subscribe(
        (response) => {
          this.isSending = false;
          observer.next({
            title: this.transloco.translate('auth.mail-input.email_sent'),
            body: this.transloco.translate('auth.mail-input.' + response.body.message),
            config: {
              closeOnClick: true,
              timeout: 2000,
            },
          });
          observer.complete();
          localStorage.setItem('email', this.form.value.email);
          this.router.navigateByUrl('/password-info');
        },
        (error) => {
          this.isSending = false;
          observer.error({
            title: this.transloco.translate('auth.login.error'),
            body: this.transloco.translate('auth.mail-input.' + error.error.error),
            config: {
              closeOnClick: true,
              timeout: 2000,
            },
          });
        }
      )
    }
    );
    this.snotifyService.async(this.transloco.translate('auth.mail-input.sending_email'), status, {
      showProgressBar: true,
      closeOnClick: true,
      pauseOnHover: false,
      position: 'centerBottom',
    })
  }
}

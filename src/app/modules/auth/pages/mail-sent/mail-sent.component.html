<div class="flex items-center justify-center min-h-screen p-4 bg-neutral-100" *transloco="let t; read: 'auth.verification'">
  <div class="w-full max-w-2xl p-8 bg-white shadow-lg rounded-3xl">
    <!-- Logo ve Başlık -->
    <div class="mb-8 text-center">
      <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4">
        <img src="assets/img/mail-verification.png" class="w-16 h-16" alt="Mail verification">
      </div>
      <h1 class="mb-2 text-2xl font-bold text-gray-900">
        {{t('pass_title')}}
      </h1>
      <p class="text-gray-600">
        {{t('desc')}}
      </p>
    </div>

    <!-- Email Display -->
    <div class="p-4 mb-6 text-center bg-brand-blue-50 rounded-3xl">
      <p class="font-medium text-brand-blue-700">
        {{mail}}
      </p>
    </div>

    <!-- Resend Section -->
    <div class="mb-6 text-center">
      <p class="mb-2 text-gray-600">
        {{t('didnt_receive')}}
      </p>
      <button [disabled]="isSending" (click)="sendMail()"
        class="font-medium transition-colors text-brand-blue-500 hover:text-brand-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
        <ng-container *ngIf="isSending">
          <ng-icon name="lucideLoader" class="inline w-4 h-4 mr-1 animate-spin"></ng-icon>
        </ng-container>
        {{t('resend')}}
      </button>
    </div>

    <!-- Navigation Links -->
    <div class="flex justify-center gap-6 mb-6">
      <a routerLink="/login" 
        class="text-sm font-medium transition-colors text-brand-blue-500 hover:text-brand-blue-600">
        {{t('back')}}
      </a>
      <a routerLink="/password/reset"
        class="text-sm font-medium transition-colors text-brand-blue-500 hover:text-brand-blue-600">
        {{t('update')}}
      </a>
    </div>

    <!-- Copyright -->
    <p class="text-xs text-center text-gray-500">
      © istabot 2025
    </p>
  </div>
</div>
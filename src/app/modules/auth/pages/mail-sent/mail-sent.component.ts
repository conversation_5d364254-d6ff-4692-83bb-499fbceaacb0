import { Component } from '@angular/core';
import { SnotifyService } from 'ng-alt-snotify';
import { AuthService } from '../../auth.service';
import { TranslocoService } from '@ngneat/transloco';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-mail-sent',
  templateUrl: './mail-sent.component.html',
  styleUrls: ['./mail-sent.component.scss']
})
export class MailSentComponent {
  constructor(
    private formBuilder: FormBuilder,
    private snotifyService: SnotifyService,
    private authService: AuthService,
    private transloco: TranslocoService,
    private router : Router
  ) { 
    this.mail = localStorage.getItem('email')
  }
  message
  ngOnInit(): void {
  }
  isSending = false;
  mail=''
  sendMail() {
    const body = {
      user: {
        email: localStorage.getItem('email')
      }
    };
    this.isSending = true;
    const status = Observable.create((observer) => {
      this.authService.sendPasswordResetEmail(body).subscribe(
        (response) => {
          this.isSending = false;
          observer.next({
            title: this.transloco.translate('auth.mail-input.email_sent'),
            body: this.transloco.translate('auth.mail-input.' + response.body.message),
            config: {
              closeOnClick: true,
              timeout: 2000,
            },
          });
          observer.complete();
        },
        (error) => {
          this.isSending = false;
          observer.error({
            title: this.transloco.translate('auth.login.error'),
            body: this.transloco.translate('auth.mail-input.' + error.error.error),
            config: {
              closeOnClick: true,
              timeout: 2000,
            },
          });
        }
      )
    }
    );
    this.snotifyService.async(this.transloco.translate('auth.mail-input.sending_email'), status, {
      showProgressBar: true,
      closeOnClick: true,
      pauseOnHover: false,
      position: 'centerBottom',
    })
  }
}

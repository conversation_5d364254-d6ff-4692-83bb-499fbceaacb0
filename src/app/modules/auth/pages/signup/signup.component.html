<div class="flex items-center justify-center min-h-screen p-4 bg-neutral-100" *transloco="let t; read: 'auth.signup'">
  <div class="w-full max-w-2xl p-8 pb-4 bg-white shadow-lg rounded-3xl">
    <!-- Logo ve Başlık -->
    <div class="mb-4 text-center">
      <div class="flex items-center justify-center w-16 h-16 mx-auto mb-2 rounded-full bg-brand-blue-100">
        <img src="assets/icons/istabot-logo.svg" alt="istabot Logo" class="mx-auto size-32">
      </div>
      <h1 class="mb-2 text-2xl font-bold text-gray-900">
        {{currentStep === 1 ? t('welcome') : t('security_title')}}
      </h1>
      <p class="text-gray-600">
        {{currentStep === 1 ? t('first_step') : t('create_password')}}
      </p>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON> -->
    <div class="w-full mb-4">
      <div class="flex justify-between mb-2">
        <ng-container *ngFor="let step of ['personal_info', 'security']; let i = index">
          <div class="flex flex-col items-center">
            <div [class]="'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ' + 
              (currentStep > i + 1 ? 'bg-brand-blue-500' : 
              currentStep === i + 1 ? 'border-2 border-brand-blue-500 bg-white' :
              'border-2 border-gray-200 bg-white')">
              <ng-container *ngIf="currentStep > i + 1 else stepNumber">
                <ng-icon name="lucideCheck" class="w-6 h-6 text-white"></ng-icon>
              </ng-container>
              <ng-template #stepNumber>
                <span [class]="'text-lg ' + (currentStep === i + 1 ? 'text-brand-blue-500' : 'text-gray-400')">
                  {{i + 1}}
                </span>
              </ng-template>
            </div>
            <span [class]="'text-sm mt-2 font-medium transition-all duration-300 ' +
              (currentStep === i + 1 ? 'text-brand-blue-500' : 'text-gray-400')">
              {{t(step)}}
            </span>
          </div>
        </ng-container>
      </div>
      <div class="w-full h-2 mt-4 bg-gray-200 rounded-full">
        <div class="h-2 transition-all duration-500 rounded-full bg-brand-blue-500"
          [style.width.%]="((currentStep - 1) / 1) * 100">
        </div>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="space-y-4">
      <!-- Adım 1: Kişisel Bilgiler -->
      <ng-container *ngIf="currentStep === 1">
        <div class="grid grid-cols-2 gap-4">
          <!-- İsim -->
          <div class="space-y-1">
            <label class="block text-sm font-medium text-gray-700">{{t('first_name')}}</label>
            <div class="relative">
              <input type="text" formControlName="firstName"
                class="w-full px-4 py-3 transition-all border-2 border-gray-200 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
                [ngClass]="{'border-red-500': submittedStep1 && f.firstName.errors}"
                placeholder="{{t('first_name_placeholder')}}">
              <div *ngIf="submittedStep1 && f.firstName.errors" class="absolute left-0 text-sm text-red-500 -bottom-5">
                {{t('first_name_required')}}
              </div>
            </div>
          </div>

          <!-- Soyisim -->
          <div class="space-y-1">
            <label class="block text-sm font-medium text-gray-700">{{t('last_name')}}</label>
            <div class="relative">
              <input type="text" formControlName="lastName"
                class="w-full px-4 py-3 transition-all border-2 border-gray-200 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
                [ngClass]="{'border-red-500': submittedStep1 && f.lastName.errors}"
                placeholder="{{t('last_name_placeholder')}}">
              <div *ngIf="submittedStep1 && f.lastName.errors" class="absolute left-0 text-sm text-red-500 -bottom-5">
                {{t('last_name_required')}}
              </div>
            </div>
          </div>
        </div>

        <!-- Email -->
        <div class="space-y-1">
          <label class="block text-sm font-medium text-gray-700">{{t('email')}}</label>
          <div class="relative">
            <input type="email" formControlName="email"
              class="w-full py-3 pr-4 transition-all border-2 border-gray-200 pl-11 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
              [ngClass]="{'border-red-500': submittedStep1 && f.email.errors}" placeholder="{{t('email_placeholder')}}">
            <ng-icon name="lucideMail" class="absolute text-xl left-4 top-4 text-brand-blue-500"></ng-icon>
            <div *ngIf="submittedStep1 && f.email.errors" class="absolute left-0 text-sm text-red-500 -bottom-5">
              <span *ngIf="f.email.errors?.required">{{t('email_required')}}</span>
              <span *ngIf="f.email.errors?.email">{{t('email_invalid')}}</span>
            </div>
          </div>
        </div>

        <!-- Telefon -->
        <div class="space-y-1" [formGroup]="phoneForm">
          <label class="block text-sm font-medium text-gray-700">{{t('phone')}}</label>
          <ngx-intl-tel-input formControlName="phone" class="*:w-full *:flex"
            [cssClass]="'z-20 block flex h-12 peer  *:w-[%100] autofill:shadow-[inset_0_0_0px_1000px_rgb(255,255,255)] bg-transparent border-gray-300 rounded-3xl shadow-sm input focus:ring-prussian-blue-950 group-has-[:focus]:bg-white group-has-[:focus]:border-prussian-blue-950 sm:text-sm'"
            [preferredCountries]="['tr', 'us']" [enableAutoCountrySelect]="true" [separateDialCode]="true"
            [searchCountryFlag]="true" [searchCountryField]="[SearchCountryField.All]" [selectFirstCountry]="true"
            [phoneValidation]="true" [inputId]="'phone'" [inputName]="'phone'" minlength="10"
            [inputClass]="'z-20 block h-12 peer autofill:shadow-[inset_0_0_0px_1000px_rgb(255,255,255)]  bg-transparent border-gray-300 rounded-3xl shadow-sm input focus:ring-prussian-blue-950 group-has-[:focus]:bg-white  group-has-[:focus]:border-prussian-blue-950 sm:text-sm'"
            [inputValue]="phone" [inputMaxLength]="15" [inputMinLength]="10" [inputType]="'tel'"
            [inputAutoComplete]="'tel'" [inputAutoCorrect]="'off'" [inputAutoCapitalize]="'on'"
            [inputSpellCheck]="'false'" [inputTabIndex]="1" [inputDisabled]="false" [inputReadOnly]="false"
            [inputRequired]="true" [inputAriaLabel]="'Phone Number*'" [inputAriaDescribedBy]="'phone'"
            [inputAriaInvalid]="false" [inputAriaRequired]="true" [inputAriaLabelledBy]="'phone'"
            [inputAriaLive]="'polite'">
          </ngx-intl-tel-input>
          <div *ngIf="submittedStep1 && phoneForm.get('phone').hasError('required')" class="mt-1 text-sm text-red-500">
            {{t('phone_required')}}
          </div>
          <div *ngIf="submittedStep1 && phoneForm.get('phone').hasError('validatePhoneNumber')"
            class="mt-1 text-sm text-red-500">
            {{t('phone_is_invalid')}}
          </div>
        </div>
      </ng-container>

      <!-- Adım 2: Güvenlik -->
      <ng-container *ngIf="currentStep === 2">
        <!-- Güvenlik İpuçları -->
        <div class="p-4 mb-6 bg-brand-blue-50 rounded-xl">
          <h3 class="mb-2 text-sm font-medium text-brand-blue-700">{{t('password_tips')}}</h3>
          <ul class="space-y-1 text-sm text-gray-600">
            <li class="flex items-center gap-2">
              <ng-icon name="lucideCheck" class="w-4 h-4 text-brand-blue-500"></ng-icon>
              {{t('password_tip_1')}}
            </li>
            <li class="flex items-center gap-2">
              <ng-icon name="lucideCheck" class="w-4 h-4 text-brand-blue-500"></ng-icon>
              {{t('password_tip_2')}}
            </li>
            <li class="flex items-center gap-2">
              <ng-icon name="lucideCheck" class="w-4 h-4 text-brand-blue-500"></ng-icon>
              {{t('password_tip_3')}}
            </li>
          </ul>
        </div>

        <!-- Şifre -->
        <div class="space-y-1">
          <label class="block text-sm font-medium text-gray-700">{{t('password')}}</label>
          <div class="relative">
            <input [type]="showPassword ? 'text' : 'password'" formControlName="password"
              class="w-full px-4 py-3 transition-all border-2 border-gray-200 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
              [ngClass]="{'border-red-500': submittedStep2 && f.password.errors}"
              placeholder="{{t('password_placeholder')}}">
            <button type="button" (click)="showPassword = !showPassword"
              class="absolute right-4 top-3.5 text-gray-400 hover:text-gray-600 transition-colors">
              <ng-icon [name]="showPassword ? 'lucideEyeOff' : 'lucideEye'" class="text-xl"></ng-icon>
            </button>
            <div *ngIf="submittedStep2 && f.password.errors" class="absolute left-0 text-sm text-red-500 -bottom-5">
              <span *ngIf="f.password.errors?.required">{{t('password_required')}}</span>
              <span *ngIf="f.password.errors?.minlength">{{t('password_min_length')}}</span>
              <span *ngIf="f.password.errors?.maxlength">
                {{t('password_max_length')}}
              </span>
            </div>
          </div>
        </div>

        <!-- Şifre Tekrar -->
        <div class="space-y-1">
          <label class="block text-sm font-medium text-gray-700">{{t('confirm_password')}}</label>
          <div class="relative">
            <input [type]="showPassword ? 'text' : 'password'" formControlName="confirmPassword"
              class="w-full px-4 py-3 transition-all border-2 border-gray-200 rounded-3xl focus:border-brand-blue-500 focus:ring-2 focus:ring-brand-blue-100"
              [ngClass]="{'border-red-500': submittedStep2 && (f.confirmPassword.errors || registerForm.errors?.passwordMismatch)}"
              placeholder="{{t('confirm_password_placeholder')}}">
            <div *ngIf="submittedStep2 && (f.confirmPassword.errors || registerForm.errors?.passwordMismatch)"
              class="flex flex-col mt-1 text-sm text-red-500">
              <span *ngIf="f.confirmPassword.errors?.required">{{t('confirm_password_required')}}</span>
              <span
                *ngIf="registerForm.errors?.passwordMismatch && !f.confirmPassword.errors?.required">{{t('passwords_not_match')}}</span>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Navigation Buttons -->
      <div class="flex justify-between">
        <button *ngIf="currentStep > 1" type="button" (click)="previousStep()"
          class="flex items-center gap-2 px-6 py-3 font-medium transition-all duration-300 rounded-full text-brand-blue-500 bg-brand-blue-50 hover:bg-brand-blue-100">
          <ng-icon name="lucideArrowLeft" class="text-xl"></ng-icon>
          {{t('back')}}
        </button>

        <button type="button" [disabled]="isSending" (click)="currentStep === 2 ? onSubmit() : nextStep()"
          class="flex items-center gap-2 px-8 py-3 text-white bg-brand-blue-500 rounded-full font-medium hover:bg-brand-blue-600 hover:-translate-y-0.5 transition-all duration-300 ml-auto disabled:opacity-50 disabled:cursor-not-allowed">
          <ng-container *ngIf="isSending">
            <ng-icon name="lucideLoader" class="text-xl animate-spin"></ng-icon>
            {{t('creating_account')}}
          </ng-container>
          <ng-container *ngIf="!isSending">
            {{currentStep === 2 ? t('create_account') : t('continue')}}
            <ng-icon *ngIf="currentStep === 1" name="lucideArrowRight" class="text-xl"></ng-icon>
          </ng-container>
        </button>
      </div>

      <!-- Login Link -->
      <p *ngIf="currentStep === 1" class="text-sm text-center text-gray-500 ">
        {{t('already_have_account')}}
        <a routerLink="/login" class="font-medium text-brand-blue-500 hover:text-brand-blue-600">
          {{t('login')}}
        </a>
      </p>

    </form>
    <p class="mt-2 text-xs text-center text-gray-500">
      © istabot 2025
    </p>
  </div>
</div>
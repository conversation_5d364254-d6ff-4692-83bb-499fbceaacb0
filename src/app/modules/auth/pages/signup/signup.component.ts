import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { AuthService } from '../../auth.service';
import { Router } from '@angular/router';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { SearchCountryField } from '@justin-s/ngx-intl-tel-input';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html'
})
export class SignupComponent implements OnInit {
  currentStep = 1;
  SearchCountryField = SearchCountryField;
  showPassword = false;
  submitted = false;
  isSending = false;
  submittedStep1 = false;
  submittedStep2 = false;

  registerForm: FormGroup;
  phoneForm: FormGroup;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService
  ) {}

  ngOnInit(): void {
    if (this.authService.authenticated) {
      this.router.navigateByUrl('/projects');
    }

    // Form initialization
    this.phoneForm = this.formBuilder.group({
      phone: ['', [Validators.required]]
    });

    this.registerForm = this.formBuilder.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(24)
      ]],
      confirmPassword: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(24)
      ]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  // Form validation helpers
  get f() {
    return this.registerForm.controls;
  }

  passwordMatchValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');
    return password && confirmPassword && password.value === confirmPassword.value 
      ? null 
      : { passwordMismatch: true };
  };

  // Step management
  nextStep(): void {
    if (this.currentStep === 1 && this.validateStep1()) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  validateStep1(): boolean {
    this.submittedStep1 = true;
    const step1Fields = ['firstName', 'lastName', 'email'];
    const valid = step1Fields.every(field => !this.registerForm.get(field).errors);
    return valid && this.phoneForm.valid;
  }

  // Form submission
  onSubmit(): void {
    if (this.currentStep === 2) {
      this.submittedStep2 = true;
      if (this.registerForm.invalid || this.phoneForm.invalid) {
        return;
      }

      this.isSending = true;
      localStorage.setItem('email', this.registerForm.value.email);

      const user = {
        user: {
          name: this.registerForm.value.firstName,
          surname: this.registerForm.value.lastName,
          email: this.registerForm.value.email,
          password: this.registerForm.value.password,
          phone_number: this.phoneForm.value.phone.internationalNumber,
          locale: this.transloco.getActiveLang()
        }
      };

      const status = new Observable(observer => {
        this.authService.signup(user).subscribe(
          response => {
            this.isSending = false;
            observer.next({
              title: this.transloco.translate('auth.signup.success'),
              body: this.transloco.translate('auth.signup.redirect'),
              config: {
                closeOnClick: true,
                timeout: 5000,
                showProgressBar: true
              }
            });
            observer.complete();
            setTimeout(() => {
              this.router.navigate(['/verification']);
            }, 1500);
          },
          error => {
            this.isSending = false;
            observer.error({
              title: this.transloco.translate('auth.signup.error'),
              body: this.handleError(error.error.error),
              config: {
                closeOnClick: true,
                timeout: 5000,
                showProgressBar: true
              }
            });
          }
        );
      });

      this.snotifyService.async(
        this.transloco.translate('auth.signup.creating_account'),
        status,
        {
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: false,
          position: 'centerBottom'
        }
      );
    }
  }

  handleError(error: string): string {
    if (error.includes('email_taken') && !error.includes('phone_number_taken')) {
      return this.transloco.translate('auth.signup.errors.email_taken');
    }
    if (error.includes('email_taken') && error.includes('phone_number_taken')) {
      return this.transloco.translate('auth.signup.errors.email_phone_taken');
    }
    if (error.includes('phone_number_taken') && !error.includes('email_taken')) {
      return this.transloco.translate('auth.signup.errors.phone_taken');
    }
    return '';
  }
}
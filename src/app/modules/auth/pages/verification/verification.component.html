<div class="flex items-center justify-center min-h-screen p-4 bg-neutral-100" *transloco="let t; read: 'auth.verification'">
  <div class="w-full max-w-md p-8 bg-white shadow-lg rounded-3xl">
    <!-- Logo ve Başlık -->
    <div class="mb-8 text-center">
      <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4">
        <img src="assets/img/mail-verification.png" class="w-16 h-16" alt="Mail verification">
      </div>
      <h1 class="mb-2 text-2xl font-bold text-gray-900">
        {{t('desc_title')}}
      </h1>
      <p class="text-gray-600">
        {{t('desc')}}
      </p>
    </div>

    <!-- Email Display -->
    <div class="p-4 mb-6 text-center bg-brand-blue-50 rounded-xl">
      <p class="font-medium text-brand-blue-700">
        {{mail}}
      </p>
    </div>

    <!-- Resend Section -->
    <div class="mb-6 text-center">
      <p class="mb-2 text-gray-600">
        {{t('didnt_receive')}}
      </p>
      <button [disabled]="isSend" (click)="isSend ? null : resend()"
        class="font-medium transition-colors text-brand-blue-500 hover:text-brand-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
        <ng-container *ngIf="isLoading">
          <ng-icon name="lucideLoader" class="inline w-4 h-4 mr-1 animate-spin"></ng-icon>
        </ng-container>
        {{t('resend')}}
        <span *ngIf="isSend" class="ml-1 text-gray-500">
          ({{countdown}}s)
        </span>
      </button>
    </div>

    <!-- Navigation -->
    <div class="text-center">
      <a routerLink="/login" 
        class="text-sm font-medium transition-colors text-brand-blue-500 hover:text-brand-blue-600">
        {{t('back')}}
      </a>
    </div>

    <!-- Copyright -->
    <p class="mt-6 text-xs text-center text-gray-500">
      © istabot {{currentYear}}
    </p>
  </div>
</div>
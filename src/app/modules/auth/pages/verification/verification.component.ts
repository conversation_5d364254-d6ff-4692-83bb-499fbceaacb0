import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { AuthService } from '../../auth.service';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { Observable, interval, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'app-verification',
  templateUrl: './verification.component.html',
  styleUrls: ['./verification.component.scss']
})
export class VerificationComponent implements OnInit, OnDestroy {
  mail: string = '';
  isSend = false;
  isLoading = false;
  countdown = 60;
  currentYear = new Date().getFullYear();
  private countdownSubscription: Subscription;

  constructor(
    private authService: AuthService,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService
  ) {}

  ngOnInit(): void {
    this.mail = localStorage.getItem('email') || '';
  }

  ngOnDestroy(): void {
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
    }
  }

  resend(): void {
    if (this.isSend) return;

    this.isLoading = true;
    this.isSend = true;
  

    const status = new Observable(observer => {
      this.authService.resendMail(this.mail).subscribe(
        response => {
          this.isLoading = false;
          observer.next({
            title: this.transloco.translate('auth.verification.success'),
            body: this.transloco.translate('auth.verification.' + response.body.message),
            config: {
              closeOnClick: true,
              timeout: 2000,
              showProgressBar: true
            }
          });
          observer.complete();
          this.startCountdown();
        },
        error => {
          this.isLoading = false;
          this.isSend = false;
          observer.error({
            title: this.transloco.translate('auth.verification.error'),
            body: this.transloco.translate('auth.verification.' + error.error.error),
            config: {
              closeOnClick: true,
              timeout: 2000,
              showProgressBar: true
            }
          });
        }
      );
    });

    this.snotifyService.async(
      this.transloco.translate('auth.verification.sending_email'),
      status,
      {
        showProgressBar: true,
        closeOnClick: true,
        pauseOnHover: false,
        position: 'centerBottom'
      }
    );
  }

  private startCountdown(): void {
    this.countdown = 60;
    this.countdownSubscription = interval(1000)
      .pipe(take(60))
      .subscribe(() => {
        this.countdown--;
        if (this.countdown === 0) {
          this.isSend = false;
        }
      });
  }
}

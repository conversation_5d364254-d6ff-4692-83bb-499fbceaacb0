import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../auth.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
@Component({
  selector: 'app-verified',
  templateUrl: './verified.component.html',
  styleUrls: ['./verified.component.scss']
})
export class VerifiedComponent implements OnInit {
  constructor(
    private authService: AuthService,
    private route: ActivatedRoute,
    private router: Router,
    private snotify: SnotifyService,
    private transloco: TranslocoService
  ) { }

  ngOnInit(): void {
    this.status = 'check'
    this.route.queryParams.subscribe(params => {
      this.route.queryParams.subscribe((params: Params) => {
        if (params['token']) {
          this.verifyEmail(params['token']);
        } else {
          this.status = 'confirmation_token_invalid';
        }
      })
    }
    );
  }
  status: string = '';
  verifyEmail(token: string): void {
    this.authService.verifyEmail(token).subscribe(
      (response) => {
        this.status = response.body.message;
      },
      (error) => {
        // confirmation_token_invalid  email_already_confirmed 
        this.status = error.error.error;
      }
    );
  }
  goLogin() {
    if (this.authService.authenticated) {
      this.router.navigateByUrl('/projects');
    }
    else {
      this.router.navigateByUrl('/login');
    }
  }
  isSend = false;
  resend(): void {
    const mail = localStorage.getItem('email');
    this.authService.resendMail(mail).subscribe(
      {

        error: (e) => {
          this.snotify.error(this.transloco.translate('auth.verified.' + this.status), this.transloco.translate('auth.login.error'))
        }
      }
    )
  }
}

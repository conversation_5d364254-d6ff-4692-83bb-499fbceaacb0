import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@app/shared/shared.module';
import { ComputeVariableComponent } from './dialogs/compute-variable/compute-variable.component';
import { DataViewComponent } from './dialogs/data-view/data-view.component';
import { DiagnoseComponent } from './dialogs/diagnose/diagnose.component';
import { ValidationDetailsComponent } from './dialogs/validation-details/validation-details.component';
import { ValidationSummaryComponent } from './dialogs/validation-summary/validation-summary-dialog.component';
import { MissingValueComponent } from './dialogs/missing-value/missing-value.component';
import { ImputationModalComponent } from './dialogs/imputation-modal/imputation-modal.component';
import { ValueLabelComponent } from './dialogs/value-label/value-label.component';
import { AutoRecodingComponent } from './dialogs/auto-recoding/auto-recoding.component';



@NgModule({
  declarations: [
    ComputeVariableComponent,
    DataViewComponent,
    DiagnoseComponent,
    ValidationDetailsComponent,
    ValidationSummaryComponent,
    MissingValueComponent,
    ImputationModalComponent,
    ValueLabelComponent,
    AutoRecodingComponent,
  ],
  imports: [CommonModule, SharedModule],
})
export class DiagnoseModule { }

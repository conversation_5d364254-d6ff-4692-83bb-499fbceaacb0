<div [@slideInOut]="animationState" class="relative flex flex-col w-screen overflow-hidden h-dvh"
    *transloco="let t;read:'shared.auto_recoding'">
    <div class="flex flex-col items-center justify-center w-full h-full overflow-hidden bg-white shadow-lg">
        <div *ngIf="isLoading" class="absolute inset-0 z-50 flex items-center justify-center bg-white/50">
            <div class="loader">Loading...</div>
        </div>

        <!-- Header -->
        <div class="w-full p-4 mx-auto">
            <div class="flex items-center justify-between">
                <!-- sol buton -->
                <!-- <button class="flex items-center justify-center p-2 text-2xl border-2 rounded-full">
                    <ng-icon name="lucideCircleHelp"></ng-icon>
                </button> -->
                <div class="w-6 h-6"></div>

                <!-- ba<PERSON><PERSON><PERSON><PERSON> kısmı -->
                <div class="flex items-center gap-2 text-brand-blue-600">
                    <ng-icon name="heroArrowPath" class="text-3xl"></ng-icon>
                    <h2 class="text-2xl font-medium">{{ t('title') }}</h2>
                </div>

                <!-- modal kapama butonu -->
                <button (click)="closeModal()"
                    class="p-2 text-3xl text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <ng-icon name="matCloseRound"></ng-icon>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex justify-center flex-1 w-full gap-2 p-3 px-24 max-h-[calc(100vh-100px)] overflow-hidden">
            <!-- Left Panel - Source Variable Selection (only visible when right panel is not shown) -->
            <div *ngIf="!showRightPanel" class="flex flex-col w-full max-w-4xl">
                <div class="flex flex-col h-full overflow-hidden border rounded-3xl">
                    <!-- <div class="flex flex-col gap-2 p-3 border-b bg-gray-50">
                        <h2 class="text-lg font-semibold text-blue-900">{{ t('source_variables') }}</h2>
                    </div> -->

                    <!-- Search Box and buttons -->
                    <div class="flex flex-col gap-2 p-2 bg-white border-b">
                        <div class="relative">
                            <input type="text" [(ngModel)]="searchText" (input)="filterVariables(searchText)"
                                [placeholder]="t('search_variables')"
                                class="w-full p-2 pl-8 text-base border rounded-full border-neutral-400">
                            <ng-icon name="heroMagnifyingGlass"
                                class="absolute text-gray-400 transform -translate-y-1/2 left-2 top-1/2">
                            </ng-icon>
                        </div>
                        <div class="flex w-full gap-2">
                            <!-- Select All Button -->
                            <button (click)="toggleSelectAll()"
                                [class]="isAllSelected() ? 'secondary-status-error-button' : 'secondary-blue-button'"
                                class="w-full transition-all">
                                <ng-icon [name]="isAllSelected() ? 'lucideCircleX' : 'lucideCircleCheck'"
                                    [class]="isAllSelected() ? 'text-red-600' : 'text-brand-blue-600'"
                                    class="text-xl transition-all"></ng-icon>
                                {{ isAllSelected() ? t('deselect_all') : t('select_all') }}
                            </button>
                            <!-- Group Variables Button -->
                            <button (click)="openGroupVariablesModal()" [disabled]="isAllSelected()"
                                class="w-full secondary-blue-button">
                                <ng-icon name="lucideFolderPlus" class="text-xl text-brand-blue-600"></ng-icon>
                                {{ t('group_variables') }}
                            </button>
                        </div>

                    </div>

                    <!-- Variables List -->
                    <div class="flex-1 p-2 space-y-1 overflow-y-auto">
                        <div *ngFor="let variable of filteredVariables" (click)="onSourceVariableSelect(variable)"
                            class="flex items-center justify-between p-2 rounded-full cursor-pointer hover:bg-gray-50"
                            [class.bg-blue-50]="selectedVariableIds.has(variable.header)">
                            <div class="flex items-center gap-2">
                                <input type="checkbox" [checked]="selectedVariableIds.has(variable.header)"
                                    (click)="$event.stopPropagation()" (change)="onSourceVariableSelect(variable)"
                                    class="rounded-full text-brand-green-500 size-5 focus:ring-brand-green-500">
                                <div class="flex flex-col">
                                    <span class="font-medium text-gray-900">{{variable.header}}</span>
                                </div>
                            </div>
                            <!-- Variable Type Badge -->
                            <span class="px-2 py-0.5 text-xs rounded-full" [ngClass]="{
                                'bg-amber-100 text-amber-600': variable.measure === 'Scale',
                                'bg-blue-100 text-blue-600': variable.measure === 'Ordinal',
                                'bg-purple-100 text-purple-600': variable.measure === 'Nominal'
                            }">
                                {{variable.measure}}
                            </span>
                        </div>

                        <!-- No variables message -->
                        <div *ngIf="filteredVariables.length === 0" class="p-4 text-center text-gray-500">
                            {{ t('no_variables_available') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Value Mapping Preview with integrated output settings -->
            <div *ngIf="showRightPanel" class="flex flex-col w-full max-w-4xl overflow-hidden">
                <div class="flex items-center justify-between p-3 mb-2 border rounded-3xl bg-gray-50">
                    <div class="flex items-center gap-2">
                        <h3 class="text-lg font-semibold text-blue-900">{{ t('value_mapping_preview') }}</h3>
                        <span class="text-xs text-gray-500" *ngIf="selectedSourceVariables.length != 0">
                            {{ t('total') }}: {{valueMappings.length}}
                        </span>
                    </div>
                    <div class="relative" *ngIf="selectedSourceVariables.length != 0">
                        <input type="text" [placeholder]="t('search_value_mapping')"
                            class="px-3 py-1 pl-8 text-sm border rounded-full w-60 border-neutral-400"
                            [(ngModel)]="searchValueText" (input)="filterValues()">
                        <ng-icon name="heroMagnifyingGlass"
                            class="absolute text-gray-400 transform -translate-y-1/2 left-2 top-1/2">
                        </ng-icon>
                    </div>
                </div>

                <!-- Variable-specific tables with integrated output settings -->
                <div *ngIf="selectedSourceVariables.length > 0" class="flex flex-col flex-1 gap-4 overflow-y-auto">
                    <!-- For each selected variable, create a table with output settings -->
                    <div *ngFor="let variable of selectedSourceVariables" class="border rounded-3xl">
                        <!-- Variable header with edit button and output settings -->
                        <div class="p-3 border-b rounded-t-3xl bg-blue-50">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center gap-2">
                                    <span class="font-medium text-blue-900">{{variable.header}}</span>
                                    <span class="px-2 py-0.5 text-xs rounded-full" [ngClass]="{
                                        'bg-amber-100 text-amber-600': variable.measure === 'Scale',
                                        'bg-blue-100 text-blue-600': variable.measure === 'Ordinal',
                                        'bg-purple-100 text-purple-600': variable.measure === 'Nominal',
                                        'bg-green-100 text-green-600': variable.isGroup
                                    }">
                                        {{variable.isGroup ? 'Group' : variable.measure}}
                                    </span>
                                </div>
                                <button (click)="openRecodingModal(variable)" class="secondary-blue-button">
                                    <ng-icon name="lucidePencilLine" class="text-xl"></ng-icon>
                                    {{ t('edit') }}
                                </button>
                            </div>

                            <div class="flex flex-col gap-1">
                                <label class="text-sm font-medium text-gray-700">{{ t('name_output_variable') }}</label>
                                <input type="text" [value]="variableOutputs.get(variable.header)?.labelTr" (input)="
                                         updateVariableOutput(variable.header, 'labelTr', $event.target.value);
                                         updateVariableOutput(variable.header, 'labelEn', $event.target.value)
                                       " [placeholder]="t('enter_label_for_both')"
                                    class="p-2 bg-white border rounded-full focus:border-blue-500 focus:ring-1 focus:ring-blue-500 border-neutral-400">
                            </div>
                        </div>

                        <!-- Variable-specific table -->
                        <div>
                            <!-- Table Header -->
                            <div class="grid gap-2 p-2 font-medium text-gray-700 border-b bg-gray-50"
                                [ngClass]="variable.isGroup ? 'grid-cols-3' : 'grid-cols-2'">
                                <div class="pl-2">{{ t('old_value') }}</div>
                                <div *ngIf="variable.isGroup">{{ t('contributing_variables') }}</div>
                                <div>{{ t('new_value') }}</div>
                            </div>

                            <!-- Table Content -->
                            <div class="overflow-y-auto max-h-60">
                                <div *ngFor="let mapping of getValueMappingsForVariable(variable.header); let i = index"
                                    class="grid gap-2 p-2 border-b hover:bg-gray-50"
                                    [ngClass]="variable.isGroup ? 'grid-cols-3' : 'grid-cols-2'"
                                    [class.bg-yellow-50]="mapping.isMissing">
                                    <div class="pl-2">
                                        <span [class.text-yellow-600]="mapping.isMissing">
                                            {{mapping.isMissing ? t('missing') : mapping.oldValue }}
                                        </span>
                                    </div>
                                    <!-- Show contributing variables only for group variables -->
                                    <div *ngIf="variable.isGroup" class="text-xs text-gray-600 truncate">
                                        {{mapping.contributingVariables || ''}}
                                    </div>
                                    <div>
                                        <input type="number"
                                            [class.opacity-50]="mapping.isMissing ? preserveMissing : false"
                                            [(ngModel)]="mapping.newValue"
                                            [disabled]="mapping.isMissing ? preserveMissing : false"
                                            (ngModelChange)="onMappingChange(i, variable.header, mapping)"
                                            class="w-full p-1.5 border rounded-full">
                                    </div>
                                </div>
                                <!-- No values message -->
                                <div *ngIf="getValueMappingsForVariable(variable.header).length === 0"
                                    class="p-4 text-center text-gray-500">
                                    {{ t('no_values_for_variable') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No variables selected message -->
                    <div *ngIf="selectedSourceVariables.length === 0" class="flex-1 p-4 text-center text-gray-500">
                        {{ t('select_variables_first') }}
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="flex items-center justify-between w-full max-w-4xl py-4 bg-white">
            <!-- <div class="flex items-center gap-2">
                <ng-icon name="heroInformationCircle" class="text-gray-400"></ng-icon>
                <span class="text-sm text-gray-500">{{ t('footer.info') }}</span>
            </div> -->
            <!-- Back button that appears only when right panel is visible -->
            <button *ngIf="showRightPanel" class="secondary-blue-button" (click)="showRightPanel = false">
                <ng-icon name="lucideArrowLeft" class="mr-2 text-xl"></ng-icon>
                {{ t('back') }}
            </button>
            <!-- Reset All button -->
            <button (click)="resetAll()" [disabled]="selectedSourceVariables.length === 0"
                class="secondary-status-error-button">
                <ng-icon name="lucideUndo2" class="mr-2 text-xl"></ng-icon>
                {{ t('footer.reset_all') }}
            </button>
            <!-- Apply Changes button that appears only when right panel is visible -->
            <button *ngIf="showRightPanel" class="primary-blue-button" [disabled]="!canApplyChanges()"
                (click)="applyChanges()">
                <ng-icon name="lucideCheck" class="mr-2 text-xl"></ng-icon>
                {{ t('footer.apply') }}
            </button>
            <button *ngIf="!showRightPanel" [disabled]="selectedSourceVariables.length === 0"
                (click)="continueToMappingStep()" class="primary-blue-button">
                <ng-icon name="lucideArrowRight" class="mr-2 text-xl"></ng-icon>
                {{ t('next') }}
            </button>
        </footer>
    </div>
</div>

<!-- Recoding Options Modal -->
<ng-template #recodingModal *transloco="let t;read:'shared.auto_recoding'">
    <div class="fixed inset-0 z-50 flex items-center justify-center p-12 bg-black/50">
        <div class="overflow-hidden bg-white shadow-xl size-full rounded-3xl">
            <div class="w-full p-4 mx-auto">
                <div class="flex items-center justify-between">
                    <!-- <button class="flex items-center justify-center p-2 text-2xl border-2 rounded-full">
                        <ng-icon name="lucideCircleHelp"></ng-icon>
                    </button> -->
                    <div class="w-6 h-6"></div>
                    <div class="flex items-center gap-2 text-blue-900">
                        <ng-icon name="heroCalculator" class="text-3xl"></ng-icon>
                        <h2 class="text-2xl font-medium">{{ t('recoding_options') }}: {{ currentEditingVariable?.header
                            }}</h2>
                    </div>
                    <button (click)="closeRecodingModal()"
                        class="p-2 text-3xl text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <ng-icon name="matCloseRound"></ng-icon>
                    </button>
                </div>
            </div>

            <div class="p-4 overflow-y-auto h-full max-h-[calc(80vh-90px)]">
                <!-- Grid layout for options and preview -->
                <div class="flex w-full gap-4">
                    <!-- Left column: Variable-specific recoding options -->
                    <div class="flex flex-col w-full gap-4">
                        <!-- Sequential Recoding -->
                        <div class="overflow-hidden border rounded-3xl">
                            <!-- Expandable header -->
                            <div (click)="toggleSection('sequential')"
                                class="p-4 transition-all cursor-pointer hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <h4 class="flex items-center text-base font-medium text-blue-900">
                                        <ng-icon name="heroArrowsUpDown" class="mr-2 text-brand-blue-600"></ng-icon>
                                        {{ t('sequential_recoding') }}
                                    </h4>
                                    <ng-icon [name]="isExpanded('sequential') ? 'lucideChevronUp' : 'lucideChevronDown'"
                                        class="text-gray-500 transition-transform duration-300"></ng-icon>
                                </div>
                                <p class="mt-1 text-sm text-gray-600">
                                    {{ t('sequential_recoding_desc') }}
                                </p>
                            </div>

                            <!-- Expandable content -->
                            <div *ngIf="isExpanded('sequential')" [@expandCollapse]="'expanded'"
                                class="p-4 pt-0 border-t">
                                <div class="space-y-4">
                                    <div class="flex-1">
                                        <label class="text-sm font-medium text-gray-700">{{ t('start_from') }}</label>
                                        <input type="number"
                                            [(ngModel)]="getVariableSettings(currentEditingVariable?.header).startValue"
                                            class="w-full p-2 mt-1 border rounded-full">
                                    </div>

                                    <div class="flex gap-2">
                                        <button
                                            (click)="applySequentialNumbersForVariable(currentEditingVariable?.header, 'ascending')"
                                            class="secondary-blue-button">
                                            <ng-icon name="heroArrowUp" class="mr-2"></ng-icon>
                                            {{ t('ascending') }}
                                        </button>
                                        <button
                                            (click)="applySequentialNumbersForVariable(currentEditingVariable?.header, 'descending')"
                                            class="secondary-blue-button">
                                            <ng-icon name="heroArrowDown" class="mr-2"></ng-icon>
                                            {{ t('descending') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Range Recoding -->
                        <div class="overflow-hidden border rounded-3xl">
                            <!-- Expandable header -->
                            <div (click)="toggleSection('range')"
                                class="p-4 transition-all cursor-pointer hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <h4 class="flex items-center text-base font-medium text-blue-900">
                                        <ng-icon name="heroArrowsRightLeft" class="mr-2 text-brand-blue-600"></ng-icon>
                                        {{ t('range_recoding') }}
                                    </h4>
                                    <ng-icon [name]="isExpanded('range') ? 'lucideChevronUp' : 'lucideChevronDown'"
                                        class="text-gray-500 transition-transform duration-300"></ng-icon>
                                </div>
                                <p class="mt-1 text-sm text-gray-600">
                                    {{ t('range_recoding_desc') }}
                                </p>
                            </div>

                            <!-- Expandable content -->
                            <div *ngIf="isExpanded('range')" [@expandCollapse]="'expanded'" class="p-4 pt-0">
                                <div class="space-y-4">
                                    <!-- Range Recoding with Tabbed Interface -->
                                    <div class="space-y-4">
                                        <!-- Type Selection Buttons -->
                                        <div class="flex w-full gap-2 text-xs">
                                            <button (click)="selectRangeType('through')"
                                                class="flex items-center justify-center w-full text-center" [ngClass]="getVariableSettings(currentEditingVariable?.header).rangeType === 'through' ?
                                                    'primary-blue-button' : 'secondary-blue-button'">
                                                {{ t('recode_range') }}
                                            </button>
                                            <button (click)="selectRangeType('above')"
                                                class="flex items-center justify-center w-full text-center" [ngClass]="getVariableSettings(currentEditingVariable?.header).rangeType === 'above' ?
                                                    'primary-blue-button' : 'secondary-blue-button'">
                                                {{ t('values_above') }}
                                            </button>
                                            <button (click)="selectRangeType('below')"
                                                class="flex items-center justify-center w-full text-center" [ngClass]="getVariableSettings(currentEditingVariable?.header).rangeType === 'below' ?
                                                    'primary-blue-button' : 'secondary-blue-button'">
                                                {{ t('values_below') }}
                                            </button>
                                        </div>

                                        <!-- Dynamic Input Fields Based on Selected Type -->
                                        <div class="p-4 border rounded-3xl bg-gray-50">
                                            <!-- Through Values -->
                                            <div *ngIf="getVariableSettings(currentEditingVariable?.header).rangeType === 'through'"
                                                class="space-y-2">
                                                <div class="flex items-center gap-2">
                                                    <input type="number"
                                                        [(ngModel)]="getVariableSettings(currentEditingVariable?.header).throughStart"
                                                        [placeholder]="'A'" class="p-1 border rounded-full w-28">
                                                    <span class="text-gray-500">{{ t('to')
                                                        }}</span>
                                                    <input type="number"
                                                        [(ngModel)]="getVariableSettings(currentEditingVariable?.header).throughEnd"
                                                        [placeholder]="'B'" class="p-1 border rounded-full w-28">
                                                    <span class="text-gray-500">{{
                                                        t('values_between') }}</span>
                                                    <input type="number"
                                                        [(ngModel)]="getVariableSettings(currentEditingVariable?.header).throughNewValue"
                                                        [placeholder]="'C'" class="p-1 border rounded-full w-28">
                                                    <span class="text-gray-500">{{
                                                        t('equal_to') }}</span>
                                                </div>
                                            </div>

                                            <!-- Above Values -->
                                            <div *ngIf="getVariableSettings(currentEditingVariable?.header).rangeType === 'above'"
                                                class="space-y-2">
                                                <div class="flex items-center gap-2">
                                                    <input type="number"
                                                        [(ngModel)]="getVariableSettings(currentEditingVariable?.header).aboveValue"
                                                        [placeholder]="'A'" class="p-1 border rounded-full w-28">
                                                    <span class="text-gray-500">{{
                                                        t('greater') }}</span>
                                                    <input type="number"
                                                        [(ngModel)]="getVariableSettings(currentEditingVariable?.header).aboveNewValue"
                                                        [placeholder]="'B'" class="p-1 border rounded-full w-28">
                                                    <span class="text-gray-500">{{
                                                        t('equal_to') }}</span>
                                                </div>
                                            </div>

                                            <!-- Below Values -->
                                            <div *ngIf="getVariableSettings(currentEditingVariable?.header).rangeType === 'below'"
                                                class="space-y-2">
                                                <div class="flex items-center gap-2">
                                                    <input type="number"
                                                        [(ngModel)]="getVariableSettings(currentEditingVariable?.header).belowValue"
                                                        [placeholder]="'A'" class="p-1 border rounded-full w-28">
                                                    <span class="text-gray-500">{{ t('less')
                                                        }}</span>
                                                    <input type="number"
                                                        [(ngModel)]="getVariableSettings(currentEditingVariable?.header).belowNewValue"
                                                        [placeholder]="'B'" class="p-1 border rounded-full w-28">
                                                    <span class="text-gray-500">{{
                                                        t('equal_to') }}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Single Apply Button -->
                                        <button (click)="applyRangeRecodingForVariable(currentEditingVariable?.header)"
                                            [disabled]="!canApplyRangeRecodingForVariable(currentEditingVariable?.header)"
                                            class="primary-blue-button">
                                            <ng-icon name="heroArrowsRightLeft" class="mr-2"></ng-icon>
                                            {{ t('apply') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Missing Values -->
                        <div class="overflow-hidden border rounded-3xl">
                            <!-- Expandable header -->
                            <div (click)="toggleSection('missing')"
                                class="p-4 transition-all cursor-pointer hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <h4 class="flex items-center text-base font-medium text-blue-900">
                                        <ng-icon name="heroExclamationTriangle" class="mr-2 text-yellow-500"></ng-icon>
                                        {{ t('missing_values') }}
                                    </h4>
                                    <ng-icon [name]="isExpanded('missing') ? 'lucideChevronUp' : 'lucideChevronDown'"
                                        class="text-gray-500 transition-transform duration-300"></ng-icon>
                                </div>
                                <p class="mt-1 text-sm text-gray-600">
                                    {{ t('missing_recoding_desc') }}
                                </p>
                            </div>

                            <!-- Expandable content -->
                            <div *ngIf="isExpanded('missing')" [@expandCollapse]="'expanded'" class="p-4 pt-0 border-t">
                                <div class="p-4">
                                    <div class="flex flex-col gap-3">
                                        <!-- Preserve Missing Values -->
                                        <div class="flex items-start gap-2">
                                            <input type="checkbox"
                                                [(ngModel)]="getVariableSettings(currentEditingVariable?.header).preserveMissing"
                                                [id]="'preserveMissing-modal'"
                                                (ngModelChange)="onPreserveMissingChangeForVariable(currentEditingVariable?.header); getVariableSettings(currentEditingVariable?.header).changeMissing = !getVariableSettings(currentEditingVariable?.header).preserveMissing"
                                                class="rounded-full text-brand-green-500 size-5 focus:ring-brand-green-500">
                                            <div class="flex-1">
                                                <label for="preserveMissing-modal"
                                                    class="text-sm font-medium text-gray-700">
                                                    {{ t('preserve_original_missing_values') }}
                                                </label>
                                                <p class="text-sm text-gray-500">
                                                    {{ t('preserve_original_missing_values_description') }}
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Change Missing Value -->
                                        <div class="flex items-start gap-2">
                                            <input type="checkbox"
                                                [(ngModel)]="getVariableSettings(currentEditingVariable?.header).changeMissing"
                                                (ngModelChange)="getVariableSettings(currentEditingVariable?.header).preserveMissing = !getVariableSettings(currentEditingVariable?.header).changeMissing; onPreserveMissingChangeForVariable(currentEditingVariable?.header)"
                                                class="rounded-full text-brand-green-500 size-5 focus:ring-brand-green-500">
                                            <div class="flex-1">
                                                <label class="text-sm font-medium text-gray-700">
                                                    {{ t('change_missing_value') }}
                                                </label>
                                                <p class="text-sm text-gray-500">
                                                    {{ t('change_missing_value_description') }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <!--new value area-->
                                    <div *ngIf="getVariableSettings(currentEditingVariable?.header).changeMissing"
                                        class="mt-4 space-y-2">
                                        <!-- <label class="text-sm font-medium text-gray-700">{{ t('system_missing_value')
                                            }}</label> -->
                                        <div class="flex items-center gap-2">
                                            <input type="number"
                                                [(ngModel)]="getVariableSettings(currentEditingVariable?.header).systemMissingValue"
                                                placeholder="1" class="w-40 p-2 border rounded-full border-neutral-400">
                                            <button
                                                (click)="setSystemMissingValueForVariable(currentEditingVariable?.header)"
                                                [disabled]="!canSetSystemMissingValueForVariable(currentEditingVariable?.header)"
                                                class="primary-blue-button">
                                                <ng-icon name="lucideCheck" class="mr-2 text-lg"></ng-icon>
                                                {{ t('apply_to_missing_values') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right column: Preview -->
                    <div class="w-full max-w-xl">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-base font-medium text-blue-900">{{ t('value_mapping_preview') }}</h4>
                            <span *ngIf="recodingPreviewValues.length > 0" class="text-xs text-gray-500">
                                {{ recodingPreviewValues.length }} {{ t('values') }}
                            </span>
                        </div>

                        <!-- Loading Indicator -->
                        <div *ngIf="isLoadingRecodingPreview" class="p-8 text-center">
                            <div
                                class="inline-block w-8 h-8 border-4 border-gray-200 rounded-full border-t-blue-500 animate-spin">
                            </div>
                            <p class="mt-2 text-sm text-gray-500">{{ t('generating_preview') }}</p>
                        </div>

                        <!-- Preview Table -->
                        <div *ngIf="!isLoadingRecodingPreview && recodingPreviewValues.length > 0"
                            class="overflow-hidden border rounded-lg">
                            <!-- Table Header -->
                            <div class="grid grid-cols-2 gap-2 p-2 font-medium text-gray-700 border-b bg-gray-50">
                                <div class="pl-2">{{ t('old_value') }}</div>
                                <div>{{ t('new_value') }}</div>
                            </div>

                            <!-- Table Content -->
                            <div class="overflow-y-auto max-h-96">
                                <div *ngFor="let mapping of recodingPreviewValues"
                                    class="grid grid-cols-2 gap-2 p-2 border-b hover:bg-gray-50"
                                    [class.bg-yellow-50]="mapping.isMissing">
                                    <div class="pl-2 overflow-hidden truncate">
                                        <span [class.text-yellow-600]="mapping.isMissing">{{ mapping.isMissing ?
                                            t('missing') : mapping.oldValue }}</span>
                                    </div>
                                    <div>
                                        <input type="number" [(ngModel)]="mapping.newValue"
                                            (ngModelChange)="onRecodingPreviewValueChange()"
                                            [class.opacity-50]="mapping.isMissing && getVariableSettings(currentEditingVariable?.header).preserveMissing"
                                            [disabled]="mapping.isMissing && getVariableSettings(currentEditingVariable?.header).preserveMissing"
                                            class="w-full p-1.5 border rounded-full">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div *ngIf="!isLoadingRecodingPreview && recodingPreviewValues.length === 0"
                            class="p-6 text-center border rounded-lg">
                            <ng-icon name="heroInformationCircle" class="text-3xl text-blue-400"></ng-icon>
                            <p class="mt-2 text-sm text-gray-600">{{ t('no_values_to_preview') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end p-4">
                <button (click)="closeRecodingModal()" class="secondary-status-error-button">
                    {{ t('close') }}
                </button>
            </div>
        </div>
    </div>
</ng-template>

<!-- Group Variables Modal with Enhanced Animations -->
<ng-template #groupVariablesModal *transloco="let t;read:'shared.auto_recoding'">
    <div [@modalFade]="animationState" class="fixed inset-0 z-50 flex items-center justify-center p-12 bg-black/50">
        <div [@modalZoom]="animationState"
            class="flex flex-col items-center overflow-hidden bg-white shadow-xl rounded-3xl size-full">
            <div class="w-full p-4 mx-auto border-b">
                <div class="flex items-center justify-between">
                    <!-- <button class="flex items-center justify-center p-2 text-2xl border-2 rounded-full">
                        <ng-icon name="lucideCircleHelp"></ng-icon>
                    </button> -->
                    <div class="w-6 h-6"></div>
                    <div class="flex items-center gap-2 text-blue-900">
                        <ng-icon [@rotateIcon]="animationState" name="heroCalculator" class="text-3xl"></ng-icon>
                        <h2 class="text-2xl font-medium">
                            {{ t('recoding_options') }}: {{ currentEditingVariable?.header }}
                        </h2>
                    </div>
                    <button (click)="closeGroupVariablesModal()"
                        class="p-2 text-3xl text-gray-500 transition-colors hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <ng-icon name="matCloseRound"></ng-icon>
                    </button>
                </div>
            </div>

            <div [ngClass]="{'max-w-4xl': selectedGroupVariables.length === 0}"
                class="p-4 overflow-y-auto h-full w-full max-h-[calc(80vh-90px)]">
                <!-- Conditional grid layout for options and preview -->
                <div class="flex w-full gap-4" [ngClass]="{'justify-center': selectedGroupVariables.length === 0}">

                    <!--left side: group settings-->
                    <div [ngClass]="{'w-full max-w-4xl': selectedGroupVariables.length === 0, 'w-1/2': selectedGroupVariables.length > 0}"
                        class="flex flex-col gap-4 transition-all duration-300">

                        <!-- Group Variables Selection -->
                        <div class="flex flex-col gap-1">
                            <label class="w-full text-sm font-medium text-center text-gray-700">{{
                                t('select_variables_to_group') }}</label>
                            <div class="overflow-hidden transition-shadow border rounded-3xl">
                                <div class="p-2 bg-white border-b">
                                    <div class="relative mb-2">
                                        <input type="text" [(ngModel)]="groupSearchText"
                                            (input)="filterGroupVariables(groupSearchText)"
                                            [placeholder]="t('search_variables')"
                                            class="w-full p-2 pl-8 text-sm transition-all border rounded-full border-neutral-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                        <ng-icon name="heroMagnifyingGlass"
                                            class="absolute text-gray-400 transform -translate-y-1/2 left-2 top-1/2">
                                        </ng-icon>
                                    </div>
                                    <!-- Select All Button -->
                                    <button (click)="toggleSelectAllGroupVariables()"
                                        [class]="isAllGroupVariablesSelected() ? 'secondary-status-error-button' : 'secondary-blue-button'"
                                        class="w-full transition-all">
                                        <ng-icon
                                            [name]="isAllGroupVariablesSelected() ? 'lucideCircleX' : 'lucideCircleCheck'"
                                            [class]="isAllGroupVariablesSelected() ? 'text-red-600' : 'text-brand-blue-600'"
                                            class="text-xl transition-all"></ng-icon>
                                        {{ isAllGroupVariablesSelected() ? t('deselect_all') : t('select_all') }}
                                    </button>

                                </div>

                                <!-- Variables List -->
                                <div class="p-2 space-y-1 overflow-y-auto h-full max-h-[calc(60dvh-15rem)]">
                                    <div *ngFor="let variable of filteredGroupVariables; let i = index"
                                        [@listItemFade]="{value: '', params: {delay: i * 30}}"
                                        class="flex items-center justify-between p-2 transition-colors rounded-full cursor-pointer hover:bg-gray-50"
                                        (click)="toggleGroupVariableSelection(variable)">
                                        <div class="flex items-center gap-2">
                                            <input type="checkbox"
                                                [checked]="selectedGroupVariableIds.has(variable.header)"
                                                (click)="$event.stopPropagation()"
                                                (change)="toggleGroupVariableSelection(variable)"
                                                class="transition-opacity rounded-full text-brand-green-500 size-5 focus:ring-brand-green-500">
                                            <span class="font-medium text-gray-900">{{variable.header}}</span>
                                        </div>
                                        <!-- Variable Type Badge -->
                                        <span class="px-2 py-0.5 text-xs rounded-full transition-colors" [ngClass]="{
                                            'bg-amber-100 text-amber-600': variable.measure === 'Scale',
                                            'bg-blue-100 text-blue-600': variable.measure === 'Ordinal',
                                            'bg-purple-100 text-purple-600': variable.measure === 'Nominal'
                                        }">
                                            {{variable.measure}}
                                        </span>
                                    </div>

                                    <!-- No variables message -->
                                    <div *ngIf="filteredGroupVariables.length === 0"
                                        class="p-4 text-center text-gray-500">
                                        {{ t('no_variables_available') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Grouping Options -->
                        <div *ngIf="selectedGroupVariables.length > 0" class="flex flex-col gap-1">
                            <label class="w-full text-sm font-medium text-center text-gray-700">{{ t('grouping_options')
                                }}</label>
                            <div class="p-3 space-y-3 transition-all bg-gray-100 rounded-3xl hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center justify-center gap-2">
                                        <label class="text-sm font-medium text-gray-700 text-nowrap">{{ t('start_from')
                                            }}</label>
                                        <input type="number" [(ngModel)]="groupStartValue"
                                            (ngModelChange)="updateGroupPreview()"
                                            class="w-full p-2 mt-1 transition-all border rounded-full border-neutral-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                    </div>
                                    <div class="flex gap-2 mr-2">
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="sortOrder" [(ngModel)]="groupSortOrder"
                                                (ngModelChange)="updateGroupPreview()" value="ascending"
                                                class="w-4 h-4 transition-colors text-brand-green-600">
                                            <span class="ml-2 text-sm text-gray-700">{{ t('ascending') }}</span>

                                        </label>
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="sortOrder" [(ngModel)]="groupSortOrder"
                                                (ngModelChange)="updateGroupPreview()" value="descending"
                                                class="w-4 h-4 transition-colors text-brand-green-600">
                                            <span class="ml-2 text-sm text-gray-700">{{ t('descending') }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Section - Only show when variables are selected -->
                    <div *ngIf="selectedGroupVariables.length > 0" [@slideFade]="'in'" class="w-1/2">
                        <div class="flex items-center justify-between mb-2">
                            <label class="text-sm font-medium text-gray-700">{{ t('value_mapping_preview') }}</label>
                            <span *ngIf="groupPreviewValues.length > 0" class="text-xs text-gray-500">
                                {{ t('total') }} {{ groupPreviewValues.length }} {{ t('values') }}
                            </span>
                        </div>

                        <!-- Loading Indicator -->
                        <div *ngIf="isLoadingGroupPreview" [@fadeIn]="'in'" class="p-8 text-center">
                            <div
                                class="inline-block w-8 h-8 border-4 border-gray-200 rounded-full border-t-blue-500 animate-spin">
                            </div>
                            <p class="mt-2 text-sm text-gray-500">{{ t('generating_preview') }}</p>
                        </div>

                        <!-- Preview Table -->
                        <div *ngIf="!isLoadingGroupPreview && groupPreviewValues.length > 0" [@tableEntrance]="'in'"
                            class="overflow-hidden transition-shadow border rounded-3xl">
                            <!-- Table Header -->
                            <div class="grid grid-cols-2 gap-2 p-2 font-medium text-gray-700 border-b bg-gray-50">
                                <div class="pl-2">{{ t('old_value') }}</div>
                                <div>{{ t('new_value') }}</div>
                            </div>

                            <!-- Table Content -->
                            <div class="overflow-y-auto max-h-96">
                                <div *ngFor="let mapping of groupPreviewValues; let i = index"
                                    [@listItemFade]="{value: '', params: {delay: i * 30}}"
                                    class="grid grid-cols-2 gap-2 p-2 transition-colors border-b hover:bg-gray-50">
                                    <div class="pl-2 overflow-hidden truncate">
                                        <span>{{ mapping.oldValue }}</span>
                                    </div>
                                    <div>
                                        <input type="number" [(ngModel)]="mapping.newValue"
                                            class="w-full p-1.5 border rounded-full transition-all focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State (No values found) -->
                        <div *ngIf="!isLoadingGroupPreview && selectedGroupVariables.length > 0 && groupPreviewValues.length === 0"
                            [@fadeIn]="'in'" class="p-6 text-center border rounded-3xl">
                            <ng-icon name="lucideInfo" class="text-3xl text-yellow-400"></ng-icon>
                            <p class="mt-2 text-sm text-gray-600">{{ t('no_common_values_found') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-between w-full max-w-4xl p-4">
                <button (click)="closeGroupVariablesModal()"
                    class="flex items-center gap-2 transition-transform secondary-status-error-button hover:scale-105">
                    <ng-icon name="lucideX" class="text-xl text-red-600"></ng-icon>
                    <span> {{ t('close') }} </span>
                </button>
                <button (click)="confirmGroupVariables()" [disabled]="!canConfirmGroupVariables()"
                    class="flex items-center gap-2 transition-transform primary-blue-button hover:scale-105 disabled:hover:scale-100"
                    [ngClass]="{'opacity-50 cursor-not-allowed': !canConfirmGroupVariables()}">
                    <ng-icon name="lucideCheck" class="text-xl text-white"></ng-icon>
                    {{ t('apply_grouping') }}
                </button>
            </div>
        </div>
    </div>
</ng-template>
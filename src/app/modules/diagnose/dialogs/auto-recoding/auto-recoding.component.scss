// Add these styles to auto-recoding.component.scss

// Component specific styles here
.variable-pulse {
  animation: pulse 0.3s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
    background-color: rgba(59, 130, 246, 0.1); /* Light blue highlight */
  }
  100% {
    transform: scale(1);
  }
}

// Modal transitions
.modal-enter {
  animation: modalEnter 0.3s forwards;
}

.modal-leave {
  animation: modalLeave 0.2s forwards;
}

@keyframes modalEnter {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modalLeave {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

// Staggered list item animations
.list-item-enter {
  animation: listItemEnter 0.3s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes listItemEnter {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Button hover effects
.primary-blue-button,
.secondary-blue-button,
.secondary-status-error-button {
  transition: all 0.2s ease;
}

.primary-blue-button:hover:not(:disabled),
.secondary-blue-button:hover:not(:disabled),
.secondary-status-error-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.primary-blue-button:active:not(:disabled),
.secondary-blue-button:active:not(:disabled),
.secondary-status-error-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: none;
}

// Radio button animation
input[type="radio"] {
  transition: all 0.2s ease;
}

input[type="radio"]:checked {
  transform: scale(1.2);
}

input[type="radio"]:checked + span {
  font-weight: 500;
}

// Input field focus animations
input[type="text"],
input[type="number"] {
  transition: all 0.2s ease;
}

input[type="text"]:focus,
input[type="number"]:focus {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

// Card hover effects
.rounded-3xl {
  transition: all 0.3s ease;
}

.rounded-3xl:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

// Loading spinner with better animation
.inline-block.w-8.h-8.border-4.rounded-full.border-t-blue-500 {
  animation:
    spin 1s linear infinite,
    pulse-opacity 2s ease-in-out infinite;
}

@keyframes pulse-opacity {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

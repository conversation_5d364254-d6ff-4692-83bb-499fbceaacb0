import { animate, state, style, transition, trigger } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef, ElementRef, TemplateRef, ViewChild } from '@angular/core';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { SnotifyService } from 'ng-alt-snotify';
import { debounceTime, Subject } from 'rxjs';

import { TranslocoService } from '@ngneat/transloco';
import { ConfirmComponent } from '@components/confirm/confirm.component';
import { ExcelService } from '@app/data/services/excel.service';

// ValueMapping interface'ini güncelle
interface ValueMapping {
  oldValue: any;
  newValue: any;
  label?: string;
  isMissing?: boolean;
  // Hang<PERSON> bu değer var
  sourceVariables: Set<string>;
}
interface RecodingState {
  startValue: number;
  throughStart: number | null;
  throughEnd: number | null;
  throughNewValue: number | null;
  aboveValue: number | null;
  belowValue: number | null;
  aboveNewValue: number | null;
  belowNewValue: number | null;
  systemMissingValue: number | null;
  preserveMissing: boolean;
  activeSection: '' | 'sequential' | 'range' | 'missing';
  valueMappings: ValueMapping[];
  selectedSourceVariable: any;
  newVariableName: string;
  newVariableLabelTr: string;
  newVariableLabelEn: string;
  showRightPanel: boolean;
}

type SequentialMode = 'sequential' | 'ascending' | 'descending';

interface VariableOutput {
  originalHeader: string;
  newName: string;
  labelTr: string;
  labelEn: string;
}

// Extend RecodingState to include variable-specific settings
interface VariableSettings {
  header: string;
  startValue: number;
  throughStart: number | null;
  throughEnd: number | null;
  throughNewValue: number | null;
  aboveValue: number | null;
  belowValue: number | null;
  aboveNewValue: number | null;
  belowNewValue: number | null;
  systemMissingValue: number | null;
  preserveMissing: boolean;
  changeMissing: boolean; // New property
  rangeType: 'through' | 'above' | 'below';
  activeSection: '' | 'sequential' | 'range' | 'missing';
}

@Component({
  selector: 'app-auto-recoding',
  templateUrl: './auto-recoding.component.html',
  styleUrls: ['./auto-recoding.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ]),
    trigger('expandCollapse', [
      state('collapsed', style({ height: '0', opacity: 0, overflow: 'hidden' })),
      state('expanded', style({ height: '*', opacity: 1 })),
      transition('collapsed <=> expanded', [
        animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ])
    ]),
    trigger('modalFade', [
      state('in', style({ backgroundColor: 'rgba(0, 0, 0, 0.5)' })),
      state('out', style({ backgroundColor: 'rgba(0, 0, 0, 0)' })),
      transition('void => in', [
        style({ backgroundColor: 'rgba(0, 0, 0, 0)' }),
        animate('200ms ease-out')
      ]),
      transition('in => void', [
        animate('200ms ease-in', style({ backgroundColor: 'rgba(0, 0, 0, 0)' }))
      ])
    ]),

    // Modal content zoom animation
    trigger('modalZoom', [
      state('in', style({ transform: 'scale(1)', opacity: 1 })),
      state('out', style({ transform: 'scale(0.9)', opacity: 0 })),
      transition('void => in', [
        style({ transform: 'scale(0.9)', opacity: 0 }),
        animate('300ms ease-out')
      ]),
      transition('in => void', [
        animate('200ms ease-in', style({ transform: 'scale(0.9)', opacity: 0 }))
      ])
    ]),

    // List item fade for staggered entrance
    trigger('listItemFade', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate('300ms ease-out',
          style({ opacity: 1, transform: 'translateY(0)' }))
      ], { params: { delay: '0ms' } })
    ]),

    // Slide and fade for right panel
    trigger('slideFade', [
      state('in', style({ transform: 'translateX(0)', opacity: 1 })),
      state('out', style({ transform: 'translateX(20px)', opacity: 0 })),
      transition('void => in', [
        style({ transform: 'translateX(20px)', opacity: 0 }),
        animate('300ms ease-out')
      ]),
      transition('in => void', [
        animate('200ms ease-in', style({ transform: 'translateX(20px)', opacity: 0 }))
      ])
    ]),

    // Simple fade in
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('200ms ease-out', style({ opacity: 1 }))
      ])
    ]),

    // Table entrance animation
    trigger('tableEntrance', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),

    // Icon rotation
    trigger('rotateIcon', [
      transition('void => in', [
        style({ transform: 'rotate(-180deg)', opacity: 0 }),
        animate('300ms ease-out', style({ transform: 'rotate(0)', opacity: 1 }))
      ])
    ])
  ]
})
export class AutoRecodingComponent implements OnInit, OnDestroy {
  // Component state
  animationState = 'in';
  activeMappingMode: 'direct' | 'through' = 'direct';
  isSourceDropdownOpen = false;
  isLoading = false;
  private initialState: RecodingState | null = null;
  // Pagination

  currentPage = 1;

  // Data
  sourceVariables: any[] = [];
  filteredVariables: any[] = [];
  selectedSourceVariables: any[] = [];
  selectedVariableIds: Set<string> = new Set();
  valueMappings: ValueMapping[] = [];
  previewData: any[] | null = null;

  // UI state
  searchText: string = '';
  startValue: number = 1;
  throughStart: number | null = null;
  throughEnd: number | null = null;
  throughNewValue: number | null = null;
  aboveValue: number | null = null;
  belowValue: number | null = null;
  aboveNewValue: number | null = null;
  belowNewValue: number | null = null;

  // Output variable
  newVariableName: string = '';
  newVariableLabelTr: string = '';
  newVariableLabelEn: string = '';

  // Debounce preview updates
  private previewUpdateSubject = new Subject<void>();

  // Add new properties for missing values
  missingValues: number[] = [];
  newMissingValue: number | null = null;

  // Add new property for system missing value handling
  systemMissingValue: number | null = null;

  // Add new property for preserve missing option
  preserveMissing: boolean = true;

  // Add new property for accordion state
  activeSection: '' | 'sequential' | 'range' | 'missing' = '';

  // Add new property for value search
  searchValueText: string = '';
  filteredMappings: ValueMapping[] = [];

  // Değişken çıktıları için yeni property
  variableOutputs: Map<string, VariableOutput> = new Map();

  // Variable-specific settings
  variableSettings: Map<string, VariableSettings> = new Map();

  // Reference to the modal template
  @ViewChild('recodingModal') recodingModal: TemplateRef<any>;

  // Current variable being edited
  currentEditingVariable: any = null;

  // Reference to the modal dialog
  private modalRef: DialogRef<any> | null = null;

  // Group Variables Modal properties
  @ViewChild('groupVariablesModal') groupVariablesModal: TemplateRef<any>;
  private groupModalRef: DialogRef<any> | null = null;

  // Group variables state
  filteredGroupVariables: any[] = [];
  selectedGroupVariables: any[] = [];
  selectedGroupVariableIds: Set<string> = new Set();
  groupSearchText: string = '';
  groupSortOrder: 'ascending' | 'descending' = 'ascending';
  groupStartValue: number = 1;

  // Add a new property to track grouped variables
  groupedVariableIds: Set<string> = new Set();
  // Add a property to track group definitions
  groupDefinitions: any[] = [];
  // Add a property to track the active preview tab
  activePreviewTab: 'variables' | 'groups' = 'variables';

  isLoadingGroupPreview: boolean = false;

  // Initialize groupPreviewValues as an empty array
  groupPreviewValues: any[] = [];

  // Recoding Modal preview properties
  isLoadingRecodingPreview: boolean = false;
  recodingPreviewValues: any[] = [];
  expandedSections: Set<string> = new Set<string>();

  constructor(
    private diagnoseHelper: DiagnoseHelperService,
    private excelService: ExcelService,
    private snotifyService: SnotifyService,
    private cdr: ChangeDetectorRef,
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private dialog: Dialog,
    private translate: TranslocoService
  ) {
    this.previewUpdateSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.updatePreviewInternal();
      this.cdr.markForCheck();
    });
  }

  ngOnInit() {
    this.initializeSourceVariables();
    // İlk durumu kaydet
    this.saveInitialState();
    this.expandedSections.add('sequential');
  }

  ngOnDestroy() {
    this.previewUpdateSubject.complete();
  }

  private initializeSourceVariables() {
    this.isLoading = true;
    try {
      this.sourceVariables = this.data.variables_json.variable_list || [];
      this.filteredVariables = JSON.parse(JSON.stringify(this.sourceVariables));
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }


  // İlk durumu kaydetmek için yeni metod
  private saveInitialState() {
    this.initialState = {
      startValue: this.startValue,
      throughStart: this.throughStart,
      throughEnd: this.throughEnd,
      throughNewValue: this.throughNewValue,
      aboveValue: this.aboveValue,
      belowValue: this.belowValue,
      aboveNewValue: this.aboveNewValue,
      belowNewValue: this.belowNewValue,
      systemMissingValue: this.systemMissingValue,
      preserveMissing: this.preserveMissing,
      activeSection: this.activeSection,
      valueMappings: this.valueMappings.map(mapping => ({ ...mapping })),
      selectedSourceVariable: this.selectedSourceVariables.length > 0 ? { ...this.selectedSourceVariables[0] } : null,
      newVariableName: this.newVariableName,
      newVariableLabelTr: this.newVariableLabelTr,
      newVariableLabelEn: this.newVariableLabelEn,
      showRightPanel: this.showRightPanel
    };

    // Save the initial state of variable settings
    this.variableSettings.forEach((settings, header) => {
      this.getVariableSettings(header); // Ensure all settings are initialized
    });
  }
  // Tüm değişiklikleri sıfırlama metodu
  resetAll() {
    if (!this.initialState) return;

    // Confirm dialog göster
    const confirmDialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: 'Reset All Changes',
        content: 'This will reset all changes including variable selection and mappings. Are you sure?',
        confirm: 'Reset',
        cancel: 'Cancel',
        type: 'warning'
      }
    });

    confirmDialog.closed.subscribe((result) => {
      if (result) {
        // Tüm state'i ilk duruma döndür
        this.startValue = this.initialState.startValue;
        this.throughStart = this.initialState.throughStart;
        this.throughEnd = this.initialState.throughEnd;
        this.throughNewValue = this.initialState.throughNewValue;
        this.aboveValue = this.initialState.aboveValue;
        this.belowValue = this.initialState.belowValue;
        this.aboveNewValue = this.initialState.aboveNewValue;
        this.belowNewValue = this.initialState.belowNewValue;
        this.systemMissingValue = this.initialState.systemMissingValue;
        this.preserveMissing = this.initialState.preserveMissing;
        this.activeSection = this.initialState.activeSection;
        this.selectedVariableIds.clear();
        this.selectedSourceVariables = this.initialState.selectedSourceVariable ?
          [{ ...this.initialState.selectedSourceVariable }] : [];

        // Value mappings'i derin kopyalama ile sıfırla
        this.valueMappings = this.initialState.valueMappings.map(mapping => ({ ...mapping }));
        this.filteredMappings = this.valueMappings;

        // Output variable bilgilerini sıfırla
        this.newVariableName = this.initialState.newVariableName;
        this.newVariableLabelTr = this.initialState.newVariableLabelTr;
        this.newVariableLabelEn = this.initialState.newVariableLabelEn;

        // Preview'ı güncelle
        this.updatePreview();

        // Change detection'ı tetikle
        this.cdr.markForCheck();

        // Başarılı mesajı göster
        this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_reset'));

        // Reset variable settings
        this.variableSettings.clear();
        this.selectedSourceVariables.forEach(variable => {
          this.getVariableSettings(variable.header); // Re-initialize with defaults
        });

        // Also clear grouped variables
        this.groupedVariableIds.clear();
      }
    });

    this.showRightPanel = this.initialState.showRightPanel;
  }

  // Modify filterVariables to exclude variables that are part of groups
  filterVariables(searchText: string) {
    if (!searchText) {
      this.filteredVariables = this.sourceVariables
    } else {
      const searchLower = searchText.toLowerCase();
      this.filteredVariables = this.sourceVariables.filter(variable =>
        !this.selectedVariableIds.has(variable.header) &&
        (variable.header.toLowerCase().includes(searchLower) ||
          variable.measure.toLowerCase().includes(searchLower))
      );
    }
  }

  toggleSourceDropdown() {
    this.isSourceDropdownOpen = !this.isSourceDropdownOpen;
  }

  // Modify onSourceVariableSelect to handle grouped variables
  onSourceVariableSelect(variable: any) {
    if (this.selectedVariableIds.has(variable.header)) {

      // Standard removal logic
      this.selectedVariableIds.delete(variable.header);
      this.selectedSourceVariables = this.selectedSourceVariables.filter(v => v.header !== variable.header);
      this.variableOutputs.delete(variable.header);

      // Clean up value mappings - remove mappings for this variable only
      this.valueMappings = this.valueMappings.filter(mapping =>
        !mapping.sourceVariables.has(variable.header)
      );

      this.filteredMappings = this.valueMappings;
      this.filterVariables(this.searchText); // Refresh filtered variables
    } else {
      // Yeni değişken ekleniyor
      this.selectedVariableIds.add(variable.header);
      this.selectedSourceVariables.push(variable);
      // Create output variable with timestamp suffix
      const timestamp = new Date().getTime() % 10000; // Last 4 digits of timestamp
      this.variableOutputs.set(variable.header, {
        originalHeader: variable.header,
        newName: `${variable.header}_R${timestamp}`,
        labelTr: `${variable.label?.tr || variable.header}`,
        labelEn: `${variable.label?.en || variable.header}`
      });

      // Sadece bu değişken için değer eşleştirmelerini oluştur
      this.addValueMappingsForVariable(variable);
    }
  }

  // Yeni bir değişken için değer eşleştirmeleri oluşturan yardımcı metod
  private addValueMappingsForVariable(variable: any) {
    const columnIndex = this.data.variables_json.headers.indexOf(variable.header);
    const variableHeader = variable.header;
    const settings = this.getVariableSettings(variableHeader);

    // Bu değişkene ait benzersiz değerleri topla
    const variableValues = new Map<any, boolean>();

    // Bu değişkenin tüm değerlerini al
    this.data.variables_json.data.forEach((row: any) => {
      let value = row[columnIndex];
      // String değerleri için trim işlemi yap
      if (typeof value === 'string') {
        value = value.trim();
      }

      // Değeri kaydet
      variableValues.set(value, true);
    });

    // Bu değişken için değer eşleştirmelerini oluştur
    const newMappings: ValueMapping[] = [];
    Array.from(variableValues.keys()).forEach(value => {
      const isMissing = this.isValueMissing(value);

      // Missing değerler için yeni değer ataması
      let newValue = value;
      if (isMissing) {
        if (settings.preserveMissing) {
          newValue = value;
        } else if (settings.systemMissingValue !== null) {
          newValue = settings.systemMissingValue;
        }
      }

      // Bu değişken için yeni bir eşleştirme oluştur
      newMappings.push({
        oldValue: value,
        newValue: newValue,
        label: isMissing ? 'MISSING' : '',
        isMissing: isMissing,
        sourceVariables: new Set([variableHeader]) // Sadece bu değişken için
      });
    });

    // String değerler için otomatik numaralandırma
    let counter = 1;
    newMappings.forEach(mapping => {
      // String değerler veya sayısal olmayan değerler için
      if ((typeof mapping.oldValue === 'string' || isNaN(Number(mapping.oldValue))) && !mapping.isMissing) {
        mapping.newValue = counter++;
      }
    });

    // Yeni eşleştirmeleri mevcut listeye ekle
    this.valueMappings = [...this.valueMappings, ...newMappings];
    this.filteredMappings = this.valueMappings;
    this.updatePreview();
  }

  // Çıktı değişkeni ayarlarını güncelle
  updateVariableOutput(header: string, field: keyof VariableOutput, value: string) {
    const output = this.variableOutputs.get(header);
    if (output) {
      this.variableOutputs.set(header, { ...output, [field]: value });
    }
  }


  private initializeValueMappings() {
    this.isLoading = true;
    try {
      // Her değişken için ayrı değer eşleştirmeleri oluştur
      const newValueMappings: ValueMapping[] = [];

      // Her değişken için ayrı işlem yap
      this.selectedSourceVariables.forEach(variable => {
        const columnIndex = this.data.variables_json.headers.indexOf(variable.header);
        const variableHeader = variable.header;
        const settings = this.getVariableSettings(variableHeader);

        // Bu değişkene ait benzersiz değerleri topla
        const variableValues = new Map<any, boolean>();

        // Bu değişkenin tüm değerlerini al
        this.data.variables_json.data.forEach(row => {
          let value = row[columnIndex];
          // String değerleri için trim işlemi yap
          if (typeof value === 'string') {
            value = value.trim();
          }

          // Değeri kaydet
          variableValues.set(value, true);
        });

        // Bu değişken için değer eşleştirmelerini oluştur
        Array.from(variableValues.keys()).forEach(value => {
          const isMissing = this.isValueMissing(value);

          // Missing değerler için yeni değer ataması
          let newValue = value;
          if (isMissing) {
            if (settings.preserveMissing) {
              newValue = value;
            } else if (settings.systemMissingValue !== null) {
              newValue = settings.systemMissingValue;
            }
          }

          // Bu değişken için yeni bir eşleştirme oluştur
          newValueMappings.push({
            oldValue: value,
            newValue: newValue,
            label: isMissing ? 'MISSING' : '',
            isMissing: isMissing,
            sourceVariables: new Set([variableHeader]) // Sadece bu değişken için
          });
        });
      });

      // Değerleri sırala (önce sayısal, sonra alfabetik)
      newValueMappings.sort((a, b) => {
        // Önce değişken adına göre sırala
        const varA = Array.from(a.sourceVariables)[0];
        const varB = Array.from(b.sourceVariables)[0];

        if (varA !== varB) {
          return varA.localeCompare(varB);
        }

        // Aynı değişkense, değerlere göre sırala
        const aNum = Number(a.oldValue);
        const bNum = Number(b.oldValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
          return aNum - bNum;
        }

        return String(a.oldValue).localeCompare(String(b.oldValue));
      });

      // Mevcut eşleştirmeleri yeni oluşturulanlarla değiştir
      this.valueMappings = newValueMappings;

      // String değerler için otomatik numaralandırma
      const variableCounters = new Map<string, number>();

      // Her değişken için başlangıç sayacını oluştur
      this.selectedSourceVariables.forEach(variable => {
        variableCounters.set(variable.header, 1);
      });

      // String değerler için otomatik numaralandırma yap
      this.valueMappings.forEach(mapping => {
        // String değerler veya sayısal olmayan değerler için
        if ((typeof mapping.oldValue === 'string' || isNaN(Number(mapping.oldValue))) && !mapping.isMissing) {
          const varHeader = Array.from(mapping.sourceVariables)[0];
          const counter = variableCounters.get(varHeader) || 1;
          mapping.newValue = counter;
          variableCounters.set(varHeader, counter + 1);
        }
      });

    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }

    this.filteredMappings = this.valueMappings;
    this.updatePreview();
  }

  private isValueMissing(value: any): boolean {
    // Explicitly check for 0 and return false
    if (value === 0) return false;

    return value === null ||
      value === undefined ||
      value === '' ||
      value === ' ' ||
      (typeof value === 'string' && value.trim() === '') ||
      (typeof value === 'number' && isNaN(value)) ||
      this.diagnoseHelper.isSystemMissing(value);
  }

  setSystemMissingValue() {
    if (this.systemMissingValue === null) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_system_missing'));
      return;
    }

    // Only update missing values if not preserving them
    if (!this.preserveMissing) {
      this.valueMappings = this.valueMappings.map(mapping => ({
        ...mapping,
        newValue: mapping.isMissing ? this.systemMissingValue : mapping.newValue,
        label: mapping.isMissing ? 'MISSING' : mapping.label
      }));
    }

    this.filteredMappings = this.valueMappings; // Güncellenen filtrelenmiş listeyi de güncelle
    this.updatePreview();
    // Başarılı mesajı göster
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  // Value Mapping Actions
  applySequentialNumbers(mode: SequentialMode = 'sequential') {
    let mappings = [...this.valueMappings];

    if (mode === 'ascending' || mode === 'descending') {
      mappings.sort((a, b) => {
        const aVal = Number(a.oldValue);
        const bVal = Number(b.oldValue);

        // If both values are valid numbers, compare them numerically
        if (!isNaN(aVal) && !isNaN(bVal)) {
          return mode === 'ascending' ? aVal - bVal : bVal - aVal;
        }

        // If values are strings or mixed types, use string comparison
        const aStr = String(a.oldValue);
        const bStr = String(b.oldValue);
        return mode === 'ascending' ?
          aStr.localeCompare(bStr) :
          bStr.localeCompare(aStr);
      });
    }

    this.valueMappings = mappings.map((mapping, index) => ({
      ...mapping,
      newValue: this.startValue + index,
      label: `Category ${this.startValue + index}`
    }));

    this.filteredMappings = this.valueMappings;
    this.updatePreview();
    // Başarılı mesajı göster
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  applyThroughValues() {
    if (this.throughStart == null || this.throughEnd == null || this.throughNewValue == null) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_through_values'));
      return;
    }

    if (this.throughStart > this.throughEnd) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.start_less_than_end'));
      return;
    }

    this.valueMappings = this.valueMappings.map(mapping => {
      const currentValue = Number(mapping.oldValue);
      if (!isNaN(currentValue) &&
        currentValue >= this.throughStart &&
        currentValue <= this.throughEnd) {
        return {
          ...mapping,
          newValue: this.throughNewValue,
          label: `Recoded (${this.throughStart}-${this.throughEnd})`
        };
      }
      return mapping;
    });

    this.filteredMappings = this.valueMappings;
    this.updatePreview();
    this.throughStart = null;
    this.throughEnd = null;
    this.throughNewValue = null;

    // Başarılı mesajı göster
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  applyAboveValue() {
    if (this.aboveValue == null || this.aboveNewValue == null) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_threshold_new_value'));
      return;
    }

    this.valueMappings = this.valueMappings.map(mapping => {
      const currentValue = Number(mapping.oldValue);
      if (!isNaN(currentValue) && currentValue > this.aboveValue!) {
        return {
          ...mapping,
          newValue: this.aboveNewValue,
          label: `Above ${this.aboveValue}`
        };
      }
      return mapping;
    });

    this.filteredMappings = this.valueMappings;
    this.updatePreview();
    this.aboveValue = null;
    this.aboveNewValue = null;

    // Başarılı mesajı göster
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  applyBelowValue() {
    if (this.belowValue == null || this.belowNewValue == null) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_threshold_new_value'));
      return;
    }

    this.valueMappings = this.valueMappings.map(mapping => {
      const currentValue = Number(mapping.oldValue);
      if (!isNaN(currentValue) && currentValue < this.belowValue!) {
        return {
          ...mapping,
          newValue: this.belowNewValue,
          label: `Below ${this.belowValue}`
        };
      }
      return mapping;
    });

    this.filteredMappings = this.valueMappings;
    this.updatePreview();
    this.belowValue = null;
    this.belowNewValue = null;

    // Başarılı mesajı göster
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  onMappingChange(index: number, variableHeader: string, mapping: ValueMapping) {
    // If this mapping is used by multiple variables, we need to split it
    if (mapping.sourceVariables.size > 1) {
      // Create a new mapping specific to this variable
      const newMapping = {
        ...mapping,
        sourceVariables: new Set([variableHeader])
      };

      // Remove this variable from the original mapping's sourceVariables
      mapping.sourceVariables.delete(variableHeader);

      // Add the new mapping to the valueMappings array
      this.valueMappings.push(newMapping);

      // Update filtered mappings
      this.filteredMappings = this.valueMappings;
    }

    this.updatePreview();
  }


  // Add new method for filtering value mappings
  filterValues() {
    if (!this.searchValueText.trim()) {
      this.filteredMappings = this.valueMappings;
      this.filteredMappings = this.valueMappings.filter(mapping =>
        !mapping.isMissing && mapping.oldValue !== 0
      );
      return;
    }

    const searchTerm = this.searchValueText.toLowerCase();
    this.filteredMappings = this.valueMappings.filter(mapping =>
      String(mapping.oldValue).toLowerCase().includes(searchTerm) ||
      String(mapping.newValue).toLowerCase().includes(searchTerm) ||
      (mapping.label && mapping.label.toLowerCase().includes(searchTerm))
    );
  }

  // Preview & Statistics
  private updatePreview() {
    this.previewUpdateSubject.next();
  }

  private updatePreviewInternal() {
    if (!this.selectedSourceVariables.length || !this.valueMappings.length) return;

    // Process preview data in chunks
    const chunkSize = 100;
    this.previewData = [];

    for (let i = 0; i < this.valueMappings.length; i += chunkSize) {
      const chunk = this.valueMappings.slice(i, i + chunkSize);
      setTimeout(() => {
        this.previewData?.push(...chunk.map(mapping => ({
          oldValue: mapping.oldValue,
          newValue: mapping.newValue,
          label: mapping.label || '-'
        })));
        this.cdr.markForCheck();
      }, 0);
    }
  }

  // Apply Changes
  canApplyChanges(): boolean {
    return (
      this.selectedSourceVariables.length > 0 &&
      this.valueMappings.every(m => m.newValue !== undefined || m.newValue !== null)
    );
  }

  private determineDataType(values: any[]): 'Scale' | 'Nominal' | 'Ordinal' {
    // Boş değerleri filtrele
    const nonEmptyValues = values.filter(val =>
      val !== null && val !== undefined && val !== '');

    if (nonEmptyValues.length === 0) {
      return 'Scale'; // Default type
    }

    // String değer kontrolü
    const hasStringValues = nonEmptyValues.some(val =>
      typeof val === 'string' && isNaN(Number(val))
    );

    if (hasStringValues) {

      return 'Nominal';
    }

    // Unique değerleri al
    const uniqueValues = new Set(nonEmptyValues);
    const uniqueCount = uniqueValues.size;

    // Eğer tüm değerler sayısal görünümlü ve ondalıklı sayılar varsa kesin Scale'dir
    const hasDecimals = nonEmptyValues.some(val =>
      String(val).includes('.') && !isNaN(Number(val)));
    if (hasDecimals) {
      return 'Scale';
    }

    // Tüm değerler 1,2,3 gibi kategorik olabilecek sayılar mı?
    const allSmallIntegers = nonEmptyValues.every(val =>
      Number.isInteger(Number(val)) &&
      Number(val) >= 0 &&
      Number(val) <= 10 &&
      !isNaN(Number(val))
    );

    // Değerler sıralı ve artımsal mı kontrol et
    if (allSmallIntegers) {
      const sortedValues = [...new Set(nonEmptyValues)].map(Number).sort((a, b) => a - b);
      const isSequential = sortedValues.every((val, idx) =>
        idx === 0 || (val === sortedValues[0] + idx)
      );
      const startsWithZeroOrOne = sortedValues[0] === 0 || sortedValues[0] === 1;
      if (isSequential && sortedValues.length <= 3 && startsWithZeroOrOne) {
        return 'Nominal';
      }
    }

    // Tüm değerler sayı mı kontrol et
    const allNumeric = nonEmptyValues.every(val => !isNaN(Number(val)));

    // Eğer sayısal değerlerin unique sayısı az ve sıralı bir yapıdaysa Ordinal olabilir
    if (allNumeric && uniqueCount <= 10) {
      const sortedValues = [...uniqueValues].map(Number).sort((a, b) => a - b);
      const isSequential = sortedValues.every((val, idx, arr) =>
        idx === 0 || (val - arr[idx - 1]) <= 2
      );
      const startsWithZeroOrOne = sortedValues[0] === 0 || sortedValues[0] === 1;
      if (isSequential && startsWithZeroOrOne) {
        return 'Ordinal';
      }
    }

    return 'Scale';
  }

  private generateValueLabels(originalValues: Map<any, any>, newValues: any[]): { en: any, tr: any } {
    const labels = {
      en: {},
      tr: {}
    };

    // Orijinal ve yeni değerler arasındaki eşleştirmeyi yap
    originalValues.forEach((originalValue, newValue) => {
      if (typeof originalValue === 'string') {
        labels.en[newValue] = originalValue;
        labels.tr[newValue] = originalValue;
      } else {
        labels.en[newValue] = newValue.toString();
        labels.tr[newValue] = newValue.toString();
      }
    });

    return labels;
  }

  // Apply Changes metodunun tam ve son versiyonu
  applyChanges() {
    const newVariables = [];
    const newData = [...this.data.variables_json.data.map(row => [...row])];
    const newHeaders = [...this.data.variables_json.headers];

    // Çıktı değişken isimlerinin kontrolü
    let hasNameConflict = false;
    this.selectedSourceVariables.forEach(sourceVar => {
      const output = this.variableOutputs.get(sourceVar.header);
      if (output && newHeaders.includes(output.newName)) {
        hasNameConflict = true;
        this.snotifyService.error(
          this.translate.translate('shared.auto_recoding.errors.variable_name_exists') +
          `: ${output.newName}`
        );
      }
    });

    // Eğer isim çakışması varsa işlemi sonlandır
    if (hasNameConflict) {
      return;
    }

    // Her değişken için yeni kolon oluştur
    this.selectedSourceVariables.forEach(sourceVar => {
      const output = this.variableOutputs.get(sourceVar.header);
      if (!output) return;

      // İsim çakışması kontrolü (tekrar)
      if (newHeaders.includes(output.newName)) {
        this.snotifyService.error(
          this.translate.translate('shared.auto_recoding.errors.variable_name_exists') +
          `: ${output.newName}`
        );
        return;
      }

      // Orijinal değişkenin column index'ini bul
      const columnIndex = this.data.variables_json.headers.indexOf(sourceVar.header);
      if (columnIndex === -1) return;

      newHeaders.push(output.newName);
      const newColumnIndex = newHeaders.length - 1;

      // Bu değişken için mapping'leri al
      const variableMappings = this.valueMappings.filter(mapping =>
        mapping.sourceVariables.has(sourceVar.header)
      );

      // Mapping lookup oluştur
      const mappingLookup = new Map();
      variableMappings.forEach(mapping => {
        mappingLookup.set(mapping.oldValue, {
          newValue: mapping.newValue,
          isMissing: mapping.isMissing
        });
      });

      // Orijinal ve yeni değerlerin eşleştirmesini tut
      const valueMapping = new Map();

      // Yeni değerleri hesapla ve ekle
      newData.forEach((row, rowIndex) => {
        const oldValue = row[columnIndex];
        const mapping = mappingLookup.get(oldValue);
        const settings = this.getVariableSettings(sourceVar.header);

        // Missing değer kontrolü ve işleme
        const isMissing = this.isValueMissing(oldValue);
        let newValue;

        if (isMissing) {
          if (settings.preserveMissing) {
            // Orijinal missing değeri koru
            newValue = oldValue;
          } else if (settings.changeMissing && settings.systemMissingValue !== null) {
            // Belirtilen sistem missing değerini kullan
            newValue = settings.systemMissingValue;
          } else if (settings.systemMissingValue !== null) {
            // Fallback olarak sistem missing değerini kullan
            newValue = settings.systemMissingValue;
          } else {
            // Son çare - orijinali koru
            newValue = oldValue;
          }
        } else {
          // Missing olmayan değer - mapping varsa kullan
          newValue = mapping ? mapping.newValue : oldValue;
        }

        // Değer eşleştirmesini kaydet
        if (oldValue !== null && oldValue !== undefined && !isMissing) {
          valueMapping.set(newValue, oldValue);
        }

        row[newColumnIndex] = newValue;
      });

      // Yeni sütunun veri tipini belirle - Grup değişkenleri için özel işlem
      const newColumnData = newData.map(row => row[newColumnIndex]);
      let measureType: 'Scale' | 'Nominal' | 'Ordinal';

      if (this.groupedVariableIds.has(sourceVar.header)) {
        // Bu değişkenin orijinal tipini al
        const originalVariable = this.sourceVariables.find(v => v.header === sourceVar.header);
        const originalType = originalVariable?.measure;

        if (originalType === 'Nominal') {
          // Nominal kalır çünkü kategorik
          measureType = 'Nominal';
        } else if (originalType === 'Ordinal') {
          // Ordinal kalır çünkü sıralı kategorik
          measureType = 'Ordinal';
        } else {
          // Scale'den dönüştürülmüşse, genellikle Ordinal olur (çünkü kodlama yapılıyor)
          measureType = 'Ordinal';
        }
      } else {
        // Normal değişkenler için standart tip belirleme
        measureType = this.excelService.determineDataType(newColumnData);
      }

      // Value labels oluştur - Grup değişkenleri için özel işlem
      let valueLabels;
      if (measureType !== 'Scale') {
        if (this.groupedVariableIds.has(sourceVar.header)) {
          // Grup değişkenleri için özel value labels - orijinal değerleri kullan
          const groupValueLabels = new Map();

          // Bu değişken için olan mapping'lerden value labels oluştur
          variableMappings.forEach(mapping => {
            if (!mapping.isMissing) {
              // Orijinal değeri label olarak kullan (Group 1, 2 değil)
              const originalValueAsLabel = String(mapping.oldValue);
              groupValueLabels.set(mapping.newValue, originalValueAsLabel);
            }
          });

          valueLabels = {
            en: Object.fromEntries(groupValueLabels),
            tr: Object.fromEntries(groupValueLabels)
          };
        } else {
          // Normal değişkenler için standart value labels
          valueLabels = this.generateValueLabels(valueMapping, newColumnData);
        }
      }

      // Yeni değişken tanımını oluştur
      newVariables.push({
        id: output.newName,
        header: output.newName,
        label: {
          en: output.labelEn,
          tr: output.labelTr
        },
        measure: measureType,
        import: true,
        value_labels: valueLabels,
        sourceVariables: [sourceVar.header],
        recoded: true,
        recodingMethod: this.groupedVariableIds.has(sourceVar.header) ? 'group_recoding' : 'auto_recoding'
      });
    });

    // Variables JSON'ı güncelle
    const updatedVariablesJson = {
      ...this.data.variables_json,
      headers: newHeaders,
      variable_list: [...this.data.variables_json.variable_list, ...newVariables],
      data: newData
    };

    this.closeModal(updatedVariablesJson);
  }

  getSelectedVariableHeaders() {
    return this.selectedSourceVariables.map(v => v.header).join(', ');
  }
  closeModal(result?: any) {
    if (!result && this.hasUnsavedChanges()) {
      const confirmDialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.translate.translate('shared.confirm.changes.title'),
          content: this.translate.translate('shared.confirm.changes.content'),
          confirm: this.translate.translate('shared.confirm.changes.confirm'),
          cancel: this.translate.translate('shared.confirm.changes.cancel')
        }
      });

      confirmDialog.closed.subscribe((confirmed) => {
        if (confirmed) {
          this.performClose(result);
        }
      });
    } else {
      if (result) {
        this.snotifyService.success(
          this.translate.translate('shared.compute_variable.messages.variable_created')
        );
      }
      this.performClose(result);
    }
  }

  private hasUnsavedChanges(): boolean {
    // Check if there are any unsaved changes
    return JSON.stringify(this.initialState) !== JSON.stringify(this.getCurrentState());
  }

  private getCurrentState(): RecodingState {
    return {
      startValue: this.startValue,
      throughStart: this.throughStart,
      throughEnd: this.throughEnd,
      throughNewValue: this.throughNewValue,
      aboveValue: this.aboveValue,
      belowValue: this.belowValue,
      aboveNewValue: this.aboveNewValue,
      belowNewValue: this.belowNewValue,
      systemMissingValue: this.systemMissingValue,
      preserveMissing: this.preserveMissing,
      activeSection: this.activeSection,
      valueMappings: this.valueMappings.map(mapping => ({ ...mapping })),
      selectedSourceVariable: this.selectedSourceVariables.length > 0 ? { ...this.selectedSourceVariables[0] } : null,
      newVariableName: this.newVariableName,
      newVariableLabelTr: this.newVariableLabelTr,
      newVariableLabelEn: this.newVariableLabelEn,
      showRightPanel: this.showRightPanel
    };
  }

  private performClose(result?: any) {
    this.isLoading = true;
    this.animationState = 'out';
    setTimeout(() => {
      this.isLoading = false;
      this.cdr.markForCheck();
      if (result) {
        this.dialogRef.close(result);
      }
      else {
        this.dialogRef.close();
      }
    }, 300);
  }

  getMissingValuesCount(): number {
    return this.valueMappings.filter(m => m.isMissing).length;
  }

  getMissingValueMappings(): ValueMapping[] {
    return this.valueMappings.filter(m => m.isMissing);
  }

  onPreserveMissingChange() {
    if (this.preserveMissing) {
      // Restore original values for missing values
      this.valueMappings = this.valueMappings.map(mapping => ({
        ...mapping,
        newValue: mapping.isMissing ? mapping.oldValue : mapping.newValue
      }));
    } else {
      // Apply system missing value to missing values
      this.setSystemMissingValue();
    }
    this.updatePreview();
  }

  onMissingValueChange(mapping: ValueMapping) {
    // Update all similar missing values if not preserving
    if (!this.preserveMissing) {
      const oldValue = mapping.oldValue;
      this.valueMappings = this.valueMappings.map(m =>
        m.oldValue === oldValue && m.isMissing ?
          { ...m, newValue: mapping.newValue } : m
      );
      this.updatePreview();
    }
  }

  // Tüm değişkenlerin seçili olup olmadığını kontrol et
  isAllSelected(): boolean {
    return this.filteredVariables.length > 0 &&
      this.filteredVariables.every(v => this.selectedVariableIds.has(v.header));
  }

  // Tümünü seç/kaldır
  toggleSelectAll() {
    if (this.isAllSelected()) {
      // Tüm seçimleri kaldır
      this.filteredVariables.forEach(variable => {
        if (this.selectedVariableIds.has(variable.header)) {
          this.onSourceVariableSelect(variable);
        }
      });
    } else {
      // Tümünü seç
      this.filteredVariables.forEach(variable => {
        if (!this.selectedVariableIds.has(variable.header)) {
          this.onSourceVariableSelect(variable);
        }
      });
    }
  }

  // Yeni metod: Belirli bir değişken için value mapping'leri getir
  getValueMappingsForVariable(variableHeader: string): ValueMapping[] {
    // Get the variable object
    const variable = this.selectedSourceVariables.find(v => v.header === variableHeader);

    // Get all mappings for this variable
    const mappings = this.valueMappings.filter(mapping =>
      mapping.sourceVariables.has(variableHeader)
    );

    // For group variables, add a column showing which original variables contribute to each value
    if (variable && (variable as any).isGroup) {
      const result = mappings.map(mapping => {
        // Create a copy with additional information if it's a group mapping
        if ((mapping as any).originalVariables) {
          return {
            ...mapping,
            // Display contributing variables in the UI
            contributingVariables: Array.from((mapping as any).originalVariables).join(', ')
          };
        }
        return mapping;
      });
      return this.filterMappings(result);
    }

    return this.filterMappings(mappings);
  }

  // Helper to apply search and other filters to mappings
  private filterMappings(mappings: ValueMapping[]): ValueMapping[] {
    // Apply search filter if present
    if (this.searchValueText.trim()) {
      const searchTerm = this.searchValueText.toLowerCase();
      return mappings.filter(mapping =>
        String(mapping.oldValue).toLowerCase().includes(searchTerm) ||
        String(mapping.newValue).toLowerCase().includes(searchTerm) ||
        (mapping.label && mapping.label.toLowerCase().includes(searchTerm)) ||
        ((mapping as any).contributingVariables &&
          (mapping as any).contributingVariables.toLowerCase().includes(searchTerm))
      );
    }

    // Hide missing values if not showing them
    if (!this.searchValueText.trim() && !this.showMissingValues) {
      return mappings.filter(mapping => !mapping.isMissing);
    }

    return mappings;
  }

  // Eksik değerleri göster/gizle kontrolü için yeni property
  showMissingValues: boolean = false;

  // Eksik değerleri göster/gizle toggle metodu
  toggleShowMissingValues() {
    this.showMissingValues = !this.showMissingValues;
    this.filterValues();
  }

  // Helper method to get or create variable settings
  getVariableSettings(variableHeader: string): any {
    if (!variableHeader) {
      return {
        rangeType: 'through',
        startValue: 1,
        throughStart: null,
        throughEnd: null,
        throughNewValue: null,
        aboveValue: null,
        aboveNewValue: null,
        belowValue: null,
        belowNewValue: null,
        preserveMissing: true,
        changeMissing: false, // Default to false
        systemMissingValue: -999
      };
    }

    // If settings for this variable don't exist yet, initialize them
    if (!this.variableSettings.has(variableHeader)) {
      this.variableSettings.set(variableHeader, {
        header: variableHeader,
        rangeType: 'through',
        startValue: 1,
        throughStart: null,
        throughEnd: null,
        throughNewValue: null,
        aboveValue: null,
        aboveNewValue: null,
        belowValue: null,
        belowNewValue: null,
        preserveMissing: true,
        changeMissing: false, // Default to false
        systemMissingValue: -999,
        activeSection: ''
      });
    }

    // If rangeType is not defined, set default to 'through'
    if (!this.variableSettings.get(variableHeader).rangeType) {
      this.variableSettings.get(variableHeader).rangeType = 'through';
    }

    return this.variableSettings.get(variableHeader);
  }

  // Toggle active section for a specific variable
  toggleActiveSection(variableHeader: string, section: '' | 'sequential' | 'range' | 'missing') {
    const settings = this.getVariableSettings(variableHeader);
    settings.activeSection = settings.activeSection === section ? '' : section;
  }

  // Check if a section is active for a specific variable
  isActiveSectionForVariable(variableHeader: string, section: '' | 'sequential' | 'range' | 'missing'): boolean {
    return this.getVariableSettings(variableHeader).activeSection === section;
  }

  // Apply sequential numbers for a specific variable
  applySequentialNumbersForVariable(variableHeader: string, mode: SequentialMode = 'sequential') {
    const settings = this.getVariableSettings(variableHeader);

    // Filter mappings for this variable only
    let mappings = this.valueMappings.filter(
      mapping => mapping.sourceVariables.has(variableHeader)
    );

    if (mode === 'ascending' || mode === 'descending') {
      mappings.sort((a, b) => {
        const aVal = Number(a.oldValue);
        const bVal = Number(b.oldValue);

        // If both values are valid numbers, compare them numerically
        if (!isNaN(aVal) && !isNaN(bVal)) {
          return mode === 'ascending' ? aVal - bVal : bVal - aVal;
        }

        // If values are strings or mixed types, use string comparison
        const aStr = String(a.oldValue);
        const bStr = String(b.oldValue);
        return mode === 'ascending' ?
          aStr.localeCompare(bStr) :
          bStr.localeCompare(aStr);
      });
    }

    // Create a map to associate each oldValue with its new sequential value
    const sequentialValueMap = new Map();
    let counter = settings.startValue;

    // Assign sequential values in the sorted order
    mappings.forEach(mapping => {
      sequentialValueMap.set(mapping.oldValue, counter++);
    });

    // Update the mappings using the sequential value map
    this.valueMappings = this.valueMappings.map(mapping => {
      if (mapping.sourceVariables.has(variableHeader)) {
        const newValue = sequentialValueMap.get(mapping.oldValue);
        if (newValue !== undefined) {
          return {
            ...mapping,
            newValue: newValue,
            label: `Category ${newValue}`
          };
        }
      }
      return mapping;
    });

    this.filteredMappings = this.valueMappings;
    this.updatePreview();
    // Update the recoding preview if we're in the modal
    if (this.currentEditingVariable && this.currentEditingVariable.header === variableHeader) {
      this.updateRecodingPreview();
    }
    // Success message
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));

    // Force change detection to update the UI
    this.cdr.detectChanges();
  }

  // Check if through values can be applied for a variable
  canApplyThroughValuesForVariable(variableHeader: string): boolean {
    const settings = this.getVariableSettings(variableHeader);
    return settings.throughStart !== null &&
      settings.throughEnd !== null &&
      settings.throughNewValue !== null;
  }

  // Apply through values for a specific variable
  applyThroughValuesForVariable(variableHeader: string) {
    const settings = this.getVariableSettings(variableHeader);

    if (!this.canApplyThroughValuesForVariable(variableHeader)) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_through_values'));
      return;
    }

    if (settings.throughStart! > settings.throughEnd!) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.start_less_than_end'));
      return;
    }

    this.valueMappings = this.valueMappings.map(mapping => {
      if (mapping.sourceVariables.has(variableHeader)) {
        const currentValue = Number(mapping.oldValue);
        if (!isNaN(currentValue) &&
          currentValue >= settings.throughStart! &&
          currentValue <= settings.throughEnd!) {
          return {
            ...mapping,
            newValue: settings.throughNewValue,
            label: `Recoded (${settings.throughStart}-${settings.throughEnd})`
          };
        }
      }
      return mapping;
    });

    this.filteredMappings = this.valueMappings;
    this.updatePreview();

    // Update the recoding preview if we're in the modal
    if (this.currentEditingVariable && this.currentEditingVariable.header === variableHeader) {
      this.updateRecodingPreview();
    }

    // Clear the input fields
    settings.throughStart = null;
    settings.throughEnd = null;
    settings.throughNewValue = null;

    // Success message
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  // Check if above values can be applied for a variable
  canApplyAboveValueForVariable(variableHeader: string): boolean {
    const settings = this.getVariableSettings(variableHeader);
    return settings.aboveValue !== null && settings.aboveNewValue !== null;
  }

  selectRangeType(type: 'through' | 'above' | 'below'): void {
    if (!this.currentEditingVariable?.header) return;

    this.getVariableSettings(this.currentEditingVariable.header).rangeType = type;
  }

  // Method to apply range recoding based on selected type
  applyRangeRecodingForVariable(variableHeader: string): void {
    if (!variableHeader) return;

    const settings = this.getVariableSettings(variableHeader);
    switch (settings.rangeType) {
      case 'through':
        this.applyThroughValuesForVariable(variableHeader);
        break;
      case 'above':
        this.applyAboveValueForVariable(variableHeader);
        break;
      case 'below':
        this.applyBelowValueForVariable(variableHeader);
        break;
    }
  }

  // Method to check if any range recoding can be applied
  canApplyRangeRecodingForVariable(variableHeader: string): boolean {
    if (!variableHeader) return false;

    const settings = this.getVariableSettings(variableHeader);
    switch (settings.rangeType) {
      case 'through':
        return this.canApplyThroughValuesForVariable(variableHeader);
      case 'above':
        return this.canApplyAboveValueForVariable(variableHeader);
      case 'below':
        return this.canApplyBelowValueForVariable(variableHeader);
      default:
        return false;
    }
  }

  // Apply above value for a specific variable
  applyAboveValueForVariable(variableHeader: string) {
    const settings = this.getVariableSettings(variableHeader);

    if (!this.canApplyAboveValueForVariable(variableHeader)) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_threshold_new_value'));
      return;
    }

    this.valueMappings = this.valueMappings.map(mapping => {
      if (mapping.sourceVariables.has(variableHeader)) {
        const currentValue = Number(mapping.oldValue);
        if (!isNaN(currentValue) && currentValue > settings.aboveValue!) {
          return {
            ...mapping,
            newValue: settings.aboveNewValue,
            label: `Above ${settings.aboveValue}`
          };
        }
      }
      return mapping;
    });

    this.filteredMappings = this.valueMappings;
    this.updatePreview();

    // Update the recoding preview if we're in the modal
    if (this.currentEditingVariable && this.currentEditingVariable.header === variableHeader) {
      this.updateRecodingPreview();
    }

    // Clear the input fields
    settings.aboveValue = null;
    settings.aboveNewValue = null;

    // Success message
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  // Check if below values can be applied for a variable
  canApplyBelowValueForVariable(variableHeader: string): boolean {
    const settings = this.getVariableSettings(variableHeader);
    return settings.belowValue !== null && settings.belowNewValue !== null;
  }

  // Apply below value for a specific variable
  applyBelowValueForVariable(variableHeader: string) {
    const settings = this.getVariableSettings(variableHeader);

    if (!this.canApplyBelowValueForVariable(variableHeader)) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_threshold_new_value'));
      return;
    }

    this.valueMappings = this.valueMappings.map(mapping => {
      if (mapping.sourceVariables.has(variableHeader)) {
        const currentValue = Number(mapping.oldValue);
        if (!isNaN(currentValue) && currentValue < settings.belowValue!) {
          return {
            ...mapping,
            newValue: settings.belowNewValue,
            label: `Below ${settings.belowValue}`
          };
        }
      }
      return mapping;
    });

    this.filteredMappings = this.valueMappings;
    this.updatePreview();

    // Update the recoding preview if we're in the modal
    if (this.currentEditingVariable && this.currentEditingVariable.header === variableHeader) {
      this.updateRecodingPreview();
    }

    // Clear the input fields
    settings.belowValue = null;
    settings.belowNewValue = null;

    // Success message
    this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.changes_applied'));
  }

  // Check if system missing value can be set for a variable
  canSetSystemMissingValueForVariable(variableHeader: string): boolean {
    const settings = this.getVariableSettings(variableHeader);
    return settings.systemMissingValue !== null &&
      settings.changeMissing &&
      !settings.preserveMissing;
  }

  // Set system missing value for a specific variable
  setSystemMissingValueForVariable(variableHeader: string) {
    const settings = this.getVariableSettings(variableHeader);

    if (settings.systemMissingValue === null) {
      this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.enter_system_missing'));
      return;
    }

    // Get all missing value mappings for this variable
    const missingMappings = this.valueMappings.filter(
      mapping => mapping.sourceVariables.has(variableHeader) && mapping.isMissing
    );

    // Only update missing values if changeMissing is true and preserveMissing is false
    if (settings.changeMissing && !settings.preserveMissing && missingMappings.length > 0) {
      // Create a set of oldValues that are missing for this variable
      const missingOldValues = new Set(missingMappings.map(m => m.oldValue));

      this.valueMappings = this.valueMappings.map(mapping => {
        if (mapping.sourceVariables.has(variableHeader) &&
          missingOldValues.has(mapping.oldValue) &&
          mapping.isMissing) {
          return {
            ...mapping,
            newValue: settings.systemMissingValue,
            label: 'MISSING'
          };
        }
        return mapping;
      });

      // Make sure to update the settings to reflect that we want to change missing values
      settings.changeMissing = true;
      settings.preserveMissing = false;
    }

    this.filteredMappings = this.valueMappings;
    this.updatePreview();

    // Update the recoding preview if we're in the modal
    if (this.currentEditingVariable && this.currentEditingVariable.header === variableHeader) {
      this.updateRecodingPreview();
    }

  }

  // Handle preserve missing change for a specific variable
  onPreserveMissingChangeForVariable(variableHeader: string) {
    if (!variableHeader) return;

    const settings = this.getVariableSettings(variableHeader);

    // Ensure preserveMissing and changeMissing are exclusive
    if (settings.preserveMissing) {
      settings.changeMissing = false;
    } else {
      // If not preserving, enable changeMissing by default
      settings.changeMissing = true;
    }

    if (settings.preserveMissing) {
      // Restore original values for missing values
      this.valueMappings = this.valueMappings.map(mapping => ({
        ...mapping,
        newValue: mapping.isMissing && mapping.sourceVariables.has(variableHeader) ? mapping.oldValue : mapping.newValue
      }));
    } else if (settings.systemMissingValue !== null) {
      // Apply system missing value to missing values
      this.valueMappings = this.valueMappings.map(mapping => ({
        ...mapping,
        newValue: mapping.isMissing && mapping.sourceVariables.has(variableHeader) ?
          settings.systemMissingValue : mapping.newValue,
        label: mapping.isMissing && mapping.sourceVariables.has(variableHeader) ?
          'MISSING' : mapping.label
      }));
    }

    this.filteredMappings = this.valueMappings;
    this.updatePreview();

    // Update the recoding preview if we're in the modal
    if (this.currentEditingVariable && this.currentEditingVariable.header === variableHeader) {
      this.updateRecodingPreview();
    }
  }

  // Open the recoding modal for a specific variable
  openRecodingModal(variable: any) {
    this.currentEditingVariable = variable;

    // Initialize preview values for this variable
    this.updateRecodingPreview();

    this.modalRef = this.dialog.open(this.recodingModal, {
      disableClose: false,
      width: '800px'
    });

    this.modalRef.closed.subscribe(() => {
      this.currentEditingVariable = null;
      this.recodingPreviewValues = [];
    });
  }

  // Close the recoding modal
  closeRecodingModal() {
    if (this.modalRef) {
      this.modalRef.close();
      this.modalRef = null;
    }
  }

  // Open the group variables modal
  openGroupVariablesModal() {
    // Initialize group variables selection with variables not currently selected
    this.resetGroupVariablesState();
    this.filteredGroupVariables = this.sourceVariables.filter(v =>
      !this.selectedVariableIds.has(v.header) &&
      !this.groupedVariableIds.has(v.header)
    );

    // Create and open the modal
    this.groupModalRef = this.dialog.open(this.groupVariablesModal, {
      disableClose: false,
      width: '800px'
    });

    // Reset preview values
    this.groupPreviewValues = [];
  }

  // Close the group variables modal
  closeGroupVariablesModal() {
    if (this.groupModalRef) {
      this.groupModalRef.close();
      this.groupModalRef = null;
    }
  }

  // Reset the group variables state
  resetGroupVariablesState() {
    this.selectedGroupVariables = [];
    this.selectedGroupVariableIds.clear();
    this.groupSearchText = '';
    this.groupSortOrder = 'ascending';
    this.groupStartValue = 1;
    this.groupPreviewValues = [];
  }

  // Filter variables in the group modal
  filterGroupVariables(searchText: string) {
    if (!searchText) {
      this.filteredGroupVariables = this.sourceVariables.filter(v =>
        !this.selectedVariableIds.has(v.header) &&
        !this.groupedVariableIds.has(v.header)
      );
    } else {
      const searchLower = searchText.toLowerCase();
      this.filteredGroupVariables = this.sourceVariables.filter(v =>
        !this.selectedVariableIds.has(v.header) &&
        !this.groupedVariableIds.has(v.header) &&
        (v.header.toLowerCase().includes(searchLower) || v.measure.toLowerCase().includes(searchLower))
      );
    }
  }

  // Toggle selection of a variable in the group modal
  toggleGroupVariableSelection(variable: any) {
    if (this.selectedGroupVariableIds.has(variable.header)) {
      this.selectedGroupVariableIds.delete(variable.header);
      this.selectedGroupVariables = this.selectedGroupVariables.filter(v => v.header !== variable.header);
    } else {
      this.selectedGroupVariableIds.add(variable.header);
      this.selectedGroupVariables.push(variable);
    }

    // Add subtle animation when selection changes
    const element = document.querySelector(`[data-variable-id="${variable.header}"]`);
    if (element) {
      element.classList.add('variable-pulse');
      setTimeout(() => {
        element?.classList.remove('variable-pulse');
      }, 300);
    }

    // Always update the preview when selection changes
    this.updateGroupPreview();
  }

  // Check if all variables in the group modal are selected
  isAllGroupVariablesSelected(): boolean {
    return this.filteredGroupVariables.length > 0 &&
      this.filteredGroupVariables.every(v => this.selectedGroupVariableIds.has(v.header));
  }

  // Toggle selection of all variables in the group modal
  toggleSelectAllGroupVariables() {
    if (this.isAllGroupVariablesSelected()) {
      this.filteredGroupVariables.forEach(variable => {
        if (this.selectedGroupVariableIds.has(variable.header)) {
          this.selectedGroupVariableIds.delete(variable.header);
        }
      });
      this.selectedGroupVariables = this.selectedGroupVariables.filter(
        v => !this.filteredGroupVariables.some(fv => fv.header === v.header)
      );
    } else {
      this.filteredGroupVariables.forEach(variable => {
        if (!this.selectedGroupVariableIds.has(variable.header)) {
          this.selectedGroupVariableIds.add(variable.header);
          this.selectedGroupVariables.push(variable);
        }
      });
    }

    // Always update the preview when selection changes
    this.updateGroupPreview();
  }

  // Check if group confirmation is valid
  canConfirmGroupVariables(): boolean {
    return this.selectedGroupVariables.length > 0;
  }

  // Update confirmGroupVariables to use the groupPreviewValues
  confirmGroupVariables() {
    if (!this.canConfirmGroupVariables()) return;

    this.isLoading = true;

    try {
      if (this.groupPreviewValues.length === 0) {
        this.updateGroupPreview();
        if (this.groupPreviewValues.length === 0) {
          this.snotifyService.warning(this.translate.translate('shared.auto_recoding.warnings.no_values_to_group'));
          this.isLoading = false;
          return;
        }
      }

      // Her seçili değişken için ayrı ayrı işlem yap
      this.selectedGroupVariables.forEach(variable => {
        // Eğer değişken zaten seçili değilse, seçili yap
        if (!this.selectedVariableIds.has(variable.header)) {
          this.selectedVariableIds.add(variable.header);
          this.selectedSourceVariables.push(variable);

          // Output değişkeni oluştur
          const timestamp = new Date().getTime() % 10000;
          this.variableOutputs.set(variable.header, {
            originalHeader: variable.header,
            newName: `${variable.header}_R${timestamp}`,
            labelTr: `${variable.label?.tr || variable.header} (Recoded)`,
            labelEn: `${variable.label?.en || variable.header} (Recoded)`
          });
        }

        // Bu değişken için value mappings oluştur veya güncelle
        this.applyGroupMappingsToVariable(variable);
      });

      // Gruplanmış değişkenleri işaretleme
      this.selectedGroupVariables.forEach(variable => {
        this.groupedVariableIds.add(variable.header);
      });

      this.updatePreview();
      this.closeGroupVariablesModal();

      this.snotifyService.success(this.translate.translate('shared.auto_recoding.success.group_created') || 'Group mappings applied successfully');

    } catch (error) {
      console.error('Error applying group mappings:', error);
      this.snotifyService.error(this.translate.translate('shared.auto_recoding.errors.group_creation_failed') || 'Failed to apply group mappings');
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  // Yeni yardımcı metod: Grup mapping'lerini belirli bir değişkene uygula
  private applyGroupMappingsToVariable(variable: any) {
    const variableHeader = variable.header;
    const columnIndex = this.data.variables_json.headers.indexOf(variableHeader);

    if (columnIndex === -1) return;

    // Bu değişkenin mevcut değerlerini al
    const variableValues = new Set();
    this.data.variables_json.data.forEach(row => {
      let value = row[columnIndex];
      if (typeof value === 'string') {
        value = value.trim();
      }
      if (!this.isValueMissing(value)) {
        variableValues.add(value);
      }
    });

    // Mevcut value mappings'leri bu değişken için temizle
    this.valueMappings = this.valueMappings.filter(mapping =>
      !mapping.sourceVariables.has(variableHeader)
    );

    // Her değer için grup preview'dan karşılık gelen yeni değeri bul ve mapping oluştur
    const newMappings: ValueMapping[] = [];

    variableValues.forEach(oldValue => {
      // Bu değer için grup preview'da karşılık gelen mapping'i bul
      const previewMapping = this.groupPreviewValues.find(preview =>
        preview.oldValue === oldValue
      );

      if (previewMapping) {
        newMappings.push({
          oldValue: oldValue,
          newValue: previewMapping.newValue, // Preview'da girilen yeni değeri kullan
          label: previewMapping.label || `Group ${previewMapping.newValue}`,
          isMissing: false,
          sourceVariables: new Set([variableHeader])
        });
      } else {
        // Eğer preview'da bu değer yoksa, orijinal değeri koru
        newMappings.push({
          oldValue: oldValue,
          newValue: oldValue,
          label: '',
          isMissing: false,
          sourceVariables: new Set([variableHeader])
        });
      }
    });

    // Missing değerler için de mapping ekle
    this.data.variables_json.data.forEach(row => {
      const value = row[columnIndex];
      if (this.isValueMissing(value)) {
        // Missing değer için mapping ekle (eğer yoksa)
        const existingMissing = newMappings.find(m => m.oldValue === value && m.isMissing);
        if (!existingMissing) {
          newMappings.push({
            oldValue: value,
            newValue: value, // Missing değerleri varsayılan olarak koru
            label: 'MISSING',
            isMissing: true,
            sourceVariables: new Set([variableHeader])
          });
        }
      }
    });

    // Yeni mapping'leri ana listeye ekle
    this.valueMappings = [...this.valueMappings, ...newMappings];
    this.filteredMappings = this.valueMappings;
  }

  // New method to directly apply grouping when confirmed
  private applyGroupingDirectly(group: any) {
    try {
      // For each variable in the group
      for (const variable of group.variables) {
        // Check if the variable is selected
        if (!this.selectedVariableIds.has(variable.header)) {
          // Select the variable first
          this.onSourceVariableSelect(variable);
        }

        // For each mapping in the group
        for (const mapping of group.mappings) {
          // Find if this variable contributes to this mapping
          if (mapping.contributingVariables.includes(variable.header)) {
            // Find mappings for this variable and value
            const existingMappings = this.valueMappings.filter(m =>
              m.sourceVariables.has(variable.header) &&
              String(m.oldValue) === String(mapping.oldValue)
            );

            if (existingMappings.length > 0) {
              // For each existing mapping
              existingMappings.forEach(m => {
                // If this mapping is used by multiple variables, create a new one for this variable
                if (m.sourceVariables.size > 1) {
                  // Create a new mapping specific to this variable
                  const newMapping = {
                    ...m,
                    newValue: mapping.newValue,
                    label: mapping.label || `Group ${mapping.newValue}`,
                    sourceVariables: new Set([variable.header])
                  };

                  // Remove this variable from the original mapping's sourceVariables
                  m.sourceVariables.delete(variable.header);

                  // Add the new mapping to the valueMappings array
                  this.valueMappings.push(newMapping);
                } else {
                  // This mapping is only used by this variable, so update it directly
                  m.newValue = mapping.newValue;
                  m.label = mapping.label || `Group ${mapping.newValue}`;
                }
              });
            }
          }
        }
      }

      // Update the preview
      this.updatePreview();
    } catch (error) {
      console.error('Error applying group directly:', error);
      throw error; // Re-throw to handle in the parent method
    }
  }

  // Add method to get group preview
  getGroupPreview(groupIndex: number): any[] {
    const group = this.groupDefinitions[groupIndex];
    if (!group) return [];
    return group.mappings;
  }

  // Completely reimplement the updateGroupPreview method
  updateGroupPreview() {
    // Reset the preview values
    this.groupPreviewValues = [];

    // If no variables are selected, just return
    if (this.selectedGroupVariables.length === 0) {
      return;
    }

    this.isLoadingGroupPreview = true;

    try {
      // Map to store unique values and which variables they appear in
      const uniqueValues = new Map<any, string[]>();

      // For each selected variable
      for (const variable of this.selectedGroupVariables) {
        const columnIndex = this.data.variables_json.headers.indexOf(variable.header);
        if (columnIndex === -1) continue;

        // Get all values for this variable
        for (const row of this.data.variables_json.data) {
          let value = row[columnIndex];

          // Normalize string values
          if (typeof value === 'string') {
            value = value.trim();
          }

          // Skip missing values
          if (this.isValueMissing(value)) continue;

          // Add to map of unique values
          if (!uniqueValues.has(value)) {
            uniqueValues.set(value, []);
          }

          // Add variable to the list of contributors for this value
          const contributors = uniqueValues.get(value);
          if (contributors && !contributors.includes(variable.header)) {
            contributors.push(variable.header);
          }
        }
      }

      // Sort values based on the selected sort order
      const entries = Array.from(uniqueValues.entries());
      entries.sort((a, b) => {
        const valueA = a[0];
        const valueB = b[0];

        // Handle numeric values
        const numA = Number(valueA);
        const numB = Number(valueB);

        if (!isNaN(numA) && !isNaN(numB)) {
          return this.groupSortOrder === 'ascending' ? numA - numB : numB - numA;
        }

        // Handle string values
        const strA = String(valueA);
        const strB = String(valueB);
        return this.groupSortOrder === 'ascending'
          ? strA.localeCompare(strB)
          : strB.localeCompare(strA);
      });

      // Create preview entries
      let counter = this.groupStartValue;

      for (const [value, variables] of entries) {
        this.groupPreviewValues.push({
          oldValue: value,
          newValue: counter++,
          contributingVariables: variables,
          label: `Group ${counter - 1}`
        });
      }
    } catch (error) {
      console.error('Error generating group preview:', error);
      this.snotifyService.error(this.translate.translate('shared.auto_recoding.errors.preview_generation_failed'));
    } finally {
      this.isLoadingGroupPreview = false;
      this.cdr.markForCheck();
    }
  }

  // Add method to update recoding preview
  updateRecodingPreview() {
    // Reset the preview values
    this.recodingPreviewValues = [];

    // If no variable is being edited, just return
    if (!this.currentEditingVariable) {
      return;
    }

    this.isLoadingRecodingPreview = true;

    try {
      // Get all mappings for the current variable
      const variableHeader = this.currentEditingVariable.header;
      const mappings = this.valueMappings.filter(
        mapping => mapping.sourceVariables.has(variableHeader)
      );

      // Sort mappings for better display
      mappings.sort((a, b) => {
        // First sort by missing status (non-missing first)
        if (a.isMissing && !b.isMissing) return 1;
        if (!a.isMissing && b.isMissing) return -1;

        // Then sort by value
        const aVal = Number(a.oldValue);
        const bVal = Number(b.oldValue);

        // If both values are valid numbers, compare them numerically
        if (!isNaN(aVal) && !isNaN(bVal)) {
          return aVal - bVal;
        }

        // If values are strings or mixed types, use string comparison
        const aStr = String(a.oldValue);
        const bStr = String(b.oldValue);
        return aStr.localeCompare(bStr);
      });

      // Create preview entries
      this.recodingPreviewValues = mappings.map(mapping => ({
        oldValue: mapping.oldValue,
        newValue: mapping.newValue,
        isMissing: mapping.isMissing,
        label: mapping.label
      }));
    } catch (error) {
      console.error('Error generating recoding preview:', error);
      this.snotifyService.error(this.translate.translate('shared.auto_recoding.errors.preview_generation_failed'));
    } finally {
      this.isLoadingRecodingPreview = false;
      this.cdr.markForCheck();
    }
  }

  // Handle changes in recoding preview values
  onRecodingPreviewValueChange() {
    if (!this.currentEditingVariable) return;

    // Update the actual value mappings with the preview values
    const variableHeader = this.currentEditingVariable.header;

    // For each preview value, update the corresponding value mapping
    this.recodingPreviewValues.forEach(previewMapping => {
      // Find the actual mappings that match this value for this variable
      const actualMappings = this.valueMappings.filter(
        mapping => mapping.sourceVariables.has(variableHeader) &&
          mapping.oldValue === previewMapping.oldValue
      );

      // If we have multiple variables sharing the same value, we need to create separate mappings
      if (actualMappings.length > 0) {
        actualMappings.forEach(mapping => {
          // If this mapping is used by multiple variables, we need to split it
          if (mapping.sourceVariables.size > 1) {
            // Create a new mapping specific to this variable
            const newMapping = {
              ...mapping,
              newValue: previewMapping.newValue,
              sourceVariables: new Set([variableHeader])
            };

            // Remove this variable from the original mapping's sourceVariables
            mapping.sourceVariables.delete(variableHeader);

            // Add the new mapping to the valueMappings array
            this.valueMappings.push(newMapping);
          } else {
            // This mapping is only used by this variable, so we can update it directly
            mapping.newValue = previewMapping.newValue;
          }
        });
      }
    });

    // Update filtered mappings
    this.filteredMappings = this.valueMappings;

    // Force change detection
    this.cdr.detectChanges();
  }

  // Replace these problematic methods with simplified versions
  // @ts-ignore - These methods are kept as stubs for future implementation
  private collectGroupValues(groupVariables: any[]): Map<any, Set<string>> {
    // Simplified implementation to avoid freezing
    return new Map();
  }

  // @ts-ignore - These methods are kept as stubs for future implementation
  private sortGroupValues(values: Map<any, Set<string>>): Array<[any, Set<string>]> {
    // Simplified implementation to avoid freezing
    return [];
  }

  // @ts-ignore - These methods are kept as stubs for future implementation
  private createGroupMappings(groupVariable: any, sortedValues: Array<[any, Set<string>]>) {
    // Simplified implementation to avoid freezing
  }

  // Component state
  showRightPanel: boolean = false;

  continueToMappingStep() {
    // First show the right panel
    this.showRightPanel = true;

    // Initialize value mappings only for variables that don't have mappings yet
    if (this.selectedSourceVariables.length > 0) {
      this.initializeValueMappingsSelectively();
    }

    // Force change detection
    this.cdr.markForCheck();
  }

  // Yeni metod: Sadece mapping'i olmayan değişkenler için mapping oluştur
  private initializeValueMappingsSelectively() {
    this.isLoading = true;
    try {
      // Her değişkeni kontrol et
      this.selectedSourceVariables.forEach(variable => {
        // Bu değişken için zaten mapping var mı kontrol et
        const hasExistingMappings = this.valueMappings.some(mapping =>
          mapping.sourceVariables.has(variable.header)
        );

        if (!hasExistingMappings) {
          // Bu değişken için yeni mapping oluştur
          this.addValueMappingsForVariable(variable);
        }
      });

      // Filtered mappings'i güncelle
      this.filteredMappings = this.valueMappings;
      this.updatePreview();

    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  toggleSection(section: string): void {
    if (this.expandedSections.has(section)) {
      // If this section is already expanded, close it
      this.expandedSections.delete(section);
    } else {
      // Clear all expanded sections first
      this.expandedSections.clear();
      // Then add only the current section
      this.expandedSections.add(section);
    }
  }

  isExpanded(section: string): boolean {
    return this.expandedSections.has(section);
  }

  animateListItems(items: any[], delayIncrement = 30) {
    return items.map((item, index) => ({
      ...item,
      animationDelay: `${index * delayIncrement}ms`
    }));
  }



}
<div [@slideInOut]="animationState" class="flex flex-col h-screen bg-neutral-100"
  *transloco="let t; read: 'shared.compute_variable'">

  <!-- Header -->
  <div class="w-full p-4 mx-auto">
    <div class="flex items-center justify-between">
      <!-- <button class="flex items-center justify-center p-2 text-2xl border-2 rounded-full">
        <ng-icon name="lucideCircleHelp"></ng-icon>
      </button> -->
      <div class="w-6 h-6"></div>
      <div class="flex items-center gap-2 text-brand-blue-600">
        <ng-icon name="heroCalculator" class="text-3xl"></ng-icon>
        <h2 class="text-2xl font-medium">{{ t('title') }}</h2>
      </div>
      <button (click)="closeModal()"
        class="p-2 text-3xl text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <ng-icon name="matCloseRound"></ng-icon>
      </button>
    </div>
  </div>

  <!-- Content Area -->
  <div class="flex flex-col flex-1 w-full px-8 overflow-auto">
    <div class="py-8 mx-auto bg-white size-full rounded-3xl">

      <div class="flex flex-col items-center justify-start flex-1 w-full gap-4 p-12 pt-0 overflow-hidden">

        <span class="block text-2xl font-medium text-center" [ngSwitch]="currentStep">
          <ng-container *ngSwitchCase="1">
            {{t('select_variable_for_compute')}}
          </ng-container>
          <ng-container *ngSwitchCase="2">
            <span class="text-brand-blue-500">{{ t('operations.' + selectedOperation + '.name') || '' }}</span>
            <span class="text-black"> {{ t('select_option') }}</span>
          </ng-container>
          <ng-container *ngSwitchCase="3">
            {{t('name_output_variable')}}
          </ng-container>
        </span>

        <!-- STEP 1: Options (varsayılan olarak görünür) -->
        <div *ngIf="currentStep === 1" class="w-full h-full">
          <div class="grid grid-cols-5 gap-6">
            <button *ngFor="let op of operations" (click)="selectOperation(op.id); goToStep(2)"
              class="flex flex-col items-center justify-center gap-1 p-12 transition-all duration-300 border rounded-3xl hover:border-brand-blue-500 hover:bg-brand-blue-100">
              <span class="text-3xl text-brand-blue-500">{{ op.icon }}</span>
              <span class="text-brand-blue-500">{{ op.nameKey | transloco }}</span>
              <span class="text-xs text-center">{{ op.descriptionKey | transloco }}</span>
            </button>
          </div>
        </div>

        <!-- Formula Preview -->
        <div *ngIf="currentStep !== 1"
          class="flex flex-col items-center justify-center w-full text-center min-h-[60px]">
          <span *ngIf="selectedVariables.length > 0" class="w-full max-w-5xl p-4 font-medium text-black">
            {{ t('compute_explanation') }}:
            <span class="w-full break-words">
              <ng-container *ngFor="let variable of selectedVariables.slice(0, 4); let i = index">
                <span class="px-2 py-0.5 text-sm rounded-full" [ngClass]="{
                      'text-amber-600 bg-amber-100': variable.measure === 'Scale',
                      'text-blue-600 bg-blue-100': variable.measure === 'Ordinal',
                      'text-purple-600 bg-purple-100': variable.measure === 'Nominal'
                    }">
                  {{ variable.header }}
                </span>
                <span *ngIf="i < selectedVariables.slice(0, 4).length - 1">, </span>
              </ng-container>
              <ng-container *ngIf="selectedVariables.length > 4">
                <span (click)="openDialog(variableDialog)" class="underline cursor-pointer">
                  {{t('and')}} +{{ selectedVariables.length - 4 }} {{t('more')}}
                </span>
              </ng-container>
            </span>
          </span>


          <!-- Native HTML5 dialog -->
          <dialog class="max-h-[calc(100vh-200px)] max-w-3xl p-4 px-6 bg-white rounded-3xl" #variableDialog>

            <div class="flex text-2xl font-medium text-center">{{t('variables_selected')}}</div>

            <ul class="overflow-auto max-h-[calc(100vh-425px)]">
              <li *ngFor="let variable of selectedVariables" class="flex flex-col p-1">
                <span class="px-2 py-1 text-sm rounded-full" [ngClass]="{
                                'text-amber-600 bg-amber-100': variable.measure === 'Scale',
                                'text-blue-600 bg-blue-100': variable.measure === 'Ordinal',
                                'text-purple-600 bg-purple-100': variable.measure === 'Nominal'
                              }">
                  {{ variable.header }}
                </span>
              </li>
            </ul>

            <div class="flex items-center justify-center w-full pt-3">
              <button (click)="closeDialog(variableDialog)"
                class="secondary-status-error-button">{{t('close')}}</button>
            </div>

          </dialog>
        </div>

        <!-- STEP 2: İşlem Seçimi -->
        <div *ngIf="currentStep === 2" class="flex flex-col items-center w-full gap-4">
          <!-- Arama Girdisi - Burada searchText kullanıyoruz ve filterVariables çağırıyoruz -->
          <div class="relative w-full max-w-4xl">
            <input type="text" [(ngModel)]="searchText" (input)="filterVariables(searchText)"
              [placeholder]="t('search_variables')"
              class="w-full py-3 pr-4 border rounded-full text border-neutral-200 pl-9 focus:ring-2 focus:ring-blue-500">
            <ng-icon name="heroMagnifyingGlass"
              class="absolute text-xl text-gray-400 transform -translate-y-1/2 left-2 top-1/2">
            </ng-icon>
          </div>


          <!-- Değişken Listesi -->
          <div class="flex justify-center w-full">
            <div class="flex-none w-full max-w-4xl overflow-y-auto border rounded-3xl h-full max-h-[calc(100vh-450px)]">
              <!-- Hiç işlem seçilmemişse -->
              <div *ngIf="!selectedOperation"
                class="flex flex-col items-center justify-center h-full p-4 text-center text-gray-500">
                <ng-icon name="heroVariable" class="mb-2 text-3xl"></ng-icon>
                <p>{{ t('no_operation') }}</p>
              </div>

              <!-- Uygun değişken yoksa -->
              <div *ngIf="selectedOperation && availableVariables.length === 0"
                class="flex flex-col items-center justify-center h-full p-4 text-center text-gray-500">
                <ng-icon name="heroVariable" class="mb-2 text-3xl"></ng-icon>
                <p>{{ t('no_compatible') }}</p>
              </div>

              <!-- Arama sonucu bulunamadıysa - burada searchText kullanıyoruz -->
              <div *ngIf="selectedOperation && availableVariables.length > 0 && filteredVariables.length === 0"
                class="flex flex-col items-center justify-center h-full p-4 text-center text-gray-500">
                <ng-icon name="heroMagnifyingGlass" class="mb-2 text-3xl"></ng-icon>
                <p>{{ t('no_results', { searchTerm: searchText }) }}</p>
              </div>

              <!-- Değişken Listesi -->
              <div *ngIf="selectedOperation && filteredVariables.length > 0"
                class="p-2 border max-h-[calc(100vh-425px)] rounded-3xl">
                <div *ngFor="let variable of filteredVariables; let i = index"
                  (click)="toggleVariable(variable, $event)"
                  [matTooltip]="!isVariableCompatible(variable).compatible ? isVariableCompatible(variable).message : ''"
                  class="flex items-center justify-between p-2 cursor-pointer rounded-2xl hover:bg-gray-50"
                  [class.bg-blue-50]="isVariableSelected(variable)"
                  [class.cursor-not-allowed]="!isVariableCompatible(variable).compatible" [attr.data-index]="i">
                  <!-- Sol kısım: Checkbox ve değişken adı -->
                  <div class="flex items-center gap-2">
                    <input type="checkbox" [checked]="isVariableSelected(variable)"
                      [disabled]="!isVariableCompatible(variable).compatible" (click)="$event.stopPropagation()"
                      class="rounded-full text-brand-green-500 size-5 focus:ring-brand-green-500">
                    <div class="flex flex-col">
                      <span class="font-medium text-gray-900">{{ variable.header }}</span>
                    </div>
                  </div>
                  <!-- Sağ kısım: Hesaplanmış badge ve ölçü tipi rozeti -->
                  <div class="flex items-center gap-2">
                    <span *ngIf="variable.computed"
                      class="px-2 py-0.5 text-xs bg-green-100 text-green-600 rounded-full">
                      {{ t('computed_badge') }}
                    </span>
                    <span class="px-2 py-0.5 text-sm rounded-full" [ngClass]="{
                   'bg-amber-100 text-amber-600': variable.measure === 'Scale',
                   'bg-blue-100 text-blue-600': variable.measure === 'Ordinal',
                   'bg-purple-100 text-purple-600': variable.measure === 'Nominal'
                 }">
                      {{ variable.measure }}
                    </span>
                  </div>
                </div>
              </div>

            </div>
          </div>


        </div>

        <!-- STEP 3: Dönüştürme İşlemleri -->
        <div *ngIf="currentStep === 3"
          class="flex flex-col w-full h-full max-w-3xl gap-4 p-4 mx-auto overflow-hidden bg-brand-blue-50 rounded-3xl">
          <!-- Üst kısım: Açıklama metni -->


          <!-- Dönüştürme işlemi değilse -->
          <div *ngIf="selectedOperation && !isTransformationOperation()">
            <!-- <h3 class="mb-2 text-sm font-medium text-gray-600">{{ t('output_variable') }}</h3> -->
            <input type="text" [(ngModel)]="newVariableName" [placeholder]="t('new_name')"
              class="w-full p-2 bg-white border rounded-3xl border-neutral-200 focus:ring-2 focus:ring-blue-500">
          </div>

          <!-- Dönüştürme işlemi ise: Listenin taşması durumunda scroll ekledik -->
          <div *ngIf="selectedVariables.length > 0 && isTransformationOperation()" class="flex-1 overflow-auto">
            <h3 class="mb-2 text-sm font-medium text-gray-600">{{ t('transformation_names.title') }}</h3>
            <div class="space-y-2 max-h-[calc(100vh-475px)] overflow-auto">
              <div *ngFor="let item of transformationVariables; let i = index" class="p-2 rounded-3xl bg-gray-50">
                <div class="flex items-center gap-2">
                  <span class="text-sm text-gray-500 truncate">{{ item.sourceVariable.header }}</span>
                  <ng-icon name="heroArrowLongRight" class="text-gray-400"></ng-icon>
                  <input type="text" [(ngModel)]="item.newName"
                    [placeholder]="t('transformation_names.new_name', { variable: item.sourceVariable.header })"
                    class="flex-1 p-2 border rounded-3xl border-neutral-200 min-w-32">
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>

  <!-- Footer Navigation -->
  <div class="w-full max-w-4xl py-4 mx-auto">
    <div class="flex items-center justify-between gap-3">
      <button (click)="goToStep(currentStep - 1)" *ngIf="currentStep === 2 || currentStep === 3"
        class="secondary-blue-button">
        Geri
      </button>
      <button *ngIf="selectedVariables.length > 0 && currentStep === 2" (click)="clearSelectedVariables()"
        class="secondary-status-error-button">
        {{ t('clear_selection') }}
      </button>
      <!-- Devam Et butonu için disable koşulunu düzelttik -->
      <button *ngIf="currentStep !== 1" (click)="currentStep === 2 ? goToStep(3) : computeVariable()"
        [disabled]="currentStep === 2 ? selectedVariables.length === 0 : !canComputeVariable"
        class="primary-blue-button">
        {{ currentStep === 2 ? 'Devam Et' : 'Değişken Oluştur' }}
      </button>
    </div>
  </div>

</div>
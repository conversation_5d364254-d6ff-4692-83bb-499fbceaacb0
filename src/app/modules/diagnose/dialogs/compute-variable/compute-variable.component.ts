import { animate, state, style, transition, trigger } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, HostListener, Inject, OnDestroy, OnInit } from '@angular/core';
import { SnotifyService } from 'ng-alt-snotify';
import { Subject } from 'rxjs';
import { ComputeVariableService } from '../../services/compute-variable.service';
import { TranslocoService } from '@ngneat/transloco';

interface VariableOutput {
  originalHeader: string;
  newName: string;
  labelTr: string;
  labelEn: string;
}

@Component({
  selector: 'app-compute-variable',
  templateUrl: './compute-variable.component.html',
  styleUrls: ['./compute-variable.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class ComputeVariableComponent implements OnInit, OnDestroy {
  // Animation state
  animationState = 'in';

  // Component state
  newVariableName = '';
  selectedVariables: any[] = [];
  selectedOperation = '';
  previewData: any[] | null = null;

  isOperationDropdownOpen = false;
  transformationVariables: any[] = [];

  // Search için değişken adını düzelttik - HTML ile tutarlı hale getirdik
  searchText = '';
  filteredVariables: any[] = [];
  lastSelectedIndex: number | null = null;

  // Cleanup subject
  private destroy$ = new Subject<void>();

  // Computed properties
  get availableVariables() {
    if (!this.selectedOperation) {
      return [];
    }

    const operation = this.operations.find(op => op.id === this.selectedOperation)
    if (!operation) return [];

    // Sayısal işlemler için sadece numeric değişkenleri göster
    const numericOperations = ['add', 'subtract', 'multiply', 'divide', 'mean', 'log', 'ln', 'square', 'sqrt'];
    if (numericOperations.includes(this.selectedOperation)) {
      return this.data.variables_json.variable_list.filter(variable =>
        this.checkVariableType(variable) === 'numeric'
      );
    }

    // Concat işlemi için tüm değişkenleri göster
    if (this.selectedOperation === 'concat') {
      return this.data.variables_json.variable_list;
    }

    return [];
  }

  get operations() {
    return this.computeService.getOperations();
  }

  // Add new computed properties for visibility control
  get canShowPreviewButton(): boolean {
    return this.selectedOperation &&
      this.selectedVariables.length > 0 &&
      (!this.previewData || this.hasChangedSinceLastPreview);
  }

  get canComputeVariable(): boolean {
    return this.selectedOperation &&
      this.selectedVariables.length > 0 &&
      (this.isTransformationOperation() ?
        this.isValidTransformationNames() :
        !!this.newVariableName.trim());
  }

  private hasChangedSinceLastPreview = false;

  // Add new property for variable outputs
  variableOutputs: Map<string, VariableOutput> = new Map();

  constructor(
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private computeService: ComputeVariableService,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService
  ) { }

  ngOnInit(): void {
    this.computeService.setData(this.data.variables_json);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  isVariableSelected(variable: any): boolean {
    return this.selectedVariables.includes(variable);
  }

  // Search fonksiyonunu düzelttik - artık searchText ile çalışıyor
  filterVariables(searchTerm: string): void {
    const term = searchTerm.toLowerCase().trim();

    if (!term) {
      this.filteredVariables = [...this.availableVariables];
      return;
    }

    const filtered = this.availableVariables.filter(variable =>
      variable.header.toLowerCase().includes(term)
    );

    this.filteredVariables = filtered;
  }

  // Eski onSearch metodunu kaldırdık, yerine filterVariables kullanıyoruz

  toggleVariable(variable: any, event: MouseEvent | KeyboardEvent): void {
    const operation = this.operations.find(op => op.id === this.selectedOperation);
    if (!operation) return;

    const compatibility = this.isVariableCompatible(variable);
    if (!compatibility.compatible) {
      this.snotifyService.warning(compatibility.message);
      return;
    }

    const currentIndex = this.filteredVariables.indexOf(variable);
    const isKeyboardEvent = event instanceof KeyboardEvent;
    const isShiftPressed = event.shiftKey;

    // Handle shift selection for both mouse and keyboard
    if (isShiftPressed && this.lastSelectedIndex !== null) {
      const start = Math.min(this.lastSelectedIndex, currentIndex);
      const end = Math.max(this.lastSelectedIndex, currentIndex);

      const variablesToToggle = this.filteredVariables.slice(start, end + 1)

      if (this.canAddMultipleVariables(variablesToToggle, operation)) {
        variablesToToggle.forEach(v => {
          if (!this.isVariableSelected(v)) {
            this.addVariable(v, operation);
          }
        });
      } else {
        this.snotifyService.warning(
          this.transloco.translate('shared.compute_variable.messages.cannot_select_all')
        );
      }
    } else if (isKeyboardEvent && event.key === 'Enter' || !isKeyboardEvent) {
      // Handle Enter key or mouse click
      if (this.isVariableSelected(variable)) {
        this.removeVariable(variable);
      } else if (this.canAddVariable(variable, operation)) {
        this.addVariable(variable, operation);
      }
    }

    this.lastSelectedIndex = currentIndex;
    this.hasChangedSinceLastPreview = true;

    if (this.hasActivePreview && this.isValidForPreview()) {
      this.generatePreview();
    }
  }

  private isValidForPreview(): boolean {
    // Özel durumlar için kontroller
    if (this.selectedOperation === 'divide' && this.selectedVariables.length !== 2) {
      return false;
    }

    return this.selectedOperation &&
      this.selectedVariables.length > 0
    // && this.expression?.length > 0;
  }

  private canAddMultipleVariables(variables: any[], operation: any): boolean {
    const potentialTotal = this.selectedVariables.length +
      variables.filter(v => !this.isVariableSelected(v)).length;

    if (operation.maxVariables && potentialTotal > operation.maxVariables) {
      return false;
    }

    if (operation.requiresSameType && this.selectedVariables.length > 0) {
      return variables.every(v => v.measure === this.selectedVariables[0].measure);
    }

    return true;
  }

  private canAddVariable(variable: any, operation: any): boolean {
    if (operation.id === 'divide' && this.selectedVariables.length >= 2) {
      this.showValidationMessage('maxVariables', { max: 2 });
      return false;
    }

    if (operation.maxVariables &&
      this.selectedVariables.length >= operation.maxVariables) {
      this.showValidationMessage('maxVariables', { max: operation.maxVariables });
      return false;
    }

    if (operation.requiresSameType && this.selectedVariables.length > 0) {
      if (variable.measure !== this.selectedVariables[0].measure) {
        this.showValidationMessage('sameType');
        return false;
      }
    }

    return true;
  }

  // İşlem türüne göre ön ek döndüren yeni metod
  private getOperationPrefix(operationId: string): string {
    switch (operationId) {
      case 'add': return 'sum';
      case 'subtract': return 'diff';
      case 'multiply': return 'mult';
      case 'divide': return 'div';
      case 'mean': return 'mean';
      case 'log': return 'log';
      case 'ln': return 'ln';
      case 'square': return 'square';
      case 'sqrt': return 'sqrt';
      case 'concat': return 'concat';
      default: return operationId;
    }
  }

  // 50 karakter sınırlamasını uygulayan ve Türkçe karakterleri değiştiren yeni metod
  private limitVariableName(name: string): string {
    // Türkçe karakterleri değiştir
    let cleanName = name.replace(/ç/g, 'c').replace(/ğ/g, 'g').replace(/ı/g, 'i')
      .replace(/ö/g, 'o').replace(/ş/g, 's').replace(/ü/g, 'u');

    // Özel karakterleri temizle
    cleanName = cleanName.replace(/[&]/g, '');

    // 50 karakter sınırı
    return cleanName.substring(0, 50);
  }

  // Benzer isimli değişkenler için ortak ön ek kontrol eden metod
  private findCommonPrefix(): string | null {
    if (this.selectedVariables.length < 2) return null;

    const headers = this.selectedVariables.map(v => v.header);
    let potentialPrefix = '';

    // En kısa değişken adını bul
    const minLength = Math.min(...headers.map(h => h.length));

    // Ortak önek kontrolü
    for (let i = 0; i < minLength; i++) {
      const char = headers[0][i];
      if (headers.every(h => h[i] === char)) {
        potentialPrefix += char;
      } else {
        break;
      }
    }

    // En az 2 karakter uzunluğunda ortak önek varsa ve sayısal son ek içeriyorsa kullan
    if (potentialPrefix.length >= 2 && headers.every(h => /\d+$/.test(h.substring(potentialPrefix.length)))) {
      return potentialPrefix;
    }

    return null;
  }

  private addVariable(variable: any, operation: any) {
    this.selectedVariables.push(variable);

    // Kılavuza göre işlem türüne bağlı olarak varsayılan değişken adı oluştur
    let defaultName = '';
    const operationPrefix = this.getOperationPrefix(operation.id);

    if (operation.isTransformation || operation.singleVariable) {
      // Tek değişkenli işlemler için: prefix_değişken
      defaultName = `${operationPrefix}_${variable.header}`;
    } else if (this.selectedVariables.length > 2) {
      // Üç veya daha fazla değişken için: prefix_değişken1_değişken2_etc
      const commonPrefix = this.findCommonPrefix();
      if (commonPrefix) {
        // Benzer isimli değişkenler için
        defaultName = `${operationPrefix}_${commonPrefix}`;
      } else {
        defaultName = `${operationPrefix}_${this.selectedVariables[0].header}_${this.selectedVariables[1].header}_etc`;
      }
    } else if (this.selectedVariables.length === 2) {
      // İki değişken için: prefix_değişken1_değişken2
      defaultName = `${operationPrefix}_${this.selectedVariables[0].header}_${this.selectedVariables[1].header}`;
    } else {
      // Tek değişken seçildiği durum
      defaultName = `${operationPrefix}_${variable.header}`;
    }

    // Değişken adı için karakter sınırlaması ve benzersiz isim kontrolü
    defaultName = this.limitVariableName(defaultName);
    defaultName = this.ensureUniqueVariableName(defaultName);

    // Etiketler için değişken adıyla aynı olmalı
    const defaultLabelTr = defaultName;
    const defaultLabelEn = defaultName;

    if (operation.isTransformation || operation.singleVariable) {
      this.transformationVariables.push({
        sourceVariable: variable,
        newName: defaultName
      });

      // Varsayılan etiketleri değişken adıyla aynı ayarla
      this.variableOutputs.set(variable.header, {
        originalHeader: variable.header,
        newName: defaultName,
        labelTr: defaultName,
        labelEn: defaultName
      });
    } else {
      // Normal işlemler için
      this.newVariableName = defaultName;
      this.variableOutputs.set(variable.header, {
        originalHeader: variable.header,
        newName: defaultName,
        labelTr: defaultLabelTr,
        labelEn: defaultLabelEn
      });
    }

    // Preview'ı güncelle
    this.hasChangedSinceLastPreview = true;
  }

  private removeVariable(variable: any) {
    const index = this.selectedVariables.indexOf(variable);
    if (index > -1) {
      this.selectedVariables.splice(index, 1);

      // Transformasyon değişkenlerini güncelle
      this.transformationVariables = this.transformationVariables.filter(
        v => v.sourceVariable.header !== variable.header
      );

      // Değişkene ait çıktı bilgilerini temizle
      this.variableOutputs.delete(variable.header);

      // Eğer hiç değişken kalmadıysa yeni değişken adını da temizle
      if (this.selectedVariables.length === 0) {
        this.newVariableName = '';
      } else {
        // Değişken kaldırıldıktan sonra isim ve etiketleri güncelle
        this.updateVariableNameAfterRemoval();
      }

      // Eğer aktif bir preview varsa ve hala geçerli değişkenler varsa
      if (this.hasActivePreview && this.isValidForPreview()) {
        this.generatePreview();
      } else if (this.selectedVariables.length === 0) {
        this.clearPreview();
      }
    }
  }

  // Değişken kaldırıldıktan sonra değişken adını güncelleme metodu
  private updateVariableNameAfterRemoval(): void {
    const operation = this.operations.find(op => op.id === this.selectedOperation);
    if (!operation) return;

    const operationPrefix = this.getOperationPrefix(operation.id);

    if (operation.isTransformation || operation.singleVariable) {
      // Transformasyon işlemleri için her değişken kendi adını zaten alır
      return;
    } else if (this.selectedVariables.length > 2) {
      // Üç veya daha fazla değişken için
      const commonPrefix = this.findCommonPrefix();
      if (commonPrefix) {
        this.newVariableName = `${operationPrefix}_${commonPrefix}`;
      } else {
        this.newVariableName = `${operationPrefix}_${this.selectedVariables[0].header}_${this.selectedVariables[1].header}_etc`;
      }
    } else if (this.selectedVariables.length === 2) {
      // İki değişken için
      this.newVariableName = `${operationPrefix}_${this.selectedVariables[0].header}_${this.selectedVariables[1].header}`;
    } else if (this.selectedVariables.length === 1) {
      // Tek değişken için
      this.newVariableName = `${operationPrefix}_${this.selectedVariables[0].header}`;
    }

    // Karakter sınırlaması ve benzersiz isim kontrolü
    this.newVariableName = this.limitVariableName(this.newVariableName);
    this.newVariableName = this.ensureUniqueVariableName(this.newVariableName);

    // Her değişken için çıktı bilgilerini güncelle - değişkan adıyla aynı
    this.selectedVariables.forEach(v => {
      this.variableOutputs.set(v.header, {
        originalHeader: v.header,
        newName: this.newVariableName,
        labelTr: this.newVariableName,
        labelEn: this.newVariableName
      });
    });
  }

  selectOperation(operationId: string): void {
    // Eğer bölme işlemi seçildiyse ve 2'den fazla değişken varsa seçili değişkenleri temizle
    if (operationId === 'divide') {
      if (this.selectedVariables.length > 2) {
        this.selectedVariables = [];
        this.transformationVariables = [];
        this.snotifyService.info(this.transloco.translate('shared.compute_variable.messages.division_operation_limit'));
      }
    }

    this.selectedOperation = operationId;
    this.isOperationDropdownOpen = false;

    // Reset selected variables when operation changes
    this.selectedVariables = [];
    this.transformationVariables = [];

    // Reset search and filtered variables - Bu çok önemli!
    this.searchText = '';
    this.filteredVariables = [...this.availableVariables];

    const operation = this.operations.find(op => op.id === operationId);
    if (operation?.isTransformation || operation?.singleVariable) {
      this.transformationVariables = this.selectedVariables.map(variable => {
        const operationPrefix = this.getOperationPrefix(operation.id);
        const variableName = `${operationPrefix}_${variable.header}`;
        const limitedName = this.limitVariableName(variableName);
        const uniqueName = this.ensureUniqueVariableName(limitedName);

        this.variableOutputs.set(variable.header, {
          originalHeader: variable.header,
          newName: limitedName,
          labelTr: limitedName,
          labelEn: limitedName
        });

        return {
          sourceVariable: variable,
          newName: uniqueName
        };
      });
    } else {
      this.transformationVariables = [];
    }

    // this.updateExpression();
    this.hasChangedSinceLastPreview = true;
    this.previewData = null; // Preview'ı temizle
    this.hasActivePreview = false; // Preview durumunu sıfırla
  }

  generatePreview(): void {
    if (!this.isValidForPreview()) return;

    try {
      this.previewData = this.computeService.calculatePreview(
        this.selectedVariables,
        this.selectedOperation
      );
      this.hasChangedSinceLastPreview = false;
      this.hasActivePreview = true; // Preview aktif olduğunu işaretle
    } catch (error) {
      console.error('Preview generation error:', error);
      this.snotifyService.error(this.transloco.translate('shared.compute_variable.messages.preview_error'));
      this.previewData = null;
      this.hasActivePreview = false;
    }
  }

  get operation() {
    return this.operations.find(op => op.id === this.selectedOperation);
  }

  clearSelectedVariables(): void {
    this.selectedVariables = [];
    this.previewData = null;
  }

  private hasActivePreview = false;
  computeVariable(): void {
    if (!this.validateComputeVariable()) return;

    try {
      let result;
      const operation = this.operations.find(op => op.id === this.selectedOperation);

      if (operation?.isTransformation || operation?.singleVariable) {
        // Her bir değişken için transformasyon işlemleri
        result = this.computeService.computeNewVariable(
          this.transformationVariables.map(v => ({
            ...v,
            labelTr: this.variableOutputs.get(v.sourceVariable.header)?.labelTr,
            labelEn: this.variableOutputs.get(v.sourceVariable.header)?.labelEn
          })),
          this.selectedOperation,
          this.selectedVariables
        );
      } else {
        // Normal çoklu değişken işlemleri
        result = this.computeService.computeNewVariable(
          this.newVariableName,
          this.selectedOperation,
          this.selectedVariables,
          {
            labelTr: this.newVariableName, // Doğrudan değişken adını kullan
            labelEn: this.newVariableName  // Doğrudan değişken adını kullan
          }
        );
      }

      // Data güncelleme
      this.data.variables_json = result;
      this.computeService.setData(result);

      // Preview'ı temizle ve seçimleri sıfırla
      this.clearPreview();
      this.resetSelections();

      // Başarı mesajı
      this.snotifyService.success(
        this.transloco.translate('shared.compute_variable.messages.variable_created')
      );
    } catch (error) {
      console.error('Compute error:', error);
      this.snotifyService.error(
        this.transloco.translate('shared.compute_variable.messages.compute_error')
      );
    }
    this.closeModal();
  }

  getOperationExplanation(): string {
    if (!this.selectedOperation || this.selectedVariables.length === 0) return '';

    const operation = this.operations.find(op => op.id === this.selectedOperation);
    if (!operation) return '';

    const variableNames = this.selectedVariables.map(v => v.header).join(', ');

    switch (operation.id) {
      case 'add':
        return this.transloco.translate('shared.compute_variable.messages.add', { variables: variableNames });
      case 'subtract':
        return this.transloco.translate('shared.compute_variable.messages.subtract', { variables: variableNames });
      case 'multiply':
        return this.transloco.translate('shared.compute_variable.messages.multiply', { variables: variableNames });
      case 'divide':
        return this.transloco.translate('shared.compute_variable.messages.divide', { variable1: this.selectedVariables[0]?.header, variable2: this.selectedVariables[1]?.header });
      case 'mean':
        return this.transloco.translate('shared.compute_variable.messages.mean', { variables: variableNames });
      case 'log':
        return this.transloco.translate('shared.compute_variable.messages.log', { variables: variableNames });
      case 'ln':
        return this.transloco.translate('shared.compute_variable.messages.ln', { variables: variableNames });
      case 'square':
        return this.transloco.translate('shared.compute_variable.messages.square', { variables: variableNames });
      case 'sqrt':
        return this.transloco.translate('shared.compute_variable.messages.sqrt', { variables: variableNames });
      case 'concat':
        return this.transloco.translate('shared.compute_variable.messages.concat', { variables: variableNames });
      default:
        return '';
    }
  }

  toggleOperationDropdown(): void {
    this.isOperationDropdownOpen = !this.isOperationDropdownOpen;
  }

  resetSelections(): void {
    this.selectedVariables = [];
    this.newVariableName = '';
    this.selectedOperation = '';
    // this.expression = '';
    this.transformationVariables = [];
    this.previewData = null;
  }

  // Update variable selection to handle labels
  onSourceVariableSelect(variable: any) {
    if (this.isVariableSelected(variable)) {
      const index = this.selectedVariables.indexOf(variable);
      this.selectedVariables.splice(index, 1);
      this.transformationVariables = this.transformationVariables.filter(
        (v) => v.sourceVariable.header !== variable.header
      );
      this.variableOutputs.delete(variable.header);
    } else {
      if (this.canAddVariable(variable, this.operations.find(op => op.id === this.selectedOperation))) {
        this.selectedVariables.push(variable);
        if (this.operation?.isTransformation || this.operation?.singleVariable) {
          const operationPrefix = this.getOperationPrefix(this.selectedOperation);
          const newName = `${operationPrefix}_${variable.header}`;

          this.transformationVariables.push({
            sourceVariable: variable,
            newName: this.limitVariableName(newName)
          });

          // Initialize variable output with default values, labels should match the variable name
          const limitedName = this.limitVariableName(newName);
          this.variableOutputs.set(variable.header, {
            originalHeader: variable.header,
            newName: limitedName,
            labelTr: limitedName,
            labelEn: limitedName
          });
        }
      }
    }

    this.hasChangedSinceLastPreview = true;
    this.lastSelectedIndex = this.availableVariables.indexOf(variable);

    if (this.hasActivePreview && this.isValidForPreview()) {
      this.generatePreview();
    }
  }

  closeModal(result?: any): void {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(this.data.variables_json), 300);
  }

  isTransformationOperation(): boolean {
    return this.computeService.isTransformationOperation(this.selectedOperation);
  }

  validateComputeVariable(): boolean {
    return this.computeService.validateOperation(
      this.selectedOperation,
      this.selectedVariables,
      this.newVariableName,
      this.transformationVariables
    );
  }

  getOperationName(): string {
    return this.computeService.getOperationName(this.selectedOperation);
  }

  openDialog(dialog: HTMLDialogElement): void {
    dialog.showModal();
  }

  closeDialog(dialog: HTMLDialogElement): void {
    dialog.close();
  }

  getOperationLabel(): string {
    if (!this.selectedOperation) return '';
    const translationKey = `shared.compute_variable.messages.${this.selectedOperation}`;
    const message = this.transloco.translate(translationKey);
    // Mesaj "Karesini alma: {{variables}}" şeklinde tanımlı ise, ":" karakterine göre bölüp sadece açıklama kısmını alabilirsiniz.
    return message.split(':')[0];
  }

  getSourceVariablesDisplay(sourceVariables: string[]): string {
    return sourceVariables?.join(', ') || 'No source variables';
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const dropdown = document.querySelector('.operation-dropdown');
    const target = event.target as HTMLElement;

    if (dropdown && !dropdown.contains(target)) {
      this.isOperationDropdownOpen = false;
    }
  }

  // Add this method to clear preview
  clearPreview(): void {
    this.previewData = null;
    this.hasActivePreview = false;
  }

  private isValidTransformationNames(): boolean {
    return this.transformationVariables.every(v => !!v.newName.trim());
  }

  private showValidationMessage(messageKey: string, params = {}): void {
    this.snotifyService.warning(
      this.transloco.translate(`shared.compute_variable.messages.${messageKey}`, params)
    );
  }

  // Yeni yardımcı metodlar
  private isNumericValue(value: any): boolean {
    return !isNaN(value) && value !== '' && value !== null;
  }

  private isStringValue(value: any): boolean {
    return typeof value === 'string' && value !== '';
  }

  private checkVariableType(variable: any): 'numeric' | 'string' | 'mixed' {
    // Get header index
    const headerIndex = this.data.variables_json.headers.indexOf(variable.header);
    if (headerIndex === -1) return 'string';

    // Get column data
    const columnData = this.data.variables_json.data.map(row => row[headerIndex]);

    let hasNumeric = false;
    let hasString = false;

    for (const value of columnData) {
      // Boş değerleri atla
      if (value === null || value === undefined || value === '') {
        continue;
      }

      const numValue = Number(value);
      if (!isNaN(numValue)) {
        hasNumeric = true;
      } else {
        hasString = true;
      }

      if (hasString) {
        return 'string';  // Herhangi bir string değer bulunursa hemen string olarak işaretle
      }
    }

    return hasNumeric ? 'numeric' : 'string';
  }

  // Değişken uyumluluğunu kontrol eden yeni metod
  isVariableCompatible(variable: any): { compatible: boolean; message: string } {
    const variableType = this.checkVariableType(variable);

    // Sayısal işlemler için kontroller
    const numericOperations = ['add', 'subtract', 'multiply', 'divide', 'mean', 'log', 'ln', 'square', 'sqrt'];
    if (numericOperations.includes(this.selectedOperation)) {
      if (variableType !== 'numeric') {
        return {
          compatible: false,
          message: this.transloco.translate('shared.compute_variable.messages.requires_numeric')
        };
      }
    }
    // Metin işlemleri için kontroller - concat her türlü değeri kabul eder
    if (this.selectedOperation === 'concat') {
      return { compatible: true, message: '' };
    }

    return { compatible: true, message: '' };
  }

  private ensureUniqueVariableName(baseName: string): string {
    // Mevcut değişken adlarını al
    const existingNames = this.data.variables_json.variable_list.map(v => v.header);

    if (!existingNames.includes(baseName)) {
      return baseName; // Çakışma yoksa direkt adı kullan
    }

    // Çakışma varsa sonuna sayı ekle (1, 2, 3...)
    let counter = 1;
    let newName = `${baseName}${counter}`;

    while (existingNames.includes(newName)) {
      counter++;
      newName = `${baseName}${counter}`;
    }

    return newName;
  }

  getVariableIncompatibilityReason(variable: any): string {
    const variableType = this.checkVariableType(variable);
    const numericOperations = ['add', 'subtract', 'multiply', 'divide', 'mean', 'log', 'ln', 'square', 'sqrt'];

    if (numericOperations.includes(this.selectedOperation)) {
      if (variableType === 'string') {
        return this.transloco.translate('shared.compute_variable.messages.contains_text_values');
      } else if (variableType === 'mixed') {
        return this.transloco.translate('shared.compute_variable.messages.contains_mixed_values');
      }
    }

    return '';
  }

  // Add new method to update variable outputs
  updateVariableOutput(header: string, field: keyof VariableOutput, value: string) {
    const output = this.variableOutputs.get(header);
    if (output) {
      this.variableOutputs.set(header, { ...output, [field]: value });
    }
  }

  currentStep: number = 1;

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  nextStep() {
    if (this.currentStep < 3) {
      this.currentStep++;
    }
  }

  goToStep(step: number): void {
    this.currentStep = step;
  }
}
// data-view.component.scss - S<PERSON>tun genişliği ve font boyut<PERSON> değişiklikleri ile

:host ::ng-deep {
  // Core table styling
  .handsontable .htCore {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;

    td {
      padding: 8px 10px !important; // Make this match the header padding
      border-top: none !important;
      border-bottom: none !important;
      border-left: 1px solid #e2e8f0;
      border-right: 1px solid #e2e8f0;
      font-size: 15px; // Hücre font büyük<PERSON><PERSON><PERSON><PERSON> artırıldı (13px'ten 15px'e)
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 36px; // Yükseklik artırıldı (32px'ten 36px'e) - font b<PERSON>yüklüğüne uyum için
      text-align: left;
      vertical-align: middle;
font-family: 'Baloo 2', sans-serif !important;

      &.numeric {
        text-align: right;
      }
    }

    // Header styling
    th {
      padding: 8px 10px !important;
      font-size: 15px;
      font-weight: 600;
      background-color: #f5f8ff;
      color: #0f2c6e;
      position: sticky;
      top: 0;
      z-index: 10;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 32px !important; // Match the cell height
      text-align: left;
      vertical-align: middle;

      // Fix for the header content alignment
      .colHeader {
        display: flex !important;
        align-items: center !important;
        height: 100% !important;
      }
    }
  }
}

.htContextMenu .htSubmenu {
  .compute-info {
    padding: 8px 12px;
    color: #666;
    font-size: 0.9em;

    .title {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .source-vars {
      margin-left: 8px;
      color: #0066cc;
    }
  }

  .auto-update-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;

    input[type="checkbox"] {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background-color: #f3f4f6;
    }
  }
}

.handsontable-container {
  border-radius: 1.5rem;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

// Custom scrollbar styling
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

:host ::ng-deep {
  // Core table styling
  .handsontable .htCore {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;

    td {
      padding: 8px 10px !important; // Header ile aynı padding
      border-top: none !important;
      border-bottom: none !important;
      font-size: 15px; // Hücre font büyüklüğü artırıldı (13px'ten 15px'e)
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 36px; // Yükseklik artırıldı (32px'ten 36px'e) - font büyüklüğüne uyum için
      text-align: left;
      vertical-align: middle;
font-family: 'Baloo 2', sans-serif !important;

      &.numeric {
        text-align: right;
      }
    }

    // Header styling
    th {
      padding: 8px 10px !important;
      border-top: none !important;
      border-bottom: 1px solid #cbd5e1 !important;
      font-size: 15px;
      font-weight: 600;
      background-color: #f5f8ff;
      color: #0f2c6e;
      position: sticky;
      top: 0;
      z-index: 10;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 32px !important; // Hücre ile aynı yükseklik
      text-align: left;
      vertical-align: middle;

      // Header içeriği için düzeltme
      .colHeader {
        display: flex !important;
        align-items: center !important;
        height: 100% !important;
      }
    }

    // Row styling - Zebra deseni için
    tbody tr {
      transition-property: background-color;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 200ms;
      height: 36px; // Satır yüksekliği artırıldı (32px'ten 36px'e)

      &:nth-child(even) td {
        background-color: #f9fafb;
      }

      &:hover td {
        background-color: #f5f5f5;
      }
    }
  }

  // Column resizer styling
  .handsontable .manualColumnResizer {
    cursor: col-resize;

    &:hover {
      background-color: #0066cc;
    }
  }

  // // Selection styling
  // .handsontable .htCore td.current,
  // .handsontable .htCore td.area {
  //   background-color: rgba(63, 131, 248, 0.1) !important;
  //   border: 1px solid #3f83f8 !important;
  // }

  // Sort indicators
  .handsontable .colHeader.columnSorting {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      right: 8px;
      top: 50%;
      margin-top: -4px;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
    }

    &.ascending::after {
      border-bottom: 5px solid #0f2c6e;
      border-top: none;
    }

    &.descending::after {
      border-top: 5px solid #0f2c6e;
      border-bottom: none;
    }
  }

  // Filter indicators
  .columnFilter {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      right: 8px;
      top: 50%;
      margin-top: -6px;
      width: 12px;
      height: 12px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%230F2C6E'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z' /%3E%3C/svg%3E");
      background-size: contain;
      background-repeat: no-repeat;
    }

    &.active::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230066cc'%3E%3Cpath fill-rule='evenodd' d='M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z' clip-rule='evenodd' /%3E%3C/svg%3E");
    }
  }

  // Cell types styling
  .handsontable .htCore {
    // Scale/numeric cells
    td.htNumeric {
      text-align: right;
    }

    // Different data type indicators
    .cellType {
      position: relative;
      padding-right: 24px !important;

      &::after {
        content: attr(data-type);
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 10px;
        padding: 2px 4px;
        border-radius: 9999px;
        background-color: #e2e8f0;
        color: #475569;
      }

      &.scale::after {
        background-color: #dbeafe;
        color: #1e40af;
      }

      &.categorical::after {
        background-color: #d1fae5;
        color: #065f46;
      }

      &.temporal::after {
        background-color: #fef3c7;
        color: #92400e;
      }
    }
  }

  // Loading animation
  @keyframes pulse {
    0%,
    100% {
      opacity: 0.5;
    }
    50% {
      opacity: 0.8;
    }
  }

  .handsontable .htCore .animate-pulse td {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    background-color: #f3f4f6 !important;
  }
}

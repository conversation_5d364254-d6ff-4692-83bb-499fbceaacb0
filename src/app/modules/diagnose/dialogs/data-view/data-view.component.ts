import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import Handsontable from 'handsontable';
import { HotTableRegisterer } from '@handsontable/angular';
import { HyperFormula } from 'hyperformula';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
import { ComputeVariableService } from '../../../../modules/diagnose/services/compute-variable.service';

@Component({
  selector: 'app-data-view',
  templateUrl: './data-view.component.html',
  styleUrls: ['./data-view.component.scss']
})
export class DataViewComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() data: any;
  @Input() showLabels: boolean = false;
  @Output() dataChange = new EventEmitter<any>();
  @Input() readonly: boolean = false;
  private isTableInitialized = false;
  private hotRegisterer = new HotTableRegisterer();
  id = 'dataTable';
  private isUpdatingTable = false;
  private isProcessingChanges = false;

  constructor(

    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
    private computeService: ComputeVariableService
  ) { }

  ngOnInit() {
    if (this.data) {
      // İlk data geldiğinde bekle
      setTimeout(() => {
        this.initializeTable();

        // Set initial headers based on showLabels flag
        if (this.showLabels) {
          this.toggleColumnDisplay(); // This will flip it once and apply labels
          this.toggleColumnDisplay(); // This will flip it back to true and reapply
        }
      });
    }
  }

  ngAfterViewInit() {
    if (this.data && !this.isTableInitialized) {
      const hot = this.hotRegisterer.getInstance(this.id);
      if (hot) {
        this.initializeTable();
        hot.render();

        // Allow time for the table to initialize completely
        setTimeout(() => {
          // Ensure headers are in the correct state
          if (this.showLabels) {
            this.toggleColumnDisplay(); // This will flip it once and apply labels
            this.toggleColumnDisplay(); // This will flip it back to true and reapply
          }
        }, 100);
      }
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['readonly']) {
      setTimeout(() => {
        const hot = this.hotRegisterer.getInstance(this.id);
        if (hot) {
          // Tablo ayarlarını güncelle
          hot.updateSettings({
            readOnly: this.readonly,
            cells: (row, col) => ({
              readOnly: this.readonly
            })
          });
          hot.render();
        }
      });
    }
    
    if (changes['data'] && !changes['data'].firstChange) {
      const hot = this.hotRegisterer.getInstance(this.id);
      if (hot && hot.rootElement) {
        // Tamamen yeni tablo oluştur
        this.isTableInitialized = false;
        this.initializeTable();
      }
    }
  }

  private formatNumericValue(value: any): any {
    if (typeof value === 'number') {
      // 2 decimal places için
      return Number(value.toFixed(2));
    }
    return value;
  }



  updateTable(hot: Handsontable) {
    if (!this.data?.variables_json || this.isUpdatingTable) return;
    this.isUpdatingTable = true;

    try {
      const processedData = this.data.variables_json.data

      hot.loadData(processedData);
    } catch (error) {
      console.error('Error updating table:', error);
    } finally {
      this.isUpdatingTable = false;
    }
  }

  private initializeTable() {
    if (this.isTableInitialized) return;
    this.updateComputedHeaderStyles();
    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot) return;
    // Process data and evaluate computed formulas immediately
    const processedData = this.data.variables_json.data

    const settings = {
      data: processedData,
      colHeaders: this.data.variables_json.headers,
      columns: this.data.variables_json.headers.map((header, index) => {
        const variable = this.data.variables_json.variable_list.find(v => v.header === header);
        return {
          data: index,
          type: (() => {
            // Sütundaki tüm değerleri kontrol et
            const values = this.data.variables_json.data.map(row => row[index]).filter(val => val !== null && val !== undefined && val !== '');

            // Tüm değerler sayısal mı kontrol et
            const allNumeric = values.every(val => !isNaN(Number(val)));
            if (allNumeric) {
              return 'numeric';
            }
            return 'text';
          })(),
          readOnly: variable?.computed || false,
          // Add custom header renderer
          headerClassName: variable?.computed ? 'text-green-600' : '',
        };
      }),
    };

    hot.updateSettings(settings);
    hot.render(); // Force render after initialization
    this.isTableInitialized = true;
    this.emitCurrentData();
  }
  private getContextMenuSettings(): any {
    const componentRef = this;
    return {
      callback(key: string, selection: any[]) {
      },
      items: {
        compute_options: {
          name: this.transloco.translate('shared.diagnose.context_menu.compute_options'),
          hidden: function (this: Handsontable) {
            const col = this.getSelectedRange()?.[0].from.col;
            if (col === undefined) return true;

            const header = componentRef.data.variables_json.headers[col];
            const variable = componentRef.data.variables_json.variable_list
              .find(v => v.header === header);

            return !variable?.computed;
          },

          submenu: {
            items: [
              {
                key: 'compute_options:auto_update',
                name: function (this: Handsontable) {
                  const col = this.getSelectedRange()?.[0]?.from.col;
                  if (col === undefined) return '';

                  const header = componentRef.data.variables_json.headers[col];
                  const variable = componentRef.data.variables_json.variable_list
                    .find(v => v.header === header);

                  return `<div class="auto-update-toggle">
                    <input type="checkbox" ${variable?.autoUpdate ? 'checked' : ''}>
                    <span>${componentRef.transloco.translate('shared.diagnose.context_menu.auto_update')}</span>
                  </div>`;
                },
                callback: (key: string, selection: any[]) => {
                  const col = selection[0].start.col;
                  componentRef.toggleAutoUpdate(col);
                }
              },
              {
                key: 'compute_options:source_variables',
                name: function (this: Handsontable) {
                  const col = this.getSelectedRange()?.[0]?.from.col;
                  if (col === undefined) return '';

                  const header = componentRef.data.variables_json.headers[col];
                  const variable = componentRef.data.variables_json.variable_list
                    .find(v => v.header === header);

                  return `<div class="compute-info">
                    <div class="title">${componentRef.transloco.translate('shared.diagnose.context_menu.source_variables')}:</div>
                    <div class="source-vars">${variable?.sourceVariables?.join(', ') || ''}</div>
                  </div>`;
                },
                disabled: true
              }
            ]
          }
        },
      }
    };
  }
  private getComputedVariable(col: number): any {
    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot) return null;

    const header = this.data.variables_json.headers[col];
    return this.data.variables_json.variable_list.find(v => v.header === header);
  }
  private findDependentComputedVariables(variable: any): any[] {
    if (!variable?.header) return [];

    return this.data.variables_json.variable_list.filter(v => {
      if (!v.computed || !v.sourceVariables || v.header === variable.header) return false;
      return v.sourceVariables.includes(variable.header);
    });
  }

  private toggleAutoUpdate(col: number): void {
    const variable = this.getComputedVariable(col);
    if (!variable?.computed) return;

    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot) return;

    // Store current values before toggling
    const currentValues = this.data.variables_json.data.map(row => row[col]);

    // Bu değişkene bağlı olan diğer computed değişkenleri bul
    const dependentVariables = this.findDependentComputedVariables(variable);
    const dependentValuesMap = new Map<string, any[]>();

    // Bağımlı değişkenlerin mevcut değerlerini sakla
    dependentVariables.forEach(depVar => {
      const depColIndex = this.data.variables_json.headers.indexOf(depVar.header);
      dependentValuesMap.set(
        depVar.header,
        this.data.variables_json.data.map(row => row[depColIndex])
      );
    });

    // Toggle autoUpdate value
    variable.autoUpdate = !variable.autoUpdate;

    if (variable.autoUpdate) {
      // AutoUpdate açıldığında:
      // 1. Sütunu readonly yap
      this.updateColumnSettings(col, true);
      // 2. Değerleri yeniden hesapla
      this.recalculateComputedValues(variable);

      // 3. Bağımlı değişkenleri güncelle
      dependentVariables.forEach(depVar => {
        if (depVar.autoUpdate) {
          this.recalculateComputedValues(depVar);
        }
      });
    } else {
      // AutoUpdate kapandığında:
      // 1. Sütunu editable yap
      this.updateColumnSettings(col, false);
      // 2. Mevcut değerleri koru
      this.data.variables_json.data.forEach((row, rowIndex) => {
        row[col] = currentValues[rowIndex];
      });

      // 3. Bağımlı değişkenlerin auto-update durumlarını kontrol et
      dependentVariables.forEach(depVar => {
        if (depVar.autoUpdate) {
          // Bağımlı değişken hala auto-update ise yeniden hesapla
          const depColIndex = this.data.variables_json.headers.indexOf(depVar.header);
          this.recalculateComputedValues(depVar);
        } else {
          // Auto-update kapalıysa önceki değerleri koru
          const depColIndex = this.data.variables_json.headers.indexOf(depVar.header);
          const savedValues = dependentValuesMap.get(depVar.header);
          if (savedValues) {
            this.data.variables_json.data.forEach((row, rowIndex) => {
              row[depColIndex] = savedValues[rowIndex];
            });
          }
        }
      });
    }

    // Render all changes
    hot.render();

    // Değişikliği emit et
    this.dataChange.emit({
      type: 'computedUpdate',
      variable: variable,
      autoUpdate: variable.autoUpdate,
      dependentVariables: dependentVariables
    });
  }

  private recalculateComputedValues(variable: any): void {
    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot) return;

    const colIndex = this.data.variables_json.headers.indexOf(variable.header);
    if (colIndex === -1) return;

    // Tüm satırları döngüye al
    this.data.variables_json.data.forEach((row: any[], rowIndex: number) => {
      // Mevcut değeri kontrol et
      const currentValue = row[colIndex];

      // ComputeVariableService ile yeni değeri hesapla
      const newValue = this.computeService.computeValue(
        variable,
        rowIndex,
        this.data.variables_json
      );

      // Sadece geçerli bir yeni değer varsa güncelle
      if (newValue !== undefined) {
        // Veriyi güncelle
        this.data.variables_json.data[rowIndex][colIndex] = newValue;
        // Tabloyu güncelle
        hot.setDataAtCell(rowIndex, colIndex, newValue, 'silent');
      } else {
        // Yeni değer geçersizse mevcut değeri koru
        this.data.variables_json.data[rowIndex][colIndex] = currentValue;
        hot.setDataAtCell(rowIndex, colIndex, currentValue, 'silent');
      }
    });

    hot.render();
  }
  private updateColumnSettings(col: number, readonly: boolean): void {
    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot) return;

    const currentSettings = hot.getSettings();
    const columns = currentSettings.columns || [];

    columns[col] = {
      ...columns[col],
      readOnly: readonly
    };

    hot.updateSettings({ columns });
  }

// data-view.component.ts

getHotSettings(): Handsontable.GridSettings {
  return {
    licenseKey: 'non-commercial-and-evaluation',
    data: [],
    colHeaders: [],
    columns: [],
    rowHeaders: true,
    height: '100%',
    width: '100%',

    // Hücre boyutları
    rowHeights: 40,

    stretchH: 'all',
    autoWrapRow: true,
    manualRowResize: true,
    manualColumnResize: true,
    contextMenu: this.getContextMenuSettings(),
    allowInsertColumn: false,
    allowRemoveColumn: false,
    filters: true,
    dropdownMenu: this.getDropdownMenuSettings(),
    autoRowSize: true,
    autoColumnSize: {
      samplingRatio: 1.0,
      useHeaders: true
    },

    // Tek bir cells tanımı - hem readonly hem de diğer properties'i birleştirelim
    cells: (row, col) => {
      const instance = this;
      const header = instance.data.variables_json.headers[col];
      const variable = instance.data.variables_json.variable_list
        ?.find(v => v.header === header);

      const cellProperties: any = {};

      // Eğer readonly ise tüm hücreler readonly
      if (this.readonly) {
        cellProperties.readOnly = true;
      } else if (variable?.computed) {
        cellProperties.readOnly = variable.autoUpdate;
        // Computed cell style
        cellProperties.renderer = (instance, TD, row, col, prop, value) => {
          TD.innerHTML = value !== null && value !== undefined ? value : '';
          TD.style.backgroundColor = '#dcfce750'; // Light green
          return TD;
        };
      } else if (variable?.imputed) {
        // Imputed cell style
        cellProperties.renderer = (instance, TD, row, col, prop, value) => {
          TD.innerHTML = value !== null && value !== undefined ? value : '';
          TD.style.backgroundColor = '#fef3c750'; // Light amber
          return TD;
        };
      } else if (variable?.recoded) {
        // Recoded cell style
        cellProperties.renderer = (instance, TD, row, col, prop, value) => {
          TD.innerHTML = value !== null && value !== undefined ? value : '';
          TD.style.backgroundColor = '#f3e8ff50'; // Light purple
          return TD;
        };
      }

      // Scale variables are right-aligned
      if (variable?.measure === 'Scale') {
        cellProperties.className = 'htRight'; // Handsontable's right alignment class
      }

      return cellProperties;
    },

    // Ölçüm türünü başlıkta gösterme
    afterGetColHeader: (col, TH) => {
      // If no data, exit
      if (!this.data?.variables_json?.headers) return;

      // Get the Handsontable instance
      const hotInstance = this.hotRegisterer.getInstance(this.id);
      if (!hotInstance) return;

      const colHeaders = hotInstance.getSettings().colHeaders;
      if (!colHeaders || !Array.isArray(colHeaders)) return;

      // Use the current display name from settings
      const displayName = colHeaders[col];
      if (!displayName) return;

      // Find the related variable using the original header
      const originalHeader = this.data.variables_json.headers[col];
      const variable = this.data.variables_json.variable_list?.find(v => v.header === originalHeader);
      if (!variable) return;

      // Apply different background colors based on variable properties
      if (variable.computed) {
        TH.style.backgroundColor = "#dcfce750"; // Light green for computed
      } else if (variable.imputed) {
        TH.style.backgroundColor = "#fef3c750"; // Light amber for imputed
      } else if (variable.recoded) {
        TH.style.backgroundColor = "#f3e8ff50"; // Light purple for recoded
      }

      // Determine variable type for display in the header
      let type = '';
      switch (variable.measure) {
        case 'Scale': type = 'Scale'; break;
        case 'Nominal': type = 'Nominal'; break;
        case 'Ordinal': type = 'Ordinal'; break;
        default: type = '';
      }

      // Find the header element and change its content
      const headerElement = TH.querySelector('.colHeader');
      if (headerElement && type) {
        headerElement.innerHTML = `
<div style="display:flex; align-items:center; justify-content:space-between; width:100%; font-family:'Baloo 2', sans-serif;">
  <span style="flex:1; overflow:hidden; text-overflow:ellipsis;">${displayName}</span>
  <div style="display:inline-flex; margin-left:8px; padding:2px 8px; border-radius:12px; font-size:11px; font-weight:500; background-color:${this.getTypeColor(type)}; color:${this.getTypeTextColor(type)};">
    ${type}
  </div>
</div>

      `;
      }
    },

    // Font ve stil ayarları için init hook
afterInit: () => {
  setTimeout(() => {
    const hotContainer = document.querySelector('.ht_master .wtHolder');
    if (hotContainer) {
      if (hotContainer instanceof HTMLElement) {
        hotContainer.style.fontFamily = "'Baloo 2', sans-serif";
      }

      const rows = hotContainer.querySelectorAll('.ht_master tbody tr');
      rows.forEach((row, index) => {
        if (index % 2 === 1) {
          (row as HTMLElement).style.backgroundColor = '#f8fafc';
        }
      });
    }
  }, 100);
},


    afterLoadData: (initialLoad) => {
      if (!initialLoad) {
        requestAnimationFrame(() => {
          const hot = this.hotRegisterer.getInstance(this.id);
          hot?.render();
        });
      }
    },

    // Tüm değişiklikleri tek bir event'te yakala
    afterChange: (changes, source) => {
      if (!changes || source === 'loadData') return;
      if (changes[0][2] === changes[0][3]) return;
      // ilk hali number olan değişikliklerin stringe çevrilmesine izin verme ve snotify ile uyarı verme
      if (typeof changes[0][2] === 'number' && typeof changes[0][3] === 'string' && changes[0][3].length > 0) {
        this.snotifyService.error(
          this.transloco.translate('shared.diagnose.error.invalid_value'),
          this.transloco.translate('shared.diagnose.error.title'));
        this.hotRegisterer.getInstance(this.id).setDataAtCell(changes[0][0], Number(changes[0][1]), changes[0][2]);
        return;
      }

      this.processDataChanges(changes);
    },

    // Row events için bir wrapper
    afterRemoveRow: (index: number, amount: number) => {
      // Satır silme işlemini bir değişiklik olarak işle
      const changes = [];
      // Etkilenen tüm hücreler için değişiklik oluştur
      for (let col = 0; col < this.data.variables_json.headers.length; col++) {
        changes.push([index, col, null, null]);
      }
      this.processDataChanges(changes);
    },

    afterCreateRow: (index: number, amount: number) => {
      try {
        // Yeni satır eklemek için data array'i güncelle
        const newRows = [];
        for (let i = 0; i < amount; i++) {
          const newRow = new Array(this.data.variables_json.headers.length).fill(0);
          this.data.variables_json.data.splice(index + i, 0, newRow);
          newRows.push(index + i);
        }

        // Computed değişkenleri bul ve auto-update açık olanları güncelle
        const computedVariables = this.data.variables_json.variable_list.filter(v =>
          v.computed && v.autoUpdate && v.sourceVariables
        );

        const hot = this.hotRegisterer.getInstance(this.id);
        if (hot) {
          // Her yeni satır için computed değerleri hesapla
          newRows.forEach(rowIndex => {
            computedVariables.forEach(variable => {
              const colIndex = this.data.variables_json.headers.indexOf(variable.header);
              if (colIndex !== -1) {
                const newValue = this.computeService.computeValue(
                  variable,
                  rowIndex,
                  this.data.variables_json
                );

                if (newValue !== null && newValue !== undefined) {
                  this.data.variables_json.data[rowIndex][colIndex] = newValue;
                  hot.setDataAtCell(rowIndex, colIndex, newValue, 'silent');
                }
              }
            });
          });

          // Tüm değişiklikleri bir kerede render et
          hot.render();
        }

        // Tüm sütunlar için değişiklik oluştur
        const changes = [];
        for (let col = 0; col < this.data.variables_json.headers.length; col++) {
          changes.push([index, col, null, null]);
        }
        this.processDataChanges(changes);

      } catch (error) {
        console.error('Error in afterCreateRow:', error);
        this.snotifyService.error(
          this.transloco.translate('shared.diagnose.error.row_creation_failed')
        );
      }
    }
  };
}

  private updateComputedHeaderStyles(): void {
    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot || !this.data?.variables_json) return;

    // Get computed, imputed and recoded variable headers
    const computedHeaders = this.data.variables_json.variable_list
      .filter(v => v.computed)
      .map(v => v.header);

    const imputedHeaders = this.data.variables_json.variable_list
      .filter(v => v.imputed)
      .map(v => v.header);

    const recodedHeaders = this.data.variables_json.variable_list
      .filter(v => v.recoded)
      .map(v => v.header);

    // Get header elements
    setTimeout(() => {
      const headerCells = document.querySelectorAll('.ht_clone_top .htCore thead th');
      headerCells.forEach((cell, index) => {
        // Get the header for this cell
        const header = this.data.variables_json.headers[index];
        if (header) {
          if (computedHeaders.includes(header)) {
            // Computed variable styling
            (cell as HTMLElement).classList.add('computed-header');
          } else if (imputedHeaders.includes(header)) {
            // Imputed variable styling
            (cell as HTMLElement).classList.add('imputed-header');
          } else if (recodedHeaders.includes(header)) {
            // Recoded variable styling
            (cell as HTMLElement).classList.add('recoded-header');
          }
        }
      });
    }, 50); // Small delay to ensure headers are rendered
  }

  // Ölçüm türüne göre renk döndüren fonksiyon
  private getTypeColor(type: string): string {
    switch (type.toLowerCase()) {
      case 'scale':
        return '#DBEAFE'; // Açık mavi
      case 'nominal':
        return '#FCF3CC'; // Açık sarı
      case 'ordinal':
        return '#F3E8FF'; // Açık mor
      default:
        return '#E5E7EB'; // Gri
    }
  }

  // Ölçüm türüne göre metin rengi döndüren fonksiyon
  private getTypeTextColor(type: string): string {
    switch (type.toLowerCase()) {
      case 'scale':
        return '#1E40AF'; // Koyu mavi
      case 'nominal':
        return '#D97706'; // Koyu sarı/turuncu
      case 'ordinal':
        return '#7E22CE'; // Koyu mor
      default:
        return '#4B5563'; // Koyu gri
    }
  }

  forceHeaderRefresh(): void {
    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot) return;

    // Force Handsontable to rebuild all of its header elements
    hot.updateSettings({});
    hot.render();
  }

  // Header/Label geçişini yönetecek fonksiyon
  toggleColumnDisplay(): void {
    // Toggle the showLabels flag first
    this.showLabels = !this.showLabels;

    this.updateComputedHeaderStyles();

    const hot = this.hotRegisterer.getInstance(this.id);
    if (!hot || !this.data?.variables_json) return;

    // Get all headers
    const headers = this.data.variables_json.headers;
    const variables = this.data.variables_json.variable_list || [];

    // Create new header array
    const newHeaders = headers.map((header: string) => {
      // Find corresponding variable
      const variable = variables.find(v => v.header === header);
      if (!variable) return header;

      // If in label mode and label exists
      if (this.showLabels && variable.label) {
        const currentLanguage = this.transloco.getActiveLang();
        const label = currentLanguage === 'tr' ?
          (variable.label?.tr || header) :
          (variable.label?.en || header);

        return label;
      }

      // Normal header mode
      return header;
    });

    // Completely reset and rebuild all headers
    hot.updateSettings({
      colHeaders: newHeaders
    });

    // Force an immediate render
    hot.render();

    // Sometimes Handsontable needs a second render after a short delay
    setTimeout(() => {
      hot.render();

      // Apply styles to column headers for better visibility of the change
      const headerContainer = hot.rootElement.querySelector('.ht_clone_top .htCore thead');
      if (headerContainer) {
        const headerCells = headerContainer.querySelectorAll('th');
        headerCells.forEach((cell: HTMLElement) => {
          cell.style.backgroundColor = this.showLabels ? '#eef2ff' : '#e0f2fe';
          cell.style.transition = 'background-color 0.3s';
        });
      }
    }, 50);

    // Notify parent component about the change
    this.dataChange.emit({
      type: 'displayChange',
      showLabels: this.showLabels
    });
  }

  private processDataChanges(changes: any[]) {
    if (!changes || this.isProcessingChanges) return;

    this.isProcessingChanges = true;

    try {
      const hot = this.hotRegisterer.getInstance(this.id);
      if (!hot) return;

      // Etkilenen sütunları ve satırları takip et
      const affectedColumns = new Set<number>();
      const affectedRows = new Set<number>();

      // Tüm değişiklikleri işle
      changes.forEach(([row, prop, oldValue, newValue]) => {
        const col = typeof prop === 'number' ? prop : parseInt(prop as string);
        if (isNaN(col)) return;
        // Değeri güncelle
        const normalizedValue = this.normalizeValue(newValue);
        if (this.data.variables_json.data[row]) {
          if (this.hasValueChanged(oldValue, normalizedValue)) {
            this.data.variables_json.data[row][col] = normalizedValue;
            affectedColumns.add(col);
            affectedRows.add(row);
          }
        }
      });
      // Bağımlı computed değişkenleri güncelle
      this.updateDependentComputedVariables(Array.from(affectedRows), Array.from(affectedColumns), hot);

      // Diğer güncellemeler...
      const updatedVariables = [];

      affectedColumns.forEach(colIndex => {
        const header = this.data.variables_json.headers[colIndex];
        const variable = this.data.variables_json.variable_list.find(v => v.header === header);

        if (variable && !variable.computed) {
          // Sütundaki tüm unique değerleri topla
          const uniqueValues = new Set<any>();
          this.data.variables_json.data.forEach(row => {
            const value = row[colIndex];
            if (value !== null && value !== undefined && value !== '') {
              uniqueValues.add(value);
            }
          });

          // İstatistikleri hesapla
          const missingCount = this.calculateMissingCount(colIndex);

          // Value labels güncelle (Nominal/Ordinal için)
          if ((variable.measure === 'Nominal' || variable.measure === 'Ordinal') && uniqueValues.size > 0) {
            const sortedValues = Array.from(uniqueValues).sort();
            const currentLabels = variable.value_labels || { en: {}, tr: {} };

            // Yeni value labels oluştur
            variable.value_labels = {
              en: {},
              tr: {}
            };

            sortedValues.forEach(value => {
              variable.value_labels.en[value] = currentLabels.en[value] || value.toString();
              variable.value_labels.tr[value] = currentLabels.tr[value] || value.toString();
            });
          }

          // Güncellenmiş değişkeni listeye ekle
          updatedVariables.push({
            ...variable,
            missingCount,
            validCount: this.data.variables_json.data.length - missingCount
          });
        }
      });

      // Değişiklik varsa emit et
      if (updatedVariables.length > 0) {
        hot.render();
        this.emitUpdatedData(updatedVariables);
      }

      // Emit the entire data change
      this.dataChange.emit({
        data: this.data.variables_json.data,
        updatedVariables,
        type: 'dataChange'
      });

    } finally {
      this.isProcessingChanges = false;
    }
  }

  private updateDependentComputedVariables(rows: number[], changedCols: number[], hot: any) {
    // Değişen sütunların header'larını al
    const changedHeaders = changedCols.map(col => this.data.variables_json.headers[col]);

    // Bağımlı computed değişkenleri bul
    const dependentVariables = this.data.variables_json.variable_list.filter(v => {
      if (!v.computed || !v.autoUpdate || !v.sourceVariables) return false;
      return v.sourceVariables.some(source => changedHeaders.includes(source));
    });

    // Her etkilenen satır için bağımlı değişkenleri güncelle
    rows.forEach(row => {
      dependentVariables.forEach(variable => {
        const colIndex = this.data.variables_json.headers.indexOf(variable.header);
        if (colIndex === -1) return;

        // Yeni değeri hesapla
        const newValue = this.computeService.computeValue(
          variable,
          row,
          this.data.variables_json
        );
        if (newValue !== null && newValue !== undefined) {
          // Veriyi güncelle
          this.data.variables_json.data[row][colIndex] = newValue;
          // Tabloyu güncelle
          hot.setDataAtCell(row, colIndex, newValue, 'silent');
        }
      });
    });

    // Tüm değişikliklerden sonra tabloyu yenile
    hot.render();
  }

  // Yeni metod ekle
  private emitCurrentData(): void {
    if (this.data?.variables_json) {
      // Computed değerleri güncellenmiş veriyi emit et
      const processedData = this.data.variables_json.data.map(row =>
        row.map(cell => {
          if (cell && typeof cell === 'object' && 'getValue' in cell) {
            return {
              formula: cell.formula,
              value: cell.getValue()
            };
          }
          return cell;
        })
      );

      this.dataChange.emit({
        ...this.data.variables_json,
        data: processedData
      });
    }
  }

  private hasValueChanged(currentValue: any, newValue: any): boolean {
    // null ve undefined'ı eşit kabul et
    if ((currentValue === null || currentValue === undefined) &&
      (newValue === null || newValue === undefined)) {
      return false;
    }

    // Boş string kontrolü
    if ((currentValue === '' || currentValue === null || currentValue === undefined) &&
      (newValue === '' || newValue === null || newValue === undefined)) {
      return false;
    }

    // Sayısal değerler için
    if (typeof currentValue === 'number' && typeof newValue === 'number') {
      return currentValue !== newValue;
    }

    // String karşılaştırması için trim kullan
    if (typeof currentValue === 'string' && typeof newValue === 'string') {
      return currentValue.trim() !== newValue.trim();
    }

    // Genel karşılaştırma
    return currentValue !== newValue;
  }

  onCellChange(changes: any[]) {
    if (!changes || this.isProcessingChanges) return;

    this.isProcessingChanges = true;

    try {
      const hot = this.hotRegisterer.getInstance(this.id);
      if (!hot) return;

      let updatedVariables = new Map<string, any>();

      changes.forEach(([row, prop, oldValue, newValue]) => {
        if (typeof prop === 'number' && prop >= 0) {
          const columnHeader = this.data.variables_json.headers[prop];
          const variable = this.data.variables_json.variable_list.find(v => v.header === columnHeader);

          if (!variable || variable.computed) {
            return;
          }

          // Scale tipindeki değişkenler için sayısal kontrol
          if (variable.measure === 'Scale' && newValue !== '' && newValue !== null && isNaN(Number(newValue))) {
            hot.setDataAtCell(row, prop, oldValue, 'silent');
            return;
          }

          // Normal string boşluklarını kırp
          const trimmedValue = typeof newValue === 'string' ? newValue.trim() : newValue;

          // Değeri normalize et - boş string kontrolünü güçlendir
          const valueToSet = this.normalizeValue(trimmedValue);

          // Veriyi güncelle - karşılaştırmayı güçlendir
          const currentValue = this.data.variables_json.data[row][prop];
          const hasChanged = this.hasValueChanged(currentValue, valueToSet);

          if (hasChanged) {
            this.data.variables_json.data[row][prop] = valueToSet;
            hot.setDataAtCell(row, prop, valueToSet, 'silent');

            // Değişkeni güncellenenler listesine ekle
            if (!updatedVariables.has(columnHeader)) {
              updatedVariables.set(columnHeader, {
                ...variable,
                missingCount: this.calculateMissingCount(prop),
                validCount: this.data.variables_json.data.length - this.calculateMissingCount(prop)
              });
            }
          }
        }
      });

      // Değişen değişkenler varsa missing sayılarını güncelle ve emit et
      if (updatedVariables.size > 0) {
        hot.render();
        this.emitUpdatedData(Array.from(updatedVariables.values()));
      }

    } finally {
      this.isProcessingChanges = false;
    }
  }
  private normalizeValue(value: any): any {
    if (value === undefined || value === null || value === '') {
      return null;
    }
    if (typeof value === 'string') {
      const trimmed = value.trim();
      return trimmed === '' ? null : trimmed;
    }
    return value;
  }

  private emitUpdatedData(updatedVariables: any[]) {
    this.dataChange.emit({
      data: this.data.variables_json.data,
      variables: this.data.variables_json.variable_list,
      updatedVariables,
      type: 'dataChange'
    });
  }
  private calculateMissingCount(columnIndex: number): number {
    return this.data.variables_json.data.reduce((count, row) => {
      const value = row[columnIndex];
      return (value === null || value === undefined || value === '') ? count + 1 : count;
    }, 0);
  }
  private getDropdownMenuSettings(): any {
    const componentRef = this;
    return {
      items: {
        compute_options: {
          name: this.transloco.translate('shared.diagnose.dropdown_menu.compute_options'),
          hidden: function (this: Handsontable) {
            const col = this.getSelectedRange()?.[0].from.col;
            if (col === undefined) return true;

            const header = componentRef.data.variables_json.headers[col];
            const variable = componentRef.data.variables_json.variable_list
              .find(v => v.header === header);

            return !variable?.computed;
          },
          submenu: {
            items: [
              {
                key: 'compute_options:auto_update',
                name: function (this: Handsontable) {
                  const col = this.getSelectedRange()?.[0]?.from.col;
                  if (col === undefined) return '';

                  const header = componentRef.data.variables_json.headers[col];
                  const variable = componentRef.data.variables_json.variable_list
                    .find(v => v.header === header);

                  return `<div class="auto-update-toggle">
                    <input type="checkbox" ${variable?.autoUpdate ? 'checked' : ''}>
                    <span>${componentRef.transloco.translate('shared.diagnose.dropdown_menu.auto_update')}</span>
                  </div>`;
                },
                callback: (key: string, selection: any[]) => {
                  const col = selection[0].start.col;
                  componentRef.toggleAutoUpdate(col);
                }
              },
              {
                key: 'compute_options:source_variables',
                name: function (this: Handsontable) {
                  const col = this.getSelectedRange()?.[0]?.from.col;
                  if (col === undefined) return '';

                  const header = componentRef.data.variables_json.headers[col];
                  const variable = componentRef.data.variables_json.variable_list
                    .find(v => v.header === header);

                  return `<div class="compute-info">
                    <div class="title">${componentRef.transloco.translate('shared.diagnose.dropdown_menu.source_variables')}:</div>
                    <div class="source-vars">${variable?.sourceVariables?.join(', ') || ''}</div>
                  </div>`;
                },
                disabled: true
              }
            ]
          }
        }
      }
    };
  }
}

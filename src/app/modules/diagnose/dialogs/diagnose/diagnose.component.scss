.gutter {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 12px;
  transform: translateX(-6px);
  cursor: col-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  touch-action: none;

  &-handle {
    width: 4px;
    height: 24px;
    background-color: #e5e7eb;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
}

.variable-input {
  @apply transition-all duration-200;

  &:hover {
    @apply border-gray-400;
  }

  &:focus {
    @apply ring-1 ring-blue-500 border-blue-500;
  }

  &.ng-invalid {
    @apply border-red-300;

    &:hover {
      @apply border-red-400;
    }

    &:focus {
      @apply ring-red-500 border-red-500;
    }
  }
}

// Add tooltip styles
.tooltip {
  @apply invisible absolute -top-8 left-1/2 -translate-x-1/2 px-2 py-1 text-xs text-white bg-gray-900 rounded;

  .group:hover & {
    @apply visible;
  }
}

.validation-tooltip {
  white-space: pre-line !important;
  max-width: none !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: rgb(249 250 251); // bg-gray-50
  border-bottom: 1px solid rgb(229 231 235); // border-b
}

:host {
  scroll-behavior: smooth;
}

.value-label-area {
  scroll-margin-top: 120px; // Scroll edildiğinde üstten biraz boşluk bırakır
}

.variable-card {
  transition: all 0.3s ease-out;
}

/* Value label alanı için animasyon */
.value-label-area {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Icon animasyon */
.rotate-icon {
  transition: transform 0.3s ease-out;
}

.rotate-icon.expanded {
  transform: rotate(-180deg);
}

/* Açılan değişken için vurgulama efekti */
.variable-card.expanded {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  background-color: rgb(239 246 255); // bg-blue-50
}

.bg-blue-50 {
  transition: background-color 0.3s ease-out;
}

@keyframes pulse-highlight {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.pulse-highlight {
  animation: pulse-highlight 1.5s ease-out;
  border-color: rgba(59, 130, 246, 0.5) !important;
  background-color: rgba(239, 246, 255, 0.7) !important;
}

/* Eklemek için diagnose.component.scss */
.highlight-variable {
  position: relative;
  z-index: 10;
  border: 2px solid #3b82f6 !important;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5) !important;
  background-color: rgba(239, 246, 255, 0.7) !important;
  animation: pulse 1.5s ease-in-out 2;
}

@keyframes pulse {

  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }

  50% {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  }
}

/* diagnose.component.scss dosyasına eklenecek CSS kodları */

/* Handsontable hizalama düzeltmeleri */
:host ::ng-deep {

  /* Ana tablo konteynerini düzeltme */
  .handsontable {
    position: relative;
    overflow: visible;
  }

  /* Tüm hücrelerin padding'ini tutarlı hale getirme */
  .handsontable .htCore td,
  .handsontable .htCore th {
    box-sizing: border-box;
    position: relative;
  }

  /* Fixed sütunlar için z-index tanımlama */
  .ht_clone_left {
    z-index: 11;
  }

  /* Satır yüksekliğinin tutarlı olmasını sağlama */
  .handsontable .htCore tbody tr {
    will-change: transform;
  }

  /* Kaydırma sırasında düzgün hizalama için */
  .ht_master .wtHolder {
    will-change: transform;
  }

  /* Kaydırma sırasında daha iyi performans için */
  .handsontable .wtHolder {
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  /* Özellikle sol kolonun daha iyi hizalanması için */
  .ht_clone_left .wtHolder {
    overflow: hidden;
    position: relative;
    backface-visibility: hidden;
    transform: translateZ(0);
  }
}

/* diagnose.component.scss dosyasına eklenecek CSS */

/* Filter dropdown için z-index düzeltmesi */
.filter-dropdown {
  z-index: 50 !important;
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  overflow: visible;
}

/* Grid yapısı için responsive düzenleme */
@media (max-width: 768px) {
  .variable-controls-grid {
    grid-template-columns: 1fr !important;
    gap: 0.75rem;
  }

  .variable-controls-grid>div {
    grid-column: span 12 !important;
  }
}

/* Dropdown shadow ayarı */
.filter-dropdown-shadow {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Enhance the existing validation-tooltip styles in diagnose.component.scss */
.validation-tooltip {
  white-space: pre-line !important; 
  max-width: 300px !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
  line-height: 1.4 !important;
  background-color: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Add styles for tooltip arrow */
.validation-tooltip.mat-tooltip-panel .mat-tooltip {
  position: relative;
}

.validation-tooltip.mat-tooltip-panel .mat-tooltip::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.9);
}

/* Add hover effect to status badges to indicate they're interactive */
.status-badge-hover {
  transition: all 0.2s ease;
}

.status-badge-hover:hover {
  filter: brightness(0.95);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
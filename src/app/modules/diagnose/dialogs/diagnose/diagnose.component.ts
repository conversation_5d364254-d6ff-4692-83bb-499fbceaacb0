import { animate, state, style, transition, trigger } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Overlay } from '@angular/cdk/overlay';
import { Component, Inject, OnInit, ChangeDetectorRef, OnDestroy, ChangeDetectionStrategy, NgZone } from '@angular/core';
import { DatasetService } from '@app/data/services/dataset.service';
import { ConfirmComponent } from '@components/confirm/confirm.component';
import { SnotifyService } from 'ng-alt-snotify';
import { DeviceDetectorService } from 'ngx-device-detector';
import { filter, Observable, Subject, takeUntil } from 'rxjs';
import { TranslocoService } from '@ngneat/transloco';
import { ComputeVariableComponent } from '../compute-variable/compute-variable.component';
import { MissingValueComponent } from '../missing-value/missing-value.component';
import { ValueLabelComponent } from '../value-label/value-label.component';
import { ElementRef, ViewChild } from '@angular/core';
import * as XLSX from 'xlsx';
import { ValidationIssue, ValidationSummary, VariableValidationService } from '@app/modules/diagnose/services/variable-validation.service';
import { ValidationDetailsComponent } from '../validation-details/validation-details.component';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { ValidationSummaryComponent } from '../validation-summary/validation-summary-dialog.component';
import { ExcelService } from '@app/data/services/excel.service';
import { AutoRecodingComponent } from '../auto-recoding/auto-recoding.component';
import { VideoEmbedComponent } from '@app/shared/components/video-embed/video-embed.component';
import { DataTransferService } from '@app/data/services/data-transfer.service';

interface ValueLabel {
  value: string | number;
  en: string;
  tr: string;
}

interface VariableProperties {
  id: string;
  header: string;
  label: { en: string; tr: string };
  measure: string;
  import: boolean;
  value_labels?: any;
  computed?: boolean;
  imputed?: boolean;
  recoded?: boolean;
  sourceVariables?: string[];
  formula?: string;
  expanded?: boolean;
  missingCount?: number;
}

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 'app-diagnose',
  templateUrl: './diagnose.component.html',
  styleUrls: ['./diagnose.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ]),
    trigger('expandCollapse', [
      state('collapsed', style({ height: '0', opacity: 0, overflow: 'hidden' })),
      state('expanded', style({ height: '*', opacity: 1 })),
      transition('collapsed <=> expanded', [
        animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ]),
      transition('void => expanded', [
        style({ height: '0', opacity: 0 }),
        animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({ height: '*', opacity: 1 }))
      ]),
      transition('expanded => void', [
        animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({ height: '0', opacity: 0 }))
      ])
    ])
  ]
})
export class DiagnoseComponent implements OnInit, OnDestroy {
  viewMode: 'data' | 'variable' = 'variable';
  searchQuery: string = '';
  animationState = 'in';
  selectedVariable: any = null;
  temp_data: any;
  temp: any;
  hasChanges = false;
  isProjectUpdated = false;
  submitted = false;
  validationSummary: ValidationSummary;
  private destroy$ = new Subject<void>();
  private originalData: any;
  measures = ["Scale", "Nominal", "Ordinal"];
  valueLabelEntries: { [key: string]: ValueLabel[] } = {};
  validationIssues: ValidationIssue[] = [];
  showLabels: boolean = false;

  @ViewChild('dataView') dataView: any;
  @ViewChild('fileInput') fileInput: ElementRef;

  showValidationPanel = false;
  variableErrors: { [key: string]: { type: 'error' | 'warning'; message: string }[] } = {};
  datasetErrors: string[] = [];
  unsavedValueLabels: Set<string> = new Set();
  private originalValueLabels: { [key: string]: any } = {};
  constructor(
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private dialog: Dialog,
    private overlay: Overlay,
    private datasetService: DatasetService,
    private snotifyService: SnotifyService,
    private deviceService: DeviceDetectorService,
    private transloco: TranslocoService,
    private cdr: ChangeDetectorRef, // Add ChangeDetectorRef
    private validationService: VariableValidationService,
    private diagnoseHelper: DiagnoseHelperService,
    private ngZone: NgZone,
    private dt: DataTransferService,
  ) {
    // Move validation subscription to ngOnInit
    this.originalData = JSON.parse(JSON.stringify(this.data));
    setInterval(() => {
      this.updateTotalIssues();
    }, 100);
  }

  onImportChange(variable: any): void {
    // Önce temp_data'da değişikliği yap
    const tempIndex = this.temp_data.variables_json.variable_list.findIndex(v => v.id === variable.id);
    if (tempIndex !== -1) {
      this.temp_data.variables_json.variable_list[tempIndex].import = variable.import;
    }

    // Aynı değişikliği this.data'da da yap
    const dataIndex = this.data.variables_json.variable_list.findIndex(v => v.id === variable.id);
    if (dataIndex !== -1) {
      this.data.variables_json.variable_list[dataIndex].import = variable.import;
    }

    if (!variable.import) {
      // Import false olduğunda value label panelini kapat
      variable.expanded = false;
      // Validation sonuçlarını temizle
      this.validationIssues = this.validationIssues.filter(i => i.variableId !== variable.id);
    } else {
      // Import true olduğunda validation'ı yeniden yap
      const issues = this.validationService.validateVariables([variable], this.data.variables_json.data);
      // Mevcut issue'ları güncelle
      this.validationIssues = this.validationIssues.filter(i => i.variableId !== variable.id);
      this.validationIssues.push(...issues);
    }

    this.onVariableChange(variable);
    this.checkChanges();
    this.cdr.markForCheck();
    this.updateAllSelectedState();
  }

  private fetchDatasetInfo(): void {
    this.diagnoseHelper.getDatasetInfo(this.data.dataset_id).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (dataset) => {
        console.log('Dataset full response:', dataset);
        console.log('Project object:', dataset?.project);
        console.log('Project type:', dataset?.project?.project_type);
        
        // Project type'ı data'ya ekle
        this.data.projectType = dataset?.project?.project_type;
        this.cdr.markForCheck();
        
        // canEditData çağırabilirsiniz
        const canEdit = this.canEditData();
        console.log('Can edit data:', canEdit);
      },
      error: (error) => {
        console.error('Dataset fetch error:', error);
      }
    });
  }
  datasetName: string;
    ngOnInit(): void {
      this.datasetName = this.data.datasetName;
      this.dialogRef.disableClose = true;
      if (this.data?.fromCreateProject) {
        this.hasChanges = true;
      }
    
      // Dataset bilgisini çek
      if (this.data?.dataset_id) {
        this.fetchDatasetInfo();
      }

    if (this.data?.variables_json?.variable_list) {
      this.data.variables_json.variable_list.forEach((variable: any) => {
        // Eğer Nominal veya Ordinal ama value_labels boşsa, boş string ile doldur
        if ((variable.measure === 'Nominal' || variable.measure === 'Ordinal') &&
          (variable.value_labels &&
            Object.keys(variable.value_labels.en).length === 0 &&
            Object.keys(variable.value_labels.tr).length === 0)) {

          // Veriden benzersiz değerleri çek
          const columnIndex = this.data.variables_json.headers.indexOf(variable.header);
          if (columnIndex !== -1) {
            const uniqueValues = new Set();

            this.data.variables_json.data.forEach((row: any) => {
              const value = row[columnIndex];
              if (value !== null && value !== undefined && value !== '') {
                uniqueValues.add(value.toString());
              }
            });

            // Benzersiz değerlerden boş string value labels oluştur
            Array.from(uniqueValues).sort().forEach((value: any) => {
              variable.value_labels.en[value] = ''; // Boş string kullan
              variable.value_labels.tr[value] = ''; // Boş string kullan
            });
          }
        }
      });
    }
    // Backdrop tıklamasını yakala
    this.dialogRef.backdropClick.subscribe(() => {
      this.closeModal();
    });
    this.initializeData();

    // this.datasetName = this.updateFileName(this.data.name);
    this.setupDialogBehavior();

    // Move validation subscription here
    this.validationService.validationState$
      .pipe(
        takeUntil(this.destroy$),
        filter(summary => !!summary)
      )
      .subscribe(summary => {
        Promise.resolve().then(() => {
          this.validationSummary = summary;
          this.cdr.markForCheck(); // Use markForCheck instead of detectChanges
        });
      });

    // Initial validation
    Promise.resolve().then(() => {
      this.validateData();
    });
    // Initialize expanded state for variables
    if (this.temp_data?.variables_json?.variable_list) {
      this.temp_data.variables_json.variable_list.forEach((item: any) => {
        item.expanded = false;
      });
    }

    this.updateAllSelectedState();

  }
  ngOnDestroy(): void {

  }

  private canEditData(): boolean {
    const userRoles = JSON.parse(localStorage.getItem('roles') || '[]');
    const isAdmin = userRoles.some((role: any) => role.name === 'admin');
    
    // dataset.project.project_type'ı kontrol et
    const isDemoProject = this.data?.projectType === 'demo';
    
    console.log('User roles:', userRoles);
    console.log('Is admin:', isAdmin);
    console.log('Is demo project:', isDemoProject);
    console.log('Can edit:', isAdmin || !isDemoProject);
    
    return isAdmin || !isDemoProject;
  }

  getValueLabels() {
    const checkEveryObjectisNull = (value: any) => {
      return Object.values(value).some((element) => element === null);
    }
    var value_labels = this.temp_data.variables_json.variable_list.filter((element: any) => element.value_labels && !checkEveryObjectisNull(element.value_labels.tr) && !checkEveryObjectisNull(element.value_labels.en))
    const unique_labels: any[] = [];
    const seen = new Set();

    // Döngü ile `value_labels`'ları kontrol ediyoruz
    for (const variable of value_labels) {
      // Eğer daha önce eklenmemişse
      const stringifiedValue = JSON.stringify(variable.value_labels);

      if (!seen.has(stringifiedValue)) {
        // Tekrarını önlemek için `Set` içine ekle
        seen.add(stringifiedValue);

        // `unique_labels`'a ekle
        unique_labels.push({
          labels: variable.value_labels
        });
      }
    }

    return unique_labels

  }
  openValueLabelHistory(currentItem: any): void {
    // Mevcut değişken dışındaki  value label'ları toplayalım
    const availableLabels = this.getValueLabels().filter((item: any) => item.labels !== currentItem.value_labels);

    // Eğer kullanılabilir etiket yoksa uyarı gösterelim
    if (availableLabels.length === 0) {
      this.snotifyService.info(
        this.transloco.translate('shared.diagnose.value_labels.no_available_labels')
      );
      return;
    }

    // Hedef değişkenin etiketlerini boş olarak başlat
    if (!currentItem.value_labels) {
      currentItem.value_labels = {
        en: {},
        tr: {}
      };
    }

    const positionStrategy = this.overlay.position()
      .global()
      .right('0').top('0');

    const dialog = this.dialog.open(ValueLabelComponent, {
      data: {
        availableLabels,
        currentLabels: currentItem.value_labels
      },
      positionStrategy,
      width: '500px'
    });

    dialog.closed.subscribe((result: any) => {
      if (result) {
        // Seçilen etiketleri mevcut değişkene uygula
        currentItem.value_labels = JSON.parse(JSON.stringify(result));
        this.valueLabelEntries[currentItem.id] = this.getValueLabelEntries(currentItem);

        // Değişiklik olduğunu işaretle
        this.unsavedValueLabels.add(currentItem.id);

        this.checkChanges();
        this.cdr.markForCheck();
      }
    });
  }
  private initializeData(): void {
    // Derin kopya alırken anahtar verilerin kaybolmadığından emin ol
    this.temp_data = JSON.parse(JSON.stringify(this.data));

    // Value labels için özel kontrol ekle - eğer varsa doğru şekilde işle
    if (this.temp_data?.variables_json?.variable_list) {
      this.temp_data.variables_json.variable_list.forEach((variable: any) => {
        // Value labels varsa, bunları doğru şekilde işle
        if (variable.value_labels) {
          // Eğer eksik value label alanları varsa tamamla
          if (!variable.value_labels.en) variable.value_labels.en = {};
          if (!variable.value_labels.tr) variable.value_labels.tr = {};

          // Başlangıçta, original value labels'ı kaydet
          this.originalValueLabels[variable.id] = JSON.parse(JSON.stringify(variable.value_labels));
        }

        // Nominal ve Ordinal değişkenler için value labels yoksa oluştur
        if ((variable.measure === 'Nominal' || variable.measure === 'Ordinal') && !variable.value_labels) {
          this.initializeValueLabelsForVariable(variable);
        }
      });
    }

    this.temp = JSON.parse(JSON.stringify(this.data.variables_json.variable_list));
  }

  private initializeValueLabelsForVariable(variable: any): void {
    if (variable.measure !== 'Scale' && !variable.value_labels) {
      // Value labels yapısını oluştur
      variable.value_labels = {
        en: {},
        tr: {}
      };

      // Veri setinden benzersiz değerleri al ve value labels oluştur
      if (this.data?.variables_json?.headers && this.data?.variables_json?.data) {
        const columnIndex = this.data.variables_json.headers.indexOf(variable.header);
        if (columnIndex !== -1) {
          const uniqueValues = new Set();

          // Verideki benzersiz değerleri topla
          this.data.variables_json.data.forEach((row: any) => {
            const value = row[columnIndex];
            if (value !== null && value !== undefined && value !== '') {
              uniqueValues.add(value.toString());
            }
          });

          // Her bir benzersiz değer için etiketler oluştur
          Array.from(uniqueValues).sort().forEach((value: any) => {
            variable.value_labels.en[value] = '';
            variable.value_labels.tr[value] = '';
          });
        }
      }

      // Orijinal değeri kaydet
      this.originalValueLabels[variable.id] = JSON.parse(JSON.stringify(variable.value_labels));
    }
  }



  private setupDialogBehavior(): void {
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => this.closeModal());
  }

  // Value Label işlemleri
  toggleValueLabels(item: any): void {
    if (this.selectedVariable && this.unsavedValueLabels.has(this.selectedVariable.id)) {
      // Eğer mevcut seçili değişkenin kaydedilmemiş değişiklikleri varsa
      const dialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.title'),
          content: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.content'),
          confirm: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.save'),
          cancel: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.discard')
        }
      });

      dialog.closed.subscribe((result) => {
        if (result) {
          // Değişiklikleri kaydet ve yeni değişkeni aç
          this.saveValueLabels(this.selectedVariable);
          this.processValueLabelToggle(item);
        } else if (result === false) {
          // Vazgeç'e basıldığında değişiklikleri at ve yeni değişkeni aç
          this.discardChanges(this.selectedVariable);
          this.processValueLabelToggle(item);
        }
        // result undefined ise (modal kapatıldığında) hiçbir şey yapma
      });
    } else {
      this.processValueLabelToggle(item);
    }
  }

  // Yeni method: Değişiklikleri atma
  private discardChanges(variable: any): void {
    if (this.originalValueLabels[variable.id]) {
      variable.value_labels = JSON.parse(JSON.stringify(this.originalValueLabels[variable.id]));
    }
    this.unsavedValueLabels.delete(variable.id);
    this.cdr.markForCheck();
  }

  // Method to process the value label toggle action
  private processValueLabelToggle(item: any): void {
    // Boş value_labels nesnesi varsa, boş string ile doldur
    if (item.value_labels &&
      (Object.keys(item.value_labels.en).length === 0 ||
        Object.keys(item.value_labels.tr).length === 0)) {

      // Veri setinden benzersiz değerleri al
      const columnIndex = this.temp_data.variables_json.headers.indexOf(item.header);
      if (columnIndex !== -1) {
        const uniqueValues = new Set();

        // Verideki benzersiz değerleri topla
        this.temp_data.variables_json.data.forEach((row: any) => {
          const value = row[columnIndex];
          if (value !== null && value !== undefined && value !== '') {
            uniqueValues.add(value.toString());
          }
        });

        // Her bir benzersiz değer için boş etiketler oluştur
        Array.from(uniqueValues).sort().forEach((value: any) => {
          if (!item.value_labels.en[value]) {
            item.value_labels.en[value] = ''; // Boş string kullan
          }
          if (!item.value_labels.tr[value]) {
            item.value_labels.tr[value] = ''; // Boş string kullan
          }
        });
      }
    }

    // Paneli ilk açtığımızda orijinal değerleri saklayalım
    if (!this.originalValueLabels[item.id] && item.value_labels) {
      this.originalValueLabels[item.id] = JSON.parse(JSON.stringify(item.value_labels));
    }

    // Diğer tüm açık öğeleri kapat
    this.temp_data.variables_json.variable_list.forEach((variable: any) => {
      if (variable !== item) {
        variable.expanded = false;
      }
    });

    // Toggle current item's expanded state
    item.expanded = !item.expanded;
    this.selectedVariable = item.expanded ? item : null;

    // If item is expanded, initialize value label entries
    if (item.expanded) {
      // Make sure value_labels is initialized correctly for non-Scale variables
      if (item.measure !== 'Scale') {
        if (!item.value_labels) {
          item.value_labels = {
            en: {},
            tr: {}
          };

          // Nominal ve Ordinal için veri setinden değerleri al
          const columnIndex = this.temp_data.variables_json.headers.indexOf(item.header);
          if (columnIndex !== -1) {
            const uniqueValues = new Set();

            // Verideki benzersiz değerleri topla
            this.temp_data.variables_json.data.forEach((row: any) => {
              const value = row[columnIndex];
              if (value !== null && value !== undefined && value !== '') {
                uniqueValues.add(value.toString());
              }
            });

            // Her bir benzersiz değer için boş etiketler oluştur
            Array.from(uniqueValues).sort().forEach((value: any) => {
              item.value_labels.en[value] = ''; // Boş string kullan
              item.value_labels.tr[value] = ''; // Boş string kullan
            });
          }
        }
      }

      // Value label entries'i başlat
      try {
        this.valueLabelEntries[item.id] = this.getValueLabelEntries(item);
      } catch (error) {
        console.error(`Error creating value label entries for ${item.header}:`, error);
        this.valueLabelEntries[item.id] = [];
      }
    }

    // Scroll handling
    if (item.expanded) {
      setTimeout(() => {
        const variableContainer = document.querySelector(`[data-variable-id="${item.id}"]`);
        if (variableContainer) {
          variableContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 150);
    }

    this.cdr.markForCheck();
  }


  trackByVariable(index: number, item: any): string {
    return item.id;
  }
  getValueLabelEntries(item: any): ValueLabel[] {
    // If no value_labels or value_labels is null, return empty array
    if (!item.value_labels) return [];

    // Ensure value_labels has proper structure
    if (!item.value_labels.en) item.value_labels.en = {};
    if (!item.value_labels.tr) item.value_labels.tr = {};

    try {
      // Collect all keys
      const allKeys = new Set([
        ...Object.keys(item.value_labels.en || {}),
        ...Object.keys(item.value_labels.tr || {})
      ]);

      // Create entries
      return Array.from(allKeys).map(key => ({
        value: key,
        en: item.value_labels.en[key] || '',
        tr: item.value_labels.tr[key] || ''
      }));
    } catch (error) {
      console.error('Error in getValueLabelEntries:', error);
      return [];
    }
  }

  getValueCount(item: any): number {
    if (!item.value_labels) return 0;
    return Object.keys(item.value_labels.en).length;
  }

  saveValueLabels(item: any): void {
    // Ensure item.value_labels exists
    if (!item.value_labels) {
      // Create empty structure if missing
      item.value_labels = {
        en: {},
        tr: {}
      };
    }

    // Value label validation
    const columnIndex = this.temp_data.variables_json.headers.indexOf(item.header);
    const data = [this.temp_data.variables_json.headers[columnIndex], ...this.temp_data.variables_json.data];
    const itemData = [this.temp_data.variables_json.headers[columnIndex],
    ...this.temp_data.variables_json.data.map(row => row[columnIndex])];

    const isValid = this.validationService.validateVariable(item, itemData);

    if (isValid.length > 0 && isValid.some(issue => issue.type === 'error')) {
      this.snotifyService.warning(isValid[0].message);
      return;
    }

    // Value labels'ı kaydet
    item.value_labels = {
      en: { ...item.value_labels.en },
      tr: { ...item.value_labels.tr }
    };

    // Yeni değerleri orijinal olarak kaydet
    this.originalValueLabels[item.id] = JSON.parse(JSON.stringify(item.value_labels));

    // Save sonrası expanded alanı kapat
    item.expanded = false;

    // Değişkeni yeniden validate et
    this.onVariableChange(item);

    this.checkChanges();
    this.unsavedValueLabels.delete(item.id);
    this.snotifyService.success(
      this.transloco.translate('shared.diagnose.value_labels.updated')
    );
  }

  onValueLabelChange(item: any): void {
    // Değişiklik var mı kontrol et
    if (this.hasValueLabelChanges(item)) {
      this.unsavedValueLabels.add(item.id);
    } else {
      this.unsavedValueLabels.delete(item.id);
    }

    this.checkChanges();
    this.cdr.markForCheck();
  }

  // Yeni method: Value label değişikliklerini kontrol et
  private hasValueLabelChanges(item: any): boolean {
    if (!this.originalValueLabels[item.id] || !item.value_labels) return false;

    const original = this.originalValueLabels[item.id];
    const current = item.value_labels;

    // Her bir değer için TR ve EN etiketlerini karşılaştır
    for (const value of Object.keys(original.en)) {
      if (original.en[value] !== current.en[value] ||
        original.tr[value] !== current.tr[value]) {
        return true;
      }
    }

    for (const value of Object.keys(current.en)) {
      if (!original.en.hasOwnProperty(value)) {
        return true;
      }
    }

    return false;
  }

  async saveAllChanges(): Promise<void> {
    if (!this.hasChanges) return;

    // Açık ve kaydedilmemiş value-label değişiklikleri varsa önce onları kaydetmesini iste
    if (this.selectedVariable && this.unsavedValueLabels.has(this.selectedVariable.id)) {
      const dialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.title'),
          content: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.content'),
          confirm: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.save'),
          cancel: this.transloco.translate('shared.diagnose.confirm.unsaved_labels.discard')
        }
      });

      const result = await dialog.closed.toPromise();
      if (result) {
        // Değişiklikleri kaydet
        this.saveValueLabels(this.selectedVariable);
        if (this.unsavedValueLabels.has(this.selectedVariable.id)) {
          return;
        }
      } else if (result === false) {
        // Değişiklikleri at ve devam et
        this.discardChanges(this.selectedVariable);
      } else {
        // Modal kapatıldı, işlemi iptal et
        return;
      }
    }

    // Validation kontrolü
    const validationIssues = this.validationService.validateVariables(
      this.temp_data.variables_json.variable_list,
      this.temp_data.variables_json.data
    );

    const variablesWithErrors = new Set(
      validationIssues
        .filter(i => i.type == 'error')
        .map(i => i.variableId)
    ).size;

    if (variablesWithErrors > 0) {
      const dialog = this.dialog.open(ValidationSummaryComponent, {
        data: {
          totalCount: this.temp_data.variables_json.variable_list.length,
          invalidCount: variablesWithErrors,
          variables: this.temp_data.variables_json.variable_list,
          validationIssues: validationIssues
        }
      });

      dialog.closed.subscribe(result => {
        if (result) {
          this.proceedWithSave();
        }
      });
    } else {
      await this.proceedWithSave();
    }
  }

  isFilterDropdownOpen = false;
  toggleFilterDropdown(): void {
    this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
  }
  // Filtreleme işlemleri
  filters = {
    importStatus: 'all', // 'all' | 'imported' | 'not-imported'
    measureType: 'all',  // 'all' | 'Scale' | 'Nominal' | 'Ordinal'
    validationStatus: 'all', // 'all' | 'valid' | 'warning' | 'error'
    missingValues: 'all', // 'all' | 'with-missing' | 'no-missing'
  };

  get filteredVariables() {
    if (!this.temp_data?.variables_json?.variable_list) return [];

    let filtered = this.temp_data.variables_json.variable_list;

    // Text search filter
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter((variable: any) =>
        (variable.header?.toString().toLowerCase() || '').includes(query) ||
        (variable.label?.en?.toString().toLowerCase() || '').includes(query) ||
        (variable.label?.tr?.toString().toLowerCase() || '').includes(query)
      );
    }

    // Import status filter
    if (this.filters.importStatus !== 'all') {
      filtered = filtered.filter((variable: any) =>
        this.filters.importStatus === 'imported' ? variable.import : !variable.import
      );
    }

    // Measure type filter
    if (this.filters.measureType !== 'all') {
      filtered = filtered.filter((variable: any) =>
        variable.measure === this.filters.measureType
      );
    }

    // Validation status filter
    if (this.filters.validationStatus !== 'all') {
      filtered = filtered.filter((variable: any) => {
        switch (this.filters.validationStatus) {
          case 'valid':
            return !this.hasIssues(variable.id);
          case 'warning':
            return this.hasWarnings(variable.id);
          case 'error':
            return this.hasErrors(variable.id);
          default:
            return true;
        }
      });
    }

    // Missing values filter
    if (this.filters.missingValues !== 'all') {
      filtered = filtered.filter((variable: any) =>
        this.filters.missingValues === 'with-missing'
          ? variable.missingCount > 0
          : variable.missingCount === 0
      );
    }

    return filtered;
  }

  // Helper method to reset all filters
  resetFilters(): void {
    this.filters = {
      importStatus: 'all',
      measureType: 'all',
      validationStatus: 'all',
      missingValues: 'all'
    };
    this.searchQuery = '';
  }

  // Aktif filtre sayısını hesaplayan yeni method
  getActiveFilterCount(): number {
    let count = 0;
    if (this.filters.importStatus !== 'all') count++;
    if (this.filters.measureType !== 'all') count++;
    if (this.filters.validationStatus !== 'all') count++;
    if (this.filters.missingValues !== 'all') count++;
    return count;
  }

  hasMissingValues(): boolean {
    if (!this.temp_data?.variables_json?.variable_list) return false;

    return this.temp_data.variables_json.variable_list.some((variable: any) =>
      variable.missingCount && variable.missingCount > 0
    );
  }

  // Modal işlemleri
  openMissingValueAnalysis(): void {
    if (!this.hasMissingValues()) {
      this.snotifyService.info(
        this.transloco.translate('shared.diagnose.missing_value.no_missing_values')
      );
      return;
    }

    const positionStrategy = this.overlay.position()
      .global()
      .right('0').top('0');

    const dialog = this.dialog.open(MissingValueComponent, {
      data: { variables_json: this.temp_data.variables_json },
      positionStrategy,
      width: this.deviceService.isDesktop() ? '100%' : '100%',
      height: this.deviceService.isDesktop() ? '100%' : '100%',
    });

    dialog.closed.subscribe((result: any) => {
      if (result) {
        // Update both data sources
        this.updateDataAndView(result);
      }
    });
  }
  validateLabels(item: any): boolean {
    // Basic validation - ensure labels aren't empty
    if (!item.label.en?.trim() || !item.label.tr?.trim()) {
      return false;
    }
    return true;
  }

  // Enhance the existing checkChanges method
  checkChanges(item?: any): void {
    if (item) {
      // Validate labels when they change
      if (!this.validateLabels(item)) {
        this.snotifyService.warning(
          this.transloco.translate('shared.diagnose.messages.invalid_labels')
        );
        return;
      }

      // If measure changed, handle that
      if (item.measure !== this.temp.find((temp: any) => temp.id === item.id).measure) {
        this.updateMeasure(item);
      }
    }

    // Check for any changes in the dataset
    this.hasChanges = JSON.stringify(this.temp_data.variables_json.variable_list) !==
      JSON.stringify(this.temp);// You can also see the exact differences using this helper
    // Trigger change detection
    this.cdr.markForCheck();
  }

  // Add a new method to save all changes
  async saveChanges(): Promise<void> {
    // Sadece import=true olan değişkenler için validation yap

    const validationIssues = this.validationService.validateVariables(
      this.temp_data.variables_json.variable_list,
      this.temp_data.variables_json.data
    );

    if (validationIssues.some(issue => issue.type === 'error')) {
      // Hata varsa dialog göster
      const dialogRef = this.dialog.open(ValidationSummaryComponent, {
        data: {
          invalidCount: this.temp_data.variables_json.variable_list.filter(v =>
            validationIssues.some(issue =>
              issue.variableId === v.id && issue.type === 'error'
            )
          ).length,
          totalCount: this.temp_data.variables_json.variable_list.length,
          message: this.transloco.translate('shared.diagnose.validation_warning_imported', {
            count: validationIssues.filter(i => i.type === 'error').length
          })
        }
      });

      dialogRef.closed.subscribe(result => {
        if (result) {
          this.proceedWithSave();
        }
      });
    } else {
      await this.proceedWithSave();
    }
  }

  async proceedWithSave(): Promise<void> {
    const status = new Observable((observer) => {
      (async () => {
        try {
          // İlk bildirim
          observer.next({
            title: this.transloco.translate('shared.diagnose.saving.title'),
            body: this.transloco.translate('shared.diagnose.saving.message'),
            config: {
              timeout: -1,
              showProgressBar: true,
            }
          });

          // Validation issues işleme
          const variablesWithErrors = new Set(
            this.validationIssues
              .filter(i => i.type === 'error')
              .map(i => i.variableId)
          );

          // Import durumunu güncelle
          this.temp_data.variables_json.variable_list.forEach(variable => {
            if (variablesWithErrors.has(variable.id)) {
              variable.import = false;
              variable.expanded = false;
            }

            // Value labels yapısını doğrula
            if (variable.value_labels) {
              // Derin kopya ile value labels yapısının doğru olmasını sağla
              variable.value_labels = JSON.parse(JSON.stringify(variable.value_labels));
            }
          });

          // Dışa aktarma için veri hazırla
          const dataToExport = {
            ...this.temp_data,
            variables_json: {
              ...this.temp_data.variables_json,
              variable_list: this.temp_data.variables_json.variable_list.map(variable => {
                // Her değişken için doğru yapıyı sağla
                const processedVariable = {
                  ...variable,
                  validationIssues: this.validationIssues.filter(issue => issue.variableId === variable.id)
                };

                // Value labels varsa, doğru şekilde işle
                if (processedVariable.value_labels) {
                  processedVariable.value_labels = JSON.parse(JSON.stringify(processedVariable.value_labels));
                }

                return processedVariable;
              })
            }
          };

          // Import edilen değişkenleri kontrol et
          const hasImportedVariables = this.temp_data.variables_json.variable_list
            .some(variable => variable.import === true);

          const newFileName = this.updateFileName(this.datasetName);

          // Excel'e aktar
          const excelBlob = await this.diagnoseHelper.exportToExcel({
            name: newFileName,
            variables_json: dataToExport.variables_json
          });

          // Dataseti yükle
          const newS3Url = await this.datasetService.uploadDataset(
            excelBlob,
            newFileName,
            this.data.diagnosed_s3_url ? this.data.diagnosed_s3_url : this.data.s3_url
          );

          // Yükleme sonrası gecikme ekle
          await new Promise(resolve => setTimeout(resolve, 3000));

          // Import edilen değişkenlere göre URL'leri güncelle
          if (hasImportedVariables) {
            await new Promise<void>((resolve, reject) => {
              this.datasetService.createDiagnosedDataset(
                this.data.dataset_id,
                newS3Url,
              ).subscribe({
                next: (response: any) => {
                  this.data.diagnosed_s3_url = response.diagnosed_s3_url;
                  resolve();
                },
                error: reject
              });
            });
          } else {
            await this.datasetService.updateDatasetUrl(
              this.data.dataset_id,
              newS3Url
            );
          }

          // Durumu güncelle - bu kısım çok önemli
          // Derin kopyalama kullanarak referans sorunlarından kaçın
          this.data.variables_json = JSON.parse(JSON.stringify(this.temp_data.variables_json));
          this.temp = JSON.parse(JSON.stringify(this.temp_data.variables_json.variable_list));

          // Value labels için durumu güncelle
          if (this.data.variables_json.variable_list) {
            this.data.variables_json.variable_list.forEach(variable => {
              if (variable.value_labels) {
                this.originalValueLabels[variable.id] = JSON.parse(JSON.stringify(variable.value_labels));
              }
            });
          }

          this.data.s3_url = newS3Url;
          this.hasChanges = false;
          this.isProjectUpdated = true;

          // Başarı bildirimi
          observer.next({
            title: this.transloco.translate('shared.diagnose.saving.success_title'),
            body: this.transloco.translate('shared.diagnose.messages.changes_saved'),
            config: {
              closeOnClick: true,
              timeout: 2000,
              showProgressBar: true,
            }
          });

          // Değişiklik algılamayı tetikle
          this.cdr.markForCheck();
          observer.complete();

        } catch (error) {
          console.error('Save process failed:', error);
          observer.error({
            title: this.transloco.translate('shared.diagnose.saving.error_title'),
            body: this.transloco.translate('shared.diagnose.saving.save_error'),
            config: {
              closeOnClick: true,
              timeout: 2000,
              showProgressBar: true,
            }
          });
        }
      })();
    });

    this.snotifyService.async(
      this.transloco.translate('shared.diagnose.saving.progress'),
      status
    );
  }


  updateFileName(name: string): string {
    const fileName = name
    const extension = fileName.substring(fileName.lastIndexOf('.'));
    const fileNameWithProjectId = fileName.replace(extension, '') + new Date().toISOString();
    return this.slugify(fileNameWithProjectId) + extension;
  }
  slugify(text: string): string {
    const turkishMap = {
      'ş': 's',
      'Ş': 'S',
      'ç': 'c',
      'Ç': 'C',
      'ğ': 'g',
      'Ğ': 'G',
      'ü': 'u',
      'Ü': 'U',
      'ö': 'o',
      'Ö': 'O',
      'ı': 'i',
      'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '') // Remove all non-alphanumeric characters except spaces and dashes
      .replace(/\s+/g, '-') // Replace spaces with dashes
      .replace(/-+/g, '-'); // Replace multiple dashes with a single dash
  }
  openAutoRecoding(): void {

    const dialog = this.dialog.open(AutoRecodingComponent, {
      data: {
        variables_json: this.temp_data.variables_json
      },
    });

    dialog.closed.subscribe((result: any) => {
      if (result) {
        this.updateDataAndView(result);
      }
    });
  }
  currentView: 'data' | 'variable' = 'variable';

  toggleView(view: 'data' | 'variable') {
    if (this.currentView !== view) {
      this.currentView = view;
      if (view === 'variable') {
        // Ensure that the latest data is reflected in the variable view
        this.temp_data.variables_json.variable_list = JSON.parse(JSON.stringify(this.data.variables_json.variable_list));
      } else if (view === 'data') {
        // Ensure that the latest data is reflected in the data view
        this.data.variables_json.variable_list = JSON.parse(JSON.stringify(this.temp_data.variables_json.variable_list));
      }
      this.cdr.markForCheck();
    }
  }
  // computeVariable metodunu güncelleyelim
  openComputeVariable(): void {
    const dialog = this.dialog.open(ComputeVariableComponent, {
      data: { variables_json: JSON.parse(JSON.stringify(this.temp_data.variables_json)) },
      width: '100%',
      height: '100%',
    });

    dialog.closed.subscribe((result: any) => {
      if (result) {
        this.updateDataAndView(result);
      }
    });
  }
  async exportToExcel() {
    try {
      const blob = await this.diagnoseHelper.exportToExcel({
        name: this.datasetName,
        variables_json: this.data.variables_json
      });

      // Blob'u indirme
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${this.datasetName}_export_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.snotifyService.success(
        this.transloco.translate('shared.diagnose.messages.export_success')
      );
    } catch (error) {
      console.error('Export error:', error);
      this.transloco.translate('shared.diagnose.messages.export_error')
    }
  }

  async exportDataViewToExcel() {
    try {
      const blob = await this.diagnoseHelper.exportDataviewToExcel({
        name: this.datasetName,
        variables_json: this.data.variables_json
      });

      // Blob'u indirme
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${this.datasetName}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.snotifyService.success(
        this.transloco.translate('shared.diagnose.messages.export_success')
      );
    } catch (error) {
      console.error('Export error:', error);
      this.transloco.translate('shared.diagnose.messages.export_error')
    }
  }

  updateMeasure(item: any) {
    const beforeType = this.temp.find((temp: any) => temp.id === item.id)?.measure;

    // Backup current value labels if not already backed up
    if (item.value_labels && !item.initValueLabels) {
      item.initValueLabels = JSON.parse(JSON.stringify(item.value_labels));
    }

    if (item.measure === 'Scale') {
      // When changing to Scale, store value labels temporarily but set current to null
      item.tempValueLabels = item.value_labels ? JSON.parse(JSON.stringify(item.value_labels)) : null;
      item.value_labels = null;
    } else if ((item.measure === 'Nominal' || item.measure === 'Ordinal')) {
      // Always ensure we have valid value labels for Nominal/Ordinal

      // Check if we have temporary stored labels from a previous Scale conversion
      if (item.tempValueLabels && (beforeType === 'Scale')) {
        item.value_labels = JSON.parse(JSON.stringify(item.tempValueLabels));
        item.tempValueLabels = null;
      } else if (!item.value_labels || beforeType === 'Scale') {
        // Generate new labels from data if no labels exist or coming from Scale
        const columnIndex = this.temp_data.variables_json.headers.indexOf(item.header);
        if (columnIndex !== -1) {
          const uniqueValues = new Set();

          // Collect unique values from the data
          this.temp_data.variables_json.data.forEach((row: any) => {
            const value = row[columnIndex];
            if (value !== null && value !== undefined && value !== '') {
              uniqueValues.add(value.toString());
            }
          });

          // Create value labels
          item.value_labels = {
            en: {},
            tr: {}
          };

          // Add empty value labels for each unique value
          Array.from(uniqueValues).sort().forEach((value: any) => {
            item.value_labels.en[value] = item.initValueLabels?.en?.[value] || '';
            item.value_labels.tr[value] = item.initValueLabels?.tr?.[value] || '';
          });
        } else {
          // Failsafe: ensure we always have a valid structure
          item.value_labels = { en: {}, tr: {} };
        }
      }
    }

    // Update data in both places to ensure consistency
    const dataIndex = this.data.variables_json.variable_list.findIndex(v => v.id === item.id);
    if (dataIndex !== -1) {
      this.data.variables_json.variable_list[dataIndex].measure = item.measure;

      // Ensure value_labels is properly set based on measure type
      if (item.measure === 'Scale') {
        this.data.variables_json.variable_list[dataIndex].value_labels = null;
      } else {
        this.data.variables_json.variable_list[dataIndex].value_labels =
          item.value_labels ? JSON.parse(JSON.stringify(item.value_labels)) : { en: {}, tr: {} };
      }

      // Also update tempValueLabels if it exists
      if (item.tempValueLabels) {
        this.data.variables_json.variable_list[dataIndex].tempValueLabels =
          JSON.parse(JSON.stringify(item.tempValueLabels));
      }
    }

    // If expanded, update the value label entries
    if (item.expanded && item.value_labels) {
      this.valueLabelEntries[item.id] = this.getValueLabelEntries(item);
    }

    // Update original value labels for reference if not Scale
    if (item.measure !== 'Scale' && item.value_labels) {
      this.originalValueLabels[item.id] = JSON.parse(JSON.stringify(item.value_labels));
    }
  }

  onDataChange(event: any) {
    if (!event) return;

    switch (event.type) {
      case 'dataChange':
        // Handle regular data changes
        if (event.data) {
          this.data.variables_json.data = event.data;
        }
        if (event.updatedVariables) {
          event.updatedVariables.forEach((updatedVar: any) => {
            const index = this.data.variables_json.variable_list
              .findIndex(v => v.header === updatedVar.header);
            if (index !== -1) {
              this.data.variables_json.variable_list[index] = {
                ...this.data.variables_json.variable_list[index],
                ...updatedVar
              };

              // Eğer bu değişken expand edilmiş ise value label entries'i güncelle
              const currentVar = this.data.variables_json.variable_list[index];
              if (currentVar.expanded) {
                this.valueLabelEntries[currentVar.id] = this.getValueLabelEntries(currentVar);
              }
            }
          });
        }
        break;

      case 'computedUpdate':
        // Handle computed variable updates
        if (event.variable) {
          const index = this.data.variables_json.variable_list
            .findIndex(v => v.header === event.variable.header);
          if (index !== -1) {
            this.data.variables_json.variable_list[index] = {
              ...this.data.variables_json.variable_list[index],
              autoUpdate: event.autoUpdate
            };
          }
        }
        break;

      case 'cellChange':
        // Handle individual cell changes
        if (Array.isArray(event.changes)) {
          event.changes.forEach(([row, col, oldValue, newValue]) => {
            if (this.data.variables_json.data[row]) {
              this.data.variables_json.data[row][col] = newValue;
            }
          });
        }
        break;

      case 'structureChange':
        // Handle structural changes (row/column additions/deletions)
        if (event.data) {
          this.data.variables_json.data = event.data;
        }
        break;
    }

    // Update temp_data to reflect changes
    this.temp_data = JSON.parse(JSON.stringify(this.data));

    // After updating data, if we're in data view and showing labels,
    // make sure headers are updated accordingly
    if (this.currentView === 'data' && this.showLabels && this.dataView) {
      setTimeout(() => {
        this.toggleColumnDisplay();
      });
    }

    // Validate if necessary
    if (event.type !== 'cellChange') {
      this.validateData();
    }

    // Update UI
    this.checkChanges();
    this.cdr.markForCheck();
  }


  private updateDataAndView(newData: any) {
    // Derin kopya ile güncelle
    this.data.variables_json = JSON.parse(JSON.stringify(newData));
    this.temp_data.variables_json = JSON.parse(JSON.stringify(newData));

    // data-view'a referansı değiştirerek güncelleme sinyali gönder
    this.data = { ...this.data };
    this.checkChanges();

    // Change detection'ı zorla
    this.cdr.markForCheck();
  }

  validateData() {
    // Sadece import=true olan değişkenleri validate et

    this.validationIssues = this.validationService.validateVariables(
      this.data.variables_json.variable_list,
      this.data.variables_json.data
    );
  }

  onVariableChange(variable: any) {
    // Update the temp_data to reflect changes
    const index = this.temp_data.variables_json.variable_list.findIndex(v => v.id === variable.id);
    if (index !== -1) {
      this.temp_data.variables_json.variable_list[index] = { ...variable };
    }

    // Eğer import=false ise validation yapma
    if (!variable.import) {

      // Varsa önceki validation sonuçlarını temizle
      this.validationIssues = this.validationIssues.filter(i => i.variableId !== variable.id);
      this.cdr.markForCheck();
      return;
    }
    const columnIndex = this.temp_data.variables_json.headers.indexOf(variable.header);
    const data = [this.temp_data.variables_json.headers[columnIndex], ...this.temp_data.variables_json.data]
    const itemData = [this.temp_data.variables_json.headers[columnIndex], ...this.temp_data.variables_json.data.map(row => row[columnIndex])]
    // Sadece değişen değişkeni validate et
    const issues = this.validationService.validateVariable(variable, itemData);

    // Mevcut issue'ları güncelle
    this.validationIssues = this.validationIssues.filter(i => i.variableId !== variable.id);
    this.validationIssues.push(...issues);
    this.checkChanges();
    this.cdr.markForCheck();
  }

  private generateValueLabelsFromData(variable: any): void {
    // Generate new labels from data
    const columnIndex = this.temp_data.variables_json.headers.indexOf(variable.header);
    if (columnIndex !== -1) {
      const uniqueValues = new Set();

      // Collect unique values from the data
      this.temp_data.variables_json.data.forEach((row: any) => {
        const value = row[columnIndex];
        if (value !== null && value !== undefined && value !== '') {
          uniqueValues.add(value.toString());
        }
      });

      // Create empty value labels structure
      variable.value_labels = {
        en: {},
        tr: {}
      };

      // Add empty value labels for each unique value
      Array.from(uniqueValues).sort().forEach((value: any) => {
        variable.value_labels.en[value] = '';
        variable.value_labels.tr[value] = '';
      });
    } else {
      // Failsafe: ensure we always have a valid structure even if column not found
      variable.value_labels = { en: {}, tr: {} };
    }
  }

  onTypeChange(variable: any): void {
    // Get previous measure type
    const beforeType = this.data.variables_json.variable_list.find((element: any) => element.id === variable.id)?.measure;

    // Always backup current value labels if they exist and we don't have a backup yet
    if (variable.value_labels && !variable.initValueLabels) {
      variable.initValueLabels = JSON.parse(JSON.stringify(variable.value_labels));
    }

    if (variable.measure === 'Scale') {
      // When changing to Scale, keep a copy but set value_labels to null
      variable.tempValueLabels = variable.value_labels ? JSON.parse(JSON.stringify(variable.value_labels)) : null;
      variable.value_labels = null;
    } else if ((variable.measure === 'Nominal' || variable.measure === 'Ordinal')) {
      // For Nominal/Ordinal measures

      // IMPORTANT FIX: Check if we're switching between categorical types (Nominal/Ordinal)
      // If so, preserve the existing value labels rather than regenerating them
      if (beforeType === 'Nominal' || beforeType === 'Ordinal') {
        // Do not regenerate value labels when switching between categorical types
        // Just ensure the structure is valid
        if (!variable.value_labels) {
          variable.value_labels = { en: {}, tr: {} };
        }
      }
      // Coming from Scale type, try to restore previous labels or generate new ones
      else if (beforeType === 'Scale') {
        // First try to retrieve previously saved labels
        if (variable.tempValueLabels) {
          variable.value_labels = JSON.parse(JSON.stringify(variable.tempValueLabels));
          variable.tempValueLabels = null;
        } else {
          // Generate new labels from data
          this.generateValueLabelsFromData(variable);
        }
      }
      // For initial setup or other cases
      else if (!variable.value_labels) {
        this.generateValueLabelsFromData(variable);
      }

      // Update both data sources to ensure consistency
      const dataIndex = this.data.variables_json.variable_list.findIndex(v => v.id === variable.id);
      if (dataIndex !== -1) {
        this.data.variables_json.variable_list[dataIndex].measure = variable.measure;
        this.data.variables_json.variable_list[dataIndex].value_labels =
          variable.value_labels ? JSON.parse(JSON.stringify(variable.value_labels)) : { en: {}, tr: {} };
      }

      // If this variable is expanded, update the value label entries
      if (variable.expanded) {
        this.valueLabelEntries[variable.id] = this.getValueLabelEntries(variable);
      }

      // Store original value labels
      this.originalValueLabels[variable.id] = JSON.parse(JSON.stringify(variable.value_labels));
    }

    // Run validation and update UI
    this.onVariableChange(variable);
    this.checkChanges();

    // Show success notification
    this.snotifyService.success(
      this.transloco.translate('shared.diagnose.messages.measure_updated'),
      {
        timeout: 2000,
        showProgressBar: true,
        closeOnClick: true
      }
    );
  }

  getStatusText(variableId: string): string {
    if (this.hasErrors(variableId)) return 'error';
    if (this.hasWarnings(variableId)) return 'warning';
    return ''; // No status when valid
  }

  getStatusIcon(variableId: string): string {
    if (this.hasErrors(variableId)) return 'heroExclamationCircle';
    if (this.hasWarnings(variableId)) return 'heroExclamationTriangle';
    return ''; // No icon when valid
  }


  // Belirli bir değişkenin durumu
  hasIssues(variableId: string): boolean {
    return this.validationIssues.some(issue => issue.variableId === variableId && issue.type === 'warning');
  }

  getIssues(variableId: string): ValidationIssue[] {
    return this.validationIssues.filter(issue => issue.variableId === variableId);
  }

  hasErrors(variableId: string): boolean {
    return this.validationIssues.some(
      issue => issue.variableId === variableId && issue.type === 'error'
    );
  }

  hasWarnings(variableId: string): boolean {
    return this.validationIssues.some(
      issue => issue.variableId === variableId && issue.type === 'warning'
    );
  }

  hasAnyErrors(): boolean {
    return this.validationIssues.some(issue => issue.type === 'error');
  }

  hasAnyWarnings(): boolean {
    return this.validationIssues.some(issue => issue.type === 'warning');
  }


  // Validation summary methods update
  getValidCount(): number {
    const importedVariables = this.data.variables_json.variable_list.filter(v => v.import);
    const uniqueErrorVariables = new Set<string>();

    this.validationIssues.forEach(issue => {
      if (issue.type === 'error') {
        const variable = importedVariables.find(v => v.id === issue.variableId);
        if (variable) {
          uniqueErrorVariables.add(issue.variableId);
        }
      }
    });

    return importedVariables.length - uniqueErrorVariables.size;
  }

  getWarningCount(): number {
    const importedVariables = this.data.variables_json.variable_list.filter(v => v.import);
    const uniqueWarningVariables = new Set<string>();

    this.validationIssues.forEach(issue => {
      if (issue.type === 'warning') {
        const variable = importedVariables.find(v => v.id === issue.variableId);
        if (variable) {
          uniqueWarningVariables.add(issue.variableId);
        }
      }
    });

    return uniqueWarningVariables.size;
  }

  getErrorCount(): number {
    const importedVariables = this.data.variables_json.variable_list.filter(v => v.import);
    const uniqueErrorVariables = new Set<string>();

    this.validationIssues.forEach(issue => {
      if (issue.type === 'error') {
        const variable = importedVariables.find(v => v.id === issue.variableId);
        if (variable) {
          uniqueErrorVariables.add(issue.variableId);
        }
      }
    });

    return uniqueErrorVariables.size;
  }

  resetChanges(): void {
    if (!this.hasChanges) return;

    const dialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: this.transloco.translate('shared.diagnose.confirm.reset.title'),
        content: this.transloco.translate('shared.diagnose.confirm.reset.content'),
        confirm: this.transloco.translate('shared.diagnose.confirm.reset.confirm'),
        cancel: this.transloco.translate('shared.diagnose.confirm.reset.cancel')
      }
    });

    dialog.closed.subscribe((confirmed) => {
      if (confirmed) {
        // Orijinal value label değerlerini de sıfırla
        this.originalValueLabels = {};
        this.unsavedValueLabels.clear();

        // Reset both data sources
        this.temp_data = JSON.parse(JSON.stringify(this.originalData));
        this.data = JSON.parse(JSON.stringify(this.originalData));

        // Reset DataView if it exists and we're in data view
        if (this.currentView === 'data' && this.dataView) {
          const hot = this.dataView.hotRegisterer.getInstance(this.dataView.id);
          if (hot) {
            hot.loadData(this.data.variables_json.data);
            hot.updateSettings({
              colHeaders: this.data.variables_json.headers,
              columns: this.data.variables_json.headers.map((header, index) => {
                const variable = this.data.variables_json.variable_list.find(v => v.header === header);
                return {
                  data: index,
                  type: (() => {
                    // Sütundaki tüm değerleri kontrol et
                    const values = this.data.variables_json.data.map(row => row[index]).filter(val => val !== null && val !== undefined && val !== '');

                    // Tüm değerler sayısal mı kontrol et
                    const allNumeric = values.every(val => !isNaN(Number(val)));
                    if (allNumeric) {
                      return 'numeric';
                    }
                    return 'text';
                  })(),
                  readOnly: variable?.computed || false,
                  headerClassName: variable?.computed ? 'bg-green-100 text-green-600' : '',
                };
              })
            });
            hot.render();
          }
        }

        this.hasChanges = false;
        this.validateData();
        this.cdr.markForCheck();
      }
    });
  }

  openValidationDetails() {
    const positionStrategy = this.overlay.position()
      .global()
      .right('0').top('0');

    const dialogRef = this.dialog.open(ValidationDetailsComponent, {
      positionStrategy,
      data: {
        variables: this.data.variables_json.variable_list,
        issues: this.validationIssues,
        getIssues: (id: string) => this.getIssues(id),
        hasErrors: (id: string) => this.hasErrors(id),
        hasWarnings: (id: string) => this.hasWarnings(id)
      }
    });

    // Get the component instance
    const componentInstance = dialogRef.componentInstance as ValidationDetailsComponent;

    // Regular variable selection handler
    componentInstance.variableSelected.subscribe((variableId: string) => {
      // Close the validation details panel
      dialogRef.close();

      // If in data view, switch to variable view
      if (this.currentView !== 'variable') {
        this.toggleView('variable');
      }

      // Scroll to and expand the variable
      setTimeout(() => {
        this.scrollToAndExpandVariable(variableId);
      }, 100);
    });

    // Add this new handler for missing value analysis
    componentInstance.openMissingValueAnalysis.subscribe((variableId: string) => {
      // Close the dialog
      dialogRef.close();

      // Open the data management dialog
      this.openDataManagementDialog();

      // Then open missing value analysis
      setTimeout(() => {
        this.closeDataManagementDialog();
        this.openMissingValueAnalysis();
      }, 300);
    });
  }

  ngAfterViewInit() {
    if (this.dataView) {
      const hot = this.dataView.hotRegisterer.getInstance(this.dataView.id);
      if (hot) {
        // Get the table container element
        const container = hot.rootElement.querySelector('.ht_master .wtHolder');
        if (container) {
          container.addEventListener('scroll', () => {
            // Force header realignment
            hot.render();
          });
        }
      }
    }
  }


  scrollToAndExpandVariable(variableId: string): void {
    // Find the variable
    const variable = this.temp_data.variables_json.variable_list.find(v => v.id === variableId);
    if (!variable) return;

    // Close any other expanded variables first
    this.temp_data.variables_json.variable_list.forEach(v => {
      if (v.id !== variableId) {
        v.expanded = false;
      }
    });

    // Expand the target variable
    variable.expanded = true;
    this.selectedVariable = variable;

    // If it has value labels, initialize them
    if (variable.measure !== 'Scale' && variable.value_labels) {
      this.valueLabelEntries[variableId] = this.getValueLabelEntries(variable);
    }

    // Scroll to the variable element
    setTimeout(() => {
      const variableElement = document.querySelector(`[data-variable-id="${variableId}"]`);
      if (variableElement) {
        variableElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Highlight the element temporarily to draw attention
        variableElement.classList.add('pulse-highlight');
        setTimeout(() => {
          variableElement.classList.remove('pulse-highlight');
        }, 1500);
      }
    }, 50);

    // Trigger change detection
    this.cdr.markForCheck();
  }

  async closeModal() {
    if (this.unsavedValueLabels.size > 0 || this.hasChanges) {
      const dialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('shared.diagnose.confirm.changes.title'),
          content: this.transloco.translate('shared.diagnose.confirm.changes.content'),
          confirm: this.transloco.translate('shared.diagnose.confirm.changes.confirm'),
          cancel: this.transloco.translate('shared.diagnose.confirm.changes.cancel')
        }
      });

      dialog.closed.subscribe(async (result) => {
        if (result) {
          try {
            // Tüm değişkenleri DataTransferService'e aktar
            if (this.temp_data?.variables_json?.variable_list) {
              this.dt.setAllVariables(this.temp_data.variables_json.variable_list);
            }

            this.animationState = 'out';
            setTimeout(() => this.dialogRef.close({ saved: true, projectId: this.data.project_id }), 300);
          } catch (error) {
            console.error('Error while closing modal:', error);
            this.snotifyService.error(
              this.transloco.translate('shared.diagnose.messages.save_error')
            );
          }
        }
      });
    } else {
      // Değişiklik yoksa
      // Tüm değişkenleri DataTransferService'e aktar
      if (this.temp_data?.variables_json?.variable_list) {
        this.dt.setAllVariables(this.temp_data.variables_json.variable_list);
      }

      this.animationState = 'out';
      setTimeout(() => this.dialogRef.close({ saved: false, projectId: this.data.project_id }), 300);
    }
  }
  totalIssues = 0;

  updateTotalIssues() {
    this.totalIssues = this.getWarningCount() + this.getErrorCount();
    this.cdr.detectChanges(); // veya this.cdr.markForCheck();
  }


  public openDataManagementDialog(): void {
    const dialog = document.getElementById('dataManagementDialog') as HTMLDialogElement;
    if (dialog) {
      dialog.style.opacity = '0';
      dialog.style.transition = 'opacity 0.1s ease-out';

      dialog.showModal();

      requestAnimationFrame(() => {
        dialog.style.opacity = '1';
      });
    }
  }

  public closeDataManagementDialog(): void {
    const dialog = document.getElementById('dataManagementDialog') as HTMLDialogElement;
    if (dialog) {
      dialog.style.opacity = '0';

      setTimeout(() => {
        dialog.close();
      }, 100);
    }
  }

  // diagnose.component.ts'deki toggleColumnDisplay metodunu tamamen değiştirin
  // diagnose.component.ts
  toggleColumnDisplay(): void {
    if (this.currentView !== 'data' || !this.dataView) {
      return;
    }

    // NgZone.run kullanarak Angular'ın değişiklik tespitini tetikleyin
    this.ngZone.run(() => {
      const hot = this.dataView.hotRegisterer.getInstance(this.dataView.id);
      if (!hot || !this.data?.variables_json) {
        console.error('Handsontable instance veya data bulunamadı');
        return;
      }

      // dataView'daki showLabels değerini güncelle
      this.dataView.showLabels = this.showLabels;

      // Tüm başlıkları al
      const headers = this.data.variables_json.headers;
      const variables = this.data.variables_json.variable_list || [];

      // Yeni başlık dizisi oluştur
      const newHeaders = headers.map((header: string) => {
        // İlgili değişkeni bul
        const variable = variables.find(v => v.header === header);
        if (!variable) return header;

        // Label gösterme modunda ve label varsa
        if (this.showLabels) {
          const currentLanguage = this.transloco.getActiveLang();
          const label = currentLanguage === 'tr' ?
            (variable.label?.tr || header) :
            (variable.label?.en || header);

          return label;
        }

        // Normal header gösterme modu
        return header;
      });

      // Handsontable'ı doğrudan güncelle
      hot.updateSettings({
        colHeaders: newHeaders
      });

      // Zorla render et
      hot.render();

      // İşlem tamamlandıktan sonra bir timeout ile yeniden render et
      setTimeout(() => {
        hot.render();
        this.cdr.detectChanges(); // Force change detection

      }, 100);

      // Bildirim göster
      this.snotifyService.info(
        this.transloco.translate(
          this.showLabels
            ? 'shared.diagnose.show_label_desc'
            : 'shared.diagnose.show_header_desc'
        ),
        {
          timeout: 2000,
          showProgressBar: false,
          closeOnClick: true
        }
      );

      this.cdr.detectChanges(); // Change detection'ı zorla
    });
  }
  // Helper method to generate column headers based on current display mode
  private generateColumnHeaders(): string[] {
    if (!this.data?.variables_json?.headers || !this.data?.variables_json?.variable_list) {
      return [];
    }

    if (this.showLabels) {
      // Use variable labels as column headers
      return this.data.variables_json.headers.map(header => {
        const variable = this.data.variables_json.variable_list.find(v => v.header === header);
        if (!variable) return header;

        // Get the appropriate label based on current language
        const currentLang = this.transloco.getActiveLang();
        const label = currentLang === 'tr'
          ? (variable.label?.tr || header)
          : (variable.label?.en || header);

        return label;
      });
    } else {
      // Use original headers
      return [...this.data.variables_json.headers];
    }
  }

  allVariablesSelected: boolean = false;

  // Add this method to the DiagnoseComponent class
  toggleSelectAllVariables(): void {
    // Determine the new state based on current state
    const newState = !this.allVariablesSelected;
    this.allVariablesSelected = newState;

    // First, clear all validation issues to avoid duplicates
    this.validationIssues = [];

    // Toggle all variables' import status
    if (this.temp_data?.variables_json?.variable_list) {
      // Apply the new state to all variables
      this.temp_data.variables_json.variable_list.forEach((variable: any) => {
        // Only change if the state is different to avoid unnecessary validations
        if (variable.import !== newState) {
          variable.import = newState;

          // Update the same variable in the data object
          const dataIndex = this.data.variables_json.variable_list.findIndex(v => v.id === variable.id);
          if (dataIndex !== -1) {
            this.data.variables_json.variable_list[dataIndex].import = newState;
          }
        }
      });

      // After setting all variables, perform a single validation pass
      if (newState) {
        // Only validate if we're selecting variables (not if deselecting)
        const variablesToValidate = this.temp_data.variables_json.variable_list.filter(v => v.import);
        this.validationIssues = this.validationService.validateVariables(
          variablesToValidate,
          this.data.variables_json.data
        );
      }
    }

    // Show a notification
    const message = this.allVariablesSelected ?
      this.transloco.translate('shared.diagnose.messages.all_selected') :
      this.transloco.translate('shared.diagnose.messages.all_deselected');

    this.snotifyService.info(message, {
      timeout: 2000,
      showProgressBar: true,
      closeOnClick: true
    });

    // Check for changes and update the UI
    this.checkChanges();
    this.updateTotalIssues(); // Update the total issues count
    this.cdr.markForCheck();
  }

  updateAllSelectedState(): void {
    if (this.temp_data?.variables_json?.variable_list) {
      // Check if all variables are imported
      const allImported = this.temp_data.variables_json.variable_list.every((variable: any) => variable.import === true);
      this.allVariablesSelected = allImported;
      this.cdr.markForCheck();
    }
  }

  clearSearch(): void {
    this.searchQuery = '';
    // Trigger change detection to update the UI
    this.cdr.markForCheck();
  }

  hasAvailableLabels(currentItem: any): boolean {
    // Get all value labels
    const availableLabels = this.getValueLabels().filter((item: any) =>
      item.labels !== currentItem.value_labels
    );

    // Return true if there are available labels
    return availableLabels.length > 0;
  }

  getIssueMessages(variableId: string): string {
    const issues = this.validationIssues.filter(issue => issue.variableId === variableId);
    if (issues.length === 0) return '';

    // Format the messages with bullet points for multiple issues
    return issues.map(issue => issue.message).join('\n• ');
  }

  //TODO Feauture
  // importFromExcel(event: any) {
  //   try {
  //     const file = event.target.files[0];
  //     if (!file) return;

  //     // Add confirmation dialog before proceeding
  //     const dialog = this.dialog.open(ConfirmComponent, {
  //       data: {
  //         title: this.transloco.translate('shared.confirm.import.title'),
  //         content: this.transloco.translate('shared.confirm.import.content'),
  //         confirm: this.transloco.translate('shared.confirm.import.confirm'),
  //         cancel: this.transloco.translate('shared.confirm.import.cancel')
  //       }
  //     });

  //     dialog.closed.subscribe((confirmed) => {
  //       if (confirmed) {
  //         const reader = new FileReader();
  //         reader.onload = (e: any) => {
  //           try {
  //             // Excel dosyasını oku
  //             const exceldata = new Uint8Array(e.target.result);
  //             const workbook = XLSX.read(exceldata, { type: 'array' });

  //             // Önce Data View sayfasını işle
  //             const dataSheet = workbook.Sheets["Data View"] || workbook.Sheets[workbook.SheetNames[0]];
  //             if (!dataSheet) {
  //               throw new Error('No valid data sheet found');
  //             }

  //             // Header'lar ile veriyi al
  //             const rawData = XLSX.utils.sheet_to_json(dataSheet, { header: 1 });
  //             const headers = rawData[0] as string[];
  //             const data = rawData.slice(1);

  //             // Variable View sayfasını kontrol et veya oluştur
  //             let variableList;
  //             const variableSheet = workbook.Sheets["Variable View"];

  //             if (variableSheet) {
  //               // Variable View sayfası varsa, mevcut bilgileri kullan
  //               const variableData = XLSX.utils.sheet_to_json(variableSheet);
  //               variableList = variableData.map((row: any) => ({
  //                 id: row['Variable Name'],
  //                 header: row['Variable Name'],
  //                 label: {
  //                   en: row['Label (EN)'] || row['Variable Name'],
  //                   tr: row['Label (TR)'] || row['Variable Name']
  //                 },
  //                 measure: row['Type'] || 'Scale',
  //                 import: true,
  //                 value_labels: this.parseValueLabels(row['Value Labels (EN)'], row['Value Labels (TR)']),
  //                 computed: row['Is Computed'] === 'Yes',
  //                 formula: row['Formula'] || null
  //               }));
  //             } else {
  //               // Variable View sayfası yoksa, header'lardan otomatik oluştur
  //               variableList = headers.map(header => {
  //                 // Veri tipini belirle
  //                 const columnData = data.map(row => row[headers.indexOf(header)]).filter(val => val != null);
  //                 const measure = this.determineDataType(columnData);

  //                 return {
  //                   id: header,
  //                   header: header,
  //                   label: {
  //                     en: header,
  //                     tr: header
  //                   },
  //                   measure: measure,
  //                   import: true,
  //                   value_labels: measure !== 'Scale' ? this.generateValueLabels(columnData) : null,
  //                   computed: false,
  //                   formula: null
  //                 };
  //               });
  //             }

  //             // variables_json yapısını güncelle
  //             this.data.variables_json = {
  //               headers: headers,
  //               variable_list: variableList,
  //               data: data
  //             };

  //             // Temp datayı güncelle
  //             this.temp_data = JSON.parse(JSON.stringify(this.data));

  //             // DataView'i güncelle
  //             if (this.dataView) {
  //               const hot = this.dataView.hotRegisterer.getInstance(this.dataView.id);
  //               if (hot) {
  //                 hot.updateSettings({
  //                   data: this.data.variables_json.data,
  //                   colHeaders: this.data.variables_json.headers,
  //                   columns: this.data.variables_json.headers.map((header, index) => {
  //                     const variable = variableList.find(v => v.header === header);
  //                     return {
  //                       data: index,
  //                       type: this.getColumnType(variable?.measure),
  //                       readOnly: variable?.computed || false
  //                     };
  //                   })
  //                 });
  //                 hot.render();
  //               }
  //             }

  //             // Validation kontrollerini yap
  //             this.validateData();

  //             // Validation durumuna göre bildirim göster
  //             if (this.validationIssues.some(issue => issue.type === 'error')) {
  //               this.snotifyService.warning(this.transloco.translate('shared.diagnose.import.with_validation'),
  //                 {
  //                   timeout: 5000,
  //                   showProgressBar: true,
  //                   closeOnClick: true,
  //                   buttons: [{
  //                     text: this.transloco.translate('shared.diagnose.import.view_details'),
  //                     action: () => this.openValidationDetails()
  //                   }]
  //                 });
  //             } else {
  //               this.snotifyService.success(this.transloco.translate('shared.diagnose.import.success'));
  //             }

  //             this.cdr.markForCheck();

  //           } catch (error) {
  //             console.error('Import parsing error:', error);
  //             this.snotifyService.error(
  //               this.transloco.translate('shared.diagnose.import.error')
  //             );
  //           }
  //         };

  //         reader.onerror = () => {
  //           this.snotifyService.error(
  //             this.transloco.translate('shared.diagnose.import.error')
  //           );
  //         };

  //         reader.readAsArrayBuffer(file);
  //       }
  //       // Clear file input in either case
  //       this.fileInput.nativeElement.value = '';
  //     });

  //   } catch (error) {
  //     console.error('Import error:', error);
  //     this.snotifyService.error(
  //       this.transloco.translate('shared.diagnose.import.error')
  //     );
  //   }
  // }
  // private getColumnType(measure: string): 'text' | 'numeric' {
  //   return 'text';
  // }
  // private parseValueLabels(enLabels: string, trLabels: string): any {
  //   if (!enLabels && !trLabels) return null;

  //   try {
  //     return {
  //       en: enLabels ? JSON.parse(enLabels) : {},
  //       tr: trLabels ? JSON.parse(trLabels) : {}
  //     };
  //   } catch {
  //     // Parse hatası durumunda null dön
  //     return null;
  //   }
  // }
  // private determineDataType(values: any[]): 'Scale' | 'Nominal' | 'Ordinal' {
  //   // Sayısal değerlerin oranını hesapla
  //   const numericCount = values.filter(val => !isNaN(val) && val !== '').length;
  //   const totalCount = values.length;
  //   const numericRatio = numericCount / totalCount;

  //   // Benzersiz değerlerin sayısını hesapla
  //   const uniqueValues = new Set(values).size;

  //   if (numericRatio > 0.8) {
  //     return 'Scale';
  //   } else if (uniqueValues < 10) {
  //     return 'Nominal';
  //   } else {
  //     return 'Scale';
  //   }
  // }

  // private generateValueLabels(values: any[]): { en: { [key: string]: string }, tr: { [key: string]: string } } | null {
  //   const uniqueValues = Array.from(new Set(values))
  //     .filter(val => val != null && val !== '')
  //     .sort();

  //   if (uniqueValues.length > 20) return null; // Çok fazla benzersiz değer varsa value label oluşturma

  //   const labels = {
  //     en: {} as { [key: string]: string },
  //     tr: {} as { [key: string]: string }
  //   };

  //   uniqueValues.forEach(value => {
  //     labels.en[value] = value.toString();
  //     labels.tr[value] = value.toString();
  //   });

  //   return labels;
  // }

}

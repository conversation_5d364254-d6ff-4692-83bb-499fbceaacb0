<div class="flex flex-col bg-gray-50 rounded-2xl" *transloco="let t; read: 'shared.imputation_modal'">
    <!-- Form Content -->
    <div class="flex-1 p-4">
        <!-- No Methods Warning -->
        <div *ngIf="availableMethods.length === 0"
            class="p-4 mb-4 text-yellow-700 border border-yellow-200 rounded-lg bg-yellow-50">
            <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                    <ng-icon name="heroExclamationTriangle" class="text-yellow-600"></ng-icon>
                    <p class="font-medium">{{ t('no_applicable_methods_title') }}</p>
                </div>
                <p class="ml-6 text-sm">{{ t('no_applicable_methods_description', {
                    type: data.variableType,
                    variable: data.variableName
                    }) }}</p>
                <ul class="mt-2 ml-6 text-sm list-disc list-inside">
                    <li>{{ t('no_applicable_methods_hint_1') }}</li>
                    <li>{{ t('no_applicable_methods_hint_2') }}</li>
                </ul>
            </div>
        </div>

        <form [formGroup]="form" class="space-y-4" *ngIf="availableMethods.length > 0">
            <!-- Adım 1: Method Selection ve Preview -->
            <div *ngIf="currentStep === 'method'">
                <!-- Metot Seçimi -->
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <div *ngFor="let method of availableMethods" class="cursor-pointer"
                        [ngClass]="form.get('method').value === method.id ? 'primary-blue-button' : 'secondary-blue-button'"
                        [class.border-blue-500]="form.get('method').value === method.id"
                        [class.bg-blue-50]="form.get('method').value === method.id"
                        (click)="form.get('method').setValue(method.id)">
                        <div class="flex items-center justify-between w-full gap-3">
                            <div class="flex-grow">
                                <h4 class="text-lg font-medium">{{ method.name }}</h4>
                                <p class="mt-1 text-sm">{{ method.description }}</p>
                            </div>
                            <div
                                class="flex items-center justify-center w-6 h-6 bg-white border-2 rounded-full shadow border-brand-blue-50">
                                <ng-icon *ngIf="form.get('method').value === method.id" name="lucideCheck"
                                    class="text-xl text-brand-blue-500"></ng-icon>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Preview Bölümü (otomatik güncellenir) -->
                <div *ngIf="previewData" class="p-4 mt-4 border rounded-3xl">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-lg font-semibold text-blue-900">{{ t('preview') }}</h3>
                        <div class="text-sm text-gray-600">
                            {{ t('showing_first_5') }}
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr>
                                    <th class="p-2 text-left">{{ t('row') }}</th>
                                    <th class="p-2 text-left">{{ t('original_value') }}</th>
                                    <th class="p-2 text-left">{{ t('new_value') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let item of previewData | slice:0:5">
                                    <td class="p-2">{{ item.row }}</td>
                                    <td class="p-2">
                                        <span class="px-2 py-1 text-sm text-red-600 bg-red-100 rounded-full">
                                            {{ t('missing') }}
                                        </span>
                                    </td>
                                    <td class="p-2">
                                        <span class="px-2 py-1 text-sm text-green-600 bg-green-100 rounded-full">
                                            {{ item.newValue }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Adım 2: Name & Labels -->
            <div *ngIf="currentStep === 'name'" class="mt-4">
                <div class="space-y-4">
                    <div>
                        <span class="flex w-full text-sm font-medium text-center text-blue-900">{{ t('variable_name')
                            }}</span>
                        <input formControlName="name"
                            class="w-full px-3 py-2 mt-1 border border-neutral-200 rounded-3xl focus:ring-blue-500"
                            [placeholder]="t('variable_name_placeholder')">
                    </div>
                    <!-- <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-blue-900">{{ t('label_en') }}</label>
                            <input formControlName="labelEN" 
                                   class="w-full px-3 py-2 mt-1 border rounded-lg focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-blue-900">{{ t('label_tr') }}</label>
                            <input formControlName="labelTR" 
                                   class="w-full px-3 py-2 mt-1 border rounded-lg focus:ring-blue-500">
                        </div>
                    </div> -->
                </div>
            </div>
        </form>
    </div>

    <!-- Footer -->
    <div class="flex justify-between gap-3 p-4">
        <!-- Adım 1 Footer: İleri Butonu -->
        <button *ngIf="currentStep === 'method'" (click)="closeModal()" class="secondary-blue-button">
            {{t('back')}}
        </button>
        <button *ngIf="currentStep === 'method' && previewData" (click)="nextStep()"
            [disabled]="!form.get('method').value || !previewData" class="primary-blue-button">
            {{ t('next') }}
        </button>
        <!-- Adım 2 Footer: Geri ve Oluştur Butonları -->
        <ng-container *ngIf="currentStep === 'name'">
            <button type="button" (click)="previousStep()" class="secondary-blue-button">
                {{ t('back') }}
            </button>
            <button type="button" (click)="applyImputation()" [disabled]="!form.valid" class="primary-blue-button">
                {{ t('create_variable') }}
            </button>
        </ng-container>
    </div>
</div>
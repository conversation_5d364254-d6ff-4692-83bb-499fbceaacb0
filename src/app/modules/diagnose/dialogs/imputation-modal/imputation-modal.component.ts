import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';

interface ImputationMethod {
  id: string;
  name: string;
  description: string;
  applicableTypes: string[];
}
interface NewVariableConfig {
  name: string;
  labelEN: string;
  labelTR: string;
  method: 'mean' | 'median' | 'mode';
}

@Component({
  selector: 'app-imputation-modal',
  templateUrl: './imputation-modal.component.html',
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class ImputationModalComponent implements OnInit {

  // Mevcut değişken isimlerini kontrol etmek için yardımcı method
  private isVariableNameExists(name: string): boolean {
    if (!this.data || !this.data.variables_json || !this.data.variables_json.variable_list) {
      return false;
    }

    return this.data.variables_json.variable_list.some(
      (variable: any) => variable.header === name || variable.id === name
    );
  }
  // Veriler parent bileşenden @Input ile gelecek
  @Input() data: any;
  // Modal kapanırken updatedData veya null göndermek için @Output event emitter
  @Output() closeEvent = new EventEmitter<any>();

  animationState = 'in';
  selectedMethod: string = '';
  previewData: any[] | null = null;
  form = new FormGroup({
    name: new FormControl('', [Validators.required]),
    labelEN: new FormControl(''),
    labelTR: new FormControl(''),
    method: new FormControl('', [Validators.required])
  });

  methods: ImputationMethod[] = [];
  availableMethods: ImputationMethod[] = [];

  currentStep: string = 'method';

  constructor(
    private snotifyService: SnotifyService,
    private diagnoseHelper: DiagnoseHelperService,
    private translate: TranslocoService
  ) { }

  ngOnInit() {
    this.methods = [
      {
        id: 'mean',
        name: this.translate.translate('shared.imputation_modal.methods.mean.name'),
        description: this.translate.translate('shared.imputation_modal.methods.mean.description'),
        applicableTypes: ['Scale']
      },
      {
        id: 'median',
        name: this.translate.translate('shared.imputation_modal.methods.median.name'),
        description: this.translate.translate('shared.imputation_modal.methods.median.description'),
        applicableTypes: ['Scale', 'Nominal', 'Ordinal']
      },
      {
        id: 'mode',
        name: this.translate.translate('shared.imputation_modal.methods.mode.name'),
        description: this.translate.translate('shared.imputation_modal.methods.mode.description'),
        applicableTypes: ['Nominal', 'Ordinal']
      }
    ];

    this.filterApplicableMethods();
    this.updateDefaultVariableName();

    // "method" alanındaki değişiklikleri dinleyip, variableName'i güncelle
    this.form.get('method')?.valueChanges.subscribe(method => {
      this.updateDefaultVariableName();
      this.generatePreview();
    });
  }

  // Eksik Değer İsimlendirme Standartlarına uygun şekilde değişken adı oluştur
  updateDefaultVariableName() {
    const method = this.form.get('method')?.value;
    let defaultName = '';

    if (method) {
      // İsimlendirme standartlarını uygula
      switch (method) {
        case 'mean':
          defaultName = `imp_mean_${this.data.variableName}`;
          break;
        case 'median':
          defaultName = `imp_med_${this.data.variableName}`;
          break;
        case 'mode':
          defaultName = `imp_mod_${this.data.variableName}`;
          break;
      }

      // Değişken isim uzunluğu kontrolü (maksimum 50 karakter)
      if (defaultName.length > 50) {
        defaultName = defaultName.substring(0, 50);
      }
    }

    // Değişken adının benzersiz olduğundan emin ol
    if (method) {
      let suffix = '';
      let counter = 0;

      // Aynı isimli değişken varsa numaralandırma ekle
      let isNameExists = true;
      while (isNameExists) {
        // Default isim oluştur
        switch (method) {
          case 'mean':
            defaultName = `imp_mean_${this.data.variableName}${suffix}`;
            break;
          case 'median':
            defaultName = `imp_med_${this.data.variableName}${suffix}`;
            break;
          case 'mode':
            defaultName = `imp_mod_${this.data.variableName}${suffix}`;
            break;
        }

        // İsim benzersiz mi kontrol et
        isNameExists = this.isVariableNameExists(defaultName);

        if (isNameExists) {
          counter++;
          suffix = counter.toString();
        }
      }
    }

    // Form'u güncelle - labelları değişken adıyla aynı yap
    this.form.patchValue({
      name: defaultName,
      labelEN: this.data.variableName,
      labelTR: this.data.variableName
    });
  }

  // Çeviri yardımcı metodu: Eğer t() metodunuz yoksa ekleyin.
  t(key: string): string {
    return this.translate.translate(key);
  }

  private convertToNumber(value: any): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }
    const num = Number(value);
    return isNaN(num) ? null : num;
  }

  private isNumericColumn(data: any[]): boolean {
    const validValues = data.filter(val => val !== null && val !== undefined && val !== '');
    return validValues.every(val => !isNaN(Number(val)));
  }

  filterApplicableMethods() {
    // İlgili sütun verilerini al
    const columnIndex = this.data.variables_json.headers.indexOf(this.data.variableName);
    const columnData = this.data.variables_json.data.map(row => row[columnIndex]);
    const isNumeric = this.isNumericColumn(columnData);

    this.availableMethods = this.methods.filter(method => {
      // Değişken tipine göre filtrele
      if (!method.applicableTypes.includes(this.data.variableType)) {
        return false;
      }
      // Numeric olmayan sütunlarda mean/median metodlarını uygula
      if (!isNumeric && (method.id === 'mean' || method.id === 'median')) {
        return false;
      }
      return true;
    });
  }

  generatePreview() {
    if (!this.form.get('method').value) {
      this.snotifyService.warning(this.translate.translate('shared.imputation_modal.select_method_warning'));
      return;
    }

    const columnIndex = this.data.variables_json.headers.indexOf(this.data.variableName);
    const columnData = this.data.variables_json.data.map(row => row[columnIndex]);

    // Seçilen metod için veri tipi kontrolü
    const isNumeric = this.isNumericColumn(columnData);
    if ((this.form.get('method').value === 'mean' || this.form.get('method').value === 'median') && !isNumeric) {
      this.snotifyService.warning(this.translate.translate('shared.imputation_modal.numeric_required'));
      return;
    }

    const previewResult = this.diagnoseHelper.previewImputation(
      this.data.variables_json,
      this.form.get('method').value,
      this.data.variableName
    );

    this.previewData = previewResult;
  }

  private determineDataType(columnData: any[]): 'Scale' | 'Nominal' | 'Ordinal' {
    const nonEmptyValues = columnData.filter(val =>
      val !== null && val !== undefined && val !== '');

    if (nonEmptyValues.length === 0) {
      return 'Scale';
    }

    const uniqueValues = new Set(nonEmptyValues);
    const uniqueCount = uniqueValues.size;

    const hasDecimals = nonEmptyValues.some(val =>
      String(val).includes('.') && !isNaN(Number(val)));
    if (hasDecimals) {
      return 'Scale';
    }

    const allSmallIntegers = nonEmptyValues.every(val =>
      Number.isInteger(Number(val)) &&
      Number(val) >= 0 &&
      Number(val) <= 10 &&
      !isNaN(Number(val))
    );

    if (allSmallIntegers && uniqueCount <= 5) {
      return 'Nominal';
    }

    const allNumeric = nonEmptyValues.every(val => !isNaN(Number(val)));

    if (allNumeric && uniqueCount <= 10) {
      const sortedValues = [...uniqueValues].map(Number).sort((a, b) => a - b);
      const isSequential = sortedValues.every((val, idx, arr) =>
        idx === 0 || (val - arr[idx - 1]) <= 2
      );
      if (isSequential) {
        return 'Ordinal';
      }
    }

    if (!allNumeric && uniqueCount <= 5) {
      return 'Nominal';
    }

    if (!allNumeric && uniqueCount <= 10) {
      return 'Ordinal';
    }

    return 'Scale';
  }

  private generateValueLabels(values: any[]) {
    const uniqueValues = [...new Set(values)].filter(v => v != null && v !== '' && v !== undefined);
    if (uniqueValues.length > 10) return undefined;

    const labels = uniqueValues.reduce((acc, val) => {
      acc[val] = val.toString();
      return acc;
    }, {});

    return { en: labels, tr: labels };
  }

  applyImputation() {
    if (!this.form.valid) {
      this.snotifyService.warning(this.translate.translate('shared.imputation_modal.form_invalid_error'));
      return;
    }
  
    try {
      const config = this.form.value;
      const updatedData = { ...this.data.variables_json };
      const columnIndex = updatedData.headers.indexOf(this.data.variableName);
      const columnData = updatedData.data.map(row => row[columnIndex]);
  
      // Numerik metodlar için verileri sayıya dönüştür
      const processedData = (config.method === 'mean' || config.method === 'median')
        ? columnData.map(val => this.convertToNumber(val))
        : columnData;
  
      let imputedValues;
      switch (config.method) {
        case 'mean':
          imputedValues = this.diagnoseHelper.imputeMean(processedData);
          break;
        case 'median':
          imputedValues = this.diagnoseHelper.imputeMedian(processedData);
          break;
        case 'mode':
          imputedValues = this.diagnoseHelper.imputeMode(processedData);
          break;
        default:
          throw new Error(`Unknown imputation method: ${config.method}`);
      }
  
      // Eksik değerleri impute edilen değerle doldur
      const newColumnValues = columnData.map((value, idx) => {
        if (value === null || value === undefined || value === '') {
          return imputedValues;
        }
        return value;
      });
  
      const measureType = this.determineDataType(newColumnValues);
      const valueLabels = measureType !== 'Scale'
        ? this.generateValueLabels(newColumnValues)
        : undefined;
  
      updatedData.headers = [...updatedData.headers, config.name];
  
      updatedData.variable_list = [
        ...updatedData.variable_list,
        {
          id: config.name,
          header: config.name,
          label: {
            en: config.labelEN || config.name,
            tr: config.labelTR || config.name
          },
          measure: measureType,
          import: true,
          value_labels: valueLabels,
          description: `Imputed version of ${this.data.variableName} using ${config.method} method`,
          sourceVariables: [this.data.variableName],
          // Add imputed flag and method
          imputed: true,
          imputationMethod: config.method
        }
      ];
  
      updatedData.data = updatedData.data.map((row, idx) => {
        return [...row, newColumnValues[idx]];
      });
  
      this.snotifyService.success(this.translate.translate('shared.imputation_modal.imputation_success'));
      this.closeModal(updatedData);
  
    } catch (error) {
      console.error('Error in applyImputation:', error);
      this.snotifyService.error(this.translate.translate('shared.imputation_modal.imputation_error'));
    }
  }

  closeModal(result?: any) {
    this.animationState = 'out';
    setTimeout(() => {
      this.closeEvent.emit(result);
    }, 300);
  }

  nextStep() {
    // Metot seçili ve preview varsa, sonraki adıma geç
    if (this.form.get('method').value && this.previewData) {
      this.currentStep = 'name';
    }
  }

  previousStep() {
    this.currentStep = 'method';
  }
}
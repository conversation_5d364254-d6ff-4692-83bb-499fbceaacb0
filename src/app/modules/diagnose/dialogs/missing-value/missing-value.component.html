<div [@slideInOut]="animationState" class="flex flex-col h-screen bg-gray-50"
  *transloco="let t; read: 'shared.missing_value'">
  <!-- Header -->
  <div class="flex items-center justify-between p-4">
    <!-- <button class="flex items-center justify-center p-2 text-2xl border-2 rounded-full">
      <ng-icon name="lucideCircleHelp"></ng-icon>
    </button> -->
    <div class="w-6 h-6"></div>
    <div class="flex items-center gap-2 text-brand-blue-600">
      <ng-icon name="heroExclamationCircle" class="text-3xl"></ng-icon>
      <h2 class="text-2xl font-medium">{{ t('header') }}</h2>
    </div>
    <button (click)="closeModal()"
      class="p-2 text-3xl text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
      <ng-icon name="matCloseRound"></ng-icon>
    </button>
  </div>

  <!-- Table Section -->
  <div class="flex justify-center p-2 px-8 overflow-auto">
    <div class="border max-h-[calc(100vh-150px)] overflow-auto rounded-3xl bg-white w-full max-w-5xl"
      *ngIf="missingStats.length > 0; else noDataPlaceholder">
      <div class="w-full">
        <div class="overflow-auto rounded-3xl">
          <!-- Başlık Satırı -->
          <div class="sticky top-0 flex bg-brand-blue-50">
            <div class="px-2 py-2 text-sm font-medium text-left text-gray-600 rounded-tl-3xl" style="width: 40px;">
            </div>
            <div class="flex-1 px-2 py-2 text-sm font-medium text-left text-gray-600">{{ t('variable') }}</div>
            <div class="flex-1 px-2 py-2 text-sm font-medium text-left text-gray-600">{{ t('type') }}</div>
            <div class="px-2 py-2 text-sm font-medium text-center text-gray-600" style="width: 100px;">{{
              t('missing_count') }}</div>
            <div class="px-2 py-2 text-sm font-medium text-center text-gray-600 rounded-tr-3xl" style="width: 120px;">{{
              t('missing_percentage') }}</div>
          </div>

          <!-- İçerik Satırları -->
          <div class="p-2 border">
            <ng-container *ngIf="!selectedStat">
              <div *ngFor="let stat of missingStats" (click)="onRowSelect(stat)"
                class="flex items-center cursor-pointer rounded-2xl hover:bg-gray-50">
                <div class="p-2 text-center" style="width: 40px;">
                  <input type="checkbox" class="rounded-full text-brand-green-500 size-5 focus:ring-brand-green-500"
                    [checked]="false">
                </div>
                <div class="flex-1 p-2">
                  <div class="font-medium text-blue-900">{{ stat.variableName }}</div>
                </div>
                <div class="flex-1 p-2">
                  <span class="px-2 py-1 text-sm font-medium rounded-full" [ngClass]="{
                    'bg-amber-100 text-amber-600': getVariableType(stat.variableName) === 'Scale',
                    'bg-blue-100 text-blue-600': getVariableType(stat.variableName) === 'Ordinal',
                    'bg-purple-100 text-purple-600': getVariableType(stat.variableName) === 'Nominal'
                  }">
                    {{ getVariableType(stat.variableName) }}
                  </span>
                </div>
                <div class="p-2 text-center" style="width: 100px;">
                  {{ stat.missingCount }}/{{ stat.totalCount }}
                </div>
                <div class="p-2 text-center" style="width: 120px;">
                  <div [class]="getMissingRateClass(stat.missingPercentage)">
                    {{ stat.missingPercentage | number:'1.0-1' }}%
                  </div>
                </div>
              </div>
            </ng-container>


            <!-- Seçili satır varsa -->
            <ng-container *ngIf="selectedStat" class="rounded-2xl">
              <div (click)="onRowSelect(selectedStat)"
                class="flex items-center cursor-pointer rounded-2xl hover:bg-gray-50">
                <div class="p-2 text-center" style="width: 40px;">
                  <input type="checkbox" class="rounded-full text-brand-green-500 size-5 focus:ring-brand-green-500"
                    [checked]="true">
                </div>
                <div class="flex-1 p-2">
                  <div class="font-medium text-blue-900">{{ selectedStat.variableName }}</div>
                </div>
                <div class="flex-1 p-2">
                  <span [class]="'px-2 py-1 text-sm font-medium rounded-full ' + 
                  (getVariableType(selectedStat.variableName) === 'Scale' ? 'text-amber-600 bg-amber-100' :
                   getVariableType(selectedStat.variableName) === 'Ordinal' ? 'text-blue-600 bg-blue-100' :
                   getVariableType(selectedStat.variableName) === 'Nominal' ? 'text-purple-600 bg-purple-100' : '')">
                    {{ getVariableType(selectedStat.variableName) }}
                  </span>
                </div>
                <div class="p-2 text-center" style="width: 100px;">
                  {{ selectedStat.missingCount }}/{{ selectedStat.totalCount }}
                </div>
                <div class="p-2 text-center" style="width: 120px;">
                  <div [class]="getMissingRateClass(selectedStat.missingPercentage)">
                    {{ selectedStat.missingPercentage | number:'1.0-1' }}%
                  </div>
                </div>
              </div>
              <!-- Seçili satırın altındaki imputation modal -->
              <div class="mt-2 rounded-2xl">
                <app-imputation-modal #imputationModal [data]="imputationModalData"
                  (closeEvent)="onImputationModalClose($event)" class="rounded-2xl">
                </app-imputation-modal>
              </div>
            </ng-container>
          </div>
        </div>
      </div>

    </div>
    <ng-template #noDataPlaceholder>
      <div class="flex items-center justify-center h-full text-gray-500">
        {{ t('no_missing_values') }}
      </div>
    </ng-template>
  </div>
</div>
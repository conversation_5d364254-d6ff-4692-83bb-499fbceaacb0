import { animate, state, style, transition, trigger } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { DiagnoseHelperService, MissingValueStats } from '@app/data/helper/diagnose.helper.service';
import { ImputationModalComponent } from '../imputation-modal/imputation-modal.component';
import { Overlay } from '@angular/cdk/overlay';
import { DeviceDetectorService } from 'ngx-device-detector';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'app-missing-value',
  templateUrl: './missing-value.component.html',
  styleUrls: ['./missing-value.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class MissingValueComponent implements OnInit {
  missingStats: MissingValueStats[] = [];
  animationState = 'in';
  
  // Inline imputation modal kontrolü için eklemeler
  showImputationModal: boolean = false;
  imputationModalData: any = null;
  
  // Seçilen satırı tutan değişken; satır seçildiğinde diğerleri gizlenecek.
  selectedStat: MissingValueStats | null = null;
  
  constructor(
    private diagnoseHelper: DiagnoseHelperService,
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private dialog: Dialog,
    private overlay: Overlay,
    private deviceService: DeviceDetectorService,
    private translate: TranslocoService
  ) {}

  ngOnInit() {
    this.analyzeMissingValues();
  }

  async analyzeMissingValues() {
    this.missingStats = await this.diagnoseHelper.analyzeMissingValues(this.data.variables_json);
  }

  getTotalVariables(): number {
    return this.data.variables_json.variable_list.length;
  }

  getVariablesWithMissing(): number {
    return this.missingStats.length;
  }

  getOverallMissingRate(): number {
    const totalCells = this.missingStats.reduce((acc, stat) => acc + stat.totalCount, 0);
    const totalMissing = this.missingStats.reduce((acc, stat) => acc + stat.missingCount, 0);
    return (totalMissing / totalCells) * 100;
  }

  getVariableType(variableName: string): string {
    const variable = this.data.variables_json.variable_list.find(
      (v: any) => v.header === variableName
    );
    return variable?.measure || 'Unknown';
  }

  getMissingRateClass(percentage: number): string {
    if (percentage < 5) return 'text-green-600';
    if (percentage < 10) return 'text-yellow-600';
    return 'text-red-600';
  }

  getPatternClass(pattern: string): string {
    switch (pattern) {
      case 'MCAR':
        return 'px-2 py-1 text-sm font-medium text-green-700 bg-green-100 rounded-full';
      case 'MAR':
        return 'px-2 py-1 text-sm font-medium text-yellow-700 bg-yellow-100 rounded-full';
      case 'MNAR':
        return 'px-2 py-1 text-sm font-medium text-red-700 bg-red-100 rounded-full';
      default:
        return 'px-2 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-full';
    }
  }

  // Satır (veya checkbox) tıklandığında, seçili satırı ayarlıyor ve modal verilerini hazırlıyor.
  onRowSelect(stat: MissingValueStats) {
    this.selectedStat = stat;
    this.imputationModalData = {
      variableName: stat.variableName,
      variableType: this.getVariableType(stat.variableName),
      missingCount: stat.missingCount,
      totalCount: stat.totalCount,
      pattern: stat.pattern,
      variables_json: this.data.variables_json
    };
    this.showImputationModal = true;
  }

  // Inline modal kapanma event'ini yakalayan method; iptal veya güncelleme sonrası tüm seçim sıfırlanır.
  onImputationModalClose(result: any) {
    if (result) {
      this.data.variables_json = result;
      this.analyzeMissingValues();
      // Güncellenen veriyle bileşenin kapanmasını istiyorsanız:
      this.dialogRef.close(result);
    }
    // Seçim sıfırlanır, böylece tüm satırlar yeniden gösterilir.
    this.selectedStat = null;
    this.showImputationModal = false;
  }

  // İptal butonuna basıldığında tüm durumu sıfırlayarak başlangıç haline döner.
  resetSelection() {
    this.selectedStat = null;
    this.showImputationModal = false;
  }

  closeModal() {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(), 300);
  }
}

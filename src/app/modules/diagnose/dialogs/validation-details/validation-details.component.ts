import { animate, state, style, transition, trigger } from "@angular/animations";
import { DIALOG_DATA, DialogRef } from "@angular/cdk/dialog";
import { Component, OnInit, Inject, Output, EventEmitter, ChangeDetectorRef } from "@angular/core";
import { ValidationIssue } from "@app/modules/diagnose/services/variable-validation.service";
import { TranslocoService } from "@ngneat/transloco";

@Component({
  selector: 'app-validation-details',
  template: `
    <div [@slideInOut]="animationState" *transloco="let t; read: 'shared.validation_messages'" class="flex flex-col bg-gray-50 h-screen" style="width: 500px;">
      <!-- Header -->
      <div class="flex items-center justify-between p-4">
        <h2 class="text-lg font-medium">{{ t('validation_details.title') }}</h2>
        <button (click)="close()" class="p-2 text-2xl text-gray-500 hover:text-gray-700">
          <ng-icon name="matCloseRound"></ng-icon>
        </button>
      </div>

      <!-- Issues List -->
      <div class="flex-1 overflow-auto p-4">
        <div class="space-y-4">
          <!-- Empty State -->
          <div *ngIf="groupedIssues.length === 0" class="text-center py-8">
            <ng-icon name="heroCheckCircle" class="text-3xl text-brand-green-600 mx-auto"></ng-icon>
            <p class="mt-2 text-brand-green-600">{{ t('no_issues_found') }}</p>
          </div>

          <!-- Grouped Issues -->
          <ng-container *ngFor="let group of groupedIssues; let i = index">
            <div class="rounded-3xl overflow-hidden"
            [ngStyle]="{
            'background': group.type === 'error' 
              ? 'linear-gradient(to bottom right, #FEF1F2, #F9FAFB80)' 
              : 'linear-gradient(to bottom right, #FEF3C740, #F9FAFB80)'
            }">
              <!-- Issue Header -->
              <div class="p-3">
                <div class="flex gap-2 justify-start">
                  <ng-icon [name]="group.type === 'error' ? 'heroExclamationCircle' : 'heroExclamationTriangle'"
                          [class]="group.type === 'error' ? 'text-red-500' : 'text-amber-500'"
                          class="text-2xl w-6 min-w-6">
                  </ng-icon>
                  <div class="flex flex-col gap-2 w-full">
                    <!-- Message - Main issue message, without variables -->
                    <div class="text-sm">
                      {{ group.messageKey | transloco }}
                    </div>

                    <!-- Show Details Button -->
                     <div class="flex justify-end w-full">
                      <button (click)="toggleExpand(i)" 
                        class="flex items-center gap-1 text-sm w-fit text-nowrap mt-1 py-1 px-0.5 rounded-3xl text-brand-blue-600 hover:text-brand-blue-700 font-medium">
                        {{ expandedStates[i] ? t('validation_details.hide_details') : t('validation_details.show_details') }}
                        <ng-icon [@rotateChevron]="expandedStates[i] ? 'up' : 'down'" 
                                name="lucideChevronDown" class="text-xl"></ng-icon>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Expandable Details Section -->
              <div [@expandCollapse]="expandedStates[i] ? 'expanded' : 'collapsed'" 
                  class="overflow-hidden">
                <div class="px-3 pb-3 pt-0">
                  <div class="p-3 rounded-xl bg-white/80">
                    <!-- Details -->
                    <div class="mb-3">
                      <h4 class="text-xs font-semibold text-gray-700 mb-1">{{ t('validation_details.issue_explanation') }}</h4>
                      <p class="text-sm text-gray-600">{{ t('issue_explanations.' + extractSimpleKey(group.messageKey)) }}</p>
                    </div>
                    
                    <!-- Suggestions -->
                    <div class="mb-3">
                      <h4 class="text-xs font-semibold text-gray-700 mb-1">{{ t('validation_details.how_to_fix') }}</h4>
                      <p class="text-sm text-gray-600">{{ t('issue_recommendations.' + extractSimpleKey(group.messageKey)) }}</p>
                    </div>
                    
                    <!-- Affected Variables List with Edit Buttons -->
                    <div>
                      <h4 class="text-xs font-semibold text-gray-700 mb-1">{{ t('validation_details.affected_variables') }}</h4>
                      <div class="flex flex-wrap gap-2">
                        <div *ngFor="let variable of group.variables" class="flex items-center justify-between w-full">
                          <span class="text-sm font-medium bg-gray-50 rounded-full px-1 py-0.5"
                          [ngClass]="{
                                'text-data-orange-500 bg-data-orange-500/20': variable.measure === 'Nominal',
                                'text-data-blue-500 bg-data-blue-500/20': variable.measure === 'Scale',
                                'text-data-purple-500 bg-data-purple-500/20': variable.measure === 'Ordinal'
                              }">
                            {{ variable.header }}
                          </span>
                          
                          <!-- Edit button next to each variable -->
                          <button (click)="editVariable(variable.id, $event)" 
                                  class="flex items-center justify-center text-sm font-medium px-1 py-0.5 rounded-full text-brand-blue-600 hover:bg-brand-blue-50">
                            <ng-icon name="lucidePencilLine" class="text-lg font-medium mr-1"></ng-icon>
                            {{ t('validation_details.edit') }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      max-width: 800px;
      margin: 0 auto;
    }
  `],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ]),
    trigger('expandCollapse', [
      state('collapsed', style({ 
        height: '0px',
        opacity: 0
      })),
      state('expanded', style({ 
        height: '*',
        opacity: 1
      })),
      transition('collapsed <=> expanded', [
        animate('250ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ])
    ]),
    trigger('rotateChevron', [
      state('down', style({ transform: 'rotate(0deg)' })),
      state('up', style({ transform: 'rotate(180deg)' })),
      transition('down <=> up', [
        animate('250ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ])
    ])
  ]
})
export class ValidationDetailsComponent implements OnInit {
  selectedFilter: 'all' | 'error' | 'warning' = 'all';
  animationState = 'in';
  expandedStates: boolean[] = [];
  groupedIssues: any[] = [];
  
  // Helper method to extract simple key from full path
  extractSimpleKey(fullKey: string): string {
    // Check if the key contains the namespace
    if (fullKey.includes('shared.validation_messages.')) {
      // Extract only the last part (e.g., 'missing_values' from 'shared.validation_messages.missing_values')
      return fullKey.split('.').pop() || fullKey;
    }
    return fullKey;
  }
  
  @Output() variableSelected = new EventEmitter<string>();
  @Output() openMissingValueAnalysis = new EventEmitter<string>();

  constructor(
    @Inject(DIALOG_DATA) public data: {
      variables: any[],
      issues: ValidationIssue[],
      getIssues: (id: string) => ValidationIssue[],
      hasErrors: (id: string) => boolean,
      hasWarnings: (id: string) => boolean
    },
    public dialogRef: DialogRef<ValidationDetailsComponent>,
    private transloco: TranslocoService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    // Initialize the component and process issues
    this.processIssues();
  }

  processIssues() {
    // Group issues by messageKey and type
    const groupedByMessage: Record<string, any> = {};
    
    for (const issue of this.data.issues) {
      // Skip issues that don't match current filter
      if (this.selectedFilter !== 'all' && issue.type !== this.selectedFilter) {
        continue;
      }
      
      const key = `${issue.type}-${issue.messageKey}`;
      
      if (!groupedByMessage[key]) {
        groupedByMessage[key] = {
          type: issue.type,
          messageKey: issue.messageKey,
          variables: []
        };
      }
      
      // Find variable and add if not already in group
      const variable = this.data.variables.find(v => v.id === issue.variableId && v.import);
      if (variable && !groupedByMessage[key].variables.some((v: any) => v.id === variable.id)) {
        groupedByMessage[key].variables.push(variable);
      }
    }
    
    // Convert to array and sort
    this.groupedIssues = Object.values(groupedByMessage)
      .filter(group => group.variables.length > 0)
      .sort((a, b) => {
        // Errors first, then by variable count
        if (a.type !== b.type) {
          return a.type === 'error' ? -1 : 1;
        }
        return b.variables.length - a.variables.length;
      });
    
    // Reset expanded states array
    this.expandedStates = new Array(this.groupedIssues.length).fill(false);
    
    // Force UI update
    this.cdr.detectChanges();
  }

  toggleExpand(index: number) {
    if (index >= 0 && index < this.expandedStates.length) {
      // Simply toggle the state for this index
      this.expandedStates[index] = !this.expandedStates[index];
      // Force UI update
      this.cdr.detectChanges();
    }
  }

  editVariable(variableId: string, event: Event) {
    event.stopPropagation();
    
    // Find the variable to check if it has missing values
    const variable = this.data.variables.find(v => v.id === variableId);
    
    // Check if this variable has missing value issues
    const hasMissingValueIssue = this.data.issues.some(
      issue => issue.variableId === variableId && 
      issue.messageKey.includes('missing_values')
    );
    
    // If it has missing values, emit a separate event for opening missing value analysis
    if (hasMissingValueIssue && variable && variable.missingCount > 0) {
      this.openMissingValueAnalysis.emit(variableId);
    } else {
      // For other types of issues, continue with the normal variable editing flow
      this.variableSelected.emit(variableId);
    }
    
    this.close();
  }

  setFilter(filter: 'all' | 'error' | 'warning') {
    this.selectedFilter = filter;
    this.processIssues(); // Re-process with new filter
  }

  close() {
    this.animationState = 'out';
    setTimeout(() => {
      this.dialogRef.close();
    }, 300);
  }

  getWarningCount(): number {
    const importedVariables = this.data.variables.filter(v => v.import);
    const uniqueWarningVariables = new Set<string>();

    this.data.issues.forEach(issue => {
      if (issue.type === 'warning') {
        const variable = importedVariables.find(v => v.id === issue.variableId);
        if (variable) {
          uniqueWarningVariables.add(issue.variableId);
        }
      }
    });

    return uniqueWarningVariables.size;
  }

  getErrorCount(): number {
    const importedVariables = this.data.variables.filter(v => v.import);
    const uniqueErrorVariables = new Set<string>();
    this.data.issues.forEach(issue => {
      if (issue.type === 'error') {
        const variable = importedVariables.find(v => v.id === issue.variableId);
        if (variable) {
          uniqueErrorVariables.add(issue.variableId);
        }
      }
    });
    return uniqueErrorVariables.size;
  }
}
import { Component, Inject } from '@angular/core';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { ValidationIssue } from '../../services/variable-validation.service';
import { TranslocoService } from "@ngneat/transloco";

interface GroupedError {
  messageKey: string;
  params?: any;
  variables: any[];
}

@Component({
  selector: 'app-validation-summary-dialog',
  template: `
    <div class="w-full max-w-5xl p-6 rounded-3xl bg-white" *transloco="let t; read: 'shared.validation_details'">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold">{{ t('title') }}</h2>
        <button (click)="dialogRef.close(false)" class="text-gray-400 hover:text-gray-600">
          <ng-icon name="heroXMark" class="text-2xl"></ng-icon>
        </button>
      </div>
      
      <!-- Summary Stats -->
      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="p-4 bg-status-success-100 text-status-success-500 rounded-xl">
          <div>
            {{ t('total_valid_variables') }}
          </div>
          <div class="text-3xl font-medium">{{data.totalCount - data.invalidCount}}</div>
        </div>
        <div class="p-4 bg-status-error-100 text-status-error-500 rounded-xl">
          <div class="">{{ t('invalid_variables') }}</div>
          <div class="text-3xl font-medium">{{data.invalidCount}}</div>
        </div>
        <div class="p-4 bg-gray-100 rounded-xl">
          <div class="text-gray-500">{{ t('not_imported') }}</div>
          <div class="text-3xl font-medium text-gray-600">{{getNotImportedCount()}}</div>
        </div>
      </div>

      <!-- Variables List -->
      <div class="space-y-2 max-h-96 overflow-y-auto mb-6">
        <!-- Invalid Variables -->
        <div class="bg-red-50 rounded-lg p-4" *ngIf="groupedErrors.length > 0">
          <div class="flex items-center space-x-2 mb-2">
            <ng-icon name="heroXCircle" class="w-5 h-5 text-red-500"></ng-icon>
            <h3 class="font-medium text-red-800">{{ t('invalid_variables') }}</h3>
          </div>
          <div class="pl-7 space-y-4">
            <div *ngFor="let group of groupedErrors" class="space-y-2">
              <div class="text-sm font-medium text-red-700">
                {{group.messageKey | transloco:group.params}}
              </div>
              <div *ngFor="let variable of group.variables" 
                   class="text-xs text-red-600 pl-4">
                • {{variable.header}}
              </div>
            </div>
          </div>
        </div>

        <!-- Not Imported Variables -->
        <div class="bg-gray-100 rounded-lg p-4" *ngIf="getNotImportedVariables().length > 0">
          <div class="flex items-center space-x-2 mb-2">
            <ng-icon name="heroMinusCircle" class="w-5 h-5 text-gray-500"></ng-icon>
            <h3 class="font-medium text-gray-800">{{ t('not_imported_variables') }}</h3>
          </div>
          <div class="pl-7 space-y-1">
            <div *ngFor="let variable of getNotImportedVariables()" 
                 class="text-sm text-gray-600">
                 • {{variable.header}}
            </div>
          </div>
        </div>
      </div>

      <!-- Warning Message -->


      <!-- Actions -->
      <div class="flex justify-between items-center pt-4 border-gray-200">

      <div class="bg-data-orange-100/50 rounded-3xl p-3 flex items-center justify-center gap-2">
          <ng-icon name="heroExclamationTriangle" class="text-3xl text-amber-500"></ng-icon>
          <div>
            <p class="font-medium text-status-warning-500">{{t('warning_message')}}</p>
            <!-- <p class="text-sm text-amber-800">{{ t('warning_message.line1') }}</p>
            <p class="text-sm text-amber-800">{{ t('warning_message.line2') }}</p> -->
          </div>
      </div>

      <div class="flex gap-2">
      <button 
          (click)="dialogRef.close(false)"
          class="secondary-status-error-button">
          {{ t('actions.cancel') }}
        </button>
        <button
          (click)="dialogRef.close(true)"
          class="primary-blue-button">
          {{ t('actions.continue') }}
        </button>
      </div>

      </div>
    </div>
  `
})
export class ValidationSummaryComponent {
  groupedErrors: GroupedError[] = [];

  constructor(
    @Inject(DIALOG_DATA) public data: {
      totalCount: number;
      invalidCount: number;
      variables: any[];
      validationIssues: ValidationIssue[];
    },
    public dialogRef: DialogRef<boolean>
  ) {
    this.groupErrors();
  }

  private groupErrors() {
    const groupedByMessage: Record<string, GroupedError> = {};
    
    this.data.validationIssues
      .filter(issue => issue.type === 'error')
      .forEach(issue => {
        const key = `${issue.messageKey}-${JSON.stringify(issue.params || {})}`;
        
        if (!groupedByMessage[key]) {
          groupedByMessage[key] = {
            messageKey: issue.messageKey,
            params: issue.params,
            variables: []
          };
        }

        const variable = this.data.variables.find(v => 
          v.id === issue.variableId && v.import
        );
        
        if (variable && !groupedByMessage[key].variables.find(v => v.id === variable.id)) {
          groupedByMessage[key].variables.push(variable);
        }
      });

    this.groupedErrors = Object.values(groupedByMessage)
      .filter(group => group.variables.length > 0);
  }

  getNotImportedVariables(): any[] {
    return this.data.variables.filter(variable => !variable.import);
  }

  getNotImportedCount(): number {
    return this.getNotImportedVariables().length;
  }

  getVariableErrors(variableId: string): ValidationIssue[] {
    return this.data.validationIssues.filter(issue =>
      issue.variableId === variableId && issue.type === 'error'
    );
  }
}
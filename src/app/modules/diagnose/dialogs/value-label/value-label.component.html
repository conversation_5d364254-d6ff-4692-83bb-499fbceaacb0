<div  [@slideInOut]="animationState" class="flex flex-col h-screen overflow-auto bg-gray-50" style="width: 500px;" *transloco="let t; read: 'shared.value_label'" >
  <!-- Header -->
  <div class="bg-white border-b">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <h1 class="text-xl font-semibold text-gray-900">{{ t('title') }}</h1>
        <button (click)="close()" class="text-gray-400 hover:text-gray-500">
          <ng-icon name="heroXMark" class="w-5 h-5"></ng-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 p-6 overflow-auto">
    <!-- Eğer etiket yoksa -->
    <div *ngIf="filteredLabels.length === 0" class="p-4 text-center text-gray-500 rounded-lg bg-gray-50">
      {{ t('no_labels') }}
    </div>

    <!-- Etiket listesi -->
    <div *ngIf="filteredLabels.length > 0" class="space-y-3 ">
      <div *ngFor="let item of filteredLabels"
        class="p-4 transition-colors border rounded-lg cursor-pointer hover:bg-gray-50"
        (click)="selectLabels(item.labels)">
        <div class="flex items-center justify-between mb-3">
          <span class="font-medium text-gray-900">{{item.variableName}}</span>
          <span class="text-sm text-gray-500">{{getValidLabelCount(item.labels)}} {{ t('value')}}</span>
        </div>

        <!-- Önizleme grid -->
        <div class="grid grid-cols-3 gap-4 text-sm">
          <div class="font-medium text-gray-500">{{ t('value')}}</div>
          <div class="font-medium text-gray-500">{{ t('label_tr')}}</div>
          <div class="font-medium text-gray-500">{{ t('label_en')}}</div>
        </div>

        <!-- Tam değerleri göster -->
        <div *ngFor="let key of getPreviewKeys(item.labels)" class="grid grid-cols-3 gap-4 mt-2 text-sm">
          <div>{{ key }}</div>
          <div>{{ item.labels.tr[key] }}</div>
          <div>{{ item.labels.en[key] }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="px-6 py-4 bg-white border-t">
    <div class="flex justify-end gap-3">
      <button (click)="close()"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
        {{ t('cancel') }}
      </button>
    </div>
  </div>
</div>
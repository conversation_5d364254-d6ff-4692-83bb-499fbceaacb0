import { animate, state, style, transition, trigger } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';

interface ValueLabel {
  variableName: string;
  labels: {
    en: { [key: string]: string };
    tr: { [key: string]: string };
  };
}

@Component({
  selector: 'app-value-label',
  templateUrl: './value-label.component.html',
  styleUrls: ['./value-label.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class ValueLabelComponent {
  filteredLabels: ValueLabel[] = [];
  animationState = 'in';

  constructor(
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private transloco: TranslocoService
  ) {
    this.initializeFilteredLabels();
  }

  private initializeFilteredLabels(): void {
    // Sadece hedef değişkenin etiketleriyle eşleşen etiketleri olan değişkenleri filtrele
    const targetLabels = Object.keys(this.data.currentLabels.en);
    this.filteredLabels = this.data.availableLabels.filter((item: ValueLabel) => {
      const hasMatchingLabels = targetLabels.every(key => 
        item.labels.en[key]?.trim() && item.labels.tr[key]?.trim()
      );
      
      return hasMatchingLabels && Object.keys(item.labels.en).length > 0;
    });
  }

  getPreviewKeys(labels: any): string[] {
    return Object.keys(labels.en).filter(key => 
      labels.en[key]?.trim() && labels.tr[key]?.trim()
    );
  }

  getValidLabelCount(labels: any): number {
    return this.getPreviewKeys(labels).length;
  }

  selectLabels(labels: any): void {
    // Mevcut değişkenin etiket sayısı
    const currentLabelCount = Object.keys(this.data.currentLabels.en).length;
    
    // Seçilen değişkenin dolu etiketleri
    const validKeys = this.getPreviewKeys(labels);
    
    // Mevcut sayı kadar etiket al
    const selectedLabels = {
      en: {},
      tr: {}
    };

    // İndekse göre kopyalama
    validKeys.slice(0, currentLabelCount).forEach((key, index) => {
      const currentKeys = Object.keys(this.data.currentLabels.en);
      selectedLabels.en[currentKeys[index]] = labels.en[key];
      selectedLabels.tr[currentKeys[index]] = labels.tr[key];
    });

    // Eğer hedef değişkenin etiketleri boşsa, boş olarak başlat
    if (currentLabelCount === 0) {
      Object.keys(labels.en).forEach(key => {
        selectedLabels.en[key] = '';
        selectedLabels.tr[key] = '';
      });
    }

    this.dialogRef.close(selectedLabels);
  }

  close(): void {
    this.animationState = 'out';
    setTimeout(() => {
      this.dialogRef.close();
    }, 300);
  }
}
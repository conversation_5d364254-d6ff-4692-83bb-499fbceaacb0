import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco'; // TranslocoService import et
export interface ComputedCell {
  formula: string;
  dependencies: string[];
  getValue: () => any;
}

@Injectable({
  providedIn: 'root'
})
export class ComputeVariableService {

  private dataSubject = new BehaviorSubject<any>(null);
  data$ = this.dataSubject.asObservable();
  private readonly operations = [
    {
      id: 'add',
      icon:"+",
      nameKey: 'shared.compute_variable.operations.add.name',
      descriptionKey: 'shared.compute_variable.operations.add.description',
      symbol: '+',
      applicableTypes: ['Scale']
    },
    {
      id: 'subtract',
      icon:"-",
      nameKey: 'shared.compute_variable.operations.subtract.name',
      descriptionKey: 'shared.compute_variable.operations.subtract.description',
      symbol: '-',
      applicableTypes: ['Scale']
    },
    {
      id: 'multiply',
      icon:"×",
      nameKey: 'shared.compute_variable.operations.multiply.name',
      descriptionKey: 'shared.compute_variable.operations.multiply.description',
      symbol: '*',
      applicableTypes: ['Scale']
    },
    {
      id: 'divide',
      icon:"÷",
      nameKey: 'shared.compute_variable.operations.divide.name',
      descriptionKey: 'shared.compute_variable.operations.divide.description',
      symbol: '/',
      applicableTypes: ['Scale'],
      singleVariable: false
    },
    {
      id: 'mean',
      icon:"x̄",
      nameKey: 'shared.compute_variable.operations.mean.name',
      descriptionKey: 'shared.compute_variable.operations.mean.description',
      symbol: 'mean()',
      applicableTypes: ['Scale'],
    },
    {
      id: 'log',
      icon:"log₁₀",
      nameKey: 'shared.compute_variable.operations.log.name',
      descriptionKey: 'shared.compute_variable.operations.log.description',
      symbol: 'LOG10',
      applicableTypes: ['Scale'],
      singleVariable: true,
      isTransformation: true
    },
    {
      id: 'ln',
      icon:"ln",
      nameKey: 'shared.compute_variable.operations.ln.name',
      descriptionKey: 'shared.compute_variable.operations.ln.description',
      symbol: 'LN',
      applicableTypes: ['Scale'],
      isTransformation: true
    },
    {
      id: 'square',
      icon:"x²",
      nameKey: 'shared.compute_variable.operations.square.name',
      descriptionKey: 'shared.compute_variable.operations.square.description',
      symbol: 'POWER',
      applicableTypes: ['Scale'],
      isTransformation: true
    },
    {
      id: 'sqrt',
      icon:"√x",
      nameKey: 'shared.compute_variable.operations.sqrt.name',
      descriptionKey: 'shared.compute_variable.operations.sqrt.description',
      symbol: 'SQRT',
      applicableTypes: ['Scale'],
      isTransformation: true
    },
    {
      id: 'concat',
      icon:"x+y",
      nameKey: 'shared.compute_variable.operations.concat.name',
      descriptionKey: 'shared.compute_variable.operations.concat.description',
      symbol: 'CONCAT',
      applicableTypes: ['Nominal', 'Ordinal'],
      outputType: 'Nominal',
      isTextOperation: true,
      requiresSameType: false,
      minVariables: 2,
      separator: ' ',
      maxVariables: 5
    }
  ];

  constructor(
    private snotifyService: SnotifyService,
    private transloco: TranslocoService  // TranslocoService ekle
  ) { }

  setData(data: any): void {
    this.dataSubject.next(data);
  }

  getOperations() {
    return this.operations;
  }

  isTransformationOperation(operationId: string): boolean {
    return this.operations.find(op => op.id === operationId)?.isTransformation || false;
  }

  validateOperation(operationId: string, selectedVariables: any[], newVariableName: string, transformationVariables: any[] = []): boolean {
    const data = this.dataSubject.getValue();
    if (!data) return false;

    if (!operationId) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.validations.select_operation')
      );
      return false;
    }

    if (selectedVariables.length === 0) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.validations.select_variables')
      );
      return false;
    }

    const operation = this.operations.find(op => op.id === operationId);

    // Sadece bölme işlemi için 2 değişken kontrolü
    if (operationId === 'divide' && selectedVariables.length !== 2) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.validations.division_two_variables')
      );
      return false;
    }

    // Transformasyon işlemleri için özel kontrol
    if (operation?.isTransformation) {
      return this.validateTransformationNames(transformationVariables, data);
    } else {
      return this.validateNewVariableName(newVariableName, data);
    }
  }
  private validateNewVariableName(newVariableName: string, data: any): boolean {
    if (!newVariableName?.trim()) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.validations.provide_name')
      );
      return false;
    }

    if (data.headers.some((header: string) => header.toLowerCase() === newVariableName.toLowerCase())) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.validations.name_exists')
      );
      return false;
    }

    return true;
  }

  private validateTransformationNames(transformationVariables: any[], data: any): boolean {
    if (!transformationVariables.every(tv => tv.newName?.trim())) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.validations.provide_all_names')
      );
      return false;
    }

    const names = transformationVariables.map(tv => tv.newName.trim());
    if (new Set(names).size !== names.length) {
      this.snotifyService.warning('New variable names must be unique');
      return false;
    }

    if (names.some(name => data.headers.includes(name))) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.validations.unique_names')
      );
      return false;
    }
    return true;
  }
  computeNewVariable(
    variableInfo: string | any[], // variableName veya transformationVariables
    operationId: string,
    selectedVariables: any[],
    labels?: { labelTr?: string; labelEn?: string } // Opsiyonel etiketler parametresi
  ): any {
    const data = this.dataSubject.getValue();
    if (!data) return null;
    
    try {
      const operation = this.operations.find(op => op.id === operationId);
      if (!operation) {
        throw new Error(this.transloco.translate('shared.compute_variable.errors.invalid_operation'));
      }

      let newVariables = [];
      let newHeaders = [...data.headers];

      // Eğer transformasyon işlemiyse (variableInfo bir array)
      if (Array.isArray(variableInfo)) {
        newVariables = variableInfo.map(transformVar => {
          const colIndex = data.headers.indexOf(transformVar.sourceVariable.header);
          const columnData = data.data.map(row => {
            const value = this.calculateTransformation(row[colIndex], operationId);
            return value;
          });

          const measureType = this.determineDataType(columnData);
          const valueLabels = measureType !== 'Scale' ? 
            this.generateValueLabels(columnData) : undefined;

          return {
            id: transformVar.newName,
            header: transformVar.newName,
            label: {
              en: transformVar.labelEn || transformVar.newName,
              tr: transformVar.labelTr || transformVar.newName
            },
            measure: measureType,
            value_labels: valueLabels,
            import: true,
            computed: true,
            sourceVariables: [transformVar.sourceVariable.header],
            computationType: operationId,
            dependencies: [transformVar.sourceVariable.header],
            autoUpdate: true
          };
        });
        
        newHeaders.push(...newVariables.map(v => v.header));
      } else {
        // Normal işlem (variableInfo bir string - değişken adı)
        const newColumnData = data.data.map(row => {
          const sourceValues = selectedVariables.map(v => {
            const colIndex = data.headers.indexOf(v.header);
            return row[colIndex];
          });

          return operationId === 'concat' ?
            (sourceValues.some(v => v === null || v === undefined || v === '') ? 
              null : sourceValues.join(' ')) :
            this.calculateOperation(
              sourceValues.map(v => parseFloat(v)), 
              operationId
            );
        });

        const measureType = this.determineDataType(newColumnData);
        const valueLabels = measureType !== 'Scale' ? 
          this.generateValueLabels(newColumnData) : undefined;

        newVariables = [{
          id: variableInfo,
          header: variableInfo,
          label: {
            en: labels?.labelEn || variableInfo,
            tr: labels?.labelTr || variableInfo
          },
          measure: measureType,
          value_labels: valueLabels,
          import: true,
          computed: true,
          sourceVariables: selectedVariables.map(v => v.header),
          computationType: operationId,
          dependencies: selectedVariables.map(v => v.header),
          autoUpdate: true
        }];

        newHeaders.push(variableInfo);
      }

      // Hesaplama kısmı
      const newData = data.data.map((row: any[], rowIndex: number) => {
        const newRow = [...row];
        newVariables.forEach(newVar => {
          const sourceValues = newVar.sourceVariables.map(header => {
            const colIndex = data.headers.indexOf(header);
            return row[colIndex];
          });

          let computedValue;
          // Transformasyon işlemleri için - tek değişken
          if (operation.isTransformation || operation.singleVariable) {
            computedValue = this.calculateTransformation(parseFloat(sourceValues[0]), operationId);
          }
          // Concat işlemi için 
          else if (operationId === 'concat') {
            computedValue = [sourceValues.some(v => v === null || v === undefined || v === '') ? null : sourceValues.join(' ')];
          } 
          // Normal işlemler için
          else {
            computedValue = this.calculateOperation(sourceValues.map(v => parseFloat(v)), operationId);
          }
            if (operationId === 'concat') {
            newRow.push(computedValue);
            } else {
            newRow.push(computedValue === null ? null : Number(computedValue.toFixed(3)));
            }
        });
        return newRow;
      });
      newVariables.forEach(v => {
        v.value_labels = this.generateValueLabels(newData.map(row => row[newHeaders.indexOf(v.header)]));
      }
      );
      return {
        ...data,
        headers: newHeaders,
        variable_list: [...data.variable_list, ...newVariables],
        data: newData
      };

    } catch (error) {
      console.error('Compute error:', error);
      this.snotifyService.error(
        this.transloco.translate('shared.compute_variable.errors.computation_failed')
      );
      return null;
    }
  }
  private generateValueLabels(values: any[]) {
    const uniqueValues = [...new Set(values)]
      .filter(v => v != null && v !== undefined && v !== '')
      .sort((a, b) => String(a).localeCompare(String(b)));
  
    if (uniqueValues.length > 10) return undefined;
  
    const labels = uniqueValues.reduce((acc, val) => {
      acc[val] = val.toString();
      return acc;
    }, {});
  
    return { en: labels, tr: labels };
  }
  private determineDataType(values: any[]): 'Scale' | 'Nominal' | 'Ordinal' {
    // Boş değerleri filtrele
    const nonEmptyValues = values.filter(val =>
      val !== null && val !== undefined && val !== '');
  
    if (nonEmptyValues.length === 0) {
      return 'Scale';
    }
  
    // Unique değerleri al
    const uniqueValues = new Set(nonEmptyValues);
    const uniqueCount = uniqueValues.size;
  
    // Ondalıklı sayılar varsa Scale
    const hasDecimals = nonEmptyValues.some(val =>
      String(val).includes('.') && !isNaN(Number(val)));
    if (hasDecimals) {
      return 'Scale';
    }
  
    // Küçük tam sayılar için kontrol
    const allSmallIntegers = nonEmptyValues.every(val =>
      Number.isInteger(Number(val)) &&
      Number(val) >= 0 &&
      Number(val) <= 10 &&
      !isNaN(Number(val))
    );
  
    if (allSmallIntegers && uniqueCount <= 5) {
      return 'Nominal';
    }
  
    // Sayısal değerler kontrolü
    const allNumeric = nonEmptyValues.every(val => !isNaN(Number(val)));
  
    if (allNumeric && uniqueCount <= 10) {
      const sortedValues = [...uniqueValues].map(Number).sort((a, b) => a - b);
      const isSequential = sortedValues.every((val, idx, arr) =>
        idx === 0 || (val - arr[idx - 1]) <= 2
      );
      if (isSequential) {
        return 'Ordinal';
      }
    }
  
    if (!allNumeric && uniqueCount <= 5) {
      return 'Nominal';
    }
  
    if (!allNumeric && uniqueCount <= 10) {
      return 'Ordinal';
    }
  
    return 'Scale';
  }

  // Yardımcı metodlar
  private calculateTransformation(value: number, operationType: string): number | null {
    // Return null for any invalid input
    if (value === null || value === undefined || isNaN(value)) {
      return null;
    }

    try {
      switch (operationType) {
        case 'log':
          return value <= 0 ? null : Number(Math.log10(value).toFixed(3));
        case 'ln':
          return value <= 0 ? null : Number(Math.log(value).toFixed(3));
        case 'square':
          return Number(Math.pow(value, 2).toFixed(3));
        case 'sqrt':
          return value < 0 ? null : Number(Math.sqrt(value).toFixed(3));
        default:
          return null;
      }
    } catch (error) {
      return null;
    }
  }

  private calculateOperation(values: number[], operationType: string): number | null {
    // Filter out null, undefined and NaN values
    const validValues = values.filter(v => v !== null && v !== undefined && !isNaN(v));

    let result: number | null = null;
    switch (operationType) {
      case 'add':
        // Addition: Skip missing values, calculate sum of valid values
        result = validValues.length > 0 ? validValues.reduce((a, b) => a + b, 0) : null;
        break;
      
      case 'subtract':
        // Subtraction: Skip missing values, but need at least 2 valid values
        result = validValues.length >= 2 ? validValues.reduce((a, b) => a - b) : null;
        break;
      
      case 'multiply':
        // Multiplication: Return null if any value is missing
        result = values.length === validValues.length ? validValues.reduce((a, b) => a * b, 1) : null;
        break;
      
      case 'divide':
        // Division: Return null if any value is missing or if dividing by zero
        if (values.length !== validValues.length || validValues.length !== 2) result = null;
        else result = validValues[1] === 0 ? null : validValues[0] / validValues[1];
        break;
      
      case 'mean':
        // Mean: Skip missing values, calculate average of valid values
        result = validValues.length > 0 ? validValues.reduce((a, b) => a + b, 0) / validValues.length : null;
        break;
      
      default:
        result = null;
    }

    return result !== null ? Number(result.toFixed(3)) : null;
  }

  calculatePreview(selectedVariables: any[], operationId: string): any[] {
    const data = this.dataSubject.getValue();
    if (!data || !selectedVariables.length) {
      this.snotifyService.warning(
        this.transloco.translate('shared.compute_variable.preview.no_data')
      );
      return [];
    }
    
    const previewRows = Math.min(5, data.data.length);
    const preview = [];
    const operation = this.operations.find(op => op.id === operationId);

    for (let rowIndex = 0; rowIndex < previewRows; rowIndex++) {
      try {
        // Get source values and handle missing values
        const sourceValues = selectedVariables.map(variable => {
          const colIndex = data.headers.indexOf(variable.header);
          const cellValue = data.data[rowIndex][colIndex];
          
          // Handle missing values
          if (cellValue === null || cellValue === undefined || cellValue === '') {
            return null;
          }

          // Process value
          let value = cellValue?.value !== undefined ? cellValue.value : cellValue;
          if (typeof value === 'string' && !operation?.isTextOperation) {
            const numValue = parseFloat(value);
            return isNaN(numValue) ? null : numValue;
          }
          return value;
        });

        // Calculate result based on operation type
        let result;
        if (operation?.isTransformation || operation?.singleVariable) {
          // Handle transformation operations
          result = sourceValues.map(value => {
            if (value === null) return null;
            return this.calculateTransformation(value, operationId);
          });
        } else if (operationId === 'concat') {
          // Return null if any value is missing or empty
          result = [sourceValues.some(v => v === null || v === undefined || v === '') ? null : sourceValues.join(' ')];
        } else {
          // Handle regular operations
          const computedValue = this.calculateOperation(sourceValues, operationId);
          result = [computedValue === null ? null : computedValue.toFixed(3)];
        }

        preview.push({
          index: rowIndex + 1,
          values: sourceValues.map(v => v === null ? null : operation?.isTextOperation ? v : Number(v).toFixed(3)),
          result: operation?.isTransformation ? result : result,
          results: operation?.isTransformation ? result : undefined
        });

      } catch (error) {
        console.error(`Preview calculation error:`, error);
        preview.push({
          index: rowIndex + 1,
          values: selectedVariables.map(() => null),
          result: [null],
          results: operation?.isTransformation ? selectedVariables.map(() => null) : undefined
        });
      }
    }

    return preview;
  }

  // generateFormula(operationId: string, selectedVariables: any[]): string {
  //   const data = this.dataSubject.getValue();
  //   if (!data) return '';

  //   const columnRefs = selectedVariables.map(variable => {
  //     const colIndex = data.headers.indexOf(variable.header);
  //     return this.formulaService.columnToLetter(colIndex);
  //   });
  //   console.log(columnRefs);
  //   return this.formulaService.generateFormula(operationId, columnRefs);
  // }
  // operations.name için yeni getter metodu
  getOperationName(operationId: string): string {
    const operation = this.operations.find(op => op.id === operationId);
    if (!operation) return '';
    return this.transloco.translate(operation.nameKey);
  }

  // operations.description için yeni getter metodu 
  getOperationDescription(operationId: string): string {
    const operation = this.operations.find(op => op.id === operationId);
    if (!operation) return '';
    return this.transloco.translate(operation.descriptionKey);
  }

  computeValue(variable: any, rowIndex: number, data: any): any {
    try {
      if (!variable?.computed || !variable.computationType) {
        return null;
      }

      const sourceValues = variable.sourceVariables?.map(header => {
        const colIndex = data.headers.indexOf(header);
        if (colIndex === -1) return null;
        
        const value = data.data[rowIndex][colIndex];
        if (value === null || value === undefined || value === '') return null;
        
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        return isNaN(numValue) ? null : numValue;
      });

      if (!sourceValues || sourceValues.includes(null)) {
        if (['add', 'subtract', 'mean'].includes(variable.computationType)) {
          // These operations can handle missing values
          const validValues = sourceValues.filter(v => v !== null);
          if (validValues.length > 0) {
            return this.calculateOperation(validValues, variable.computationType);
          }
        }
        // For other operations, return null if any value is missing
        return null;
      }

      // For concat operation
      if (variable.computationType === 'concat') {
        // Return null if any value is missing or empty
        if (sourceValues.some(v => v === null || v === undefined || v === '')) {
          return null;
        }
        return sourceValues.join(' ');
      }

      // For transformation operations
      if (variable.computationType.match(/log|ln|square|sqrt/)) {
        return this.calculateTransformation(sourceValues[0], variable.computationType);
      }

      // For regular operations
      return this.calculateOperation(sourceValues, variable.computationType);

    } catch (error) {
      console.error('Error computing value:', error);
      return null;
    }
  }

}

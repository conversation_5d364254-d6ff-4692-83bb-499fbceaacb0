import { Injectable } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';
import { BehaviorSubject } from 'rxjs';

export interface ValidationIssue {
  type: 'error' | 'warning';
  message: string;
  messageKey: string;     // Yeni eklenen - çeviri anahtarı için
  params?: any;          // Yeni eklenen - çeviri parametreleri için
  variableId: string;
}
export interface ValidationState {
  isValid: boolean;
  errors: ValidationIssue[];
  warnings: ValidationIssue[];
  importStatus: boolean;
}

export interface ValidationSummary {
  validVariables: number;
  invalidVariables: number;
  variableStates: { [key: string]: ValidationState };
  canProceed: boolean;
}
@Injectable({
  providedIn: 'root'
})
export class VariableValidationService {
  constructor(private transloco: TranslocoService) { }
  private validateState = new BehaviorSubject<ValidationSummary>(null);
  public validationState$ = this.validateState.asObservable();



  validateVariable(variable: any, data: any[]): ValidationIssue[] {
    const issues: ValidationIssue[] = [];
    if (!variable.import || !data) return issues;
    // 2. Etiket validasyonu
    if (!this.validateLabels(variable)) {
      issues.push({
        type: 'error',
        messageKey: 'shared.validation_messages.invalid_labels',
        variableId: variable.id,
        message: this.transloco.translate('shared.validation_messages.invalid_labels')
      });
    }

    // 3. Value Labels validasyonu (Nominal ve Ordinal için)
    if (variable.measure !== 'Scale') {
      if (variable.value_labels) {
        if (!this.validateValueLabels(variable.value_labels)) {
          issues.push({
            type: 'error',
            messageKey: 'shared.validation_messages.invalid_value_labels',
            variableId: variable.id,
            message: this.transloco.translate('shared.validation_messages.invalid_value_labels')
          });
        }
        if (!this.uniqueValueLabels(variable.value_labels)) {
          issues.push({
            type: 'error',
            messageKey: 'shared.validation_messages.unique_value_labels',
            variableId: variable.id,
            message: this.transloco.translate('shared.validation_messages.unique_value_labels')
          });
        }
      } else {
        issues.push({
          type: 'error',
          messageKey: 'shared.validation_messages.missing_value_labels',
          variableId: variable.id,
          message: this.transloco.translate('shared.validation_messages.missing_value_labels')
        });
      }
    }

    // 4. Veri validasyonu
    const columnData = data.slice(1)

    if (variable.measure === 'Scale') {
      // Scale değişkenler için sayısal veri kontrolü
      if (!this.validateScaleData(columnData)) {
        issues.push({
          type: 'error',
          messageKey: 'shared.validation_messages.invalid_scale_data',
          variableId: variable.id,
          message: this.transloco.translate('shared.validation_messages.invalid_scale_data')
        });
      }
    } else {
      // Nominal/Ordinal değişkenler için value label kontrolü
      if (!this.validateCategoricalData(columnData, variable.value_labels)) {
        issues.push({
          type: 'error',
          messageKey: 'shared.validation_messages.invalid_categorical_data',
          variableId: variable.id,
          message: this.transloco.translate('shared.validation_messages.invalid_categorical_data')
        });
      }
    }

    // 5. Eksik değer kontrolü
    const missingCount = this.countVariableMissingValues(data);
    if (missingCount > 0) {
      issues.push({
        type: 'warning',
        messageKey: 'shared.validation_messages.missing_values',
        params: { count: missingCount },
        variableId: variable.id,
        message: this.transloco.translate('shared.validation_messages.missing_values', { count: missingCount })
      });
    }

    return issues;
  }

  private countMissingValues(data: any[][], columnIndex: number): number {
    return data.filter(row =>
      row[columnIndex] === null ||
      row[columnIndex] === undefined ||
      row[columnIndex] === ''
    ).length;
  }
  private countVariableMissingValues(data: any[]): number {
    return data.filter(row =>
      row === null ||
      row === undefined ||
      row === ''
    ).length;
  }
  private hasConsistentFormat(data: any[][], columnIndex: number): boolean {
    const values = data.map(row => row[columnIndex]).filter(val => val != null);
    const firstValueType = typeof values[0];
    return values.every(val => typeof val === firstValueType);
  }

  // Otomatik import durumu yönetimi
  updateImportStatus(variableId: string, state: ValidationState): void {
    const currentState = this.validateState.value;
    if (currentState) {
      if (!state.isValid) {
        state.importStatus = false;
      }
      currentState.variableStates[variableId] = state;
      this.validateState.next(currentState);
    }
  }

  validateVariables(variables: any[], data: any[][]): ValidationIssue[] {
    const issues: ValidationIssue[] = [];

    // Dataset validasyonları
    if (!variables || variables.length === 0) {
      issues.push({
        type: 'error',
        messageKey: 'shared.validation_messages.dataset_empty',
        variableId: 'dataset',
        message: this.transloco.translate('shared.validation_messages.dataset_empty')
      });
      return issues;
    }

    if (!data || data.length < 2) {
      issues.push({
        type: 'error',
        messageKey: 'shared.validation_messages.insufficient_rows',
        variableId: 'dataset',
        message: this.transloco.translate('shared.validation_messages.insufficient_rows')
      });
      return issues;
    }

    // Değişken validasyonları
    variables.forEach(variable => {
      if (!variable.import) return;
      if (!this.validateLabels(variable)) {
        issues.push({
          type: 'error',
          messageKey: 'shared.validation_messages.invalid_labels',
          variableId: variable.id,
          message: this.transloco.translate('shared.validation_messages.invalid_labels')
        });
      }

      if (variable.measure !== 'Scale' && variable.value_labels) {
        if (!this.validateValueLabels(variable.value_labels)) {
          issues.push({
            type: 'error',
            messageKey: 'shared.validation_messages.invalid_value_labels',
            variableId: variable.id,
            message: this.transloco.translate('shared.validation_messages.invalid_value_labels')
          });
        }
      }
      if (variable.measure !== 'Scale' && !this.uniqueValueLabels(variable.value_labels)) {

        issues.push({
          type: 'error',
          messageKey: 'shared.validation_messages.unique_value_labels',
          variableId: variable.id,
          message: this.transloco.translate('shared.validation_messages.unique_value_labels')
        });
      }
      const columnIndex = variables.findIndex(v => v.id === variable.id);
      if (variable.measure === 'Scale') {
        if (!this.validateData(data, columnIndex)) {
          issues.push({
            type: 'error',
            messageKey: 'shared.validation_messages.invalid_scale_data',
            variableId: variable.id,
            message: this.transloco.translate('shared.validation_messages.invalid_scale_data')
          });
        }
      }

      if (variable.measure != 'Scale') {
        // Sayısal veri kontrolü
        if (!this.validateData(data, columnIndex)) {
          issues.push({
            type: 'error',
            messageKey: 'shared.validation_messages.non_numeric_data',
            variableId: variable.id,
            message: this.transloco.translate('shared.validation_messages.non_numeric_data')
          });
        }
      }
      if (this.countMissingValues(data, columnIndex) > 0) {
        issues.push({
          type: 'warning',
          messageKey: 'shared.validation_messages.missing_values',
          variableId: variable.id,
          message: this.transloco.translate('shared.validation_messages.missing_values')
        });
      }
      if (!this.hasConsistentFormat(data, columnIndex)) {
        issues.push({
          type: 'warning',
          messageKey: 'shared.validation_messages.inconsistent_format',
          variableId: variable.id,
          message: this.transloco.translate('shared.validation_messages.inconsistent_format')
        });
      }
    });
    return issues;
  }

  private validateLabels(variable: any): boolean {
    // Check if label exists
    if (!variable?.label?.en || !variable?.label?.tr) return false;

    // Check if label is purely numeric
    const isNumericLabel = !isNaN(Number(variable.label.en)) && !isNaN(Number(variable.label.tr));
    if (isNumericLabel) return false;

    // Check other validation rules
    return this.validateLabelContent(variable.label.en) &&
      this.validateLabelContent(variable.label.tr);
  }

  private validateValueLabels(valueLabels: any): boolean {
    if (!valueLabels?.en || !valueLabels?.tr) return false;

    const enKeys = Object.keys(valueLabels.en);
    const trKeys = Object.keys(valueLabels.tr);

    // Base checks
    if (enKeys.length === 0 || trKeys.length === 0) return false;
    if (enKeys.length !== trKeys.length) return false;

    // Check each value has non-empty labels
    return enKeys.every(key => {
      const enLabel = valueLabels.en[key];
      const trLabel = valueLabels.tr[key];

      return Boolean(enLabel?.trim()) &&
        Boolean(trLabel?.trim()) &&
        this.validateLabelContent(enLabel) &&
        this.validateLabelContent(trLabel);
    });
  }

  private uniqueValueLabels(valueLabels: any): boolean {
    const enValues = Object.values(valueLabels.en).map(val => (val as string).toLowerCase());
    const trValues = Object.values(valueLabels.tr).map(val => (val as string).toLowerCase());

    const hasSameEnValues = enValues.some((val, index) => enValues.indexOf(val) !== index);
    const hasSameTrValues = trValues.some((val, index) => trValues.indexOf(val) !== index);

    if (hasSameEnValues || hasSameTrValues) return false;

    return true
  }

  private validateLabelContent(text: string): boolean {
    if (!text) return false;

    // Check for & 
    if (text.includes('&')
    ) return false;

    // Allow text that contains at least one non-numeric character
    const hasNonNumeric = /[^0-9]/.test(text);
    const result = hasNonNumeric;
    return result;
  }

  private validateData(data: any[][], columnIndex: number): boolean {
    const result = data.every(row => {
      const value = row[columnIndex];
      if (value === null || value === undefined || value === '') return true;
      return !isNaN(Number(value)) && typeof value !== 'boolean';
    });
    return result;
  }

  private validateScaleData(columnData: any[]): boolean {
    return columnData.every(value => {
      if (value === null || value === undefined || value === '') return true;
      return !isNaN(Number(value)) && typeof value !== 'boolean';
    });
  }

  private validateCategoricalData(columnData: any[], valueLabels: any): boolean {
    if (!valueLabels?.en) return false;
    const validValues = new Set(Object.keys(valueLabels.en));
    return columnData.every(value => {
      if (value === null || value === undefined || value === '') return true;
      return validValues.has(value.toString());
    });
  }
}

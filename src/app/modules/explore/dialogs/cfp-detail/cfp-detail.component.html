<div class="flex flex-col w-full h-full p-8 overflow-hidden bg-white rounded-2xl">

    <!-- Header -->
    <div class="flex-shrink-0">
        <!-- Top Actions -->
        <!-- Close Button -->
        <button (click)="closeDialog()" title="Close"
            class="absolute top-12 right-12 flex items-center justify-center p-1 text-sm font-normal transition-all bg-white border shadow gap-0.5 hover:border-red-600 text-green-950 active:scale-95 group rounded-3xl hover:border-3">
            <span class="flex items-center justify-center w-9 h-9">
                <ng-icon name="lucideX" class="text-xl"></ng-icon>
            </span>
        </button>

        <!-- Name (Organizer) -->
        <div class="mb-2">
            <span class="text-lg font-semibold text-purple-600">{{ data.organizerName }}</span>
        </div>

        <!-- Title -->
        <h1 class="mb-4 text-3xl font-bold leading-tight text-gray-900">
            {{ data.title }}
        </h1>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-hidden">
        <div class="grid h-full grid-cols-3 gap-8">

            <!-- Main Content (2 columns) -->
            <div class="col-span-2 pr-4 space-y-8 overflow-y-auto">

                <!-- Quick Info Section - Single Line Format -->
                <section>
                    <div class="p-4 border-2 border-blue-200 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50">
                        <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
                            <!-- When -->
                            <div class="flex items-center gap-2">
                                <ng-icon name="lucideCalendar" class="text-blue-600" size="16"></ng-icon>
                                <div>
                                    <div class="text-xs text-gray-600">When</div>
                                    <div class="text-sm font-semibold text-gray-900">{{ formatDate(data.conferenceDate)
                                        }}</div>
                                </div>
                            </div>

                            <!-- Where -->
                            <div class="flex items-center gap-2">
                                <ng-icon name="lucideMapPin" class="text-blue-600" size="16"></ng-icon>
                                <div>
                                    <div class="text-xs text-gray-600">Where</div>
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-semibold text-gray-900">{{ data.location }}</span>
                                        <span [class]="getLocationBadgeClass()"
                                            class="px-1.5 py-0.5 text-xs font-bold rounded border">
                                            {{ getLocationLabel() }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Submission Deadline -->
                            <div class="flex items-center gap-2">
                                <ng-icon name="lucideUpload" class="text-red-600" size="16"></ng-icon>
                                <div>
                                    <div class="text-xs text-gray-600">Submission Deadline</div>
                                    <div class="text-sm font-semibold text-gray-900">{{
                                        formatDate(data.keyDates.fullPaperDeadline) }}</div>
                                    <div class="text-xs text-red-600" *ngIf="!isDeadlinePassed()">
                                        {{ getDaysUntilDeadline() }} gün kaldı
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Due -->
                            <div class="flex items-center gap-2">
                                <ng-icon name="lucideBell" class="text-blue-600" size="16"></ng-icon>
                                <div>
                                    <div class="text-xs text-gray-600">Notification Due</div>
                                    <div class="text-sm font-semibold text-gray-900">{{
                                        formatDate(data.keyDates.notificationDate) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Long Description -->
                <section>
                    <div class="flex items-center gap-2 mb-4">
                        <ng-icon name="lucideFileText" class="text-purple-600" size="20"></ng-icon>
                        <h2 class="text-xl font-semibold text-gray-900">Description</h2>
                    </div>
                    <div class="p-6 bg-gray-50 rounded-xl">
                        <div class="space-y-4 leading-relaxed text-justify text-gray-700">
                            <p>{{ data.description }}</p>

                            <!-- Additional long description content -->
                            <div *ngIf="data.longDescription && data.longDescription.length > 0" class="space-y-4">
                                <div *ngFor="let paragraph of data.longDescription" class="text-gray-700">
                                    {{ paragraph }}
                                </div>
                            </div>

                            <!-- Default extended description if not provided -->
                            <div *ngIf="!data.longDescription || data.longDescription.length === 0"
                                class="space-y-4 text-gray-700">
                                <p>
                                    Bu konferans, {{ data.disciplines.join(', ') }} alanlarında çalışan araştırmacıları,
                                    akademisyenleri ve sektör uzmanlarını bir araya getirmeyi amaçlamaktadır.
                                    Katılımcılar en son araştırma bulgularını paylaşacak, gelecekteki işbirlikleri
                                    için fırsatlar yaratacak ve alanın geleceğini şekillendirecek konuları
                                    tartışacaklardır.
                                </p>

                                <p>
                                    Konferans kapsamında sunulacak bildiriler, hem teorik hem de uygulamalı
                                    araştırmaları
                                    kapsayacaktır. Özellikle {{ data.topics.slice(0, 3).join(', ') }}
                                    konularında yenilikçi yaklaşımlar ve çözümler öne çıkarılacaktır.
                                </p>

                                <p *ngIf="data.isIndexed">
                                    Konferans bildirileri {{ data.indexingServices.join(', ') }} tarafından
                                    indekslenecek
                                    olup, bu da araştırmanızın uluslararası görünürlüğünü artıracaktır.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Sidebar (1 column) -->
            <div class="col-span-1 space-y-6 overflow-y-auto">

                <!-- Links -->
                <section>
                    <div class="flex items-center gap-2 mb-4">
                        <ng-icon name="lucideLink" class="text-purple-600" size="20"></ng-icon>
                        <h3 class="text-lg font-semibold text-gray-900">Links</h3>
                    </div>

                    <div class="space-y-3">
                        <!-- Wiki CFP Link -->
                        <button
                            class="flex items-center justify-between w-full p-4 text-sm font-medium transition-colors border border-gray-200 rounded-xl hover:bg-gray-50 hover:border-gray-300"
                            (click)="openWikiCFP()">
                            <div class="flex items-center gap-3">
                                <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                                    <span class="text-lg">📖</span>
                                </div>
                                <div class="text-left">
                                    <div class="font-semibold text-gray-900">Wiki CFP</div>
                                    <div class="text-xs text-gray-500">Community information</div>
                                </div>
                            </div>
                            <ng-icon name="lucideExternalLink" class="text-gray-400" size="16"></ng-icon>
                        </button>

                        <!-- Event Link -->
                        <button
                            class="flex items-center justify-between w-full p-4 text-sm font-medium transition-colors border border-purple-200 rounded-xl hover:bg-purple-50 hover:border-purple-300"
                            (click)="visitWebsite()">
                            <div class="flex items-center gap-3">
                                <div class="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg">
                                    <span class="text-lg">🌐</span>
                                </div>
                                <div class="text-left">
                                    <div class="font-semibold text-gray-900">Event Website</div>
                                    <div class="text-xs text-gray-500">Official conference site</div>
                                </div>
                            </div>
                            <ng-icon name="lucideExternalLink" class="text-purple-400" size="16"></ng-icon>
                        </button>
                    </div>
                </section>

                <!-- Related Resources -->
                <section>
                    <div class="flex items-center gap-2 mb-4">
                        <ng-icon name="lucideFolder" class="text-purple-600" size="20"></ng-icon>
                        <h3 class="text-lg font-semibold text-gray-900">Related Resources</h3>
                    </div>

                    <div class="p-4 bg-gray-50 rounded-xl">
                        <div class="space-y-3">
                            <!-- Previous Year Proceedings -->
                            <div class="flex items-center gap-3">
                                <ng-icon name="lucideArchive" class="text-gray-500" size="16"></ng-icon>
                                <div class="flex-1">
                                    <span class="text-sm font-medium text-gray-700">Previous Proceedings</span>
                                    <p class="text-xs text-gray-500">Past conference papers</p>
                                </div>
                                <button (click)="viewPreviousProceedings()"
                                    class="text-xs font-medium text-blue-600 hover:text-blue-800">
                                    View
                                </button>
                            </div>

                            <!-- Call for Papers PDF -->
                            <div class="flex items-center gap-3" *ngIf="data.cfpPdfUrl">
                                <ng-icon name="lucideFileText" class="text-gray-500" size="16"></ng-icon>
                                <div class="flex-1">
                                    <span class="text-sm font-medium text-gray-700">CFP Document</span>
                                    <p class="text-xs text-gray-500">Detailed call document</p>
                                </div>
                                <button (click)="downloadCFPPdf()"
                                    class="text-xs font-medium text-blue-600 hover:text-blue-800">
                                    Download
                                </button>
                            </div>

                            <!-- Submission Guidelines -->
                            <div class="flex items-center gap-3">
                                <ng-icon name="lucideClipboardList" class="text-gray-500" size="16"></ng-icon>
                                <div class="flex-1">
                                    <span class="text-sm font-medium text-gray-700">Submission Guidelines</span>
                                    <p class="text-xs text-gray-500">Format and submission rules</p>
                                </div>
                                <button (click)="viewSubmissionGuidelines()"
                                    class="text-xs font-medium text-blue-600 hover:text-blue-800">
                                    View
                                </button>
                            </div>

                            <!-- Related Conferences -->
                            <div class="flex items-center gap-3">
                                <ng-icon name="lucideCalendarDays" class="text-gray-500" size="16"></ng-icon>
                                <div class="flex-1">
                                    <span class="text-sm font-medium text-gray-700">Related Conferences</span>
                                    <p class="text-xs text-gray-500">Similar events</p>
                                </div>
                                <button (click)="viewRelatedConferences()"
                                    class="text-xs font-medium text-blue-600 hover:text-blue-800">
                                    List
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
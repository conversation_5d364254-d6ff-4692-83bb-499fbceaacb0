import { Component, Inject, OnInit } from '@angular/core';
import { DialogRef, DIALOG_DATA } from '@angular/cdk/dialog';

interface CallForPaper {
  id: string;
  title: string;
  description: string;
  longDescription?: string[]; // YENİ: Detaylı açıklama paragrafları
  organizerName: string;
  organizerInstitution: string;
  conferenceDate: Date;
  deadline: Date;
  location: string;
  locationType: 'online' | 'hybrid' | 'physical';
  topics: string[];
  disciplines: string[];
  submissionTypes: ('full_paper' | 'short_paper' | 'poster' | 'workshop' | 'demo')[];
  impactFactor?: number;
  acceptanceRate?: number;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  isIndexed: boolean;
  indexingServices: string[];
  registrationFee?: number;
  currency: string;
  language: string[];
  expectedParticipants: number;
  keyDates: {
    abstractDeadline?: Date;
    fullPaperDeadline: Date;
    notificationDate: Date;
    cameraReadyDeadline: Date;
  };
  websiteUrl: string;
  wikiCfpUrl?: string; // YENİ: Wiki CFP linki
  cfpPdfUrl?: string; // YENİ: CFP PDF dokümanı
  contactEmail: string;
  viewCount: number;
  isBookmarked: boolean;
  isFavorited: boolean;
  popularity: number;
  createdAt: Date;
  isOpenAccess: boolean;
  hasStudentDiscount: boolean;
  acceptsRemotePresentation: boolean;
}

@Component({
  selector: 'app-cfp-detail',
  templateUrl: './cfp-detail.component.html',
  styleUrls: ['./cfp-detail.component.scss']
})
export class CfpDetailComponent implements OnInit {

  constructor(
    public dialogRef: DialogRef<CfpDetailComponent>,
    @Inject(DIALOG_DATA) public data: CallForPaper
  ) { }

  ngOnInit(): void {
    // Increment view count when dialog opens
    this.data.viewCount++;
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  toggleBookmark(): void {
    this.data.isBookmarked = !this.data.isBookmarked;
    // TODO: Emit event or call service to save bookmark status
    console.log('Bookmark toggled:', this.data.isBookmarked);
  }

  toggleFavorite(): void {
    this.data.isFavorited = !this.data.isFavorited;
    // TODO: Emit event or call service to save favorite status
    console.log('Favorite toggled:', this.data.isFavorited);
  }

  shareContent(): void {
    if (navigator.share) {
      navigator.share({
        title: this.data.title,
        text: this.data.description,
        url: `${window.location.origin}/cfp/${this.data.id}`
      }).catch(err => console.log('Error sharing:', err));
    } else {
      // Fallback: copy to clipboard
      const url = `${window.location.origin}/cfp/${this.data.id}`;
      navigator.clipboard.writeText(url).then(() => {
        // TODO: Show toast notification instead of alert
        alert('Link kopyalandı!');
      }).catch(err => {
        console.error('Could not copy text: ', err);
      });
    }
  }

  visitWebsite(): void {
    if (this.data.websiteUrl) {
      window.open(this.data.websiteUrl, '_blank', 'noopener,noreferrer');
    }
  }

  sendEmail(): void {
    if (this.data.contactEmail) {
      const subject = encodeURIComponent(`${this.data.title} - Soru`);
      const body = encodeURIComponent(`Merhaba,\n\n${this.data.title} konferansı hakkında bilgi almak istiyorum.\n\nTeşekkürler.`);
      window.open(`mailto:${this.data.contactEmail}?subject=${subject}&body=${body}`);
    }
  }

  addToCalendar(): void {
    // Google Calendar link oluştur
    const startDate = this.formatDateForCalendar(this.data.keyDates.fullPaperDeadline);
    const endDate = this.formatDateForCalendar(new Date(this.data.keyDates.fullPaperDeadline.getTime() + 60 * 60 * 1000)); // 1 hour later
    const title = encodeURIComponent(`${this.data.title} - Deadline`);
    const details = encodeURIComponent(`CFP Deadline: ${this.data.title}\nWebsite: ${this.data.websiteUrl}`);

    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startDate}/${endDate}&details=${details}`;
    window.open(googleCalendarUrl, '_blank', 'noopener,noreferrer');
  }

  applyNow(): void {
    if (this.data.websiteUrl) {
      window.open(this.data.websiteUrl, '_blank', 'noopener,noreferrer');
    }
  }

  // Helper Methods for Deadline and Dates
  getDaysUntilDeadline(): number {
    const today = new Date();
    const diffTime = this.data.keyDates.fullPaperDeadline.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getUrgencyClasses(): string {
    const days = this.getDaysUntilDeadline();
    if (days <= 7) return 'bg-red-100 text-red-700 border-red-200';
    if (days <= 30) return 'bg-orange-100 text-orange-700 border-orange-200';
    return 'bg-blue-100 text-blue-700 border-blue-200';
  }

  // Location and Type Helpers
  getLocationBadgeClass(): string {
    const classes = {
      'online': 'bg-green-100 text-green-700 border-green-200',
      'hybrid': 'bg-blue-100 text-blue-700 border-blue-200',
      'physical': 'bg-gray-100 text-gray-700 border-gray-200'
    };
    return classes[this.data.locationType] || 'bg-gray-100 text-gray-700 border-gray-200';
  }

  getLocationLabel(): string {
    const labels = {
      'online': 'Online',
      'hybrid': 'Hibrit',
      'physical': 'Fiziksel'
    };
    return labels[this.data.locationType] || 'Fiziksel';
  }

  // Difficulty Badge
  getDifficultyBadgeClass(): string {
    const classes = {
      'Beginner': 'bg-green-100 text-green-800 border-green-200',
      'Intermediate': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'Advanced': 'bg-red-100 text-red-800 border-red-200'
    };
    return classes[this.data.difficulty] || 'bg-gray-100 text-gray-800 border-gray-200';
  }

  // Submission Type Labels
  getSubmissionTypeLabel(type: string): string {
    const labels: { [key: string]: string } = {
      'full_paper': 'Tam Makale',
      'short_paper': 'Kısa Makale',
      'poster': 'Poster',
      'workshop': 'Workshop',
      'demo': 'Demo'
    };
    return labels[type] || type;
  }

  // Date Formatting
  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  }

  formatDateWithTime(date: Date): string {
    return new Intl.DateTimeFormat('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }

  private formatDateForCalendar(date: Date): string {
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  }

  // Number Formatting
  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  // Progress and Time Calculations
  getDeadlineProgress(): number {
    const now = new Date();
    const created = this.data.createdAt;
    const deadline = this.data.keyDates.fullPaperDeadline;

    const totalTime = deadline.getTime() - created.getTime();
    const elapsed = now.getTime() - created.getTime();

    return Math.max(0, Math.min(100, (elapsed / totalTime) * 100));
  }

  getTimeRemaining(): { days: number, hours: number, minutes: number } {
    const now = new Date();
    const diff = this.data.keyDates.fullPaperDeadline.getTime() - now.getTime();

    if (diff <= 0) {
      return { days: 0, hours: 0, minutes: 0 };
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    return { days, hours, minutes };
  }

  // Status Checks
  isDeadlinePassed(): boolean {
    return new Date() > this.data.keyDates.fullPaperDeadline;
  }

  getConferenceStatus(): string {
    const now = new Date();
    if (now > this.data.conferenceDate) {
      return 'completed';
    } else if (now > this.data.keyDates.fullPaperDeadline) {
      return 'submission_closed';
    } else {
      return 'open';
    }
  }

  getStatusLabel(): string {
    const status = this.getConferenceStatus();
    const labels = {
      'open': 'Başvurular Açık',
      'submission_closed': 'Başvurular Kapandı',
      'completed': 'Konferans Tamamlandı'
    };
    return labels[status] || 'Bilinmiyor';
  }

  getStatusBadgeClass(): string {
    const status = this.getConferenceStatus();
    const classes = {
      'open': 'bg-green-100 text-green-800 border-green-200',
      'submission_closed': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'completed': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 border-gray-200';
  }

  // YENİ: Related Resources Methods
  openWikiCFP(): void {
    const wikiUrl = this.data.wikiCfpUrl || `http://www.wikicfp.com/cfp/servlet/tool.search?q=${encodeURIComponent(this.data.title)}`;
    window.open(wikiUrl, '_blank', 'noopener,noreferrer');
  }

  downloadCFPPdf(): void {
    if (this.data.cfpPdfUrl) {
      window.open(this.data.cfpPdfUrl, '_blank', 'noopener,noreferrer');
    } else {
      console.log('CFP PDF not available');
    }
  }

  viewPreviousProceedings(): void {
    // TODO: Implement previous proceedings functionality
    console.log('View previous proceedings for:', this.data.title);
  }

  viewSubmissionGuidelines(): void {
    // Usually part of the main website
    if (this.data.websiteUrl) {
      window.open(`${this.data.websiteUrl}/submission`, '_blank', 'noopener,noreferrer');
    }
  }

  viewRelatedConferences(): void {
    // TODO: Implement related conferences functionality
    console.log('View related conferences for topics:', this.data.topics);
  }

  // YENİ: Analytics and Tracking
  trackCFPView(): void {
    // TODO: Implement analytics tracking
    console.log('CFP viewed:', {
      id: this.data.id,
      title: this.data.title,
      organizerName: this.data.organizerName,
      timestamp: new Date()
    });
  }

  trackActionClick(action: string): void {
    // TODO: Implement action tracking
    console.log('CFP action:', {
      action,
      cfpId: this.data.id,
      timestamp: new Date()
    });
  }

  // YENİ: Validation Methods
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // YENİ: Calendar Integration
  addDeadlineToCalendar(deadlineType: 'abstract' | 'fullPaper' | 'notification' | 'cameraReady'): void {
    let date: Date;
    let title: string;

    switch (deadlineType) {
      case 'abstract':
        if (!this.data.keyDates.abstractDeadline) return;
        date = this.data.keyDates.abstractDeadline;
        title = `${this.data.title} - Abstract Deadline`;
        break;
      case 'fullPaper':
        date = this.data.keyDates.fullPaperDeadline;
        title = `${this.data.title} - Full Paper Deadline`;
        break;
      case 'notification':
        date = this.data.keyDates.notificationDate;
        title = `${this.data.title} - Notification Date`;
        break;
      case 'cameraReady':
        date = this.data.keyDates.cameraReadyDeadline;
        title = `${this.data.title} - Camera Ready Deadline`;
        break;
    }

    const startDate = this.formatDateForCalendar(date);
    const endDate = this.formatDateForCalendar(new Date(date.getTime() + 60 * 60 * 1000));
    const encodedTitle = encodeURIComponent(title);
    const details = encodeURIComponent(`${title}\nWebsite: ${this.data.websiteUrl}`);

    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodedTitle}&dates=${startDate}/${endDate}&details=${details}`;
    window.open(googleCalendarUrl, '_blank', 'noopener,noreferrer');
  }

  addConferenceToCalendar(): void {
    const startDate = this.formatDateForCalendar(this.data.conferenceDate);
    const endDate = this.formatDateForCalendar(new Date(this.data.conferenceDate.getTime() + 24 * 60 * 60 * 1000)); // Assume 1 day conference
    const title = encodeURIComponent(this.data.title);
    const details = encodeURIComponent(`Conference: ${this.data.title}\nLocation: ${this.data.location}\nWebsite: ${this.data.websiteUrl}`);

    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startDate}/${endDate}&details=${details}&location=${encodeURIComponent(this.data.location)}`;
    window.open(googleCalendarUrl, '_blank', 'noopener,noreferrer');
  }

  // YENİ: Export Methods
  exportToBibTeX(): void {
    const bibtex = `@conference{${this.data.id},
  title={${this.data.title}},
  organization={${this.data.organizerName}},
  year={${this.data.conferenceDate.getFullYear()}},
  address={${this.data.location}},
  url={${this.data.websiteUrl}}
}`;

    const blob = new Blob([bibtex], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${this.data.title.replace(/[^a-zA-Z0-9]/g, '_')}.bib`;
    a.click();
    window.URL.revokeObjectURL(url);
  }

  exportToCSV(): void {
    const csvData = [
      ['Field', 'Value'],
      ['Title', this.data.title],
      ['Organizer', this.data.organizerName],
      ['Institution', this.data.organizerInstitution],
      ['Conference Date', this.formatDate(this.data.conferenceDate)],
      ['Submission Deadline', this.formatDate(this.data.keyDates.fullPaperDeadline)],
      ['Location', this.data.location],
      ['Location Type', this.data.locationType],
      ['Topics', this.data.topics.join('; ')],
      ['Disciplines', this.data.disciplines.join('; ')],
      ['Website', this.data.websiteUrl],
      ['Contact Email', this.data.contactEmail]
    ];

    const csvContent = csvData.map(row => row.map(field => `"${field}"`).join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${this.data.title.replace(/[^a-zA-Z0-9]/g, '_')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  }
}
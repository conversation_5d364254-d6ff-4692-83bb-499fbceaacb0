import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ExploreRoutingModule } from './explore-routing.module';
import { CfpComponent } from './pages/cfp/cfp.component';
import { ExploreComponent } from './pages/explore/explore.component';
import { CfpDetailComponent } from './dialogs/cfp-detail/cfp-detail.component';
import { NgIconsModule } from '@ng-icons/core';
import { TranslocoModule } from '@ngneat/transloco';


@NgModule({
  declarations: [
    CfpComponent,
    ExploreComponent,
    CfpDetailComponent
  ],
  imports: [
    CommonModule,
    ExploreRoutingModule,
    NgIconsModule,
    TranslocoModule
  ]
})
export class ExploreModule { }

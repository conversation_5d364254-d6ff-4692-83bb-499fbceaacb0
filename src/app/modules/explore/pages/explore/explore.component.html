<div class="flex flex-col h-full overflow-hidden explore-container" (keydown)="onKeyboardShortcut($event)"
    *transloco="let t; read: 'explore'">

    <!-- Interest Discovery Dialog -->
    <div *ngIf="showInterestDiscoveryDialog"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="flex flex-col w-full max-w-6xl p-6 mx-4 bg-white rounded-3xl h-full max-h-[90vh] overflow-y-auto">
            <!-- Header -->
            <div class="mb-6 text-center">
                <!-- <div class="mb-4 text-4xl">🎯</div> -->
                <h2 class="mb-2 text-2xl font-bold text-gray-900">Hangi alanda araştırma yapıyorsunuz?</h2>
                <p class="text-gray-600">
                    <PERSON><PERSON> uygun Call for Papers'ları göstermek için ilgi alan<PERSON>ızı seçin. Maksimum 5 alan se<PERSON>.
                </p>
            </div>

            <!-- GÜNCELLENEN: Interest Categories Grid - Dropdown Sistemi -->
            <div class="grid grid-cols-1 gap-4 mb-6 lg:grid-cols-2">
                <div *ngFor="let category of interestCategories" class="flex flex-col">

                    <!-- Ana Kategori Butonu -->
                    <button class="p-4 text-left transition-all duration-200 border-2 rounded-3xl hover:shadow-md"
                        [class.border-purple-500]="category.selected" [class.bg-purple-50]="category.selected"
                        [class.border-gray-200]="!category.selected" [class.bg-white]="!category.selected"
                        (click)="toggleInterestCategory(category)">

                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="text-2xl">{{ category.icon }}</div>
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900">{{ category.name }}</h3>
                                    <p class="text-xs text-gray-600">{{ category.description }}</p>
                                </div>
                            </div>

                            <!-- Dropdown Arrow -->
                            <div class="transition-transform duration-200" [class.rotate-180]="category.expanded">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </button>

                    <!-- Subcategories Dropdown -->
                    <div *ngIf="category.expanded"
                        class="p-4 mt-2 border border-gray-200 bg-gray-50 rounded-3xl animate-fade-in">

                        <div class="grid grid-cols-1 gap-2">
                            <button *ngFor="let subcategory of category.subcategories"
                                class="flex items-center justify-between p-3 text-left transition-all duration-150 border rounded-2xl hover:bg-white"
                                [class.border-purple-400]="subcategory.selected"
                                [class.bg-purple-100]="subcategory.selected"
                                [class.border-gray-200]="!subcategory.selected" [class.bg-white]="!subcategory.selected"
                                [disabled]="!subcategory.selected && selectedSubcategoryCount >= 5"
                                [class.opacity-50]="!subcategory.selected && selectedSubcategoryCount >= 5"
                                [class.cursor-not-allowed]="!subcategory.selected && selectedSubcategoryCount >= 5"
                                (click)="toggleSubcategory(category, subcategory)">

                                <div class="flex items-center gap-2">
                                    <!-- Checkbox -->
                                    <div class="flex items-center justify-center w-4 h-4 border-2 rounded-full"
                                        [class.border-purple-500]="subcategory.selected"
                                        [class.bg-purple-500]="subcategory.selected"
                                        [class.border-gray-300]="!subcategory.selected">
                                        <svg *ngIf="subcategory.selected" class="w-3 h-3 text-white" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </div>

                                    <span class="text-sm font-medium text-gray-800">{{ subcategory.name }}</span>
                                </div>

                                <!-- Keywords Preview -->
                                <div class="text-xs text-gray-500">
                                    {{ subcategory.keywords.slice(0, 2).join(', ') }}
                                    <span *ngIf="subcategory.keywords.length > 2">...</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- GÜNCELLENEN: Selection Counter - Minimum şart kaldırıldı -->
            <div class="mb-6 text-center">
                <div class="inline-flex items-center px-4 py-2 rounded-full"
                    [class.bg-green-100]="selectedSubcategoryCount >= 1"
                    [class.text-green-800]="selectedSubcategoryCount >= 1"
                    [class.bg-gray-100]="selectedSubcategoryCount < 1"
                    [class.text-gray-600]="selectedSubcategoryCount < 1">
                    <span class="text-sm font-medium">
                        {{ selectedSubcategoryCount }}/5 alan seçildi
                        <span *ngIf="selectedSubcategoryCount < 1" class="text-red-600">(En az 1 alan seçin)</span>
                    </span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-3">
                <button class="flex-1 secondary-blue-button" (click)="skipInterestDiscovery()">
                    <span>Şimdilik Atla</span>
                </button>

                <button class="flex-1 primary-blue-button" [disabled]="selectedSubcategoryCount < 1"
                    [class.opacity-50]="selectedSubcategoryCount < 1"
                    [class.cursor-not-allowed]="selectedSubcategoryCount < 1" (click)="saveSelectedInterests()">
                    <span class="text-white">Devam Et</span>
                </button>
            </div>

            <!-- Skip Notice -->
            <div class="mt-4 text-xs text-center text-gray-500">
                Atladığınız takdirde genel akademik içerik gösterilir.
                İstediğiniz zaman Ayarlar'dan değiştirebilirsiniz.
            </div>
        </div>
    </div>

    <!-- Content wrapper -->
    <div class="flex flex-col h-full content-wrapper">

        <!-- Page Header -->
        <div class="flex flex-col flex-shrink-0 p-4 pb-0 mb-6 bg-white border border-gray-200 rounded-3xl">
            <!-- Header Top -->
            <div class="flex items-center justify-between gap-4">
                <div class="flex items-center gap-3">
                    <h1 class="flex items-center justify-center gap-2 text-3xl font-bold text-gray-950 max-w-160">
                        <span>{{ t('title') }}</span>
                    </h1>
                    <div
                        class="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full">
                        {{ t('beta') }}
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs - Sadece CFP odaklı -->
            <div class="flex w-full">
                <button *ngFor="let tab of tabs"
                    class="flex items-center justify-center px-4 py-3 border-b-2 text-gray-600 hover:text-blue-600 hover:border-blue-300 transition-all duration-200 min-w-[120px]"
                    [class.border-purple-500]="currentTab === tab.key && tab.key === 'call_for_papers'"
                    [class.text-purple-600]="currentTab === tab.key && tab.key === 'call_for_papers'"
                    [class.border-gray-300]="currentTab === tab.key && tab.key !== 'call_for_papers'"
                    [class.text-gray-500]="currentTab === tab.key && tab.key !== 'call_for_papers'"
                    [class.cursor-not-allowed]="tab.key !== 'call_for_papers'" (click)="switchTab(tab.key)">
                    <span class="mr-2 text-lg">{{ tab.icon }}</span>
                    <span class="font-medium">{{ tab.label }}</span>
                </button>
            </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="isLoading" class="flex items-center justify-center flex-1 py-16">
            <div class="text-center">
                <div
                    class="w-12 h-12 mx-auto mb-4 border-4 border-gray-200 rounded-full loading-spinner border-t-purple-600 animate-spin">
                </div>
                <p class="text-lg font-medium text-gray-600">{{ t('loading') }}</p>
            </div>
        </div>

        <!-- Main Content Area -->
        <div *ngIf="!isLoading" class="flex-1 overflow-y-auto">

            <!-- Search Section - Sadece CFP için -->
            <div class="mb-6" *ngIf="currentTab === 'call_for_papers'">
                <div class="flex gap-4">
                    <div class="relative flex-1">
                        <input type="text" [placeholder]="t('search.placeholder')" #searchInput
                            class="w-full py-3 pl-10 pr-4 transition-colors border-2 border-gray-200 rounded-full focus:border-purple-500 focus:ring-2 focus:ring-purple-100"
                            [(ngModel)]="searchFilters.query" (keyup.enter)="onSearch()" />

                        <div class="absolute inset-y-0 flex items-center pointer-events-none left-4">
                            <ng-icon name="lucideSearch" class="text-xl text-gray-400"></ng-icon>
                        </div>
                    </div>

                    <div class="relative">
                        <button (click)="toggleAdvancedSearch()" class="secondary-blue-button">
                            <ng-icon name="lucideFilter" class="text-2xl text-purple-500"></ng-icon>
                            <span class="text-purple-500">{{ t('search.filters') }}</span>
                        </button>
                    </div>
                </div>

                <!-- Advanced Search Panel - CFP odaklı filtreler -->
                <div class="p-6 mt-4 border border-gray-200 bg-gray-50 rounded-2xl" *ngIf="showAdvancedSearch">
                    <div class="grid grid-cols-1 gap-6 mb-6 md:grid-cols-2 lg:grid-cols-3">
                        <!-- Discipline Filter -->
                        <div>
                            <label class="block mb-2 text-sm font-semibold text-gray-900">{{
                                t('search.advanced.discipline.label') }}</label>
                            <select
                                class="w-full p-3 text-gray-900 bg-white border border-gray-200 rounded-full focus:outline-none focus:border-purple-500 focus:ring-3 focus:ring-purple-500/10"
                                [(ngModel)]="searchFilters.discipline" (change)="onFilterChange()">
                                <option value="all">{{ t('search.advanced.discipline.all') }}</option>
                                <option *ngFor="let discipline of filterOptions.disciplines" [value]="discipline">
                                    {{ discipline }}
                                </option>
                            </select>
                        </div>

                        <!-- Year Filter -->
                        <div>
                            <label class="block mb-2 text-sm font-semibold text-gray-900">{{
                                t('search.advanced.year.label')
                                }}</label>
                            <select
                                class="w-full p-3 text-gray-900 bg-white border border-gray-200 rounded-full focus:outline-none focus:border-purple-500 focus:ring-3 focus:ring-purple-500/10"
                                [(ngModel)]="searchFilters.year" (change)="onFilterChange()">
                                <option value="all">{{ t('search.advanced.year.all') }}</option>
                                <option *ngFor="let year of filterOptions.years" [value]="year">
                                    {{ year }}
                                </option>
                            </select>
                        </div>

                        <!-- Language Filter -->
                        <div>
                            <label class="block mb-2 text-sm font-semibold text-gray-900">{{
                                t('search.advanced.language.label') }}</label>
                            <select
                                class="w-full p-3 text-gray-900 bg-white border border-gray-200 rounded-full focus:outline-none focus:border-purple-500 focus:ring-3 focus:ring-purple-500/10"
                                [(ngModel)]="searchFilters.language" (change)="onFilterChange()">
                                <option value="all">{{ t('search.advanced.language.all') }}</option>
                                <option *ngFor="let language of filterOptions.languages" [value]="language">
                                    {{ language }}
                                </option>
                            </select>
                        </div>

                        <!-- Location Type Filter - CFP specific -->
                        <div>
                            <label class="block mb-2 text-sm font-semibold text-gray-900">Etkinlik Türü</label>
                            <select
                                class="w-full p-3 text-gray-900 bg-white border border-gray-200 rounded-full focus:outline-none focus:border-purple-500 focus:ring-3 focus:ring-purple-500/10"
                                [(ngModel)]="searchFilters.locationType" (change)="onFilterChange()">
                                <option value="all">Tümü</option>
                                <option value="physical">Yüz Yüze</option>
                                <option value="online">Online</option>
                                <option value="hybrid">Hibrit</option>
                            </select>
                        </div>

                        <!-- Deadline Filter - CFP specific -->
                        <div>
                            <label class="block mb-2 text-sm font-semibold text-gray-900">Son Başvuru</label>
                            <select
                                class="w-full p-3 text-gray-900 bg-white border border-gray-200 rounded-full focus:outline-none focus:border-purple-500 focus:ring-3 focus:ring-purple-500/10"
                                [(ngModel)]="searchFilters.deadline" (change)="onFilterChange()">
                                <option value="all">Tümü</option>
                                <option value="urgent">Acil (30 gün içinde)</option>
                                <option value="soon">Yakın (3 ay içinde)</option>
                                <option value="later">Daha Sonra (3+ ay)</option>
                            </select>
                        </div>

                        <!-- Sort Options -->
                        <div>
                            <label class="block mb-2 text-sm font-semibold text-gray-900">{{
                                t('search.advanced.sort.label')
                                }}</label>
                            <select
                                class="w-full p-3 text-gray-900 bg-white border border-gray-200 rounded-full focus:outline-none focus:border-purple-500 focus:ring-3 focus:ring-purple-500/10"
                                [(ngModel)]="searchFilters.sortBy" (change)="onFilterChange()">
                                <option value="relevance">{{ t('search.advanced.sort.relevance') }}</option>
                                <option value="deadline">Son Başvuru Tarihi</option>
                                <option value="date">Konferans Tarihi</option>
                                <option value="popularity">Popülerlik</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex justify-end gap-4">
                        <button class="secondary-status-error-button" (click)="clearFilters()">
                            <span>🗑️</span>
                            {{ t('search.advanced.clearFilters') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Urgent CFP Widget - Sadece call_for_papers tab'ında göster -->
            <div *ngIf="currentTab === 'call_for_papers' && urgentCFPs.length > 0 && !isUrgentCFPDismissed"
                class="mb-8">
                <div
                    class="flex items-center justify-between p-4 border-2 border-red-200 rounded-2xl bg-gradient-to-r from-red-50 to-orange-50 urgent-cfp-widget">
                    <!-- Sol taraf: İçerik -->
                    <div class="flex items-center flex-1 min-w-0 gap-4">
                        <!-- Uyarı ikonu -->
                        <span class="flex-shrink-0 text-2xl animate-bounce">🚨</span>

                        <!-- Başlık ve sayı -->
                        <div class="flex items-center flex-shrink-0 gap-2">
                            <h3 class="text-lg font-bold text-red-800">
                                {{ t('urgentCfp.title') }}
                            </h3>
                        </div>

                        <!-- CFP başlıkları (scrollable) -->
                        <div class="flex-1 overflow-hidden">
                            <div class="flex items-center gap-6 animate-scroll">
                                <div *ngFor="let cfp of urgentCFPs"
                                    class="flex items-center gap-2 transition-colors cursor-pointer whitespace-nowrap hover:text-red-600 cfp-item"
                                    (click)="viewCFPDetails(cfp)" [title]="cfp.title">
                                    <span class="text-sm font-medium cfp-title">{{ cfp.title | slice:0:50 }}{{
                                        cfp.title.length > 50 ? '...'
                                        : '' }}</span>
                                    <span
                                        class="px-1.5 py-0.5 text-xs font-semibold text-red-600 bg-red-100 rounded-full flex-shrink-0">
                                        {{ getDaysUntilDeadline(cfp.deadline) }} {{ t('urgentCfp.days') }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Sağ taraf: Toplam sayı -->
                        <div class="flex items-center gap-2 px-3 py-1 bg-red-100 rounded-full">
                            <span class="text-sm font-semibold text-red-800">
                                Toplam: {{ callForPapers.length }}
                            </span>
                        </div>
                    </div>

                    <!-- Kapatma butonu -->
                    <button (click)="dismissUrgentCFP()"
                        class="flex items-center justify-center flex-shrink-0 w-8 h-8 ml-4 text-red-400 transition-colors rounded-lg hover:bg-red-100 hover:text-red-600 dismiss-btn"
                        [title]="t('urgentCfp.dismiss')">
                        <ng-icon name="lucideX" size="18"></ng-icon>
                    </button>
                </div>
            </div>

            <!-- CFP Grid View - Sadece Call for Papers için -->
            <div *ngIf="currentTab === 'call_for_papers'" class="relative cards-container">
                <div class="grid w-full grid-cols-1 gap-6 pb-8 mx-auto lg:grid-cols-2 xl:grid-cols-3">
                    <!-- Her CFP kartı -->
                    <div *ngFor="let cfp of getCurrentTabData()"
                        class="relative p-6 transition-all duration-300 bg-white border border-gray-200 cursor-pointer card-with-background group rounded-2xl hover:-translate-y-2 hover:shadow-xl hover:border-purple-500"
                        [style.--bg-image]="'url(' + ('https://images.unsplash.com/photo-1628786739444-6186c7c79834?q=80&w=596&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D') + ')'"
                        (click)="viewCFPDetails(cfp)">

                        <!-- CFP Header - Name & Title -->
                        <div class="relative z-10 mb-4">
                            <div
                                class="flex items-center gap-1 pr-2 mb-3 rounded-3xl text-nowrap w-fit bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-sm">
                                <!-- CFP Icon -->
                                <div class="flex items-center justify-center w-10 h-10">
                                    <span class="text-lg text-white">📢</span>
                                </div>

                                <!-- Organizer Name -->
                                <div>
                                    <span class="text-sm font-semibold text-purple-600">{{ cfp.organizerName }}</span>
                                </div>
                            </div>

                            <!-- CFP Title -->
                            <h3
                                class="text-lg font-bold leading-tight text-gray-900 transition-colors line-clamp-2 group-hover:text-purple-600">
                                {{ cfp.title }}
                            </h3>
                        </div>

                        <!-- When - Conference Date -->
                        <div class="flex justify-between ">
                            <div class="relative z-10 mb-4">
                                <div class="flex items-center gap-2 mb-2">
                                    <ng-icon name="lucideCalendar" class="text-blue-600" size="16"></ng-icon>
                                    <div class="text-sm text-gray-600">
                                        {{ formatDate(cfp.conferenceDate) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Where - Location -->
                            <div class="relative z-10 mb-4">
                                <div class="flex items-center gap-2 mb-2">
                                    <ng-icon name="lucideMapPin" class="text-blue-600" size="16"></ng-icon>
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm text-gray-600">{{ cfp.location }}</span>
                                        <span [class]="getLocationBadgeClass(cfp.locationType)"
                                            class="px-1.5 py-0.5 text-xs font-medium rounded border">
                                            {{ cfp.locationType | titlecase }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Key Dates -->
                        <div class="relative z-10 mb-4 space-y-2">
                            <!-- Submission Deadline -->
                            <div class="p-3 border border-red-200 rounded-lg bg-red-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-2">
                                        <ng-icon name="lucideUpload" class="text-red-600" size="14"></ng-icon>
                                        <span class="text-sm font-semibold text-red-800">{{ t('cfp.submissionDeadline')
                                            }}</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-bold text-red-700">{{
                                            formatDate(cfp.keyDates.fullPaperDeadline) }}</div>
                                        <div class="text-xs text-red-600"
                                            *ngIf="!isDeadlinePassed(cfp.keyDates.fullPaperDeadline)">
                                            {{ getDaysUntilDeadline(cfp.keyDates.fullPaperDeadline) }} {{
                                            t('cfp.daysLeft')
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Due -->
                            <div class="p-3 border border-blue-200 rounded-lg bg-blue-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-2">
                                        <ng-icon name="lucideBell" class="text-blue-600" size="14"></ng-icon>
                                        <span class="text-sm font-semibold text-blue-800">{{ t('cfp.notificationDue')
                                            }}</span>
                                    </div>
                                    <div class="text-sm font-bold text-blue-700">{{
                                        formatDate(cfp.keyDates.notificationDate) }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Links -->
                        <div class="relative z-10 flex gap-2">
                            <!-- Wiki CFP Link -->
                            <button
                                class="flex items-center justify-between w-full p-2 text-sm font-medium transition-colors border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300"
                                (click)="$event.stopPropagation(); openWikiCFPLink(cfp)">
                                <div class="flex items-center gap-2">
                                    <span class="text-base">📖</span>
                                    <span class="text-gray-700">{{ t('cfp.wikiCfp') }}</span>
                                </div>
                                <ng-icon name="lucideExternalLink" class="text-gray-400" size="14"></ng-icon>
                            </button>

                            <!-- Event Link -->
                            <button
                                class="flex items-center justify-between w-full p-2 text-sm font-medium transition-colors border border-purple-200 rounded-lg hover:bg-purple-50 hover:border-purple-300"
                                (click)="$event.stopPropagation(); openEventLink(cfp)">
                                <div class="flex items-center gap-2">
                                    <span class="text-base">🌐</span>
                                    <span class="text-purple-700">{{ t('cfp.eventWebsite') }}</span>
                                </div>
                                <ng-icon name="lucideExternalLink" class="text-purple-400" size="14"></ng-icon>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty State - Diğer tablar için -->
            <div *ngIf="currentTab !== 'call_for_papers'"
                class="flex flex-col items-center justify-center max-w-md py-16 mx-auto text-center">
                <div class="mb-6 text-6xl opacity-50">🚧</div>
                <h3 class="mb-2 text-xl font-semibold text-gray-900">{{ getCurrentTabLabel() }}</h3>
                <p class="mb-8 leading-relaxed text-gray-600">
                    Bu özellik yakında eklenecek. Şimdilik Call for Papers sekmesini kullanabilirsiniz.
                </p>
                <button
                    class="px-6 py-3 font-semibold text-white transition-all duration-200 bg-purple-600 hover:bg-purple-700 rounded-xl hover:-translate-y-1 hover:shadow-lg"
                    (click)="switchTab('call_for_papers')">
                    Call for Papers'a Git
                </button>
            </div>

            <!-- CFP Empty State - İYİLEŞTİRİLDİ -->
            <div *ngIf="currentTab === 'call_for_papers' && !isLoading && getCurrentTabData().length === 0"
                class="flex flex-col items-center justify-center max-w-md py-16 mx-auto text-center">

                <!-- Farklı durumlar için farklı mesajlar -->
                <ng-container *ngIf="!userKeywords || userKeywords.trim() === ''; else hasKeywordsNoResults">
                    <!-- Kullanıcının hiç interest'i yok -->
                    <div class="mb-6 text-6xl opacity-50">🎯</div>
                    <h3 class="mb-2 text-xl font-semibold text-gray-900">İlgi Alanlarınızı Belirleyin</h3>
                    <p class="mb-8 leading-relaxed text-gray-600">
                        Size uygun Call for Papers gösterebilmek için araştırma alanlarınızı belirlemeniz gerekiyor.
                    </p>
                    <button
                        class="px-6 py-3 font-semibold text-white transition-all duration-200 bg-purple-600 hover:bg-purple-700 rounded-xl hover:-translate-y-1 hover:shadow-lg"
                        (click)="showInterestDiscoveryDialog = true">
                        İlgi Alanlarımı Seç
                    </button>
                </ng-container>

                <!-- Kullanıcının interest'i var ama sonuç yok -->
                <ng-template #hasKeywordsNoResults>
                    <div class="mb-6 text-6xl opacity-50">🔍</div>
                    <h3 class="mb-2 text-xl font-semibold text-gray-900">{{ t('empty.title') }}</h3>
                    <p class="mb-8 leading-relaxed text-gray-600">
                        {{ t('empty.description') }}
                    </p>
                    <div class="flex gap-3">
                        <button
                            class="px-6 py-3 font-semibold text-white transition-all duration-200 bg-purple-600 hover:bg-purple-700 rounded-xl hover:-translate-y-1 hover:shadow-lg"
                            (click)="clearFilters()">
                            {{ t('empty.clearFilters') }}
                        </button>
                        <button
                            class="px-6 py-3 font-semibold text-purple-600 transition-all duration-200 border-2 border-purple-600 hover:bg-purple-50 rounded-xl hover:-translate-y-1 hover:shadow-lg"
                            (click)="showInterestDiscoveryDialog = true">
                            İlgi Alanlarımı Değiştir
                        </button>
                    </div>
                </ng-template>
            </div>
        </div>
    </div>
</div>
// Container with background image
.explore-container {
  position: relative;
  width: 100%;
  height: 100%;

  // Background overlay - artık <PERSON>
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    z-index: 1;
  }

  // Content wrapper
  .content-wrapper {
    position: relative;
    z-index: 2;
    height: 100%;
  }
}

// Card background image styling
.card-with-background {
  position: relative;
  overflow: hidden;

  // Background image her kartın üstünde
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120px; // Kartın üst kısmında sabit yükseklik
    background-image: var(--bg-image); // CSS variable ile image
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 1; // <PERSON>, blur yok
    z-index: 0;
    border-radius: 1rem 1rem 0 0; // Sadece üst köşeler yu<PERSON>lak
  }

  // Kartın içeriği
  > * {
    position: relative;
    z-index: 1;
  }

  // Overlay effect (opsiyonel - içeriğin okunabilirliği için)
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(255, 255, 255, 2) 100%
    );
    z-index: 0;
    border-radius: 1rem 1rem 0 0;
  }
}

// CFP Card hover effects
.cfp-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);

    .card-gradient-overlay {
      opacity: 1;
    }
  }
}

// Urgent CFP widget animation
.urgent-cfp-widget {
  animation: pulseGlow 2s ease-in-out infinite;

  .animate-scroll {
    display: flex;
    animation: scroll 20s linear infinite;

    &:hover {
      animation-play-state: paused;
    }
  }

  .cfp-item {
    flex-shrink: 0;
    padding-right: 2rem;
  }
}

@keyframes pulseGlow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(239, 68, 68, 0.4);
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

// Loading spinner
.loading-spinner {
  @apply animate-spin;
}

// Card grid responsiveness
.card-grid {
  display: grid;
  gap: 2rem;

  @media (min-width: 640px) {
    grid-template-columns: repeat(1, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1280px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

// Status badges
.status-badge {
  &.status-open {
    @apply bg-green-100 text-green-800 border-green-200;
  }

  &.status-closing-soon {
    @apply bg-orange-100 text-orange-800 border-orange-200;
  }

  &.status-closed {
    @apply bg-red-100 text-red-800 border-red-200;
  }
}

// Difficulty badges with icons
.difficulty-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;

  &.beginner {
    @apply bg-green-100 text-green-800;

    &::before {
      content: "🌱";
    }
  }

  &.intermediate {
    @apply bg-yellow-100 text-yellow-800;

    &::before {
      content: "⚡";
    }
  }

  &.advanced {
    @apply bg-red-100 text-red-800;

    &::before {
      content: "🔥";
    }
  }
}

// YENİ: Subcategories Dropdown Animations
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// YENİ: Interest Discovery Dialog Styles
.interest-discovery-dialog {
  .category-card {
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border-color: rgb(168, 85, 247);
      background-color: rgb(250, 245, 255);
      box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
    }

    &.expanded {
      border-color: rgb(168, 85, 247);

      .dropdown-arrow {
        transform: rotate(180deg);
      }
    }
  }

  .subcategory-card {
    transition: all 0.15s ease-in-out;

    &:hover {
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &.selected {
      border-color: rgb(168, 85, 247);
      background-color: rgb(243, 232, 255);

      .checkbox {
        background-color: rgb(168, 85, 247);
        border-color: rgb(168, 85, 247);
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: inherit;
        box-shadow: none;
      }
    }
  }

  .dropdown-arrow {
    transition: transform 0.2s ease-in-out;
  }

  .checkbox {
    transition: all 0.15s ease-in-out;
    border: 2px solid rgb(209, 213, 219);

    &.checked {
      background-color: rgb(168, 85, 247);
      border-color: rgb(168, 85, 247);
    }
  }
}

// YENİ: Selection Counter Animations
.selection-counter {
  transition: all 0.3s ease-in-out;

  &.valid {
    background-color: rgb(220, 252, 231);
    color: rgb(22, 101, 52);
    transform: scale(1.05);
  }

  &.invalid {
    background-color: rgb(243, 244, 246);
    color: rgb(75, 85, 99);
  }
}

// YENİ: Button States
.action-button {
  transition: all 0.2s ease-in-out;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;

    &:hover {
      transform: none;
    }
  }

  &:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:not(:disabled):active {
    transform: translateY(0);
  }
}

// YENİ: Responsive Design for Interest Discovery
@media (max-width: 768px) {
  .interest-discovery-dialog {
    .category-grid {
      grid-template-columns: 1fr;
    }

    .subcategory-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 640px) {
  .interest-discovery-dialog {
    padding: 1rem;
    margin: 1rem;
    max-height: 95vh;

    .category-card {
      padding: 1rem;
    }

    .subcategory-card {
      padding: 0.75rem;
    }
  }
}

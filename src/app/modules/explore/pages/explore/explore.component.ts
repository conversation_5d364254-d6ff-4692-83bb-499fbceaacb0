import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, ElementRef } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { Dialog } from '@angular/cdk/dialog';
import { CfpDetailComponent } from '../../dialogs/cfp-detail/cfp-detail.component';
import { TranslocoService } from '@ngneat/transloco';
import { McpService } from '../../services/mcp.service';
import { AuthService } from '@app/modules/auth/auth.service';

// CFP Interface - Call for Papers odaklı sistem
interface CallForPaper {
  id: string;
  title: string;
  description: string;
  longDescription?: string[];
  organizerName: string;
  organizerInstitution: string;
  conferenceDate: Date;
  deadline: Date;
  location: string;
  locationType: 'online' | 'hybrid' | 'physical';
  topics: string[];
  disciplines: string[];
  submissionTypes: ('full_paper' | 'short_paper' | 'poster' | 'workshop' | 'demo')[];
  impactFactor?: number;
  acceptanceRate?: number;
  difficulty: '<PERSON>gin<PERSON>' | 'Intermediate' | 'Advanced';
  isIndexed: boolean;
  indexingServices: string[];
  registrationFee?: number;
  currency: string;
  language: string[];
  expectedParticipants: number;
  keyDates: {
    abstractDeadline?: Date;
    fullPaperDeadline: Date;
    notificationDate: Date;
    cameraReadyDeadline: Date;
  };
  websiteUrl: string;
  wikiCfpUrl?: string;
  cfpPdfUrl?: string;
  contactEmail: string;
  viewCount: number;
  isBookmarked: boolean;
  isFavorited: boolean;
  popularity: number;
  createdAt: Date;
  isOpenAccess: boolean;
  hasStudentDiscount: boolean;
  acceptsRemotePresentation: boolean;
}

interface FilterOptions {
  disciplines: string[];
  years: number[];
  languages: string[];
}

interface SearchFilters {
  query: string;
  discipline: string;
  year: string;
  language: string;
  locationType: string;
  deadline: string;
  sortBy: 'relevance' | 'deadline' | 'date' | 'popularity';
  sortOrder: 'asc' | 'desc';
}

// YENİ: Interest Category Interface
interface InterestCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  selected: boolean;
  expanded: boolean; // YENİ: Dropdown açık mı?
  subcategories: InterestSubcategory[];
}

// YENİ: Interest Subcategory Interface
interface InterestSubcategory {
  id: string;
  name: string;
  keywords: string[];
  selected: boolean;
}

@Component({
  selector: 'app-explore',
  templateUrl: './explore.component.html',
  styleUrls: ['./explore.component.scss']
})
export class ExploreComponent implements OnInit, OnDestroy {
  @ViewChild('searchInput') searchInput!: ElementRef;

  private destroy$ = new Subject<void>();

  // State Management
  isLoading = false;
  currentTab = 'call_for_papers';
  viewMode: 'grid' | 'list' | 'detailed' = 'grid';

  // GÜNCELLENEN: Interest Discovery Dialog State
  showInterestDiscoveryDialog = false;
  interestCategories: InterestCategory[] = []; // Tip güncellendi
  selectedInterestCount = 0;
  selectedSubcategoryCount = 0; // YENİ: Toplam seçili subcategory sayısı

  // YENİ: CFP Data Management (ana odak)
  callForPapers: CallForPaper[] = [];
  urgentCFPs: CallForPaper[] = [];

  // YENİ: Urgent CFP dismiss durumu
  isUrgentCFPDismissed = false;

  // Background image artık üniversite ile alakalı değil
  backgroundImageUrl: string | null = null;

  // YENİ: User Data
  userKeywords: string = '';

  // YENİ: Profile değişiklikleri takip etmek için flag'ler 🎯
  private isProfileUpdateInProgress = false;
  private lastKnownInterests: string = '';

  // Search and Filters - CFP odaklı
  searchFilters: SearchFilters = {
    query: '',
    discipline: 'all',
    year: 'all',
    language: 'all',
    locationType: 'all',
    deadline: 'all',
    sortBy: 'relevance',
    sortOrder: 'desc'
  };

  filterOptions: FilterOptions = {
    disciplines: [
      'Bilgisayar Bilimleri', 'Mühendislik', 'Tıp', 'Fizik', 'Kimya',
      'Biyoloji', 'Matematik', 'Psikoloji', 'Sosyoloji', 'Ekonomi',
      'Hukuk', 'Eğitim', 'Tarih', 'Felsefe', 'Sanat'
    ],
    years: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),
    languages: ['Türkçe', 'İngilizce', 'Almanca', 'Fransızca', 'İspanyolca']
  };

  // GÜNCELLENEN: Tab Configuration - CFP odaklı
  tabs = [
    { key: 'call_for_papers', label: 'Call for Papers', icon: '📢', description: 'Aktif bildiri çağrıları' },
    { key: 'featured', label: 'Öne Çıkanlar', icon: '⭐', description: 'Yakında eklenecek' },
    { key: 'recent', label: 'Yeni Yayınlar', icon: '🆕', description: 'Yakında eklenecek' },
    { key: 'researchers', label: 'Araştırmacılar', icon: '👨‍🎓', description: 'Yakında eklenecek' },
    { key: 'interdisciplinary', label: 'Disiplinler Arası', icon: '🔗', description: 'Yakında eklenecek' },
    { key: 'open_access', label: 'Açık Erişim', icon: '🔓', description: 'Yakında eklenecek' }
  ];

  // Advanced Search
  showAdvancedSearch = false;

  constructor(
    private dialog: Dialog,
    private transloco: TranslocoService,
    private mcpService: McpService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.isLoading = true;
    this.initializeInterestCategories();

    // DEV: Test için localStorage temizle (production'da kaldırılmalı)
    console.log('🧹 Clearing localStorage for testing...');
    localStorage.removeItem('hasSeenInterestDiscovery'); // TEST İÇİN AÇIK

    // YENİ: AuthService event'lerini dinle 🎧
    this.setupAuthServiceListeners();

    // Kullanıcı verilerini al, sonra CFP verilerini yükle
    this.loadUserData()
      .then(() => {
        return this.loadMCPData(); // Sadece CFP verilerini yükle
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // YENİ: AuthService event'lerini dinleme sistemi 🎯
  private setupAuthServiceListeners(): void {
    console.log('🎧 Setting up AuthService listeners...');

    // Profile genel update'lerini dinle
    this.authService.profileUpdated$
      .pipe(takeUntil(this.destroy$))
      .subscribe((profileUpdated) => {
        if (profileUpdated && !this.isProfileUpdateInProgress) {
          console.log('🔄 Profile updated detected, refreshing content...');
          this.handleProfileUpdate();
        }
      });

    // User interests özel değişikliklerini dinle (daha spesifik)
    this.authService.userInterestsChanged$
      .pipe(takeUntil(this.destroy$))
      .subscribe((newInterests) => {
        if (newInterests && newInterests.trim().length > 0 && newInterests !== this.lastKnownInterests) {
          console.log('🎯 User interests changed detected:', newInterests);
          console.log('📋 Previous interests:', this.lastKnownInterests);

          this.lastKnownInterests = newInterests;
          this.handleInterestsChange(newInterests);
        }
      });
  }

  // YENİ: Profile güncellendiğinde çalışacak metod
  private async handleProfileUpdate(): Promise<void> {
    try {
      this.isProfileUpdateInProgress = true;
      console.log('🔄 Handling profile update...');

      // Kullanıcı verilerini yeniden yükle
      await this.loadUserData();

      // CFP'leri yeniden yükle
      await this.loadMCPData();

      console.log('✅ Profile update handled successfully');
    } catch (error) {
      console.error('❌ Error handling profile update:', error);
    } finally {
      this.isProfileUpdateInProgress = false;
    }
  }

  // YENİ: Interest'ler değiştiğinde çalışacak metod (daha hızlı)
  private async handleInterestsChange(newInterests: string): Promise<void> {
    try {
      console.log('🎯 Handling interests change with new data:', newInterests);

      // Keyword'leri güncelle
      this.userKeywords = await this.convertInterestsToKeywords(newInterests);
      console.log('🔄 Updated keywords:', this.userKeywords);

      // Sadece CFP'leri yeniden yükle (user data'ya gerek yok)
      this.isLoading = true;
      await this.loadCallForPapersFromMCP();

      // Arka plan resmini de güncelle
      await this.loadBackgroundImage();

      console.log('✅ Interests change handled successfully');
    } catch (error) {
      console.error('❌ Error handling interests change:', error);
    } finally {
      this.isLoading = false;
    }
  }

  // GÜNCELLENEN: Interest string'ini keyword'lere dönüştürme - NO FALLBACK
  private async convertInterestsToKeywords(interests: string): Promise<string> {
    try {
      if (!interests || interests.trim().length === 0) {
        return ''; // NO FALLBACK - boş string döndür
      }

      // Interest'ları parçala ve temizle
      const keywordArray = interests
        .split(/[,;\n]+/)
        .map(item => item.trim())
        .filter(item => item.length > 2) // 2 karakterden uzun olanlar
        .slice(0, 5); // İlk 5 tanesi

      if (keywordArray.length === 0) {
        return ''; // NO FALLBACK - boş string döndür
      }

      return keywordArray.join(' ').toLowerCase();
    } catch (error) {
      console.error('Error converting interests to keywords:', error);
      return ''; // NO FALLBACK - hata durumunda da boş string
    }
  }

  // GÜNCELLENEN: Interest kategorilerini koda göme - kesfet_kategoriler.json verilerine göre
  private initializeInterestCategories(): void {
    try {
      console.log('🔄 Initializing interest categories from code...');

      this.interestCategories = [
        {
          id: 'medical_medicine',
          name: 'Tıp ve Klinik Bilimler',
          icon: '🏥',
          description: 'Klinik tıp, tıbbi araştırma ve sağlık hizmetleri',
          selected: false,
          expanded: false,
          subcategories: [
            {
              id: 'neurology',
              name: 'Nörobilimler ve Nöroloji',
              keywords: [
                'neurology', 'neuroscience', 'neurological disorders', 'brain', 'nervous system',
                'alzheimer', 'parkinson', 'epilepsy', 'stroke', 'neuroimaging', 'EEG', 'MRI',
                'nöroloji', 'beyin', 'sinir sistemi', 'nörolojik hastalıklar'
              ],
              selected: false
            },
            {
              id: 'emergency_medicine',
              name: 'Acil Tıp',
              keywords: [
                'emergency medicine', 'critical care', 'trauma', 'emergency department', 'resuscitation',
                'triage', 'intensive care', 'emergency surgery', 'cardiac arrest',
                'acil tıp', 'acil servis', 'travma', 'yoğun bakım', 'kritik bakım'
              ],
              selected: false
            },
            {
              id: 'gynecology',
              name: 'Kadın Hastalıkları ve Doğum',
              keywords: [
                'gynecology', 'obstetrics', 'pregnancy', 'maternal health', 'reproductive health',
                'fertility', 'contraception', 'menstrual disorders', 'gynecological surgery',
                'jinekoloji', 'obstetrik', 'gebelik', 'doğum', 'üreme sağlığı'
              ],
              selected: false
            },
            {
              id: 'cardiology',
              name: 'Kardiyovasküler Sistem ve Kardiyoloji',
              keywords: [
                'cardiology', 'cardiovascular', 'heart disease', 'cardiac surgery', 'hypertension',
                'coronary artery', 'heart failure', 'arrhythmia', 'echocardiography',
                'kardiyoloji', 'kalp hastalıkları', 'hipertansiyon', 'kalp yetmezliği'
              ],
              selected: false
            },
            {
              id: 'internal_medicine',
              name: 'Genel Dahiliye',
              keywords: [
                'internal medicine', 'general medicine', 'primary care', 'diabetes', 'hypertension',
                'infectious diseases', 'chronic diseases', 'preventive medicine',
                'dahiliye', 'genel tıp', 'birinci basamak', 'kronik hastalıklar'
              ],
              selected: false
            }
          ]
        },
        {
          id: 'dentistry',
          name: 'Diş Hekimliği ve Ağız Bilimleri',
          icon: '🦷',
          description: 'Diş hekimliği, ağız sağlığı ve dental araştırmalar',
          selected: false,
          expanded: false,
          subcategories: [
            {
              id: 'pedodontics',
              name: 'Pedodonti (Çocuk Diş Hekimliği)',
              keywords: [
                'pediatric dentistry', 'pedodontics', 'children dental care', 'primary teeth',
                'dental caries children', 'fluoride treatment', 'dental trauma children',
                'pedodonti', 'çocuk diş hekimliği', 'süt dişleri', 'çürük tedavisi'
              ],
              selected: false
            },
            {
              id: 'orthodontics',
              name: 'Ortodonti',
              keywords: [
                'orthodontics', 'braces', 'dental alignment', 'malocclusion', 'clear aligners',
                'orthognathic surgery', 'dental crowding', 'bite correction',
                'ortodonti', 'diş teli', 'maloklüzyon', 'diş düzeltme'
              ],
              selected: false
            },
            {
              id: 'oral_surgery',
              name: 'Ağız, Diş ve Çene Cerrahisi',
              keywords: [
                'oral surgery', 'maxillofacial surgery', 'dental implants', 'tooth extraction',
                'jaw surgery', 'oral pathology', 'dental trauma', 'wisdom teeth',
                'ağız cerrahisi', 'çene cerrahisi', 'dental implant', 'diş çekimi'
              ],
              selected: false
            },
            {
              id: 'periodontics',
              name: 'Periodontoloji',
              keywords: [
                'periodontics', 'gum disease', 'periodontitis', 'gingivitis', 'dental hygiene',
                'periodontal treatment', 'gum recession', 'dental scaling',
                'periodontoloji', 'diş eti hastalıkları', 'periodontitis', 'gingivit'
              ],
              selected: false
            },
            {
              id: 'endodontics',
              name: 'Endodonti',
              keywords: [
                'endodontics', 'root canal', 'dental pulp', 'endodontic treatment',
                'tooth pain', 'dental abscess', 'apicoectomy', 'pulpotomy',
                'endodonti', 'kanal tedavisi', 'diş ağrısı', 'apeks rezeksiyonu'
              ],
              selected: false
            }
          ]
        },
        {
          id: 'health_sciences',
          name: 'Sağlık Bilimleri ve Hemşirelik',
          icon: '⚕️',
          description: 'Hemşirelik, halk sağlığı ve sağlık yönetimi',
          selected: false,
          expanded: false,
          subcategories: [
            {
              id: 'nursing',
              name: 'Hemşirelik',
              keywords: [
                'nursing', 'patient care', 'nursing education', 'clinical nursing', 'nursing research',
                'nursing management', 'community nursing', 'nursing ethics', 'nursing practice',
                'hemşirelik', 'hasta bakımı', 'hemşirelik eğitimi', 'klinik hemşirelik'
              ],
              selected: false
            },
            {
              id: 'public_health',
              name: 'Halk Sağlığı',
              keywords: [
                'public health', 'epidemiology', 'health promotion', 'disease prevention',
                'environmental health', 'health policy', 'community health', 'vaccination',
                'halk sağlığı', 'epidemiyoloji', 'sağlığı geliştirme', 'hastalık önleme'
              ],
              selected: false
            },
            {
              id: 'health_management',
              name: 'Sağlık Yönetimi',
              keywords: [
                'health management', 'healthcare administration', 'hospital management',
                'health economics', 'healthcare quality', 'health services research',
                'sağlık yönetimi', 'hastane yönetimi', 'sağlık ekonomisi', 'sağlık hizmetleri'
              ],
              selected: false
            },
            {
              id: 'rehabilitation',
              name: 'Rehabilitasyon Bilimleri',
              keywords: [
                'rehabilitation', 'physical therapy', 'occupational therapy', 'physiotherapy',
                'disability', 'motor recovery', 'functional recovery', 'assistive technology',
                'rehabilitasyon', 'fizyoterapi', 'meslek terapisi', 'engellilik'
              ],
              selected: false
            },
            {
              id: 'nutrition',
              name: 'Beslenme ve Diyetetik',
              keywords: [
                'nutrition', 'dietetics', 'clinical nutrition', 'nutritional assessment',
                'dietary intervention', 'malnutrition', 'obesity', 'food science',
                'beslenme', 'diyetetik', 'klinik beslenme', 'obezite', 'yetersiz beslenme'
              ],
              selected: false
            }
          ]
        },
        {
          id: 'social_sciences',
          name: 'Sosyal Bilimler ve Psikoloji',
          icon: '👥',
          description: 'Psikoloji, sosyal hizmet ve davranış bilimleri',
          selected: false,
          expanded: false,
          subcategories: [
            {
              id: 'psychology',
              name: 'Psikoloji',
              keywords: [
                'psychology', 'clinical psychology', 'cognitive psychology', 'behavioral psychology',
                'mental health', 'psychotherapy', 'psychological assessment', 'counseling',
                'psikoloji', 'klinik psikoloji', 'ruh sağlığı', 'psikoterapi', 'danışmanlık'
              ],
              selected: false
            },
            {
              id: 'social_work',
              name: 'Sosyal Hizmet',
              keywords: [
                'social work', 'social services', 'community intervention', 'social welfare',
                'social policy', 'family intervention', 'child welfare', 'social justice',
                'sosyal hizmet', 'sosyal hizmetler', 'toplum müdahalesi', 'sosyal politika'
              ],
              selected: false
            },
            {
              id: 'behavioral_sciences',
              name: 'Davranış Bilimleri',
              keywords: [
                'behavioral sciences', 'human behavior', 'behavioral intervention', 'addiction',
                'behavior modification', 'cognitive behavioral therapy', 'behavioral assessment',
                'davranış bilimleri', 'insan davranışı', 'davranış değişikliği', 'bağımlılık'
              ],
              selected: false
            }
          ]
        },
        {
          id: 'educational_sciences',
          name: 'Eğitim Bilimleri',
          icon: '📚',
          description: 'Eğitim araştırmaları, pedagoji ve öğrenme bilimleri',
          selected: false,
          expanded: false,
          subcategories: [
            {
              id: 'medical_education',
              name: 'Tıp Eğitimi',
              keywords: [
                'medical education', 'medical training', 'clinical skills', 'simulation training',
                'medical curriculum', 'residency training', 'medical pedagogy', 'assessment',
                'tıp eğitimi', 'tıbbi eğitim', 'klinik beceriler', 'simülasyon', 'müfredat'
              ],
              selected: false
            },
            {
              id: 'nursing_education',
              name: 'Hemşirelik Eğitimi',
              keywords: [
                'nursing education', 'nursing curriculum', 'clinical training', 'nursing pedagogy',
                'nursing skills', 'nursing simulation', 'nursing assessment', 'nursing competency',
                'hemşirelik eğitimi', 'hemşirelik müfredatı', 'klinik eğitim', 'beceri eğitimi'
              ],
              selected: false
            },
            {
              id: 'health_education',
              name: 'Sağlık Eğitimi',
              keywords: [
                'health education', 'health literacy', 'patient education', 'health promotion education',
                'health behavior education', 'health communication', 'health awareness',
                'sağlık eğitimi', 'sağlık okuryazarlığı', 'hasta eğitimi', 'sağlık iletişimi'
              ],
              selected: false
            },
            {
              id: 'educational_technology',
              name: 'Eğitim Teknolojisi',
              keywords: [
                'educational technology', 'e-learning', 'online education', 'digital learning',
                'educational software', 'learning management systems', 'virtual reality education',
                'eğitim teknolojisi', 'uzaktan eğitim', 'dijital öğrenme', 'sanal gerçeklik'
              ],
              selected: false
            }
          ]
        }
      ];

      console.log(`🎯 Initialized ${this.interestCategories.length} interest categories with subcategories`);

    } catch (error) {
      console.error('❌ Error initializing interest categories:', error);
      // Fallback olarak boş array bırak
      this.interestCategories = [];
    }
  }

  // DÜZELTME: Interest Discovery Popup - localStorage kontrolü düzeltildi
  private async showInterestDiscoveryPopup(): Promise<void> {
    try {
      console.log('🎭 Interest Discovery popup called');

      // DÜZELTME: Popup kontrolünü daha spesifik yap
      const hasSeenPopup = localStorage.getItem('hasSeenInterestDiscovery');
      console.log('📋 localStorage hasSeenInterestDiscovery:', hasSeenPopup);

      // Eğer popup daha önce gösterilmişse ve kullanıcı atlamışsa, tekrar gösterme
      if (hasSeenPopup === 'true') {
        console.log('❌ User has seen popup before, not showing again');
        this.userKeywords = ''; // Boş bırak
        return;
      }

      // Popup'ı göster
      console.log('✅ Showing Interest Discovery dialog');
      this.showInterestDiscoveryDialog = true;

    } catch (error) {
      console.error('❌ Error showing interest discovery popup:', error);
      this.userKeywords = ''; // Hata durumunda boş bırak
    }
  }

  // GÜNCELLENEN: Ana kategori toggle (dropdown açma/kapama)
  toggleInterestCategory(category: InterestCategory): void {
    // Eğer kategori zaten expanded ise, kapat
    if (category.expanded) {
      category.expanded = false;
      // Kategori kapandığında tüm subcategory'leri de unselect yap
      category.subcategories.forEach(sub => sub.selected = false);
      category.selected = false;
      this.updateSelectionCounts();
      return;
    }

    // Diğer kategorileri kapat (sadece bir kategori açık olsun)
    this.interestCategories.forEach(cat => {
      if (cat.id !== category.id) {
        cat.expanded = false;
        cat.selected = false;
        cat.subcategories.forEach(sub => sub.selected = false);
      }
    });

    // Bu kategoriyi aç
    category.expanded = true;
    category.selected = true;

    this.updateSelectionCounts();
  }

  // YENİ: Subcategory toggle
  toggleSubcategory(category: InterestCategory, subcategory: InterestSubcategory): void {
    // Max 5 subcategory seçilebilir
    if (!subcategory.selected && this.selectedSubcategoryCount >= 5) {
      console.log('❌ Maximum 5 subcategory seçilebilir');
      return;
    }

    // Subcategory'yi toggle et
    subcategory.selected = !subcategory.selected;

    // Ana kategorinin selected durumunu güncelle
    const hasSelectedSubcategories = category.subcategories.some(sub => sub.selected);
    category.selected = hasSelectedSubcategories;

    this.updateSelectionCounts();
  }

  // YENİ: Seçim sayılarını güncelle
  private updateSelectionCounts(): void {
    this.selectedInterestCount = this.interestCategories.filter(cat => cat.selected).length;
    this.selectedSubcategoryCount = this.interestCategories
      .flatMap(cat => cat.subcategories)
      .filter(sub => sub.selected).length;

    console.log(`📊 Selection counts - Categories: ${this.selectedInterestCount}, Subcategories: ${this.selectedSubcategoryCount}`);
  }

  // GÜNCELLENEN: Interest'ları kaydet - Minimum seçim şartı kaldırıldı
  async saveSelectedInterests(): Promise<void> {
    // Minimum şart kaldırıldı - en az 1 subcategory yeterli
    if (this.selectedSubcategoryCount < 1) {
      console.log('❌ En az 1 subcategory seçilmeli');
      return;
    }

    try {
      // Seçili subcategory'lerin keyword'lerini topla
      const selectedSubcategories = this.interestCategories
        .flatMap(cat => cat.subcategories)
        .filter(sub => sub.selected);

      const allKeywords = selectedSubcategories.flatMap(sub => sub.keywords);
      const interestString = allKeywords.join(', ');

      console.log('🎯 Selected subcategories:', selectedSubcategories.map(s => s.name));
      console.log('📝 Generated interest string:', interestString);

      // Profile'a kaydet
      const profileData = {
        profile: {
          interests: interestString
        }
      };

      // Kullanıcının mevcut profile'ını al
      const user = await this.authService.getUser().toPromise();

      if (user?.profile?.id) {
        // Mevcut profile'ı güncelle
        await this.authService.updateProfile(user.profile.id, profileData).toPromise();
      } else {
        // Yeni profile oluştur
        await this.authService.createProfile(profileData).toPromise();
      }

      // Keyword'leri güncelle - keywords kullan
      this.userKeywords = allKeywords.slice(0, 5).join(' ');
      this.lastKnownInterests = this.userKeywords;

      // Dialog'u kapat
      this.closeInterestDiscoveryDialog();

      // CFP'leri yeniden yükle
      await this.loadMCPData();

      console.log('✅ User selected interests saved:', this.userKeywords);

    } catch (error) {
      console.error('❌ Error saving interests:', error);
      this.closeInterestDiscoveryDialog();
    }
  }

  // GÜNCELLENEN: Interest dialog'u şimdilik atla - NO FALLBACK
  skipInterestDiscovery(): void {
    this.userKeywords = ''; // GÜNCELLEME: Fallback keyword kullanılmayacak
    this.lastKnownInterests = this.userKeywords;
    this.closeInterestDiscoveryDialog();
    console.log('User skipped interest selection, no keywords will be used');
  }

  // GÜNCELLENEN: Interest dialog'u kapat
  private closeInterestDiscoveryDialog(): void {
    this.showInterestDiscoveryDialog = false;
    // Popup gösterildi olarak işaretle
    localStorage.setItem('hasSeenInterestDiscovery', 'true');

    // Kategorileri sıfırla
    this.interestCategories.forEach(cat => {
      cat.selected = false;
      cat.expanded = false;
      cat.subcategories.forEach(sub => sub.selected = false);
    });
    this.selectedInterestCount = 0;
    this.selectedSubcategoryCount = 0;
  }

  // GÜNCELLENEN: Kullanıcı verilerini yükleme - Interest Discovery mantığı düzeltildi
  private async loadUserData(): Promise<void> {
    try {
      console.log('🔍 Loading user data and checking interests...');

      // Kullanıcının MCP keyword'lerini al
      const userData = await this.authService.getMCPKeywords().toPromise();
      console.log('📋 Raw user keywords from AuthService:', userData);

      this.userKeywords = userData || '';
      this.lastKnownInterests = this.userKeywords;

      // DÜZELTME: Interest Discovery popup kontrolü
      console.log('🎯 Checking if Interest Discovery should be shown...');
      console.log('📊 Current userKeywords:', `"${this.userKeywords}"`);
      console.log('📊 Keywords length:', this.userKeywords.length);
      console.log('📊 Keywords trimmed:', `"${this.userKeywords.trim()}"`);

      // Eğer kullanıcının hiç keyword'i yoksa veya sadece boşluk varsa popup göster
      if (!this.userKeywords || this.userKeywords.trim() === '' || this.userKeywords.trim() === 'academic conference research') {
        console.log('❗ No valid user keywords found, showing Interest Discovery popup');
        await this.showInterestDiscoveryPopup();
      } else {
        console.log('✅ User has valid keywords, proceeding without popup');
      }

      console.log('📝 Final userKeywords after loadUserData:', this.userKeywords);
    } catch (error) {
      console.error('❌ Error loading user data:', error);
      // Hata durumunda da popup göster
      console.log('❗ Error occurred, showing Interest Discovery popup as fallback');
      await this.showInterestDiscoveryPopup();
    }
  }

  // GÜNCELLENEN: MCP verilerini yükleme - artık user verilerini kullanıyor
  private async loadMCPData(): Promise<void> {
    try {
      // Paralel olarak CFP ve background image yükle
      await Promise.all([
        this.loadCallForPapersFromMCP(),
        this.loadBackgroundImage()
      ]);
    } catch (error) {
      console.error('Error loading MCP data:', error);
    }
  }

  // GÜNCELLENEN: MCP'den CFP verilerini yükle - dinamik keyword'lerle
  private async loadCallForPapersFromMCP(): Promise<void> {
    try {
      console.log('Loading CFPs with keywords:', this.userKeywords);

      // GÜNCELLEME: Eğer keyword yoksa MCP çağrısı yapma
      if (!this.userKeywords || this.userKeywords.trim() === '') {
        console.log('No user keywords available, skipping MCP call');
        this.callForPapers = [];
        this.urgentCFPs = [];
        return;
      }

      // Kullanıcının keyword'leri ile CFP'leri al
      const mcpEvents = await this.mcpService.getCallForPapers(this.userKeywords, 3);

      // MCP verilerini CallForPaper formatına dönüştür
      const mappedEvents = mcpEvents.map(event =>
        this.mcpService.mapMCPEventToCallForPaper(event)
      );

      // Eksik verisi olan kartları filtrele
      this.callForPapers = mappedEvents.filter(cfp => {
        return cfp &&
          cfp.title &&
          cfp.description &&
          cfp.deadline &&
          cfp.conferenceDate &&
          cfp.location;
      });

      // Urgent CFPs hesaplama
      this.urgentCFPs = this.callForPapers
        .filter(cfp => this.getDaysUntilDeadline(cfp.deadline) <= 30)
        .sort((a, b) => a.deadline.getTime() - b.deadline.getTime());

      console.log(`Loaded ${this.callForPapers.length} CFPs with user keywords`);
    } catch (error) {
      console.error('Error loading CFPs from MCP:', error);
      this.callForPapers = [];
      this.urgentCFPs = [];
    }
  }

  // GÜNCELLENEN: Arka plan resmi yükle - kullanıcının interest'larına göre
  private async loadBackgroundImage(): Promise<void> {
    try {
      // GÜNCELLEME: Eğer keyword yoksa background image yükleme
      if (!this.userKeywords || this.userKeywords.trim() === '') {
        console.log('No user keywords available, skipping background image');
        this.backgroundImageUrl = null;
        return;
      }

      // Kullanıcının keyword'lerine göre görsel arama URL'i oluştur
      const imageSearchUrl = this.generateImageSearchUrl(this.userKeywords);

      console.log('Taking screenshot for user interests:', imageSearchUrl);

      const screenshot = await this.mcpService.takeScreenshot(imageSearchUrl, 1920, 1080);

      if (screenshot && screenshot.url) {
        this.backgroundImageUrl = screenshot.url;
        console.log('Background image loaded:', this.backgroundImageUrl);
      }
    } catch (error) {
      console.error('Error loading background image:', error);
    }
  }

  // GÜNCELLENEN: Kullanıcı interest'larına göre görsel URL'i oluşturma
  private generateImageSearchUrl(keywords: string): string {
    try {
      if (!keywords || keywords.trim().length === 0) {
        // GÜNCELLEME: Fallback URL kullanılmayacak
        return '';
      }

      // Unsplash'tan high-quality görseller için URL oluştur
      const cleanKeywords = keywords
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '') // Özel karakterleri temizle
        .split(' ')
        .filter(word => word.length > 0)
        .slice(0, 3) // İlk 3 kelimeyi al
        .join(',');

      // Unsplash collection URL - akademik/araştırma görselleri
      return `https://unsplash.com/s/photos/${cleanKeywords}?utm_source=academic_search&utm_medium=referral`;
    } catch (error) {
      console.error('Error generating image search URL:', error);
      return ''; // GÜNCELLEME: Fallback URL yok
    }
  }

  // YENİ: Urgent CFP bildirimi kapatma
  dismissUrgentCFP(): void {
    this.isUrgentCFPDismissed = true;
  }

  // YENİ: Wiki CFP linkini açma
  openWikiCFPLink(cfp: CallForPaper): void {
    const wikiUrl = cfp.wikiCfpUrl || `http://www.wikicfp.com/cfp/servlet/tool.search?q=${encodeURIComponent(cfp.title)}`;
    window.open(wikiUrl, '_blank');
  }

  // YENİ: Event linkini açma  
  openEventLink(cfp: CallForPaper): void {
    window.open(cfp.websiteUrl, '_blank');
  }

  // YENİ: Deadline kontrolü
  isDeadlinePassed(deadline?: Date): boolean {
    const checkDate = deadline || new Date();
    return new Date() > checkDate;
  }

  // Tab Management
  switchTab(tabKey: string): void {
    this.currentTab = tabKey;
  }

  // GÜNCELLENEN: performSearch metodu - dinamik keyword'lerle
  performSearch(): void {
    this.isLoading = true;

    // Arama query'si varsa onu kullan, yoksa user keyword'lerini kullan
    const searchKeywords = this.searchFilters.query.trim() || this.userKeywords;

    console.log('Performing search with keywords:', searchKeywords);

    // GÜNCELLEME: Eğer arama keyword'leri yoksa arama yapma
    if (!searchKeywords || searchKeywords.trim() === '') {
      console.log('No search keywords available, skipping search');
      this.callForPapers = [];
      this.urgentCFPs = [];
      this.isLoading = false;
      return;
    }

    // MCP'den yeni arama yap
    this.mcpService.getCallForPapers(searchKeywords, 8)
      .then(mcpEvents => {
        const mappedEvents = mcpEvents.map(event =>
          this.mcpService.mapMCPEventToCallForPaper(event)
        );

        this.callForPapers = mappedEvents.filter(cfp => {
          return cfp && cfp.title && cfp.description && cfp.deadline && cfp.conferenceDate && cfp.location;
        });

        this.urgentCFPs = this.callForPapers
          .filter(cfp => this.getDaysUntilDeadline(cfp.deadline) <= 30)
          .sort((a, b) => a.deadline.getTime() - b.deadline.getTime());

        console.log(`Search completed: ${this.callForPapers.length} results`);
      })
      .catch(error => {
        console.error('Error performing search:', error);
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  // GÜNCELLENEN: getCurrentTabData metodu - sadece CFP ve placeholder'lar
  getCurrentTabData(): CallForPaper[] {
    switch (this.currentTab) {
      case 'call_for_papers':
        return this.callForPapers;
      case 'featured':
      case 'recent':
      case 'researchers':
      case 'interdisciplinary':
      case 'open_access':
      default:
        return []; // Diğer tablar için boş array
    }
  }

  // YENİ: CFP Helper Methods
  getDaysUntilDeadline(deadline: Date): number {
    const today = new Date();
    const diffTime = deadline.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getUrgencyClass(days: number): string {
    if (days <= 7) return 'urgent-red';
    if (days <= 30) return 'urgent-orange';
    return 'normal-blue';
  }

  getDifficultyBadgeClass(difficulty: string): string {
    const classes = {
      'Beginner': 'bg-green-100 text-green-800',
      'Intermediate': 'bg-yellow-100 text-yellow-800',
      'Advanced': 'bg-red-100 text-red-800'
    };
    return classes[difficulty as keyof typeof classes] || 'bg-gray-100 text-gray-800';
  }

  getLocationBadgeClass(locationType: string): string {
    const classes = {
      'online': 'bg-green-100 text-green-700',
      'hybrid': 'bg-blue-100 text-blue-700',
      'physical': 'bg-gray-100 text-gray-700'
    };
    return classes[locationType as keyof typeof classes] || 'bg-gray-100 text-gray-700';
  }

  // Content Actions - Sadece CFP için gerekli olanlar
  viewCFPDetails(cfp: CallForPaper): void {
    this.dialog.open(CfpDetailComponent, {
      data: cfp,
      width: 'calc(100vw - 4rem)',
      height: 'calc(100vh - 4rem)',
      maxWidth: 'calc(100vw - 4rem)',
      maxHeight: 'calc(100vh - 4rem)',
      disableClose: false,
    });
  }

  toggleCFPBookmark(cfp: CallForPaper): void {
    cfp.isBookmarked = !cfp.isBookmarked;
    // API call burada olacak
  }

  toggleCFPFavorite(cfp: CallForPaper): void {
    cfp.isFavorited = !cfp.isFavorited;
    // API call burada olacak
  }

  // Search and Filter Methods
  onSearch(): void {
    if (this.searchFilters.query.trim()) {
      this.addToRecentSearches(this.searchFilters.query);
      this.performSearch();
    }
  }

  onFilterChange(): void {
    this.performSearch();
  }

  toggleAdvancedSearch(): void {
    this.showAdvancedSearch = !this.showAdvancedSearch;
  }

  clearFilters(): void {
    this.searchFilters = {
      query: '',
      discipline: 'all',
      year: 'all',
      language: 'all',
      locationType: 'all',
      deadline: 'all',
      sortBy: 'relevance',
      sortOrder: 'desc'
    };
    this.performSearch();
  }

  private addToRecentSearches(query: string): void {
    // Gerçek uygulamada bu localStorage'a kaydedilir
    console.log('Search query added:', query);
  }

  // View Management
  toggleViewMode(): void {
    const modes: ('grid' | 'list' | 'detailed')[] = ['grid', 'list', 'detailed'];
    const currentIndex = modes.indexOf(this.viewMode);
    this.viewMode = modes[(currentIndex + 1) % modes.length];
  }

  // Utility Methods
  getCurrentTabLabel(): string {
    const currentTabObj = this.tabs.find(t => t.key === this.currentTab);
    return currentTabObj ? currentTabObj.label : '';
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  formatDate(date: Date | null | undefined): string {
    // Tarih değeri null, undefined veya geçersiz ise
    if (!date || isNaN(date.getTime())) {
      return 'Tarih belirtilmemiş';
    }

    try {
      return new Intl.DateTimeFormat('tr-TR', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }).format(date);
    } catch (error) {
      console.error('Date formatting error:', error, date);
      return 'Geçersiz tarih';
    }
  }

  getImpactColor(impact?: number): string {
    if (!impact) return 'default';
    if (impact >= 5) return 'excellent';
    if (impact >= 3) return 'good';
    if (impact >= 1) return 'fair';
    return 'low';
  }

  onKeyboardShortcut(event: KeyboardEvent): void {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'k':
          event.preventDefault();
          this.searchInput?.nativeElement?.focus();
          break;
        case 'f':
          event.preventDefault();
          this.toggleAdvancedSearch();
          break;
      }
    }
  }
}
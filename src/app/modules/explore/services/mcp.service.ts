import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, from, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { mcpConfig, cloudinaryConfig } from '@env/environment';

declare const require: any;

export interface MCPEvent {
  id: string;
  name: string;
  title: string;
  when: string;
  where: string;
  submission_deadline: string;
  notification_due: string;
  wikicfp_link: string;
  description: string;
  external_link: string;
  related_resources: Array<{
    name: string;
    title: string;
    url: string;
  }>;
}

export interface MCPScreenshot {
  status: number;
  message: string;
  url: string;
  public_id: string;
  dimensions: {
    width: number;
    height: number;
    fullPage: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class McpService {
  // Environment'tan config'leri al
  private apiKey = mcpConfig.apiKey;
  private profileId = mcpConfig.profileId;
  private baseUrl = mcpConfig.baseUrl;

  // Cloudinary config - environment'tan
  private cloudinaryConfig = {
    cloudinaryCloudName: cloudinaryConfig.cloudName,
    cloudinaryApiKey: cloudinaryConfig.apiKey,
    cloudinaryApiSecret: cloudinaryConfig.apiSecret
  };

  // YENİ: Mevcut kullanıcı keyword'leri (dinamik hale getirildi)
  private currentUserKeywords: string = '';

  constructor(private http: HttpClient) { }

  async getCallForPapers(keywords: string, limit: number = 3): Promise<MCPEvent[]> {
    try {
      // Keyword'leri saklayalım ki diğer metodlarda kullanalım
      this.currentUserKeywords = keywords;

      console.log('🔍 MCP Service - Trying to connect with keywords:', keywords);

      // GÜNCELLEME: Eğer keyword yoksa boş array döndür
      if (!keywords || keywords.trim() === '') {
        console.log('❌ No keywords provided, returning empty array');
        return [];
      }

      // Dynamic imports for browser compatibility
      const StreamableHTTPClientTransport = (await import('@modelcontextprotocol/sdk/client/streamableHttp.js' as any)).StreamableHTTPClientTransport;
      const Client = (await import('@modelcontextprotocol/sdk/client/index.js' as any)).Client;

      const url = `https://server.smithery.ai/@alperenkocyigit/call-for-papers-mcp/mcp?profile=${this.profileId}&api_key=${this.apiKey}`;
      console.log('🌐 MCP URL:', url);

      const transport = new StreamableHTTPClientTransport(url);
      const client = new Client({
        name: 'Call For Papers client',
        version: '1.0.0'
      });

      console.log('🔗 Attempting to connect to MCP...');
      await client.connect(transport);
      console.log('✅ Successfully connected to MCP');

      console.log('📞 Calling MCP tool with params:', { keywords, limit });
      const result = await client.callTool({
        name: 'get_events',
        arguments: {
          keywords: keywords,
          limit: limit
        }
      });

      console.log('📦 Raw MCP response:', result);

      await client.close();
      console.log('🔐 MCP connection closed');

      // Parse the result
      if (result.content && result.content[0] && result.content[0].text) {
        const parsedData = JSON.parse(result.content[0].text);
        console.log('📊 Parsed MCP data:', parsedData);

        const events = parsedData.events || [];
        console.log(`🎯 Found ${events.length} real events from MCP`);

        return events; // GÜNCELLEME: Fallback mantığı kaldırıldı, direkt MCP sonucunu döndür

      }

      console.warn('⚠️ MCP connected but returned no usable data');
      return []; // GÜNCELLEME: Boş array döndür, fallback YOK!

    } catch (error) {
      console.error('❌ MCP Connection/API Error:', error);
      console.error('📋 Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // GÜNCELLEME: MCP hata verirse boş array döndür, fallback YOK!
      console.warn('🚨 MCP failed completely - returning empty array');
      return [];
    }
  }

  // Screenshot alma - düzeltilmiş
  async takeScreenshot(url: string, width: number = 1280, height: number = 720): Promise<MCPScreenshot | null> {
    try {
      console.log('Taking screenshot for:', url);

      // GÜNCELLEME: Eğer URL yoksa null döndür
      if (!url || url.trim() === '') {
        console.log('❌ No URL provided for screenshot, returning null');
        return null;
      }

      const StreamableHTTPClientTransport = (await import('@modelcontextprotocol/sdk/client/streamableHttp.js' as any)).StreamableHTTPClientTransport;
      const Client = (await import('@modelcontextprotocol/sdk/client/index.js' as any)).Client;

      const serverUrl = this.createSmitheryUrl(
        'https://server.smithery.ai/@alperenkocyigit/html-to-image-mcp',
        {
          config: this.cloudinaryConfig,
          apiKey: this.apiKey
        }
      );

      const transport = new StreamableHTTPClientTransport(serverUrl);
      const client = new Client({
        name: 'HTML to Image client',
        version: '1.0.0'
      });

      await client.connect(transport);

      const result = await client.callTool({
        name: 'take_screenshot',
        arguments: {
          url: url,
          width: width,
          height: height,
          fullPage: false,
          // Cloudinary optimizasyonları
          quality: 'auto',
          format: 'auto'
        }
      });

      await client.close();

      // Parse the result
      if (result.content && result.content[0] && result.content[0].text) {
        const screenshotData = JSON.parse(result.content[0].text);
        console.log('Screenshot taken successfully:', screenshotData);
        return screenshotData;
      }

      console.log('No screenshot data received');
      return null;
    } catch (error) {
      console.error('Error taking screenshot:', error);
      return null;
    }
  }

  // GÜNCELLENEN: CFP verilerini Angular modeline dönüştürme - temizlenmiş
  mapMCPEventToCallForPaper(event: MCPEvent): any {
    // Parse dates - daha güvenli tarih parsing
    const parseEventDates = (whenString: string) => {
      try {
        // "Oct 27, 2025 - Oct 28, 2025" formatını parse et
        const parts = whenString.split(' - ');
        if (parts.length > 0) {
          const dateStr = parts[0].trim();
          const date = new Date(dateStr);
          return isNaN(date.getTime()) ? new Date(Date.now() + 180 * 24 * 60 * 60 * 1000) : date; // 6 ay sonra fallback
        }
        return new Date(Date.now() + 180 * 24 * 60 * 60 * 1000);
      } catch (error) {
        console.error('Error parsing event date:', whenString, error);
        return new Date(Date.now() + 180 * 24 * 60 * 60 * 1000);
      }
    };

    const parseDeadline = (deadlineString: string) => {
      try {
        // "Aug 1, 2028" formatını parse et
        const date = new Date(deadlineString);
        return isNaN(date.getTime()) ? new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) : date; // 3 ay sonra fallback
      } catch (error) {
        console.error('Error parsing deadline:', deadlineString, error);
        return new Date(Date.now() + 90 * 24 * 60 * 60 * 1000);
      }
    };

    const conferenceDate = parseEventDates(event.when);
    const submissionDeadline = parseDeadline(event.submission_deadline);
    const notificationDate = parseDeadline(event.notification_due);

    return {
      id: event.id,
      title: event.title || 'Conference Title',
      description: event.description ? (event.description.substring(0, 200) + '...') : 'Conference description not available',
      longDescription: this.splitIntoSentences(event.description || 'Conference description not available'),
      organizerName: event.name || 'Conference Organizer',
      organizerInstitution: 'Various Universities', // Bu bilgi API'den gelmiyor
      conferenceDate: conferenceDate,
      deadline: submissionDeadline,
      location: event.where || 'Location TBD',
      locationType: this.determineLocationType(event.where || ''),
      topics: this.extractTopics(event.description || '', this.currentUserKeywords),
      disciplines: [], // KALDIRMA: Gereksiz discipline mapping kaldırıldı
      submissionTypes: ['full_paper', 'short_paper', 'poster'],
      impactFactor: Math.round((Math.random() * 4 + 1) * 10) / 10, // 1.0-5.0 arası
      acceptanceRate: Math.floor(Math.random() * 30) + 25, // 25-55% arası
      difficulty: this.determineDifficulty(event.title || ''),
      isIndexed: true,
      indexingServices: ['Scopus', 'Web of Science'],
      registrationFee: Math.floor(Math.random() * 400) + 250, // 250-650 USD
      currency: 'USD',
      language: ['English'],
      expectedParticipants: Math.floor(Math.random() * 400) + 150, // 150-550 kişi
      keyDates: {
        abstractDeadline: new Date(submissionDeadline.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 gün önce
        fullPaperDeadline: submissionDeadline,
        notificationDate: notificationDate,
        cameraReadyDeadline: new Date(notificationDate.getTime() + 14 * 24 * 60 * 60 * 1000) // 14 gün sonra
      },
      websiteUrl: event.external_link || 'https://conference.org',
      wikiCfpUrl: event.wikicfp_link || null,
      cfpPdfUrl: null,
      contactEmail: '<EMAIL>',
      viewCount: Math.floor(Math.random() * 2000) + 500,
      isBookmarked: false,
      isFavorited: false,
      popularity: Math.floor(Math.random() * 100),
      createdAt: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000), // Son 60 gün içinde
      isOpenAccess: Math.random() > 0.4, // %60 açık erişim
      hasStudentDiscount: Math.random() > 0.3, // %70 öğrenci indirimi
      acceptsRemotePresentation: Math.random() > 0.4 // %60 uzaktan sunum kabul ediyor
    };
  }

  // TEMİZLENDİ: Gereksiz metotlar kaldırıldı
  // - getMedicalLocation(), getTechLocation(), getRandomLocation() metotları kaldırıldı
  // - generateDescription() metodu kaldırıldı
  // - Bu metotlar dummy veri üretiyordu, artık sadece MCP'den gelen gerçek veri kullanılıyor

  // Yardımcı metodlar - düzeltilmiş
  private createSmitheryUrl(baseUrl: string, options: any): string {
    const url = new URL(baseUrl + '/mcp');
    url.searchParams.set('profile', this.profileId);
    url.searchParams.set('api_key', options.apiKey);

    if (options.config) {
      try {
        const configString = JSON.stringify(options.config);
        const encodedConfig = btoa(configString);
        url.searchParams.set('config', encodedConfig);
      } catch (error) {
        console.error('Error encoding config:', error);
      }
    }

    return url.toString();
  }

  private splitIntoSentences(text: string): string[] {
    if (!text || text.trim().length === 0) {
      return ['Conference description will be provided soon.'];
    }

    try {
      // Metni cümlelere böl
      const sentences = text.match(/[^.!?]+[.!?]+/g) || [text];
      const paragraphs: string[] = [];
      let currentParagraph = '';

      sentences.forEach((sentence: string, index) => {
        currentParagraph += sentence.trim() + ' ';

        // Her 3-4 cümleden sonra yeni paragraf
        if ((index + 1) % 3 === 0 || index === sentences.length - 1) {
          if (currentParagraph.trim().length > 0) {
            paragraphs.push(currentParagraph.trim());
          }
          currentParagraph = '';
        }
      });

      return paragraphs.length > 0 ? paragraphs : [text];
    } catch (error) {
      console.error('Error splitting sentences:', error);
      return [text || 'Conference description not available'];
    }
  }

  private determineLocationType(location: string): 'online' | 'hybrid' | 'physical' {
    if (!location) return 'physical';

    const locationLower = location.toLowerCase();
    if (locationLower.includes('online') || locationLower.includes('virtual')) {
      return 'online';
    } else if (locationLower.includes('hybrid')) {
      return 'hybrid';
    }
    return 'physical';
  }

  // GÜNCELLENEN: extractTopics - artık tamamen dinamik
  private extractTopics(description: string, userKeywords: string = ''): string[] {
    if (!description) return ['Conference', 'Research'];

    const topics = [];
    const descLower = description.toLowerCase();
    const userKeywordArray = userKeywords.toLowerCase().split(' ').filter(k => k.length > 2);

    // Önce kullanıcının keyword'lerini kontrol et
    userKeywordArray.forEach(keyword => {
      if (descLower.includes(keyword)) {
        topics.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
      }
    });

    // Sonra description'dan genel akademik konuları çıkar (bias olmadan)
    const generalTopics = this.extractGeneralTopics(descLower);
    generalTopics.forEach(topic => {
      if (!topics.includes(topic)) {
        topics.push(topic);
      }
    });

    return topics.length > 0 ? topics : ['Academic Research', 'Conference'];
  }

  // GÜNCELLENEN: extractGeneralTopics - sadece kesfet_kategoriler.json'daki alanlar
  private extractGeneralTopics(descLower: string): string[] {
    const topicPatterns = [
      // Medical & Medicine - Ana kategoriler
      { patterns: ['medical', 'health', 'healthcare', 'medicine', 'clinical'], topic: 'Healthcare' },

      // Medical & Medicine - Subcategories
      { patterns: ['cardiology', 'cardiovascular', 'heart'], topic: 'Cardiology' },
      { patterns: ['neurology', 'neuroscience', 'brain', 'nervous'], topic: 'Neurosciences' },
      { patterns: ['emergency', 'critical care', 'trauma'], topic: 'Emergency Medicine' },
      { patterns: ['gynecology', 'obstetrics', 'pregnancy'], topic: 'Gynecology' },
      { patterns: ['internal medicine', 'general medicine'], topic: 'Internal Medicine' },

      // Dentistry - Ana kategori
      { patterns: ['dentistry', 'dental', 'oral'], topic: 'Dentistry' },

      // Dentistry - Subcategories
      { patterns: ['pediatric dentistry', 'pedodontics'], topic: 'Pediatric Dentistry' },
      { patterns: ['orthodontics', 'braces'], topic: 'Orthodontics' },
      { patterns: ['oral surgery', 'maxillofacial'], topic: 'Oral Surgery' },
      { patterns: ['periodontics', 'gum disease'], topic: 'Periodontics' },
      { patterns: ['endodontics', 'root canal'], topic: 'Endodontics' },

      // Health Sciences - Ana kategori
      { patterns: ['health sciences', 'health management'], topic: 'Health Sciences' },

      // Health Sciences - Subcategories
      { patterns: ['nursing', 'patient care'], topic: 'Nursing' },
      { patterns: ['public health', 'epidemiology'], topic: 'Public Health' },
      { patterns: ['rehabilitation', 'physiotherapy'], topic: 'Rehabilitation' },
      { patterns: ['nutrition', 'dietetics'], topic: 'Nutrition' },

      // Social Sciences - Ana kategori
      { patterns: ['social sciences'], topic: 'Social Sciences' },

      // Social Sciences - Subcategories
      { patterns: ['psychology', 'mental health'], topic: 'Psychology' },
      { patterns: ['social work', 'social services'], topic: 'Social Work' },
      { patterns: ['behavioral sciences', 'behavior'], topic: 'Behavioral Sciences' },

      // Educational Sciences - Ana kategori
      { patterns: ['educational sciences', 'education'], topic: 'Educational Sciences' },

      // Educational Sciences - Subcategories
      { patterns: ['medical education', 'medical training'], topic: 'Medical Education' },
      { patterns: ['nursing education'], topic: 'Nursing Education' },
      { patterns: ['health education'], topic: 'Health Education' },
      { patterns: ['educational technology', 'e-learning'], topic: 'Educational Technology' },

      // Genel akademik terimler
      { patterns: ['research', 'study', 'analysis'], topic: 'Research' },
      { patterns: ['conference', 'symposium', 'workshop'], topic: 'Academic Event' }
    ];

    const extractedTopics: string[] = [];

    topicPatterns.forEach(({ patterns, topic }) => {
      if (patterns.some(pattern => descLower.includes(pattern))) {
        extractedTopics.push(topic);
      }
    });

    return extractedTopics.slice(0, 5); // Maximum 5 topic
  }

  private determineDifficulty(title: string): 'Beginner' | 'Intermediate' | 'Advanced' {
    if (!title) return 'Intermediate';

    const titleLower = title.toLowerCase();
    if (titleLower.includes('advanced') || titleLower.includes('expert') || titleLower.includes('professional')) {
      return 'Advanced';
    } else if (titleLower.includes('beginner') || titleLower.includes('introduction') || titleLower.includes('basic')) {
      return 'Beginner';
    }
    return 'Intermediate';
  }
}
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { LandingComponent } from './pages/landing/landing.component';
import { ChangelogComponent } from './pages/changelog/changelog.component';
import { CareersComponent } from './pages/careers/careers.component';

const routes: Routes = [
  { 
    path: '', 
    children: [
      { path: '', component: LandingComponent },
      { path: 'changelog', component: ChangelogComponent },
      { path: 'careers', component: CareersComponent }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LandingRoutingModule {}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LandingRoutingModule } from './landing-routing.module';
import { LandingComponent } from './pages/landing/landing.component';
import { SharedModule } from '@app/shared/shared.module';
import { HeaderComponent } from './pages/header/header.component';
import { FeaturesComponent } from './pages/features/features.component';
import { HeroComponent } from './pages/hero/hero.component';
import { StatsComponent } from './pages/stats/stats.component';
import { HowItWorksComponent } from './pages/how-it-works/how-it-works.component';
import { UseCasesComponent } from './pages/use-cases/use-cases.component';
import { ComparisonComponent } from './pages/comparison/comparison.component';
import { TestimonialsComponent } from './pages/testimonials/testimonials.component';
import { PricingComponent } from './pages/pricing/pricing.component';
import { FaqComponent } from './pages/faq/faq.component';
import { CtaComponent } from './pages/cta/cta.component';
import { FooterComponent } from './pages/footer/footer.component';
import { CareersComponent } from './pages/careers/careers.component';
import { ChangelogComponent } from './pages/changelog/changelog.component';

@NgModule({
  declarations: [LandingComponent, HeaderComponent, FeaturesComponent, HeroComponent, StatsComponent, HowItWorksComponent, UseCasesComponent, ComparisonComponent, TestimonialsComponent, PricingComponent, FaqComponent, CtaComponent, FooterComponent, CareersComponent, ChangelogComponent],
  imports: [CommonModule, SharedModule,]
})
export class LandingModule { }

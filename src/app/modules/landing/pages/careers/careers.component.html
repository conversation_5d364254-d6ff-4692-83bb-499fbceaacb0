<div class="relative" *transloco="let t;read: 'careers'">
  <!-- Header Section -->
  <div class="pt-24 pb-16 bg-brand-blue-50">
    <div class="max-w-6xl px-4 mx-auto">
      <div class="text-center">
        <a routerLink="/" class="inline-flex items-center gap-1 mb-8 text-sm font-medium text-brand-blue-600 hover:text-brand-blue-800">
          <ng-icon name="heroArrowLeft" class="w-4 h-4"></ng-icon> {{ t('back_to_home') }}
        </a>
        <h1 class="mb-4 text-4xl font-bold text-brand-blue-950">{{ t('title') }}</h1>
        <p class="max-w-3xl mx-auto text-lg text-brand-blue-600">{{ t('subtitle') }}</p>
      </div>
    </div>
  </div>

  <!-- Jobs Section -->
  <div class="py-16">
    <div class="max-w-6xl px-4 mx-auto">
      <div class="mb-12 text-center">
        <h2 class="mb-4 text-2xl font-bold text-brand-blue-950">{{ t('open_positions') }}</h2>
        <p class="text-brand-blue-600">{{ t('positions_subtitle') }}</p>
      </div>

      <!-- Job Listings -->
      <div class="grid gap-8">
        <div *ngFor="let job of jobs" class="overflow-hidden transition-all bg-white border shadow-sm rounded-3xl border-brand-blue-100 hover:shadow-md">
          <!-- Job Header -->
          <div class="p-6 border-b border-brand-blue-100">
            <div class="flex flex-wrap items-center justify-between gap-4">
              <div>
                <h3 class="mb-1 text-xl font-semibold text-brand-blue-950">{{ job.title }}</h3>
                <div class="flex flex-wrap gap-3">
                  <span class="flex items-center gap-1 text-sm text-brand-blue-600">
                    <ng-icon name="tablerMapPin" class="w-4 h-4"></ng-icon> {{ job.location }}
                  </span>
                  <span class="flex items-center gap-1 text-sm text-brand-blue-600">
                    <ng-icon name="tablerClock" class="w-4 h-4"></ng-icon> {{ job.type }}
                  </span>
                </div>
              </div>

              <div class="flex gap-3">
                <button (click)="toggleJobDetails(job)" class="px-4 py-2 text-sm font-medium transition-colors border rounded-3xl text-brand-blue-600 border-brand-blue-200 hover:bg-brand-blue-50">
                  {{ job.showDetails ? t('hide_details') : t('view_details') }}
                </button>
                <button (click)="applyForJob(job.id)" class="px-4 py-2 text-sm font-medium text-white transition-all rounded-3xl bg-brand-green-500 hover:bg-brand-green-600 hover:scale-105">
                  {{ t('apply_now') }}
                </button>
              </div>
            </div>
          </div>

          <!-- Job Details (collapsible) -->
          <div *ngIf="job.showDetails" class="p-6 bg-white">
            <div class="mb-6">
              <h4 class="mb-2 text-lg font-medium text-brand-blue-950">{{ t('description') }}</h4>
              <p class="text-brand-blue-600">{{ job.description }}</p>
            </div>

            <div class="mb-6">
              <h4 class="mb-2 text-lg font-medium text-brand-blue-950">{{ t('responsibilities') }}</h4>
              <ul class="pl-5 space-y-1 list-disc text-brand-blue-600">
                <li *ngFor="let item of job.responsibilities">{{ item }}</li>
              </ul>
            </div>

            <div class="mb-6">
              <h4 class="mb-2 text-lg font-medium text-brand-blue-950">{{ t('requirements') }}</h4>
              <ul class="pl-5 space-y-1 list-disc text-brand-blue-600">
                <li *ngFor="let item of job.requirements">{{ item }}</li>
              </ul>
            </div>

            <div class="pt-4 mt-6 border-t border-brand-blue-100">
              <button (click)="applyForJob(job.id)" class="flex items-center gap-2 px-5 py-2 text-sm font-medium text-white transition-all rounded-3xl bg-brand-green-500 hover:bg-brand-green-600 hover:scale-105">
                <ng-icon name="tablerSend" class="w-4 h-4"></ng-icon>
                {{ t('apply_for_position') }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- No matching positions CTA -->
      <div class="p-8 mt-8 text-center bg-brand-blue-50 rounded-3xl">
        <h3 class="mb-3 text-xl font-semibold text-brand-blue-950">{{ t('no_match_title') }}</h3>
        <p class="mb-6 text-brand-blue-600">{{ t('no_match_description') }}</p>
        <a href="mailto:<EMAIL>" class="inline-flex items-center gap-2 px-6 py-3 font-medium text-white transition-all rounded-full bg-brand-blue-800 hover:bg-brand-blue-800/70 hover:scale-105">
          <ng-icon name="tablerMail" class="w-5 h-5"></ng-icon>
          {{ t('contact_us') }}
        </a>
      </div>
    </div>
  </div>
</div>

import { Component, OnInit } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';
import { Meta, Title } from '@angular/platform-browser';

interface JobListing {
    id: string;
    title: string;
    location: string;
    type: string;
    description: string;
    responsibilities: string[];
    requirements: string[];
    showDetails: boolean;
}

@Component({
    selector: 'app-careers',
    templateUrl: './careers.component.html',
})
export class CareersComponent implements OnInit {
    jobs: JobListing[] = [];


    constructor(
        private transloco: TranslocoService,
        private titleService: Title,
        private metaService: Meta
    ) {
        // Dil değişikliklerini dinle
        this.transloco.langChanges$.subscribe(() => {
            // <PERSON><PERSON> başlığını güncelle
            this.transloco.selectTranslate('careers.title').subscribe(title => {
                this.titleService.setTitle(`${title} | istabot`);
            });
        });
    }

    ngOnInit(): void {
        // <PERSON><PERSON> başlığını ve meta açıklamasını ayarla
        this.transloco.selectTranslate('careers.title').subscribe(title => {
            this.titleService.setTitle(`${title} | istabot`);
        });
        this.metaService.updateTag({ name: 'description', content: 'Join our team at istabot and help build the future of statistical analysis tools.' });

        // Çeviri dosyasından iş ilanlarını yükle
        this.addDefaultJobs();
    }

    /**
     * İş ilanlarını çeviri dosyalarından yükler
     * Çeviri dosyalarından iş ilanları yüklenemezse, varsayılan iş ilanlarını kullanır
     */
    private addDefaultJobs(): void {
        // Load job listings from translation files
        const lang = this.transloco.getActiveLang();
        this.transloco.selectTranslateObject('careers.jobs', {}, lang).subscribe({
            next: (jobsData: any) => {
                if (jobsData && Object.keys(jobsData).length > 0) {
                    // Clear existing jobs
                    this.jobs = [];

                    // Add jobs from translation file
                    Object.values(jobsData).forEach((job: any) => {
                        this.jobs.push({
                            ...job,
                            showDetails: false
                        });
                    });
                } else {
                    console.warn('No job listings found in translation files. Using defaults.');
                    this.addFallbackJobs();
                }
            },
            error: (error) => {
                console.error('Error loading job listings from translations:', error);
                this.addFallbackJobs();
            }
        });
    }

    /**
     * Fallback method to add default job listings if translations fail
     */
    private addFallbackJobs(): void {
        // Angular Frontend Developer - Fallback
        this.jobs.push({
            id: 'angular-frontend',
            title: 'Angular Frontend Developer',
            location: 'Atakum / Samsun',
            type: 'Full-time',
            description: 'Join our dynamic team as an Angular Frontend Developer and help build cutting-edge statistical analysis tools for researchers worldwide.',
            responsibilities: [
                'Develop responsive and interactive UIs using Angular',
                'Collaborate with UX designers to implement user-friendly interfaces',
                'Work with RESTful APIs and backend services',
                'Participate in code reviews and maintain code quality',
                'Implement new features and improve existing ones',
                'Optimize applications for maximum speed and scalability'
            ],
            requirements: [
                '2+ years of experience with Angular (version 10+)',
                'Strong knowledge of TypeScript, HTML5, and CSS3/SCSS',
                'Experience with responsive design and cross-browser compatibility',
                'Familiarity with state management solutions (NgRx, RxJS)',
                'Understanding of RESTful APIs and HTTP protocols',
                'Knowledge of version control systems (Git)',
                'Good problem-solving skills and attention to detail',
                'Ability to work in a team environment',
                'Nice-to-have: Experience with UI libraries like Angular Material, TailwindCSS',
                'Nice-to-have: Knowledge of testing frameworks (Jasmine, Karma)'
            ],
            showDetails: false
        });

        // Ruby on Rails Developer - Fallback
        this.jobs.push({
            id: 'ruby-on-rails',
            title: 'Ruby on Rails Developer',
            location: 'Atakum / Samsun',
            type: 'Full-time',
            description: 'We are seeking a Ruby on Rails developer to join our team, supporting ongoing projects through improving existing codebases and developing new products. You will contribute directly to the growth and enhancement of our platform, focusing specifically on API-only backend services.',
            responsibilities: [
                'Develop, test, and maintain robust, efficient, and scalable Ruby on Rails applications',
                'Collaborate effectively with the product team to implement new backend features',
                'Ensure high code quality through best practices, performance optimization, and code reviews'
            ],
            requirements: [
                'Minimum 1 year of experience in Ruby on Rails development',
                'Strong understanding of Ruby programming language and best practices',
                'Familiarity with Ruby Gems and related Rails libraries',
                'Solid foundation in Object-Oriented Programming (OOP), data structures, and algorithms',
                'Practical knowledge of PostgreSQL databases',
                'Good understanding of RESTful API principles and overall API architecture',
                'Experience with Git version control systems (e.g., Git, GitHub, GitLens, Gist)',
                'Ability to closely collaborate with product management and other development team members',
                'Commitment to writing maintainable, optimized, and high-quality code',
                'Nice-to-have: Good communication skills in English',
                'Nice-to-have: Experience with Docker, AWS, and CI/CD pipelines'
            ],
            showDetails: false
        });

        // R Junior Developer - Fallback
        this.jobs.push({
            id: 'r-junior',
            title: 'R Junior Developer',
            location: 'Atakum / Samsun',
            type: 'Full-time',
            description: 'We are looking for a dedicated R developer to join our data science team. The ideal candidate will play a critical role in developing statistical analysis algorithms and maintaining robust analytical solutions for our platform.',
            responsibilities: [
                'Collaborate effectively with the product team to implement new statistical features',
                'Develop, test, and maintain statistical analysis modules using R',
                'Ensure code quality through rigorous performance optimization and best coding practices'
            ],
            requirements: [
                'Bachelor\'s degree in Statistics, Mathematics, Computer Science, or related field',
                'Proficiency in R programming language and statistical analysis',
                'Understanding of statistical concepts and methodologies',
                'Experience with data manipulation and visualization in R',
                'Familiarity with R packages for statistical analysis (e.g., dplyr, ggplot2, tidyr)',
                'Basic knowledge of version control systems (Git)',
                'Ability to work in a collaborative team environment',
                'Strong problem-solving skills and attention to detail',
                'Nice-to-have: Experience with R Markdown or Shiny',
                'Nice-to-have: Knowledge of other programming languages (Python, SQL)'
            ],
            showDetails: false
        });
    }


    toggleJobDetails(job: JobListing): void {
        job.showDetails = !job.showDetails;
    }

    applyForJob(jobId: string): void {
        // Logic to handle job application - could redirect to a form or open email client
        window.open(`mailto:<EMAIL>?subject=Application for ${jobId}`, '_blank');
    }
}

<div class="min-h-screen bg-gray-50" *transloco="let t; read:'landing.changelog'">
  <!-- Navbar -->
  <nav class="fixed top-0 left-0 right-0 z-50 border-b bg-white/40 backdrop-blur-sm border-gray-200/20">
    <div class="flex items-center justify-between p-4">
      <div class="flex items-center gap-4">
        <!-- <PERSON><PERSON> dön butonu -->
        <a routerLink="/" class="flex items-center gap-2 text-brand-blue-600 hover:text-brand-blue-800">
          <ng-icon name="heroArrowLeft" class="w-5 h-5"></ng-icon>
          <span>{{ t('back') }}</span>
        </a>
        
        <!-- Logo -->
        <a routerLink="/" class="flex items-center gap-2">
          <img src="/assets/icons/istabot-logo.svg" alt="Logo" class="w-8 h-8" />
          <span class="text-xl font-semibold text-brand-blue-950">istabot</span>
        </a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="max-w-4xl px-4 pt-32 pb-16 mx-auto">
    <div class="mb-12 text-center">
      <h1 class="mb-4 text-4xl font-bold text-brand-blue-950">{{ t('title') }}</h1>
      <p class="text-lg text-brand-blue-600">{{ t('description') }}</p>
    </div>

    <!-- Release Timeline -->
    <div class="space-y-12">
      <div *ngFor="let release of releases" class="relative">
        <!-- Version Badge -->
        <div class="flex items-center mb-4 space-x-3">
          <span class="px-3 py-1 text-sm font-medium rounded-full bg-brand-blue-200 text-brand-blue-800">
            v{{ release.version }}
          </span>
          <span class="text-sm text-brand-blue-600">
            {{ release.date | date:(t('date_format') || 'longDate'):undefined:t('date_locale') }}
          </span>
        </div>

        <!-- Changes -->
        <div class="pl-4 space-y-6">
          <div *ngFor="let change of release.changes" class="space-y-3">
            <!-- Change Type -->
            <div [ngSwitch]="change.type" class="flex items-center space-x-2">
              <ng-icon *ngSwitchCase="'added'" name="heroPlus" class="w-5 h-5 text-green-600"></ng-icon>
              <ng-icon *ngSwitchCase="'improved'" name="heroArrowUp" class="w-5 h-5 text-blue-600"></ng-icon>
              <ng-icon *ngSwitchCase="'fixed'" name="heroBug" class="w-5 h-5 text-amber-600"></ng-icon>
              <span class="text-lg font-medium text-brand-blue-950">{{ t('types.' + change.type) }}</span>
            </div>

            <!-- Change Items -->
            <ul class="pl-6 space-y-4">
              <li *ngFor="let itemKey of change.items">
                <!-- Ana başlık -->
                <h4 class="font-medium text-brand-blue-800">
                  {{ t('releases.' + release.version + '.' + change.type + '.' + itemKey + '.title') }}
                </h4>
                <!-- Alt maddeler -->
                <ul class="pl-6 mt-2 space-y-2">
                  <li *ngFor="let subitem of t('releases.' + release.version + '.' + change.type + '.' + itemKey + '.items')" 
                      class="text-brand-blue-600">
                    • {{ subitem }}
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<section id="comparison" class="relative px-4 py-16 pt-32 overflow-hidden comparison-section bg-brand-blue-50" *transloco="let t; read:'landing'">

    <!-- Content Container -->
    <div class="relative z-10 max-w-6xl mx-auto">
        <!-- Section Header -->
        <div class="mb-10 text-center">
            <h2 class="mb-2 text-4xl font-bold text-brand-blue-600">{{ t('compare.title') }}</h2>
            <div class="w-32 h-1 mx-auto mb-6 rounded-full bg-brand-green-500"></div>
            <div class="max-w-2xl mx-auto">
                <p class="text-lg text-gray-700">
                    {{ t('compare.subtitle') }}
                </p>
            </div>
        </div>
        <!-- Feature Comparison Sections -->
        <div class="grid grid-cols-1 gap-8 mb-12 md:grid-cols-3">
            <!-- Feature 1: Time Savings -->
            <div
                class="p-6 pt-4 transition-all duration-300 bg-white shadow-sm rounded-3xl hover:shadow-md feature-card">
                <div class="w-full h-2 mb-3 overflow-hidden bg-gray-200 rounded-full">
                    <div class="h-full rounded-full bg-brand-blue-400 progress-bar-1" style="width: 85%;"></div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                            <ng-icon name="lucideClock" class="text-2xl text-brand-blue-500"></ng-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="mb-2 text-lg font-semibold text-gray-900">{{ t('compare.features.time_saving.title') }}</h3>
                        <p class="text-gray-600">{{ t('compare.features.time_saving.description') }}</p>


                    </div>
                </div>
            </div>

            <!-- Feature 2: Ease of Use -->
            <div
                class="p-6 pt-4 transition-all duration-300 bg-white shadow-sm rounded-3xl hover:shadow-md feature-card">
                <div class="w-full h-2 mb-3 overflow-hidden bg-gray-200 rounded-full">
                    <div class="h-full rounded-full bg-brand-green-500 progress-bar-2" style="width: 90%;">
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                            <ng-icon name="lucideMousePointer" class="text-2xl text-brand-blue-500"></ng-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="mb-2 text-lg font-semibold text-gray-900">{{ t('compare.features.ease_of_use.title') }}</h3>
                        <p class="text-gray-600">{{ t('compare.features.ease_of_use.description') }}</p>
                    </div>
                </div>
            </div>

            <!-- Feature 3: Professional Results -->
            <div
                class="p-6 pt-4 transition-all duration-300 bg-white shadow-sm rounded-3xl hover:shadow-md feature-card">
                <div class="w-full h-2 mb-3 overflow-hidden bg-gray-200 rounded-full">
                    <div class="h-full rounded-full bg-brand-blue-600 progress-bar-3" style="width: 95%;"></div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                            <ng-icon name="lucideFileText" class="text-2xl text-brand-blue-500"></ng-icon>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="mb-2 text-lg font-semibold text-gray-900">{{ t('compare.features.professional_results.title') }}</h3>
                        <p class="text-gray-600">{{ t('compare.features.professional_results.description') }}</p>


                    </div>
                </div>
            </div>
        </div>
        <!-- Comparison Table -->
        <div class="overflow-hidden shadow-sm rounded-3xl">
            <table class="min-w-full text-sm divide-y divide-gray-200">
                <thead class="text-white bg-brand-blue-600">
                    <tr>
                        <th class="px-6 py-4 text-left">{{ t('compare.table.feature') }}</th>
                        <th class="px-6 py-4 text-center">
                            <div class="flex flex-col items-center">
                                <img src="assets/icons/istabot-white.svg" alt="istabot Logo" class="w-8 h-8 mb-2">
                                <span>istabot</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-center">{{ t('compare.table.traditional') }}<br />(SPSS, Python, MINITAB)</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Row 1 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 font-medium">{{ t('compare.table.rows.analysis_time.title') }}</td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-brand-green-500">{{ t('compare.table.rows.analysis_time.istabot') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.analysis_time.istabot_detail') }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-gray-600">{{ t('compare.table.rows.analysis_time.traditional') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.analysis_time.traditional_detail') }}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Row 2 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 font-medium">{{ t('compare.table.rows.technical_knowledge.title') }}</td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-brand-green-500">{{ t('compare.table.rows.technical_knowledge.istabot') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.technical_knowledge.istabot_detail') }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-gray-600">{{ t('compare.table.rows.technical_knowledge.traditional') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.technical_knowledge.traditional_detail') }}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Row 3 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 font-medium">{{ t('compare.table.rows.user_interface.title') }}</td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-brand-green-500">{{ t('compare.table.rows.user_interface.istabot') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.user_interface.istabot_detail') }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-gray-600">{{ t('compare.table.rows.user_interface.traditional') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.user_interface.traditional_detail') }}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Row 4 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 font-medium">{{ t('compare.table.rows.report_format.title') }}</td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-brand-green-500">{{ t('compare.table.rows.report_format.istabot') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.report_format.istabot_detail') }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-gray-600">{{ t('compare.table.rows.report_format.traditional') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.report_format.traditional_detail') }}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Row 5 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 font-medium">{{ t('compare.table.rows.error_risk.title') }}</td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-brand-green-500">{{ t('compare.table.rows.error_risk.istabot') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.error_risk.istabot_detail') }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-gray-600">{{ t('compare.table.rows.error_risk.traditional') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.error_risk.traditional_detail') }}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Row 6 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 font-medium">{{ t('compare.table.rows.cost.title') }}</td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-brand-green-500">{{ t('compare.table.rows.cost.istabot') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.cost.istabot_detail') }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col items-center">
                                <span class="font-medium text-gray-600">{{ t('compare.table.rows.cost.traditional') }}</span>
                                <span class="text-xs text-gray-500">{{ t('compare.table.rows.cost.traditional_detail') }}</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>




        <!-- Cost Comparison Calculator -->

            <div class="p-8 mt-12 bg-white shadow-sm rounded-3xl">
                <h3 class="mb-6 text-2xl font-bold text-center text-brand-blue-600">{{ t('compare.cost_comparison.title') }}</h3>

                <div class="grid grid-cols-1 gap-8 md:grid-cols-3">
                    <!-- Calculator Form -->
                    <div class="p-6 rounded-3xl bg-gray-50 md:col-span-1">
                        <h4 class="mb-4 text-lg font-semibold text-gray-900">{{ t('compare.cost_comparison.calculator.title') }}</h4>

                        <div class="space-y-4">
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-700">{{ t('compare.cost_comparison.calculator.monthly_analyses') }}</label>
                                <input type="number" id="analysisCount"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-3xl focus:ring-2 focus:ring-brand-blue-500 focus:border-brand-blue-500"
                                    value="5" min="1" max="100">
                            </div>

                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-700">{{ t('compare.cost_comparison.calculator.hours_per_analysis') }}</label>
                                <input type="number" id="hoursPerAnalysis"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-3xl focus:ring-2 focus:ring-brand-blue-500 focus:border-brand-blue-500"
                                    value="4" min="1" max="24">
                            </div>

                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-700">{{ t('compare.cost_comparison.calculator.hourly_rate') }}</label>
                                <input type="number" id="hourlyRate"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-3xl focus:ring-2 focus:ring-brand-blue-500 focus:border-brand-blue-500"
                                    value="150" min="10">
                            </div>

                            <button id="calculateButton"
                                class="w-full px-4 py-2 text-white rounded-3xl bg-brand-blue-500 hover:bg-brand-blue-600">
                                {{ t('compare.cost_comparison.calculator.calculate_button') }}
                            </button>
                        </div>
                    </div>

                    <!-- Comparison Table -->
                    <div class="p-6 md:col-span-2">
                        <div class="overflow-hidden shadow-sm rounded-3xl">
                            <table class="min-w-full text-sm divide-y divide-gray-200 comparison-result-table">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-3 text-left bg-gray-100">{{ t('compare.cost_comparison.results.metric') }}</th>
                                        <th class="px-4 py-3 text-center bg-brand-blue-100 text-brand-blue-600">{{ t('compare.cost_comparison.results.istabot') }}</th>
                                        <th class="px-4 py-3 text-center text-gray-700 bg-gray-100">{{ t('compare.cost_comparison.results.competitor') }}</th>
                                        <th class="px-4 py-3 text-center bg-brand-green-100 text-brand-green-600">{{ t('compare.cost_comparison.results.difference') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Time per analysis -->
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-3 font-medium text-gray-700">{{ t('compare.cost_comparison.results.analysis_time') }}</td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-brand-blue-600 istabot-time">0.6 Saat</span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-gray-700 competitor-time">4 Saat</span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-brand-green-500 time-diff">3.4 Saat</span>
                                        </td>
                                    </tr>
                                    <!-- Cost per analysis -->
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-3 font-medium text-gray-700">{{ t('compare.cost_comparison.results.cost_per_analysis') }}</td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-brand-blue-600 istabot-cost">90 ₺</span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-gray-700 competitor-cost">600 ₺</span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-brand-green-500 cost-diff">510 ₺</span>
                                        </td>
                                    </tr>
                                    <!-- Total monthly cost -->
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-3 font-medium text-gray-700">{{ t('compare.cost_comparison.results.monthly_cost') }}</td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-brand-blue-600 istabot-total">450 ₺</span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-gray-700 competitor-total">3.000 ₺</span>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-brand-green-500 total-diff">2.550 ₺</span>
                                        </td>
                                    </tr>
                                    <!-- Efficiency -->
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-3 font-medium text-gray-700">{{ t('compare.cost_comparison.results.efficiency_increase') }}</td>
                                        <td class="px-4 py-3 text-center" colspan="2">
                                            <div class="w-full h-2 max-w-xs mx-auto overflow-hidden bg-gray-200 rounded-full">
                                                <div class="h-full rounded-full bg-brand-blue-500 efficiency-bar" style="width: 85%;"></div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <span class="font-medium text-brand-green-500 efficiency-value">%85</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p class="mt-4 text-sm italic text-gray-500">{{ t('compare.cost_comparison.note') }}</p>
                    </div>
                </div>
            </div>

    </div>
</section>
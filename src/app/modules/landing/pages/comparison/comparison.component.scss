// Comparison Section Styles

// Table styling
table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;

    thead th {
      position: relative;
      transition: background-color 0.3s ease;

      &:first-child {
        border-top-left-radius: 0.5rem;
      }

      &:last-child {
        border-top-right-radius: 0.5rem;
      }
    }

    tbody tr {
      transition: background-color 0.3s ease;

      &:last-child {
        td:first-child {
          border-bottom-left-radius: 0.5rem;
        }

        td:last-child {
          border-bottom-right-radius: 0.5rem;
        }
      }
    }
  }

  // Feature card styles
  .feature-card {
    transition: all 0.3s ease;
    height: 100%;

    h3 {
      transition: color 0.3s ease;
    }

    .flex-shrink-0 div {
      transition: all 0.3s ease;
      will-change: transform, background-color;
    }

    // Progress bar animation
    .progress-bar-1, .progress-bar-2, .progress-bar-3 {
      transition: width 1.5s ease-out;
      will-change: width;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 10px;
        background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5));
        filter: blur(2px);
      }
    }
  }

  // Calculator styles
  input[type="number"] {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;

    // Remove up/down arrows from number inputs
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Firefox and standard */
    -moz-appearance: textfield;
    appearance: textfield;
  }

  // Comparison result table styles
  .comparison-result-table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;

    th {
      font-weight: 600;
      position: relative;
      transition: background-color 0.3s ease;

      &:first-child {
        border-top-left-radius: 0.5rem;
      }

      &:last-child {
        border-top-right-radius: 0.5rem;
      }
    }

    tbody tr {
      transition: all 0.3s ease;

      &:last-child {
        td:first-child {
          border-bottom-left-radius: 0.5rem;
        }

        td:last-child {
          border-bottom-right-radius: 0.5rem;
        }
      }

      &:hover {
        background-color: rgba(239, 246, 255, 0.6);

        .font-medium {
          transform: scale(1.05);
          transition: transform 0.2s ease;
        }
      }
    }

    td, th {
      transition: all 0.2s ease;
    }

    .efficiency-bar {
      transition: width 1.5s ease-out;
      will-change: width;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 10px;
        background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5));
        filter: blur(2px);
      }
    }
  }

  button {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%, -50%);
      transform-origin: 50% 50%;
    }

    &:focus:not(:active)::after {
      animation: ripple 1s ease-out;
    }
  }

  @keyframes ripple {
    0% {
      transform: scale(0, 0);
      opacity: 0.5;
    }
    20% {
      transform: scale(25, 25);
      opacity: 0.3;
    }
    100% {
      opacity: 0;
      transform: scale(40, 40);
    }
  }

  // Gradient animations
  .bg-gradient-to-r {
    background-size: 200% auto;
    animation: gradient-shift 3s ease infinite;
  }

  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

// Media queries for responsive design
@media (max-width: 768px) {
    .feature-card {
      margin-bottom: 1.5rem;
    }

    table {
      display: block;
      overflow-x: auto;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;

      th, td {
        min-width: 140px;
      }

      th:first-child, td:first-child {
        position: sticky;
        left: 0;
        background-color: white;
        z-index: 1;
        box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
      }

      thead th:first-child {
        background-color: #2563EB; // Brand blue
      }
    }

    .p-1.mt-12 {
      .grid {
        grid-template-columns: 1fr;
      }

      .md\:col-span-1, .md\:col-span-2 {
        grid-column: span 1 / span 1;
      }

      .comparison-result-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;

        th, td {
          min-width: 100px;
        }

        th:first-child, td:first-child {
          position: sticky;
          left: 0;
          background-color: white;
          z-index: 1;
          box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
        }

        thead th:first-child {
          background-color: #f3f4f6; // gray-100
        }
      }
    }
  }

  // Animation for table rows on hover
  tbody tr {
    &:hover {
      td:first-child {
        color: #2563EB; // brand-blue-600
      }

      .font-medium.text-brand-green-500 {
        transform: scale(1.05);
        transition: transform 0.2s ease;
      }
    }
  }

  // Enhanced focus states for accessibility
  input:focus,
  button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }

  // Optional: Support for reduced motion preferences
  @media (prefers-reduced-motion: reduce) {
    .progress-bar-1,
    .progress-bar-2,
    .progress-bar-3,
    .bg-gradient-to-r {
      transition: none !important;
      animation: none !important;
    }

    button::after {
      animation: none !important;
    }
  }

  // Enhanced table row hover effects
  tbody tr {
    td {
      transition: all 0.2s ease;
    }

    &:hover td {
      background-color: rgba(239, 246, 255, 0.6); // Light blue background on hover
    }
  }

  // Animated calculator icon pulse effect
  .p-1.mt-12 .flex-shrink-0 .rounded-full {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      inset: -4px;
      border-radius: 9999px;
      background: inherit;
      opacity: 0.3;
      z-index: -1;
      animation: pulse 2s infinite;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.15);
      opacity: 0.1;
    }
    100% {
      transform: scale(1);
      opacity: 0.3;
    }
  }
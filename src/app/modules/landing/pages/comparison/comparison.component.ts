import { Component, OnInit, AfterViewInit, OnD<PERSON>roy } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-comparison',
  templateUrl: './comparison.component.html',
  styleUrls: ['./comparison.component.scss']
})
export class ComparisonComponent implements OnInit, AfterViewInit, OnDestroy {

  constructor() {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initTableAnimation();
      this.initProgressBarsAnimation();
      this.initFeatureCardAnimation();
      this.initCalculatorAnimation();
      this.setupCalculator();
    }, 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }

  private initTableAnimation(): void {
    const table = document.querySelector('.comparison-section table');
    const tableRows = document.querySelectorAll('.comparison-section tbody tr');

    if (!table || !tableRows.length) return;

    // Initial state
    gsap.set(table, { y: 30, opacity: 0 });
    gsap.set(tableRows, { opacity: 0, y: 20 });

    // Table entrance animation
    ScrollTrigger.create({
      trigger: table,
      start: "top bottom-=100",
      onEnter: () => {
        gsap.to(table, {
          y: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out"
        });

        // Staggered rows entrance
        gsap.to(tableRows, {
          opacity: 1,
          y: 0,
          duration: 0.5,
          stagger: 0.1,
          ease: "power2.out",
          delay: 0.3
        });
      },
      once: true
    });
  }

  private initProgressBarsAnimation(): void {
    const progressBars = [
      document.querySelector('.progress-bar-1'),
      document.querySelector('.progress-bar-2'),
      document.querySelector('.progress-bar-3')
    ].filter(bar => bar);

    if (!progressBars.length) return;

    // Initial state - width 0%
    gsap.set(progressBars, { width: '0%' });

    // Animate progress bars on scroll
    progressBars.forEach((bar, index) => {
      const targetWidth = bar.getAttribute('style')?.match(/width:\s?(\d+)%/) || ['0', '85'];
      ScrollTrigger.create({
        trigger: bar,
        start: "top bottom-=50",
        onEnter: () => {
          gsap.to(bar, {
            width:  '100%',
            duration: 1.5,
            ease: "power2.out",
            delay: index * 0.2
          });
        },
        once: true
      });
    });
  }

  private initFeatureCardAnimation(): void {
    const featureCards = document.querySelectorAll('.feature-card');

    if (!featureCards.length) return;

    // Initial state
    gsap.set(featureCards, { y: 30, opacity: 0 });

    // Staggered entrance animation
    ScrollTrigger.create({
      trigger: featureCards[0],
      start: "top bottom-=100",
      onEnter: () => {
        gsap.to(featureCards, {
          y: 0,
          opacity: 1,
          duration: 0.7,
          stagger: 0.15,
          ease: "power2.out"
        });
      },
      once: true
    });

    // Add hover animation for feature cards
    featureCards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        const icon = card.querySelector('.flex-shrink-0 div');
        const heading = card.querySelector('h3');

        if (icon && heading) {
          gsap.to(icon, {
            backgroundColor: '#DBEAFE', // brand-blue-200
            scale: 1.1,
            duration: 0.3
          });

          gsap.to(heading, {
            color: '#2563EB', // brand-blue-600
            duration: 0.3
          });
        }
      });

      card.addEventListener('mouseleave', () => {
        const icon = card.querySelector('.flex-shrink-0 div');
        const heading = card.querySelector('h3');

        if (icon && heading) {
          gsap.to(icon, {
            backgroundColor: '#EBF8FF', // brand-blue-100
            scale: 1,
            duration: 0.3
          });

          gsap.to(heading, {
            color: '#111827', // gray-900
            duration: 0.3
          });
        }
      });
    });
  }

  private initCalculatorAnimation(): void {
    const calculator = document.querySelector('.p-1.mt-12');

    if (!calculator) return;

    // Initial state
    gsap.set(calculator, { y: 30, opacity: 0 });

    // Entrance animation
    ScrollTrigger.create({
      trigger: calculator,
      start: "top bottom-=100",
      onEnter: () => {
        gsap.to(calculator, {
          y: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out"
        });
      },
      once: true
    });
  }

  private setupCalculator(): void {
    const calcButton = document.getElementById('calculateButton');
    const analysisCountInput = document.getElementById('analysisCount') as HTMLInputElement;
    const hoursPerAnalysisInput = document.getElementById('hoursPerAnalysis') as HTMLInputElement;
    const hourlyRateInput = document.getElementById('hourlyRate') as HTMLInputElement;

    // Table result elements
    const istabotTimeEl = document.querySelector('.istabot-time');
    const competitorTimeEl = document.querySelector('.competitor-time');
    const timeDiffEl = document.querySelector('.time-diff');

    const istabotCostEl = document.querySelector('.istabot-cost');
    const competitorCostEl = document.querySelector('.competitor-cost');
    const costDiffEl = document.querySelector('.cost-diff');

    const istabotTotalEl = document.querySelector('.istabot-total');
    const competitorTotalEl = document.querySelector('.competitor-total');
    const totalDiffEl = document.querySelector('.total-diff');

    const efficiencyBarEl = document.querySelector('.efficiency-bar');
    const efficiencyValueEl = document.querySelector('.efficiency-value');

    if (!calcButton || !analysisCountInput || !hoursPerAnalysisInput || !hourlyRateInput) return;
    if (!istabotTimeEl || !competitorTimeEl || !timeDiffEl) return;
    if (!istabotCostEl || !competitorCostEl || !costDiffEl) return;
    if (!istabotTotalEl || !competitorTotalEl || !totalDiffEl) return;
    if (!efficiencyBarEl || !efficiencyValueEl) return;

    // Add click event to calculate button
    calcButton.addEventListener('click', () => {
      // Get input values
      const analysisCount = parseFloat(analysisCountInput.value) || 5;
      const hoursPerAnalysis = parseFloat(hoursPerAnalysisInput.value) || 4;
      const hourlyRate = parseFloat(hourlyRateInput.value) || 150;

      // Constants
      const efficiencyPercentage = 85; // İstabot is 85% more efficient
      const istabotTimeMultiplier = 0.15; // İstabot takes 15% of the time (85% reduction)

      // Calculate time values
      const competitorTime = hoursPerAnalysis;
      const istabotTime = competitorTime * istabotTimeMultiplier;
      const timeDiff = competitorTime - istabotTime;

      // Calculate cost per analysis
      const competitorCost = competitorTime * hourlyRate;
      const istabotCost = istabotTime * hourlyRate;
      const costDiff = competitorCost - istabotCost;

      // Calculate monthly totals
      const competitorTotal = competitorCost * analysisCount;
      const istabotTotal = istabotCost * analysisCount;
      const totalDiff = competitorTotal - istabotTotal;

      // Format numbers for display
      const formatNumber = (num: number) => {
        return num.toLocaleString('tr-TR', {minimumFractionDigits: 0, maximumFractionDigits: 1});
      };

      // Current values for animation
      const currentIstabotTime = parseFloat(istabotTimeEl.textContent.replace(/[^\d.,]/g, '').replace(',', '.')) || 0.6;
      const currentCompetitorTime = parseFloat(competitorTimeEl.textContent.replace(/[^\d.,]/g, '').replace(',', '.')) || 4;
      const currentTimeDiff = parseFloat(timeDiffEl.textContent.replace(/[^\d.,]/g, '').replace(',', '.')) || 3.4;

      const currentIstabotCost = parseFloat(istabotCostEl.textContent.replace(/[^\d.,₺]/g, '')) || 90;
      const currentCompetitorCost = parseFloat(competitorCostEl.textContent.replace(/[^\d.,₺]/g, '')) || 600;
      const currentCostDiff = parseFloat(costDiffEl.textContent.replace(/[^\d.,₺]/g, '')) || 510;

      const currentIstabotTotal = parseFloat(istabotTotalEl.textContent.replace(/[^\d.,₺]/g, '')) || 450;
      const currentCompetitorTotal = parseFloat(competitorTotalEl.textContent.replace(/[^\d.,₺]/g, '')) || 3000;
      const currentTotalDiff = parseFloat(totalDiffEl.textContent.replace(/[^\d.,₺]/g, '')) || 2550;

      // Animate time values
      gsap.to({ value: currentIstabotTime }, {
        value: istabotTime,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          istabotTimeEl.textContent = formatNumber(this.targets()[0].value) + ' Saat';
        }
      });

      gsap.to({ value: currentCompetitorTime }, {
        value: competitorTime,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          competitorTimeEl.textContent = formatNumber(this.targets()[0].value) + ' Saat';
        }
      });

      gsap.to({ value: currentTimeDiff }, {
        value: timeDiff,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          timeDiffEl.textContent = formatNumber(this.targets()[0].value) + ' Saat';
        }
      });

      // Animate cost values
      gsap.to({ value: currentIstabotCost }, {
        value: istabotCost,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          istabotCostEl.textContent = formatNumber(this.targets()[0].value) + ' ₺';
        }
      });

      gsap.to({ value: currentCompetitorCost }, {
        value: competitorCost,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          competitorCostEl.textContent = formatNumber(this.targets()[0].value) + ' ₺';
        }
      });

      gsap.to({ value: currentCostDiff }, {
        value: costDiff,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          costDiffEl.textContent = formatNumber(this.targets()[0].value) + ' ₺';
        }
      });

      // Animate total values
      gsap.to({ value: currentIstabotTotal }, {
        value: istabotTotal,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          istabotTotalEl.textContent = formatNumber(this.targets()[0].value) + ' ₺';
        }
      });

      gsap.to({ value: currentCompetitorTotal }, {
        value: competitorTotal,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          competitorTotalEl.textContent = formatNumber(this.targets()[0].value) + ' ₺';
        }
      });

      gsap.to({ value: currentTotalDiff }, {
        value: totalDiff,
        duration: 1,
        ease: "power2.out",
        onUpdate: function() {
          totalDiffEl.textContent = formatNumber(this.targets()[0].value) + ' ₺';
        }
      });

      // Update efficiency bar
      gsap.to(efficiencyBarEl, {
        width: efficiencyPercentage + '%',
        duration: 1,
        ease: "power2.out"
      });

      // Efficiency value is fixed at 85%
      efficiencyValueEl.textContent = '%' + efficiencyPercentage;

      // Button press animation
      gsap.to(calcButton, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1
      });
    });

    // Add input event to dynamically update results
    const inputs = [analysisCountInput, hoursPerAnalysisInput, hourlyRateInput];
    inputs.forEach(input => {
      input.addEventListener('input', () => {
        // Validate input values
        const value = parseFloat(input.value);
        const min = parseFloat(input.min);
        const max = parseFloat(input.max);

        if (value < min) input.value = min.toString();
        if (value > max) input.value = max.toString();
      });
    });

    // Initial calculation
    calcButton.click();
  }
}
<section class="relative px-4 py-16 overflow-hidden cta-section bg-brand-blue-600">
    <!-- Decorative Elements -->
    <div class="absolute inset-0 z-0 overflow-hidden pointer-events-none">
        <!-- Animated Circles Background -->
        <div class="absolute top-0 left-0 w-full h-full">
            <div class="absolute w-64 h-64 rounded-full opacity-10 circle-1 bg-brand-blue-400 -top-20 -left-20"></div>
            <div class="absolute rounded-full w-96 h-96 opacity-10 circle-2 bg-brand-green-500 -bottom-40 -right-40"></div>
            <div class="absolute w-48 h-48 rounded-full opacity-10 circle-3 bg-brand-blue-300 top-1/2 left-1/4"></div>
        </div>
        
        <!-- Pattern Grid Background -->
        <div class="absolute inset-0 z-0 opacity-5 grid-bg"></div>
    </div>
    
    <!-- Content Container -->
    <div class="relative z-10 max-w-6xl mx-auto text-center text-white">
        <!-- Main CTA Content -->
        <div class="max-w-3xl mx-auto cta-content">
            <h2 class="mb-4 text-4xl font-bold md:text-5xl cta-heading">İstatistik Analizlerinizi Hızlandırın</h2>
            <p class="mb-10 text-xl text-blue-100 cta-description">
                Akademik araştırmalarınızı ve tezlerinizi istabot'un güçlü analiz araçlarıyla bir üst seviyeye taşıyın
            </p>
            
            <!-- Video Preview (Optional) -->
            <div class="relative w-full max-w-2xl mx-auto mb-12 overflow-hidden shadow-2xl rounded-xl video-preview">
                <div class="aspect-w-16 aspect-h-9 bg-brand-blue-900">
                    <img src="https://picsum.photos/seed/istabotdemo/1200/675" alt="İstabot Demo" class="object-cover w-full h-full rounded-xl">
                    <div class="absolute inset-0 flex items-center justify-center group">
                        <button class="flex items-center justify-center w-20 h-20 transition-all duration-300 rounded-full play-button bg-white/20 backdrop-blur-sm group-hover:bg-white/40">
                            <div class="flex items-center justify-center w-16 h-16 rounded-full bg-brand-blue-500">
                                <ng-icon name="lucidePlay" class="text-3xl text-white"></ng-icon>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-6 cta-buttons">
                <a href="#register" class="flex items-center justify-center w-full px-8 py-4 text-lg font-medium bg-white rounded-lg sm:w-auto primary-button text-brand-blue-600 hover:bg-gray-100">
                    <span>Hemen Başlayın</span>
                    <ng-icon name="lucideArrowRight" class="ml-2"></ng-icon>
                </a>
                <a href="#demo" class="flex items-center justify-center w-full px-8 py-4 text-lg font-medium transition-all duration-300 bg-transparent border rounded-lg sm:w-auto demo-button border-white/30 hover:bg-white/10">
                    <ng-icon name="lucidePlay" class="mr-2"></ng-icon>
                    <span>Demo İzleyin</span>
                </a>
            </div>
        </div>
        
        <!-- Features List -->
        <div class="grid grid-cols-1 gap-6 mt-16 md:grid-cols-3 features-list">
            <!-- Feature 1 -->
            <div class="p-6 transition-all duration-300 bg-white/10 backdrop-blur-sm rounded-xl feature-item hover:bg-white/15">
                <div class="flex items-center mb-4">
                    <div class="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-brand-blue-500/30">
                        <ng-icon name="lucideZap" class="text-2xl text-white"></ng-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-white">Hızlı Analiz</h3>
                </div>
                <p class="text-blue-100">
                    Karmaşık istatistiksel analizleri saniyeler içinde tamamlayın, saatler değil
                </p>
            </div>
            
            <!-- Feature 2 -->
            <div class="p-6 transition-all duration-300 bg-white/10 backdrop-blur-sm rounded-xl feature-item hover:bg-white/15">
                <div class="flex items-center mb-4">
                    <div class="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-brand-blue-500/30">
                        <ng-icon name="lucideFileText" class="text-2xl text-white"></ng-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-white">APA Raporları</h3>
                </div>
                <p class="text-blue-100">
                    Akademik standartlara uygun APA formatında profesyonel raporlar
                </p>
            </div>
            
            <!-- Feature 3 -->
            <div class="p-6 transition-all duration-300 bg-white/10 backdrop-blur-sm rounded-xl feature-item hover:bg-white/15">
                <div class="flex items-center mb-4">
                    <div class="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-brand-blue-500/30">
                        <ng-icon name="lucideShield" class="text-2xl text-white"></ng-icon>
                    </div>
                    <h3 class="text-xl font-semibold text-white">Güvenilir Sonuçlar</h3>
                </div>
                <p class="text-blue-100">
                    R tabanlı algoritmalarla %99.8 doğruluk oranında analiz sonuçları
                </p>
            </div>
        </div>


    </div>
</section>
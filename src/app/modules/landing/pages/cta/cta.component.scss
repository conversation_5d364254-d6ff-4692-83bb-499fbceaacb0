// CTA Section Styles

// Background grid
.grid-bg {
    background-image: 
      linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 40px 40px;
    transform: perspective(1000px) rotateX(60deg) scale(1.2);
    transform-origin: center top;
    will-change: background-position;
  }
  
  // Heading animation
  .cta-heading {
    position: relative;
    z-index: 1;
    
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -10px;
      transform: translateX(-50%);
      width: 80px;
      height: 2px;
      background: linear-gradient(to right, rgba(255, 255, 255, 0.3), #19C480, rgba(255, 255, 255, 0.3));
      border-radius: 2px;
    }
  }
  
  // Button styles
  .primary-button {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(59, 130, 246, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%, -50%);
      transform-origin: 50% 50%;
    }
    
    &:focus:not(:active)::after {
      animation: ripple 1s ease-out;
    }
  }
  
  .demo-button {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transform: translateX(-100%);
    }
    
    &:hover::before {
      animation: shimmer 1.5s infinite;
    }
  }
  
  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }
  
  @keyframes ripple {
    0% {
      transform: scale(0, 0);
      opacity: 0.5;
    }
    20% {
      transform: scale(25, 25);
      opacity: 0.3;
    }
    100% {
      opacity: 0;
      transform: scale(40, 40);
    }
  }
  
  // Video preview styles
  .video-preview {
    transition: all 0.3s ease;
    position: relative;
    will-change: transform;
    
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 0.75rem;
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
      pointer-events: none;
    }
    
    img {
      transition: all 0.5s ease;
    }
    
    &:hover img {
      transform: scale(1.02);
    }
    
    .play-button {
      will-change: transform;
      z-index: 10;
      
      div {
        transition: all 0.3s ease;
      }
    }
  }
  
  // Set aspect ratio for video
  .aspect-w-16.aspect-h-9 {
    position: relative;
    padding-bottom: 56.25%; /* 9/16 = 0.5625 */
    
    > * {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  
  // Feature items
  .feature-item {
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    &:hover {
      transform: translateY(-5px);
      
      ng-icon {
        transform: scale(1.1);
      }
    }
    
    ng-icon {
      transition: transform 0.3s ease;
    }
  }
  
  // Testimonial quote
  .testimonial-quote {
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    &::before {
      content: '';
      position: absolute;
      top: 10px;
      left: 10px;
      width: 40px;
      height: 40px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 0 1-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 0 1-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z' fill='rgba(255,255,255,0.12)'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      opacity: 0.5;
      transform: scale(2);
      pointer-events: none;
    }
    
    blockquote {
      position: relative;
      z-index: 1;
    }
  }
  
  // Guarantee badge
  .guarantee {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-3px);
      
      ng-icon {
        animation: pulse 1.5s infinite;
      }
    }
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }
  
  // Media queries for responsive design
  @media (max-width: 768px) {
    .cta-heading {
      font-size: 2rem;
    }
    
    .features-list {
      grid-template-columns: 1fr;
    }
    
    .feature-item {
      margin-bottom: 1rem;
    }
    
    .video-preview {
      max-width: 100%;
    }
  }
  
  // Accessibility enhancements
  a:focus, 
  button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
  }
  
  // Optional: Support for reduced motion preferences
  @media (prefers-reduced-motion: reduce) {
    .cta-heading::after, 
    .demo-button::before,
    .primary-button::after,
    .feature-item,
    .guarantee,
    ng-icon {
      transition: none !important;
      animation: none !important;
    }
  }
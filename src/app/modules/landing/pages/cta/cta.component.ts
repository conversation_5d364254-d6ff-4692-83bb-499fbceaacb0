import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-cta',
  templateUrl: './cta.component.html',
  styleUrls: ['./cta.component.scss']
})
export class CtaComponent implements OnInit, AfterViewInit, OnDestroy {
  
  constructor() {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initAnimations();
      this.initBackgroundEffects();
      this.setupVideoPreview();
    }, 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }
  
  private initAnimations(): void {
    // Heading and description animation
    const heading = document.querySelector('.cta-heading');
    const description = document.querySelector('.cta-description');
    
    if (heading && description) {
      gsap.fromTo([heading, description],
        { y: 50, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 1,
          stagger: 0.2,
          ease: "power2.out",
          scrollTrigger: {
            trigger: '.cta-content',
            start: "top bottom-=100"
          }
        }
      );
    }
    
    // Buttons animation
    const buttons = document.querySelectorAll('.cta-buttons a');
    
    if (buttons.length) {
      gsap.fromTo(buttons,
        { y: 30, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 0.7,
          stagger: 0.15,
          ease: "back.out(1.7)",
          delay: 0.5,
          scrollTrigger: {
            trigger: '.cta-buttons',
            start: "top bottom-=50"
          }
        }
      );
      
      // Add hover animations
      buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
          gsap.to(button, {
            y: -5,
            duration: 0.3,
            ease: "power2.out"
          });
        });
        
        button.addEventListener('mouseleave', () => {
          gsap.to(button, {
            y: 0,
            duration: 0.3,
            ease: "power2.out"
          });
        });
      });
    }
    
    // Feature items animation
    const featureItems = document.querySelectorAll('.feature-item');
    
    if (featureItems.length) {
      gsap.fromTo(featureItems,
        { y: 50, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 0.8,
          stagger: 0.15,
          ease: "power2.out",
          scrollTrigger: {
            trigger: '.features-list',
            start: "top bottom-=100"
          }
        }
      );
    }
    
    // Testimonial animation
    const testimonial = document.querySelector('.testimonial-quote');
    
    if (testimonial) {
      gsap.fromTo(testimonial,
        { y: 30, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 0.8,
          ease: "power2.out",
          scrollTrigger: {
            trigger: testimonial,
            start: "top bottom-=100"
          }
        }
      );
    }
    
    // Guarantee animation
    const guarantee = document.querySelector('.guarantee');
    
    if (guarantee) {
      gsap.fromTo(guarantee,
        { y: 20, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 0.6,
          ease: "power2.out",
          delay: 0.3,
          scrollTrigger: {
            trigger: guarantee,
            start: "top bottom-=50"
          }
        }
      );
    }
  }
  
  private initBackgroundEffects(): void {
    // Animated circles
    const circles = [
      document.querySelector('.circle-1'),
      document.querySelector('.circle-2'),
      document.querySelector('.circle-3')
    ].filter(circle => circle);
    
    circles.forEach((circle, index) => {
      // Random floating animation
      gsap.to(circle, {
        x: `random(-50, 50)`,
        y: `random(-50, 50)`,
        rotation: `random(-10, 10)`,
        duration: 15 + index * 5,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true,
        delay: index * 2
      });
    });
    
    // Grid pattern subtle movement
    const gridBg = document.querySelector('.grid-bg');
    
    if (gridBg) {
      gsap.to(gridBg, {
        backgroundPosition: '50px 50px',
        duration: 60,
        ease: 'linear',
        repeat: -1
      });
    }
  }
  
  private setupVideoPreview(): void {
    const videoPreview = document.querySelector('.video-preview');
    const playButton = document.querySelector('.play-button');
    
    if (videoPreview && playButton) {
      // Scale animation on scroll
      ScrollTrigger.create({
        trigger: videoPreview,
        start: "top bottom",
        end: "bottom top",
        onUpdate: (self) => {
          const scale = 1 + ((1 - Math.abs(self.progress * 2 - 1)) * 0.05);
          gsap.set(videoPreview, { scale: scale });
        }
      });
      
      // Play button hover effect
      playButton.addEventListener('mouseenter', () => {
        gsap.to(playButton.querySelector('div'), {
          scale: 1.1,
          duration: 0.3,
          ease: "power2.out"
        });
      });
      
      playButton.addEventListener('mouseleave', () => {
        gsap.to(playButton.querySelector('div'), {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      });
      
      // Play button click effect
      playButton.addEventListener('click', () => {
        // Click animation
        gsap.to(playButton.querySelector('div'), {
          scale: 0.9,
          duration: 0.1,
          yoyo: true,
          repeat: 1,
          ease: "power2.inOut"
        });
      });
    }
  }
}
<section id="faq" class="relative px-4 py-16 pt-32 overflow-hidden bg-brand-blue-50 faq-section"
    *transloco="let t; read:'landing'">

    <!-- Content Container -->
    <div class="relative z-10 max-w-4xl mx-auto">
        <!-- Section Header -->
        <div class="mb-16 text-center">
            <h2 class="mb-2 text-4xl font-bold text-brand-blue-600">{{ t('faq.title') }}</h2>
            <div class="w-32 h-1 mx-auto mb-6 rounded-full bg-brand-green-500"></div>
            <div class="max-w-2xl mx-auto">
                <p class="text-lg text-gray-700">
                    {{ t('faq.subtitle') }}
                </p>
            </div>
        </div>

        <!-- FAQ Accordion -->
        <div class="space-y-6">
            <!-- Category: Platform Hakkında -->
            <div class="faq-category">
                <h3 class="mb-4 text-xl font-bold text-gray-900">{{ t('faq.categories.about_platform') }}</h3>

                <!-- FAQ Item 1 -->
                <div class="mb-4 overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem1>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.what_is_istabot.question') }}
                        </h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.what_is_istabot.answer') }}</p>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="mb-4 overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem2>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.difference.question') }}</h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.difference.answer_intro') }}</p>
                        <ul class="mt-3 ml-5 space-y-2 list-disc">
                            <li *ngFor="let item of t('faq.questions.difference.answer_items')">{{ item }}</li>
                        </ul>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50" #faqItem3>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.supported_analyses.question')
                            }}</h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.supported_analyses.answer_intro') }}</p>
                        <ul class="mt-3 ml-5 space-y-2 list-disc">
                            <li *ngFor="let item of t('faq.questions.supported_analyses.answer_items')">{{ item }}</li>
                        </ul>
                        <p class="mt-3">{{ t('faq.questions.supported_analyses.answer_conclusion') }}</p>
                    </div>
                </div>
            </div>

            <!-- Category: Kullanım -->
            <div class="faq-category">
                <h3 class="mb-4 text-xl font-bold text-gray-900">{{ t('faq.categories.usage') }}</h3>

                <!-- FAQ Item 4 -->
                <div class="mb-4 overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem4>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.upload_data.question') }}</h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.upload_data.answer_intro') }}</p>
                        <ol class="mt-3 ml-5 space-y-2 list-decimal">
                            <li *ngFor="let step of t('faq.questions.upload_data.answer_steps')">{{ step }}</li>
                        </ol>
                        <p class="mt-3">{{ t('faq.questions.upload_data.answer_conclusion') }}</p>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="mb-4 overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem5>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.perform_analysis.question') }}
                        </h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.perform_analysis.answer_intro') }}</p>
                        <ol class="mt-3 ml-5 space-y-2 list-decimal">
                            <li *ngFor="let step of t('faq.questions.perform_analysis.answer_steps')">{{ step }}</li>
                        </ol>
                        <p class="mt-3">{{ t('faq.questions.perform_analysis.answer_conclusion') }}</p>
                    </div>
                </div>

                <!-- FAQ Item 6 -->
                <div class="overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50" #faqItem6>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.download_reports.question') }}
                        </h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.download_reports.answer_intro') }}</p>
                        <ol class="mt-3 ml-5 space-y-2 list-decimal">
                            <li *ngFor="let step of t('faq.questions.download_reports.answer_steps')">{{ step }}</li>
                        </ol>
                        <p class="mt-3">{{ t('faq.questions.download_reports.answer_conclusion') }}</p>
                    </div>
                </div>
            </div>

            <!-- Category: Fiyatlandırma ve Ödemeler -->
            <div class="faq-category">
                <h3 class="mb-4 text-xl font-bold text-gray-900">{{ t('faq.categories.pricing') }}</h3>

                <!-- FAQ Item 7 -->
                <div class="mb-4 overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem7>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.credit_system.question') }}
                        </h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.credit_system.answer_intro') }}</p>
                        <ul class="mt-3 ml-5 space-y-2 list-disc">
                            <li *ngFor="let item of t('faq.questions.credit_system.answer_items')">{{ item }}</li>
                        </ul>
                    </div>
                </div>

                <!-- FAQ Item 8 -->
                <div class="mb-4 overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem8>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.payment_methods.question') }}
                        </h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.payment_methods.answer_intro') }}</p>
                        <ul class="mt-3 ml-5 space-y-2 list-disc">
                            <li *ngFor="let item of t('faq.questions.payment_methods.answer_items')">{{ item }}</li>
                        </ul>
                        <p class="mt-3">{{ t('faq.questions.payment_methods.answer_conclusion') }}</p>
                    </div>
                </div>

            </div>

            <!-- Category: Teknik Sorular -->
            <div class="faq-category">
                <h3 class="mb-4 text-xl font-bold text-gray-900">{{ t('faq.categories.technical') }}</h3>

                <!-- FAQ Item 10 -->
                <div class="mb-4 overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem10>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.data_security.question') }}
                        </h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.data_security.answer_intro') }}</p>
                        <ul class="mt-3 ml-5 space-y-2 list-disc">
                            <li *ngFor="let item of t('faq.questions.data_security.answer_items')">{{ item }}</li>
                        </ul>
                        <p class="mt-3">{{ t('faq.questions.data_security.answer_conclusion') }}</p>
                    </div>
                </div>


                <!-- FAQ Item 11 -->
                <div class="overflow-hidden border border-gray-100 shadow-sm rounded-3xl faq-item bg-gray-50"
                    #faqItem12>
                    <button
                        class="flex items-center justify-between w-full p-6 text-left faq-question focus:outline-none">
                        <h4 class="text-lg font-medium text-gray-900">{{ t('faq.questions.mobile_usage.question') }}
                        </h4>
                        <div
                            class="flex items-center justify-center w-8 h-8 transition-transform duration-300 rounded-full bg-brand-blue-100 faq-icon-wrapper">
                            <ng-icon name="lucideChevronDown"
                                class="transition-transform duration-300 text-brand-blue-500 faq-icon"></ng-icon>
                        </div>
                    </button>

                    <div class="hidden px-6 pb-6 text-gray-600 faq-answer">
                        <p>{{ t('faq.questions.mobile_usage.answer_intro') }}</p>
                        <ul class="mt-3 ml-5 space-y-2 list-disc">
                            <li *ngFor="let item of t('faq.questions.mobile_usage.answer_items')">{{ item }}</li>
                        </ul>
                        <p class="mt-3">{{ t('faq.questions.mobile_usage.answer_conclusion') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="p-8 mt-16 text-center bg-brand-blue-50 rounded-3xl">
            <h3 class="mb-3 text-2xl font-bold text-gray-900">{{ t('faq.contact.title') }}</h3>
            <p class="mb-6 text-gray-600">
                {{ t('faq.contact.subtitle') }}
            </p>
            <div class="flex items-center justify-center gap-4 ">
                <a href="https://wa.me/905386150444" class="primary-blue-button">
                    <ng-icon name="lucideMessageSquare" class="mr-2"></ng-icon>
                    <span>{{ t('faq.contact.whatsapp') }}</span>
                </a>
            </div>
        </div>
    </div>
</section>
// FAQ Section Styles

// Container styling
.faq-section {
    position: relative;
    isolation: isolate;
  }
  
  // Category styling
  .faq-category {
    margin-bottom: 2rem;
    
    h3 {
      color: #1F2937; // gray-900
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -8px;
        width: 40px;
        height: 2px;
        background: linear-gradient(to right, #3B82F6, #19C480);
        border-radius: 1px;
      }
    }
  }
  
  // Accordion item styling
  .faq-item {
    transition: all 0.3s ease;
    overflow: hidden;
    
    &.active {
      background-color: #F3F4F6 !important; // gray-100
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
      
      .faq-question h4 {
        color: #2563EB; // brand-blue-600
      }
    }
    
    .faq-question {
      cursor: pointer;
      transition: background-color 0.3s ease;
      border-radius: 0.5rem;
      
      h4 {
        transition: color 0.3s ease;
      }
      
      &:hover h4 {
        color: #2563EB; // brand-blue-600
      }
    }
    
    .faq-answer {
      overflow: hidden;
      will-change: height, opacity;
      
      a {
        color: #2563EB; // brand-blue-600
        transition: all 0.2s ease;
        
        &:hover {
          color: #1D4ED8; // brand-blue-700
          text-decoration: underline;
        }
      }
      
      ul, ol {
        li {
          margin-bottom: 0.5rem;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .faq-icon-wrapper {
      transition: background-color 0.3s ease;
    }
    
    .faq-icon {
      transition: transform 0.3s ease;
      will-change: transform;
    }
  }
  
  // Contact section styling
  .p-8.mt-16 {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(to right, #3B82F6, #19C480);
      border-radius: 3px 3px 0 0;
    }
    
    a {
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-3px);
      }
      
      &:first-of-type {
        position: relative;
        overflow: hidden;
        
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 5px;
          height: 5px;
          background: rgba(255, 255, 255, 0.5);
          opacity: 0;
          border-radius: 100%;
          transform: scale(1, 1) translate(-50%, -50%);
          transform-origin: 50% 50%;
        }
        
        &:focus:not(:active)::after {
          animation: ripple 1s ease-out;
        }
      }
    }
  }
  
  @keyframes ripple {
    0% {
      transform: scale(0, 0);
      opacity: 0.5;
    }
    20% {
      transform: scale(25, 25);
      opacity: 0.3;
    }
    100% {
      opacity: 0;
      transform: scale(40, 40);
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .faq-category {
      h3 {
        font-size: 1.25rem;
      }
    }
    
    .faq-item {
      .faq-question h4 {
        font-size: 1rem;
      }
    }
    
    .p-8.mt-16 {
      padding: 1.5rem;
      
      h3 {
        font-size: 1.5rem;
      }
      
      .sm\:flex-row {
        flex-direction: column;
        
        a {
          width: 100%;
        }
      }
    }
  }
  
  // Accessibility enhancements
  .faq-question:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
  
  a:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
  
  // Optional: Support for reduced motion preferences
  @media (prefers-reduced-motion: reduce) {
    .faq-item,
    .faq-question,
    .faq-icon,
    .faq-answer,
    a {
      transition: none !important;
      animation: none !important;
    }
  }
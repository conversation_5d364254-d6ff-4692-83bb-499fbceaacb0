import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy, ViewChildren, QueryList } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-faq',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.scss']
})
export class FaqComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('faqItem1') faqItem1!: ElementRef;
  @ViewChild('faqItem2') faqItem2!: ElementRef;
  @ViewChild('faqItem3') faqItem3!: ElementRef;
  @ViewChild('faqItem4') faqItem4!: ElementRef;
  @ViewChild('faqItem5') faqItem5!: ElementRef;
  @ViewChild('faqItem6') faqItem6!: ElementRef;
  @ViewChild('faqItem7') faqItem7!: ElementRef;
  @ViewChild('faqItem8') faqItem8!: ElementRef;
  @ViewChild('faqItem9') faqItem9!: ElementRef;
  @ViewChild('faqItem10') faqItem10!: ElementRef;
  @ViewChild('faqItem11') faqItem11!: ElementRef;
  @ViewChild('faqItem12') faqItem12!: ElementRef;
  
  constructor() {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initFaqAccordion();
      this.initCategoryAnimations();
      this.initContactSectionAnimation();
      this.setupKeyboardNavigation();
    }, 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }
  
  private initFaqAccordion(): void {
    const faqItems = [
      this.faqItem1?.nativeElement,
      this.faqItem2?.nativeElement,
      this.faqItem3?.nativeElement,
      this.faqItem4?.nativeElement,
      this.faqItem5?.nativeElement,
      this.faqItem6?.nativeElement,
      this.faqItem7?.nativeElement,
      this.faqItem8?.nativeElement,
      this.faqItem9?.nativeElement,
      this.faqItem10?.nativeElement,
      this.faqItem11?.nativeElement,
      this.faqItem12?.nativeElement
    ].filter(item => item);
    
    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      const answer = item.querySelector('.faq-answer');
      const iconWrapper = item.querySelector('.faq-icon-wrapper');
      const icon = item.querySelector('.faq-icon');
      
      if (question && answer && icon && iconWrapper) {
        // Set initial state
        gsap.set(answer, { height: 0, opacity: 0, display: 'none' });
        
        // Add click event
        question.addEventListener('click', () => {
          const isOpen = answer.classList.contains('active');
          
          // Close all other answers
          faqItems.forEach(otherItem => {
            const otherAnswer = otherItem.querySelector('.faq-answer');
            const otherIcon = otherItem.querySelector('.faq-icon');
            
            if (otherAnswer && otherAnswer !== answer && otherAnswer.classList.contains('active')) {
              otherAnswer.classList.remove('active');
              gsap.to(otherAnswer, {
                height: 0,
                opacity: 0,
                duration: 0.3,
                ease: "power2.out",
                onComplete: () => {
                  gsap.set(otherAnswer, { display: 'none' });
                }
              });
              
              // Rotate icon back
              gsap.to(otherIcon, {
                rotation: 0,
                duration: 0.3,
                ease: "power2.out"
              });
              
              // Remove active state from parent
              otherItem.classList.remove('active');
            }
          });
          
          // Toggle current answer
          if (!isOpen) {
            answer.classList.add('active');
            item.classList.add('active');
            
            // First set display to block but keep height 0
            gsap.set(answer, { display: 'block', height: 'auto' });
            
            // Get the height of the content
            const height = answer.offsetHeight;
            
            // Set height back to 0 and then animate to the full height
            gsap.fromTo(answer,
              { height: 0, opacity: 0 },
              { 
                height: height, 
                opacity: 1, 
                duration: 0.3, 
                ease: "power2.out"
              }
            );
            
            // Rotate icon and change background color
            gsap.to(icon, {
              rotation: 180,
              duration: 0.3,
              ease: "power2.out"
            });
            
            gsap.to(iconWrapper, {
              backgroundColor: '#DBEAFE', // brand-blue-200
              duration: 0.3
            });
            
            // Subtle bounce animation for the question
            gsap.to(question, {
              backgroundColor: 'rgba(243, 244, 246, 0.7)', // light gray
              duration: 0.3
            });
          } else {
            answer.classList.remove('active');
            item.classList.remove('active');
            
            // Animate to height 0
            gsap.to(answer, {
              height: 0,
              opacity: 0,
              duration: 0.3,
              ease: "power2.out",
              onComplete: () => {
                gsap.set(answer, { display: 'none' });
              }
            });
            
            // Rotate icon back and reset background
            gsap.to(icon, {
              rotation: 0,
              duration: 0.3,
              ease: "power2.out"
            });
            
            gsap.to(iconWrapper, {
              backgroundColor: '#EBF8FF', // brand-blue-100
              duration: 0.3
            });
            
            // Reset question background
            gsap.to(question, {
              backgroundColor: 'transparent',
              duration: 0.3
            });
          }
        });
      }
    });
    
    // Optional: Open first FAQ item by default
    if (faqItems.length > 0) {
      const firstItem = faqItems[0];
      const firstQuestion = firstItem.querySelector('.faq-question');
      
      if (firstQuestion) {
        // Trigger a click on the first question
        setTimeout(() => {
          firstQuestion.click();
        }, 500);
      }
    }
}

private initCategoryAnimations(): void {
  const categories = document.querySelectorAll('.faq-category');
  
  categories.forEach((category, index) => {
    const heading = category.querySelector('h3');
    const items = category.querySelectorAll('.faq-item');
    
    if (heading && items.length) {
      // Set initial state
      gsap.set(heading, { y: 20, opacity: 0 });
      gsap.set(items, { y: 30, opacity: 0 });
      
      // Create scrolltrigger for each category
      ScrollTrigger.create({
        trigger: category,
        start: "top bottom-=100",
        onEnter: () => {
          // Animate heading
          gsap.to(heading, {
            y: 0,
            opacity: 1,
            duration: 0.5,
            delay: index * 0.1,
            ease: "power2.out"
          });
          
          // Animate items with stagger
          gsap.to(items, {
            y: 0,
            opacity: 1,
            duration: 0.5,
            stagger: 0.1,
            delay: index * 0.1 + 0.2,
            ease: "power2.out"
          });
        },
        once: true
      });
      
      // Add hover effect to FAQ items
      items.forEach(item => {
        item.addEventListener('mouseenter', () => {
          if (!item.classList.contains('active')) {
            gsap.to(item, {
              backgroundColor: '#F9FAFB', // gray-50 with more contrast
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              duration: 0.3
            });
          }
        });
        
        item.addEventListener('mouseleave', () => {
          if (!item.classList.contains('active')) {
            gsap.to(item, {
              backgroundColor: '#F9FAFB', // back to gray-50
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
              duration: 0.3
            });
          }
        });
      });
    }
  });
}

private initContactSectionAnimation(): void {
  const contactSection = document.querySelector('.p-8.mt-16');
  
  if (contactSection) {
    // Set initial state
    gsap.set(contactSection, { y: 30, opacity: 0 });
    
    // Create scrolltrigger
    ScrollTrigger.create({
      trigger: contactSection,
      start: "top bottom-=50",
      onEnter: () => {
        gsap.to(contactSection, {
          y: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out"
        });
      },
      once: true
    });
    
    // Add hover effect to buttons
    const buttons = contactSection.querySelectorAll('a');
    
    buttons.forEach(button => {
      button.addEventListener('mouseenter', () => {
        gsap.to(button, {
          y: -5,
          duration: 0.3,
          ease: "power2.out"
        });
      });
      
      button.addEventListener('mouseleave', () => {
        gsap.to(button, {
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    });
  }
}

private setupKeyboardNavigation(): void {
  const faqItems = document.querySelectorAll('.faq-item');
  
  faqItems.forEach(item => {
    const question = item.querySelector('.faq-question');
    
    if (question) {
      // Add keyboard support for accessibility
      question.addEventListener('keydown', (e: KeyboardEvent) => {
        // Enter or Space key
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          (question as HTMLElement).click();
        }
      });
    }
  });
}
}
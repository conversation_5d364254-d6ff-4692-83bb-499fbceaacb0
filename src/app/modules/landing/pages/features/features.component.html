<div id="features" class="relative py-12 pt-32 overflow-hidden bg-gray-50 features-section scroll-mt-24"
    *transloco="let t; read:'landing'">
    <div class="grid features-grid-first gap-4 mx-auto max-w-[calc(100dvw-(100dvw*10/100))] mb-4">
        <!-- Üst Satır -->
        <!-- Kart 1 -->
        <div
            class="relative overflow-hidden rounded-3xl h-96 bg-[radial-gradient(230%_100%_at_bottom,rgba(109,165,255,1)_10%,rgba(235,242,255,1)_30%)] shadow">
            <div class="relative z-10 flex flex-col h-full ">
                <h2 class="p-8 pb-0 mb-2 text-2xl font-bold ">{{ t('features.main_card.title') }}</h2>
                <p class="px-8 mb-4">{{ t('features.main_card.description') }}</p>


                <div #iconsContainer class="relative w-full h-48">
                    <!-- Sol taraf ikonları -->
                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="left: 0%; top: 10%;">
                        <img [src]="'assets/icons/multi.svg'"
                            class="size-5 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#0047DB]  to-[#3B82F6]  rounded-full icon-bubble size-12"
                        style="left: 15%; top: 0%;">
                        <ng-icon name="lucideSquarePen" class="text-base text-white"></ng-icon>
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="left: 24%; top: 20%;">
                        <img [src]="'assets/icons/independent.svg'"
                            class="size-5 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#0047DB]  to-[#3B82F6]  rounded-full icon-bubble size-12"
                        style="left: 12%; top: 40%;">
                        <ng-icon name="lucideFolderOpen" class="text-xl text-white"></ng-icon>
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="left: 0%; bottom: 0%;">
                        <img [src]="'assets/icons/dependent.svg'"
                            class="size-5 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="left: 20%; bottom: 0%;">
                        <img [src]="'assets/icons/descriptive.svg'"
                            class="size-5 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <!-- Orta - Logo -->
                    <div class="absolute flex items-center justify-center p-3 bg-[#1F3B97] rounded-full size-24 icon-bubble"
                        style="left: 50%; top: 40%; transform: translate(-50%, -50%);">
                        <img src="assets/icons/logo-solid.svg" alt="istabot Logo" class="size-16">
                    </div>

                    <!-- Sağ taraf ikonları -->
                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#0047DB]  to-[#3B82F6]  rounded-full icon-bubble size-12"
                        style="left: 62%; top: 10%;">
                        <ng-icon name="lucideSparkles" class="text-xl text-white"></ng-icon>
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#0047DB]  to-[#3B82F6]  rounded-full icon-bubble size-12"
                        style="left: 75%; top: 0%;">
                        <ng-icon name="lucideFileText" class="text-xl text-white"></ng-icon>
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="right: 0%; top: 5%;">
                        <img [src]="'assets/icons/chisq.svg'"
                            class="size-5 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="left: 75%; top: 35%;">
                        <img [src]="'assets/icons/correlation.svg'"
                            class="size-5 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="left: 55%; bottom: 5%;">
                        <img [src]="'assets/icons/descriptive.svg'"
                            class="size-8 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#0047DB]  to-[#3B82F6]  rounded-full icon-bubble size-12"
                        style="left: 70%; bottom: 0%;">
                        <ng-icon name="lucideGlobe" class="text-xl text-white"></ng-icon>
                    </div>

                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#DBEAFE] from-30% to-50% to-[#FFFFFF]  rounded-full icon-bubble size-16"
                        style="right: 0%; bottom: 0%;">
                        <img [src]="'assets/icons/single.svg'"
                            class="size-5 brightness-0 [filter:invert(19%)_sepia(95%)_saturate(3836%)_hue-rotate(217deg)_brightness(70%)_contrast(107%)]">
                    </div>

                    <!-- Alt orta ikon -->
                    <div class="absolute flex items-center justify-center p-3 bg-gradient-to-tr from-[#0047DB]  to-[#3B82F6]  rounded-full icon-bubble size-12"
                        style="left: 40%; bottom: 0%;">
                        <ng-icon name="lucideDatabase" class="text-xl text-white"></ng-icon>
                    </div>
                </div>

            </div>
        </div>
        <div class="overflow-hidden rounded-3xl bg-gradient-to-br from-[#CAE4F7] to-80% to-[#0047DB] h-96 shadow">
            <div class="p-6">
                <h2
                    class="text-2xl font-bold bg-gradient-to-b from-[#0F172A] to-[#334155] bg-clip-text text-transparent leading-tight">
                    {{ t('features.security_card.title') }}
                </h2>
                <p class="text-base text-white">
                    {{ t('features.security_card.description') }}
                </p>
            </div>
            <div class="flex items-center justify-center ">
                <!-- Robot/Chatbot İkonu -->
                <div class="flex items-center justify-center animate-littleBounce">
                    <ng-icon name="lucideShieldCheck" class="text-white text-7xl"></ng-icon>
                </div>

            </div>
        </div>
        <!-- Kart 3 -->
        <div class="relative overflow-hidden bg-[#EBF2FF] group rounded-3xl h-96 shadow">
            <!-- Başlık -->
            <div class="absolute left-0 w-full text-center bottom-10">
                <h2 class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-black to-blue-500">
                    {{ t('features.report_card.title') }}
                </h2>
            </div>

            <!-- Rapor Belge Görseli -->
            <div class="absolute w-4/5 max-w-md transform -translate-x-1/2 top-1/4 left-1/2 -translate-y-1/4">
                <div class="relative">
                    <!-- Belge Arka Planı -->
                    <!-- Mavi Gölge Efekti (moved to appear behind the table) -->
                    <div
                        class="absolute left-0 right-0 z-10 h-10 transition-all duration-500 rounded-full -bottom-5 bg-gradient-to-t from-blue-600/50 to-blue-900/60 blur-md group-hover:scale-105">
                    </div>

                    <div
                        class="relative z-20 p-5 transition-all duration-500 shadow-xl rounded-3xl bg-gradient-to-r from-white to-blue-100 group-hover:-translate-y-2">
                        <!-- Tablo İçeriği -->
                        <table class="w-full text-sm text-black/70">
                            <thead>
                                <tr class="border-y border-black/20">
                                    <th class="py-2 text-left opacity-70"></th>
                                    <th class="py-2 text-left opacity-70">Mean ± SD</th>
                                    <th class="py-2 text-left opacity-70">X Value</th>
                                    <th class="py-2 text-left opacity-70">Y Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="">
                                    <td class="py-2 text-left opacity-70">X Value</td>
                                    <td class="py-1">3 ± 1.581</td>
                                    <td class="py-1"></td>
                                    <td class="py-1"></td>
                                </tr>
                                <tr class="border-b border-black/10">
                                    <td class="py-2 text-left opacity-70">Y Value</td>
                                    <td class="py-1">8 ± 7.071</td>
                                    <td class="py-1">0.894  
                                        <sup>
                                            <i>
                                                x
                                            </i>
                                        </sup>
                                    </td>
                                    <td class="py-1"></td>
                                    <td class="py-1"></td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- Tablo Altı Not -->
                        <p class="mt-4 text-xs text-black/70">
                            p&lt;0.05 düzeyinde anlamlı
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="grid gap-4 features-grid-second  mx-auto w-[calc(100dvw-(100dvw*10/100))]">
        <!-- Alt Satır -->
        <!-- Kart 1 -->

        <div
            class="p-8 overflow-hidden flex items-center justify-center shadow rounded-3xl bg-gradient-to-br from-[#CAE4F7] to-80% to-[#0047DB] h-96">
            <h1 class="text-4xl font-bold text-white w-52">{{ t('features.speed_card.title') }}</h1>
        </div>
        <!-- Kart 2 -->
        <div
            class="relative overflow-hidden rounded-3xl h-96 bg-[radial-gradient(150%_40%_at_bottom,rgba(0,60,189,1)_0%,rgba(235,242,255,1)_100%)] shadow">
            <!-- Matrix stili değişen sayılar -->
            <div #matrixContainer class="absolute inset-0 overflow-hidden font-mono text-xs text-black/40"></div>
            <div #matrixContainer
                class="absolute h-48 w-full  bg-gradient-to-b from-transparent from-10% to-80% to-[#EBF2FF] "></div>

            <!-- Başlık -->


            <!-- Ağaç yapısı -->
            <div class="relative z-10 flex flex-col items-center mt-4 ">
                <!-- Ana R logosu -->
                <div #rLogo class="flex items-center justify-center w-16 h-16 text-white bg-blue-500 rounded-full">

                    <img src="assets/icons/r-logo.svg" alt="istabot Logo" class="z-0 w-full h-full ">

                </div>

                <!-- Bağlantı noktası (orta) -->
                <div #starNode
                    class="flex items-center justify-center mt-10 text-white bg-blue-800 rounded-full w-14 h-14"
                    style="top: 25%;">
                    <img src="assets/icons/logo-solid.svg" alt="istabot Logo" class="z-0 ">
                </div>

                <!-- Alt sıradaki bağlantı noktaları -->
                <div class="flex flex-wrap items-center justify-center w-full max-w-lg md:mt-20">
                    <!-- Connected -->
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-10 doc-node"
                        style="position: absolute; bottom: 30%; left: 20%;"> <!-- 1 -->
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-14 doc-node"
                        style="position: absolute; bottom: 15%; left: 30%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-10 doc-node"
                        style="position: absolute; bottom: 0%; left: 40%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-14 doc-node"
                        style="position: absolute; bottom: 5%; left: 50%;">

                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-10 doc-node"
                        style="position: absolute; bottom: 3%; right: 25%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center m-2 text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-14 doc-node"
                        style="position: absolute;  bottom: 18%; right: 23%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-10 doc-node"
                        style="position: absolute; bottom: 30%; right: 15%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-14 doc-node"
                        style="position: absolute; bottom: 28%; left: 5%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-10 doc-node"
                        style="position: absolute; bottom: 10%; left: 15%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-12 doc-node"
                        style="position: absolute; bottom: -5%; left: 25%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>

                    <div class="flex items-center justify-center m-2 text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-10 doc-node"
                        style="position: absolute;  bottom: 10%; right: 13%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                    <div class="flex items-center justify-center text-blue-400 rounded-full shadow bg-gradient-to-b from-blue-300/40 to-transparent size-12 doc-node"
                        style="position: absolute; bottom: 25%; right: 3%;">
                        <ng-icon name="lucideFileText" class="text-lg"></ng-icon>
                    </div>
                </div>
            </div>

            <!-- SVG Bağlantı Çizgileri -->
            <svg #svgContainer class="absolute top-0 left-0 z-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
                <g #connectionLines stroke="rgba(59, 130, 246, 0.3)" stroke-width="1.5" stroke-dasharray="4 2"
                    fill="none"></g>
                <g #flowLines stroke="rgba(255, 255, 255, 0.7)" stroke-width="2" fill="none"></g>
            </svg>
            <div class="relative z-10 w-full mt-8 text-center">
                <h2 class="text-2xl font-bold text-white">{{ t('features.r_based_card.title') }}</h2>
            </div>
        </div>
        <!-- Kart 3 -->
        <div
            class="flex items-center justify-center relative overflow-hidden group rounded-3xl bg-gradient-to-br shadow from-[#CAE4F7] to-80% to-[#0047DB] h-96">
            <h1 class="z-10 text-4xl font-bold text-white w-52">
                {{ t('features.results_card.title') }}
            </h1>
            <div class="absolute transform w-52 md:w-full">
                <img src="assets/icons/logo-solid.svg" alt="istabot Logo"
                    class="z-0 w-full h-full transition-all opacity-20 group-hover:scale-125 group-hover:rotate-[720deg] duration-300">

            </div>

        </div>
    </div>
</div>
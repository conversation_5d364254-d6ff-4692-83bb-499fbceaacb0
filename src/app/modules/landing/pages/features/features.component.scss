// Base styles for the features section
.features-section {
  overflow: hidden;
  position: relative;
}

// Card styles
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 24rem;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 60, 189, 0.1), 0 8px 10px -6px rgba(0, 60, 189, 0.1);
  }
}

// Icon animations
.icon-bubble {
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

// Grid layout styles
.features-grid-first {
  grid-template-columns: 2fr 1.5fr 2fr;
}

.features-grid-second {
  grid-template-columns: 1.5fr 2fr 1.5fr;
}

// Responsive adjustments
@media (max-width: 1024px) {
  .features-grid-first,
  .features-grid-second {
    grid-template-columns: 1fr;
  }

  .feature-card {
    height: 20rem;
  }
}

@media (max-width: 768px) {
  .feature-card {
    height: auto;
    min-height: 18rem;
  }

  // Adjust absolute positioned elements for mobile
  .doc-node {
    position: relative !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    margin: 0.5rem;
  }
}

@media (max-width: 640px) {
  .features-section {
    padding-top: 6rem;
  }

  .feature-card {
    min-height: 16rem;
  }
}
import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, Renderer2 } from '@angular/core';
import { gsap } from 'gsap';

@Component({
  selector: 'app-features',
  templateUrl: './features.component.html',
  styleUrls: ['./features.component.scss']
})
export class FeaturesComponent implements OnInit, AfterViewInit {

  @ViewChild('matrixContainer') matrixContainer: ElementRef;
  @ViewChild('rLogo') rLogo: ElementRef;
  @ViewChild('starNode') starNode: ElementRef;
  @ViewChild('svgContainer') svgContainer: ElementRef;
  @ViewChild('connectionLines') connectionLines: ElementRef;
  @ViewChild('flowLines') flowLines: ElementRef;

  private matrixAnimationTimeline: gsap.core.Timeline;
  private flowAnimations: gsap.core.Timeline[] = [];

  constructor(private renderer: Renderer2) { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initAnimations();
      this.initializeMatrix();
      this.createConnectionLines();
    }, 100);
  }

  private initAnimations(): void {
    // Tüm ikonları seç
    const icons = document.querySelectorAll('.icon-bubble');

    // Görünüme girme animasyonu
    gsap.set(icons, { opacity: 0, scale: 0.8 }); // Başlangıç durumu

    gsap.to(icons, {
      opacity: 1,
      scale: 1,
      duration: 0.7,
      stagger: 0.05, // İkonları sırayla görünür hale getir
      ease: "back.out(1.2)"
    });

    // Her ikona yüzen animasyon ekle
    icons.forEach((icon) => {
      this.applyFloatingAnimation(icon as HTMLElement);
    });
  }

  private applyFloatingAnimation(element: HTMLElement): void {
    // Daha hafif yüzen animasyon
    const moveY = 4 + Math.random() * 12; // 4-8px arası dikey hareket
    const moveX = 3 + Math.random() * 9; // 3-6px arası yatay hareket
    const duration = 2 + Math.random() * 2; // 2-4s arası

    // Y ekseni animasyonu
    gsap.to(element, {
      y: `+=${moveY}`,
      duration: duration,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: Math.random()
    });

    // X ekseni animasyonu (daha küçük)
    gsap.to(element, {
      x: `+=${moveX}`,
      duration: duration * 1.3,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: Math.random() * 0.5
    });
  }


  ngOnDestroy() {
    // Tüm GSAP animasyonlarını temizle
    if (this.matrixAnimationTimeline) {
      this.matrixAnimationTimeline.kill();
    }

    this.flowAnimations.forEach(timeline => {
      if (timeline) {
        timeline.kill();
      }
    });

    gsap.killTweensOf(".icon-bubble");
  }







  private initializeMatrix() {
    if (!this.matrixContainer?.nativeElement) return;

    const container = this.matrixContainer.nativeElement;
    const cols = Math.floor(container.offsetWidth / 10);
    const rows = Math.floor(container.offsetHeight / 40);

    // Container'ı temizle
    container.innerHTML = '';

    // Matrix grid'i oluştur
    const digits = [];

    for (let i = 0; i < rows; i++) {
      const row = this.renderer.createElement('div');
      this.renderer.addClass(row, 'flex');

      for (let j = 0; j < cols; j++) {
        const digit = this.renderer.createElement('span');
        this.renderer.addClass(digit, 'inline-block');
        this.renderer.setStyle(digit, 'width', '14px');
        this.renderer.setStyle(digit, 'height', '20px');
        this.renderer.setProperty(digit, 'textContent', Math.floor(Math.random() * 5));
        this.renderer.appendChild(row, digit);
        digits.push(digit);
      }

      this.renderer.appendChild(container, row);
    }

    // Matrix animasyonu GSAP ile
    this.matrixAnimationTimeline = gsap.timeline({ repeat: -1 });

    const animateRandomDigits = () => {
      // Rastgele seçilmiş 10% digit'i değiştir
      const randomDigits = digits
        .sort(() => 0.5 - Math.random())
        .slice(0, Math.floor(digits.length / 10));

      randomDigits.forEach(digit => {
        gsap.fromTo(digit,
          { opacity: 1 },
          { opacity: 0, duration: 0.5 }
        );
      });

      const otherDigits = digits.filter(digit => !randomDigits.includes(digit));
      const otherRandomDigits = otherDigits
        .sort(() => 0.5 - Math.random())
        .slice(0, Math.floor(digits.length / 10));

      otherRandomDigits.forEach(digit => {
        gsap.fromTo(digit,
          { opacity: 0 },
          { opacity: 1, duration: 0.5 }
        );
      });
    };

    // Sürekli değişim için interval
    const interval = setInterval(animateRandomDigits, 300);

    // Component destroyed olduğunda interval'i temizle
    this.matrixAnimationTimeline.eventCallback('onComplete', () => clearInterval(interval));
  }

  private createConnectionLines() {
    if (!this.rLogo?.nativeElement || !this.starNode?.nativeElement ||
        !this.svgContainer?.nativeElement || !this.connectionLines?.nativeElement ||
        !this.flowLines?.nativeElement) return;

    const rRect = this.rLogo.nativeElement.getBoundingClientRect();
    const starRect = this.starNode.nativeElement.getBoundingClientRect();
    const containerRect = this.svgContainer.nativeElement.getBoundingClientRect();

    const connectionLinesG = this.connectionLines.nativeElement;
    const flowLinesG = this.flowLines.nativeElement;

    // Namespace
    const svgns = "http://www.w3.org/2000/svg";

    // R'den star'a bağlantı
    const rToStarPath = document.createElementNS(svgns, "path");
    const rToStarPathData = `M ${rRect.left + rRect.width / 2 - containerRect.left} ${rRect.top + rRect.height - containerRect.top} L ${starRect.left + starRect.width / 2 - containerRect.left} ${starRect.top + starRect.height / 2 - containerRect.top}`;
    rToStarPath.setAttribute("d", rToStarPathData);
    connectionLinesG.appendChild(rToStarPath);

    // Animasyonlu akış çizgisi
    const rToStarFlowPath = document.createElementNS(svgns, "path");
    rToStarFlowPath.setAttribute("d", rToStarPathData);
    rToStarFlowPath.setAttribute("stroke-dasharray", "5 50");
    rToStarFlowPath.setAttribute("stroke-dashoffset", "55");
    flowLinesG.appendChild(rToStarFlowPath);

    // R -> Star akış animasyonu
    const rToStarTimeline = gsap.timeline({ repeat: -1 });
    rToStarTimeline.to(rToStarFlowPath, {
      strokeDashoffset: 0,
      duration: 1,
      ease: "none"
    });

    this.flowAnimations.push(rToStarTimeline);

    // Doküman elementlerini seç
    const docNodes = document.querySelectorAll('.doc-node');

    // Star'dan dokümanlara bağlantılar
    docNodes.forEach((nodeEl) => {
      const nodeRect = nodeEl.getBoundingClientRect();

      // Bağlantı çizgisi
      const path = document.createElementNS(svgns, "path");
      const pathData = `M ${starRect.left + starRect.width / 2 - containerRect.left} ${starRect.top + starRect.height / 2 - containerRect.top} L ${nodeRect.left + nodeRect.width / 2 - containerRect.left} ${nodeRect.top + nodeRect.height / 2 - containerRect.top}`;
      path.setAttribute("d", pathData);
      connectionLinesG.appendChild(path);

      // Akış çizgisi
      const flowPath = document.createElementNS(svgns, "path");
      flowPath.setAttribute("d", pathData);
      flowPath.setAttribute("stroke-dasharray", "5 50");
      flowPath.setAttribute("stroke-dashoffset", "55");
      flowPath.setAttribute("stroke", "rgba(0, 0, 0, 0.7)");
      flowLinesG.appendChild(flowPath);

      // Akış animasyonu
      const timeline = gsap.timeline({ repeat: -1 });
      timeline.to(flowPath, {
        strokeDashoffset: 0,
        duration: 0.5 + Math.random(),
        ease: "none"
      });

      this.flowAnimations.push(timeline);
    });
  }
}
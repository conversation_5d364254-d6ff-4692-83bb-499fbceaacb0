// Footer Section Styles

// Footer background
.footer-section {
    position: relative;
    isolation: isolate;
  }
  
  // Column styles
  .footer-col {
    position: relative;
    
    h3 {
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -10px;
        width: 30px;
        height: 2px;
        background: linear-gradient(to right, #19C480, transparent);
        border-radius: 1px;
      }
    }
  }
  
  // Social links
  .social-links {
    a {
      transition: all 0.3s ease;
      will-change: transform;
      
      &:hover {
        transform: translateY(-3px);
      }
    }
  }
  
  // Footer links
  .footer-links, .footer-contact {
    li {
      transition: transform 0.2s ease;
      
      a {
        position: relative;
        display: inline-block;
        transition: all 0.2s ease;
        
        &::before {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 0;
          height: 1px;
          background-color: white;
          transition: width 0.3s ease;
        }
        
        &:hover::before {
          width: 100%;
        }
      }
    }
  }
  
  // Newsletter form
  form {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    
    input {
      transition: all 0.3s ease;
      color: white;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
      
      &:focus {
        background-color: #1E40AF; // brand-blue-800 slightly lighter
      }
    }
    
    button {
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateX(2px);
      }
    }
  }
  
  // Back to top button
  .back-to-top {
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    will-change: transform, opacity;
    
    &:hover {
      transform: translateY(-5px);
    }
  }
  
  // Bottom footer
  .pt-8.pb-4 {
    transition: all 0.3s ease;
    
    a {
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 1px;
        background-color: white;
        transition: width 0.3s ease;
      }
      
      &:hover::before {
        width: 100%;
      }
    }
  }
  
  // Media queries for responsive design
  @media (max-width: 768px) {
    .footer-col {
      h3 {
        text-align: center;
        
        &::after {
          left: 50%;
          transform: translateX(-50%);
        }
      }
      
      .social-links {
        display: flex;
        justify-content: center;
      }
    }
    
    .footer-links, .footer-contact {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
  }
  
  // Animation for logo
  @keyframes pulse-subtle {
    0% {
      opacity: 0.9;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.9;
    }
  }
  
  img[alt="İstabot Logo"] {
    animation: pulse-subtle 3s infinite ease-in-out;
  }
  
  // Accessibility enhancements
  a:focus, 
  button:focus, 
  input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(25, 196, 128, 0.3);
  }
  
  // Optional: Support for reduced motion preferences
  @media (prefers-reduced-motion: reduce) {
    .footer-col,
    .social-links a,
    .footer-links li,
    .pt-8.pb-4 a,
    .back-to-top,
    form {
      transition: none !important;
      animation: none !important;
    }
  }
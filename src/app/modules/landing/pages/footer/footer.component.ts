import { Dialog } from '@angular/cdk/dialog';
import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { AgreementComponent } from '@app/shared/components/agreement/agreement.component';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent implements OnInit, AfterViewInit, OnDestroy {
  
  constructor(
    private dialog: Dialog
  ) {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initAnimations();
      this.setupBackToTop();
      this.setupNewsletterForm();
    }, 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }
  
  private initAnimations(): void {
    // Animate footer columns
    const footerCols = document.querySelectorAll('.footer-col');
    
    if (footerCols.length) {
      gsap.fromTo(footerCols,
        { y: 30, opacity: 0 },
        { 
          y: 0, 
          opacity: 1, 
          duration: 0.8,
          stagger: 0.15,
          ease: "power2.out",
          scrollTrigger: {
            trigger: '.footer-section',
            start: "top bottom-=100"
          }
        }
      );
    }
    
    // Animate social links
    const socialLinks = document.querySelectorAll('.social-links a');
    
    if (socialLinks.length) {
      gsap.fromTo(socialLinks,
        { scale: 0, opacity: 0 },
        { 
          scale: 1, 
          opacity: 1, 
          duration: 0.5,
          stagger: 0.1,
          ease: "back.out(1.7)",
          delay: 0.5,
          scrollTrigger: {
            trigger: '.social-links',
            start: "top bottom-=50"
          }
        }
      );
    }
    
    // Subtle animation for links on hover
    const footerLinks = document.querySelectorAll('.footer-links a, .footer-contact a');
    
    footerLinks.forEach(link => {
      link.addEventListener('mouseenter', () => {
        gsap.to(link, {
          x: 3,
          duration: 0.2,
          ease: "power1.out"
        });
      });
      
      link.addEventListener('mouseleave', () => {
        gsap.to(link, {
          x: 0,
          duration: 0.2,
          ease: "power1.out"
        });
      });
    });
  }
  
  private setupBackToTop(): void {
    const backToTopBtn = document.querySelector('.back-to-top');
    
    if (backToTopBtn) {
      // Initial state - hidden
      gsap.set(backToTopBtn, { opacity: 0, display: 'none' });
      
      // Show/hide based on scroll position
      ScrollTrigger.create({
        start: 400, // show after scrolling down 400px
        onEnter: () => {
          gsap.to(backToTopBtn, {
            opacity: 1,
            display: 'flex',
            duration: 0.3
          });
        },
        onLeaveBack: () => {
          gsap.to(backToTopBtn, {
            opacity: 0,
            duration: 0.3,
            onComplete: () => {
              gsap.set(backToTopBtn, { display: 'none' });
            }
          });
        }
      });
      
      // Add click event to scroll to top
      backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
      
      // Button hover animation
      backToTopBtn.addEventListener('mouseenter', () => {
        gsap.to(backToTopBtn, {
          y: -5,
          duration: 0.3,
          ease: "power2.out"
        });
      });
      
      backToTopBtn.addEventListener('mouseleave', () => {
        gsap.to(backToTopBtn, {
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    }
  }
  
  private setupNewsletterForm(): void {
    const newsletterForm = document.querySelector('.footer-col form');
    const newsletterInput = document.querySelector('.footer-col form input');
    const newsletterButton = document.querySelector('.footer-col form button');
    
    if (newsletterForm && newsletterInput && newsletterButton) {
      // Form submission
      newsletterForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const inputValue = (newsletterInput as HTMLInputElement).value;
        
        if (inputValue && this.validateEmail(inputValue)) {
          // Success animation
          gsap.to(newsletterButton, {
            backgroundColor: '#10B981', // Success green
            duration: 0.3
          });
          
          // Reset after 2 seconds
          setTimeout(() => {
            gsap.to(newsletterButton, {
              backgroundColor: '#19C480', // brand-green-500
              duration: 0.3
            });
            
            (newsletterInput as HTMLInputElement).value = '';
          }, 2000);

        } else {
          // Error shake animation
          gsap.to(newsletterForm, {
            x: 10,
            duration: 0.1,
            repeat: 3,
            yoyo: true
          });
          
          // Focus input
          (newsletterInput as HTMLInputElement).focus();
        }
      });
    }
  }
  showContract(type:string){
    this.dialog.open(AgreementComponent, {
      data: { type: type }
    });
  }
  private validateEmail(email: string): boolean {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(email);
  }
}
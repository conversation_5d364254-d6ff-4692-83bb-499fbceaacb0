<header #navbar class="fixed top-0 z-50 w-full navbar-wrapper" *transloco="let t; read:'landing'">
    <div class="flex items-center justify-between px-6 py-3 mx-auto navbar-container">
        <!-- Lo<PERSON> -->
        <a href="/" class="flex items-center">
            <img src="assets/icons/istabot-logo.svg" alt="istabot logo" class="logo-img size-12 md:size-16">
            <span
                class="ml-2 text-3xl font-bold logo-text md:text-3xl bg-gradient-to-r from-[#2982FF] via-[#3F12F3] via-[#8712FF] via-[#AA0FFF] via-[#7D10FF] to-[#7E12FE] text-transparent bg-clip-text">
                istabot
            </span>

        </a>

        <!-- Men<PERSON>ri - Desktop -->
        <nav class="items-center hidden space-x-6 md:flex">
            <a href="#features" class="font-medium nav-link text-neutral-600 hover:text-brand-blue-500">{{
                t('navbar.features') }}</a>
            <a href="#how-it-works" class="font-medium nav-link text-neutral-600 hover:text-brand-blue-500">{{
                t('navbar.how_it_works') }}</a>
            <a href="#use-cases" class="font-medium nav-link text-neutral-600 hover:text-brand-blue-500">{{
                t('navbar.use_cases') }}</a>
            <a href="#comparison" class="font-medium nav-link text-neutral-600 hover:text-brand-blue-500">{{
                t('navbar.comparison') }}</a>
            <a href="#pricing" class="font-medium nav-link text-neutral-600 hover:text-brand-blue-500">{{
                t('navbar.pricing') }}</a>
            <a href="#faq" class="font-medium nav-link text-neutral-600 hover:text-brand-blue-500">{{ t('navbar.faq')
                }}</a>
        </nav>

        <!-- Butonlar - Desktop -->
        <div class="items-center hidden md:flex md:space-x-4">
            <!-- Language Switcher -->
            <div class="flex items-center mr-4 space-x-2">
                <button (click)="changeLanguage('tr')" [class.text-brand-blue-600]="currentLang === 'tr'"
                    [class.font-bold]="currentLang === 'tr'"
                    class="px-2 py-1 text-sm transition-colors duration-200 rounded-3xl hover:bg-gray-100">
                    TR
                </button>
                <span class="text-gray-300">|</span>
                <button (click)="changeLanguage('en')" [class.text-brand-blue-600]="currentLang === 'en'"
                    [class.font-bold]="currentLang === 'en'"
                    class="px-2 py-1 text-sm transition-colors duration-200 rounded-3xl hover:bg-gray-100">
                    EN
                </button>
            </div>

            <a href="/login" class="font-medium login-btn text-brand-blue-500 hover:text-brand-blue-600">{{
                t('navbar.buttons.login') }}</a>
            <a href="/signup" class="primary-blue-button">
                {{ t('navbar.start_now') }}
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </a>
        </div>

        <!-- Mobile Menu Button -->
        <button #mobileMenuBtn (click)="toggleMobileMenu()"
            class="flex flex-col items-center justify-center w-10 h-10 focus:outline-none md:hidden">
            <div class="relative w-6 h-6">
                <span class="bar1 absolute w-6 h-0.5 bg-gray-800 rounded-full top-1"></span>
                <span class="bar2 absolute w-6 h-0.5 bg-gray-800 rounded-full top-3"></span>
                <span class="bar3 absolute w-6 h-0.5 bg-gray-800 rounded-full top-5"></span>
            </div>
        </button>
    </div>
</header>

<!-- Mobile Menu -->
<div #mobileMenu class="fixed z-40 w-full bg-white border-t border-gray-100 shadow-lg mobile-menu top-16"
    *transloco="let t; read:'landing'">
    <div class="container px-6 py-4 mx-auto">
        <nav class="flex flex-col space-y-4">
            <a href="#features" class="mobile-nav-link">{{ t('navbar.features') }}</a>
            <a href="#how-it-works" class="mobile-nav-link">{{ t('navbar.how_it_works') }}</a>
            <a href="#use-cases" class="mobile-nav-link">{{ t('navbar.use_cases') }}</a>
            <a href="#comparison" class="mobile-nav-link">{{ t('navbar.comparison') }}</a>
            <a href="#pricing" class="mobile-nav-link">{{ t('navbar.pricing') }}</a>
            <a href="#faq" class="mobile-nav-link">{{ t('navbar.faq') }}</a>
        </nav>

        <div class="flex flex-col pt-4 mt-6 space-y-4 border-t border-gray-100">
            <!-- Mobile Language Switcher -->
            <div class="flex items-center justify-center mb-4 space-x-4">
                <button (click)="changeLanguage('tr')" [class.text-brand-blue-600]="currentLang === 'tr'"
                    [class.font-bold]="currentLang === 'tr'" class="px-3 py-2 text-sm rounded hover:bg-gray-100">
                    Türkçe
                </button>
                <button (click)="changeLanguage('en')" [class.text-brand-blue-600]="currentLang === 'en'"
                    [class.font-bold]="currentLang === 'en'" class="px-3 py-2 text-sm rounded hover:bg-gray-100">
                    English
                </button>
            </div>

            <a href="/login" class="mobile-nav-button text-brand-blue-500">{{ t('navbar.buttons.login') }}</a>
            <a href="/signup" class="mobile-demo-button">
                {{ t('navbar.start_now') }}
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </a>
        </div>
    </div>
</div>
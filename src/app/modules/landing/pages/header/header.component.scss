// Ana navbar wrapper
.navbar-wrapper {
  width: 100%;
  transition: all 0.3s ease;
}

// İç container
.navbar-container {
  transition: all 0.3s ease;
  max-width: 1400px;
  width: 100%;
}

// Nav bağlantıları
.nav-link {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #003CBD;
    transition: width 0.2s ease;
  }

  &:hover::after {
    width: 100%;
  }
}

// Mobil menü
.mobile-menu {
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  display: none;

  .mobile-nav-link {
    @apply block py-2 text-neutral-600 font-medium transition-colors duration-200;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 2px;
      background-color: #003CBD;
      transition: width 0.2s ease;
    }

    &:hover {
      @apply text-brand-blue-500;

      &::after {
        width: 2rem;
      }
    }
  }

  .mobile-nav-button {
    @apply block py-3 font-medium text-center transition-colors duration-200;
    width: 100%;

    &:hover {
      @apply text-brand-blue-600;
    }
  }

  .mobile-demo-button {
    @apply flex items-center justify-center px-4 py-3 text-sm font-medium transition-all duration-300 rounded-3xl shadow-md text-white bg-brand-blue-500 hover:bg-brand-blue-600 focus:outline-none focus:ring-2 focus:ring-brand-blue-300;
  }
}

// Responsive düzenlemeler
@media (max-width: 768px) {
  .navbar-container {
    padding-left: 1rem;
    padding-right: 1rem;
    background-color: white;
  }

  .logo-img {
    width: 36px;
    height: 36px;
  }

  .logo-text {
    font-size: 1.25rem;
  }

  // Mobil menu açık olduğunda
  .menu-open {
    .bar1, .bar2, .bar3 {
      transition: all 0.3s ease;
    }
  }
}
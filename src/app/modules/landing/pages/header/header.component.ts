import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy, Renderer2 } from '@angular/core';
import { gsap } from 'gsap';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit, AfterViewInit, On<PERSON><PERSON>roy {
  @ViewChild('navbar') navbar!: ElementRef;
  @ViewChild('mobileMenuBtn') mobileMenuBtn!: ElementRef;
  @ViewChild('mobileMenu') mobileMenu!: ElementRef;

  isMobileMenuOpen: boolean = false;
  isScrolled: boolean = false;
  lastScrollTop: number = 0;
  scrollDirection: string = 'none';

  currentLang: string = 'tr';

  constructor(private renderer: Renderer2, private transloco: TranslocoService) {
    // Get the active language from localStorage or use default
    const activeLang = localStorage.getItem('activeLang');
    if (activeLang && (activeLang === 'tr' || activeLang === 'en')) {
      this.currentLang = activeLang;
      this.transloco.setActiveLang(activeLang);
    } else {
      this.currentLang = this.transloco.getActiveLang();
    }
  }

  changeLanguage(lang: string): void {
    this.currentLang = lang;
    this.transloco.setActiveLang(lang);
    localStorage.setItem('activeLang', lang);
  }

  ngOnInit(): void {
    window.addEventListener('scroll', this.handleScroll.bind(this));
  }

  ngAfterViewInit(): void {
    // İlk yüklemede tam genişlikte olmasını sağla
    this.setupInitialView();
    this.setupMobileMenu();
  }

  ngOnDestroy(): void {
    window.removeEventListener('scroll', this.handleScroll.bind(this));
  }

  setupInitialView(): void {
    if (!this.navbar) return;

    // İlk açılışta sayfa zaten aşağıdaysa daraltılmış navbar'ı göster
    if (window.pageYOffset > 50) {
      this.shrinkNavbar();
    }
  }

  handleScroll(): void {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Scroll yönünü belirle
    this.scrollDirection = scrollTop > this.lastScrollTop ? 'down' : 'up';

    // 50px'den fazla scroll edildiğinde compact haline getir
    if (scrollTop > 50 && !this.isScrolled) {
      this.shrinkNavbar();
    }
    // Yukarı scroll edildiğinde ve sayfa başındayken genişlet
    else if (scrollTop < 20 && this.isScrolled) {
      this.expandNavbar();
    }

    this.lastScrollTop = scrollTop;

    // Mobil menü açıksa ve aşağı kaydırılıyorsa menüyü kapat
    if (this.isMobileMenuOpen && this.scrollDirection === 'down' && scrollTop > 50) {
      this.closeMobileMenu();
    }
  }

  shrinkNavbar(): void {
    if (!this.navbar) return;

    const navbarElem = this.navbar.nativeElement;
    const container = navbarElem.querySelector('.navbar-container');
    const logoImg = navbarElem.querySelector('.logo-img');
    const logoText = navbarElem.querySelector('.logo-text');
    const links = navbarElem.querySelectorAll('.nav-link');

    // İç container
    if (container) {
      gsap.to(container, {
        width: '90%',
        borderRadius: '2rem',
        marginTop: '1rem',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
        backgroundColor: 'rgba(255, 255, 255, 0.80)',
        duration: 0.3,
        backdropFilter: 'blur(10px)'
      });
    }

    // Logo ve linkler
    if (logoImg) gsap.to(logoImg, { scale: 0.8, duration: 0.3 });
    if (logoText) gsap.to(logoText, { fontSize: '1.5rem', duration: 0.3 });

    if (links.length) {
      links.forEach((link: Element) => {
        gsap.to(link, { fontSize: '1rem', duration: 0.3 });
      });
    }

    this.isScrolled = true;
  }

  expandNavbar(): void {
    if (!this.navbar) return;

    const navbarElem = this.navbar.nativeElement;
    const container = navbarElem.querySelector('.navbar-container');
    const logoImg = navbarElem.querySelector('.logo-img');
    const logoText = navbarElem.querySelector('.logo-text');
    const links = navbarElem.querySelectorAll('.nav-link');

    // İç container
    if (container) {
      gsap.to(container, {
        width: '100%',
        borderRadius: '0',
        marginTop: '0',
        boxShadow: 'none',
        backgroundColor: 'transparent',
        duration: 0.3
      });
    }

    // Logo ve linkler
    if (logoImg) gsap.to(logoImg, { scale: 1, duration: 0.3 });
    if (logoText) gsap.to(logoText, {
      fontSize: window.innerWidth < 768 ? '1.25rem' : '1.5rem',
      duration: 0.3
    });

    if (links.length) {
      links.forEach((link: Element) => {
        gsap.to(link, { fontSize: '1rem', duration: 0.3 });
      });
    }

    this.isScrolled = false;
  }

  setupMobileMenu(): void {
    if (!this.mobileMenu) return;

    // Başlangıçta mobil menüyü gizle
    gsap.set(this.mobileMenu.nativeElement, {
      height: 0,
      opacity: 0,
      display: 'none'
    });
  }

  toggleMobileMenu(): void {
    if (this.isMobileMenuOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  openMobileMenu(): void {
    if (!this.mobileMenu || !this.mobileMenuBtn) return;

    this.isMobileMenuOpen = true;

    // Menü butonuna class ekle
    this.renderer.addClass(this.mobileMenuBtn.nativeElement, 'menu-open');

    // Mobil menüyü göster
    gsap.set(this.mobileMenu.nativeElement, {
      display: 'block',
      height: 'auto'
    });

    const height = this.mobileMenu.nativeElement.offsetHeight;

    gsap.fromTo(this.mobileMenu.nativeElement,
      { height: 0, opacity: 0 },
      { height: height, opacity: 1, duration: 0.3, ease: 'power2.out' }
    );
  }

  closeMobileMenu(): void {
    if (!this.mobileMenu || !this.mobileMenuBtn) return;

    this.isMobileMenuOpen = false;

    // Menü butonundan class kaldır
    this.renderer.removeClass(this.mobileMenuBtn.nativeElement, 'menu-open');

    // Mobil menüyü gizle
    gsap.to(this.mobileMenu.nativeElement, {
      height: 0,
      opacity: 0,
      duration: 0.3,
      ease: 'power2.in',
      onComplete: () => {
        gsap.set(this.mobileMenu.nativeElement, { display: 'none' });
      }
    });
  }
}
<section class="relative flex items-center justify-center min-h-screen px-6 py-16 overflow-hidden hero-section bg-gradient-to-b from-brand-blue-50 to-gray-50" *transloco="let t; read:'landing'">
  <!-- Particle container for background effect -->
  <div class="absolute inset-0 z-0 overflow-hidden pointer-events-none particle-container" #particleContainer></div>

  <!-- Logo Background -->
  <div class="absolute z-0 flex items-center justify-center pointer-events-none inset-12 logo-background" #logoContainer>
    <img
      #istabotLogo
      class="w-full max-w-7xl istabot-logo"
      src="assets/icons/istabot-logo.svg"
      alt="istabot Logo"
      (load)="onLogoLoaded()"
      loading="eager"
    >
  </div>

  <!-- Content -->
  <div class="relative z-20 max-w-6xl mx-auto">
    <div class="mt-16 mb-12 text-center">
      <h1 class="flex flex-col mb-6 text-5xl font-bold leading-tight tracking-tight text-gray-900 md:text-6xl">
        <span class="block text-reveal">{{ t('hero.title.line1') }}</span>
        <span class="block text-reveal">{{ t('hero.title.line2') }}</span>
      </h1>

      <p class="max-w-2xl mx-auto mt-8 mb-10 text-xl text-gray-700">
        {{ t('hero.description') }}
      </p>

      <div class="flex flex-col items-center justify-center gap-4 sm:flex-row">
        <a href="#watch-demo" class="secondary-blue-button">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
          {{ t('hero.buttons.demo_video') }}
        </a>
        <a href="/signup" class="primary-blue-button">
          {{ t('hero.buttons.start_analysis') }}
          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>

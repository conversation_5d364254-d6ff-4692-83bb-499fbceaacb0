// Animation helper classes

// Text reveal animation
.text-reveal {
  display: inline-block;
  overflow: hidden;
  padding: 0.2em 0;
  clip-path: inset(0 100% 0 0);
  animation: text-reveal 1.5s cubic-bezier(0.77, 0, 0.175, 1) forwards;

  &:nth-of-type(2) {
    animation-delay: 0.3s;
  }
}

@keyframes text-reveal {
  from {
    clip-path: inset(0 100% 0 0);
  }
  to {
    clip-path: inset(0 0 0 0);
  }
}

// Hero section
.hero-section {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

// Logo background styles
.logo-background {
  will-change: transform;
  transform-style: preserve-3d;
  backface-visibility: hidden;

  .istabot-logo {
    opacity: 0;
    transform: scale(0.5);
    transition: filter 1s ease, opacity 0.5s ease, transform 1.5s ease;
    max-width: 80%;
    height: auto;
    filter: blur(1px);
  }
}

// Mouse hover effect on the logo
.hero-section:hover .logo-background .istabot-logo {
  filter: blur(0.5px);

  path {
    opacity: 0.35;
    transition: opacity 1.5s ease;
  }
}

// Particle container
.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

// Particle styling
.particle {
  position: absolute;
  box-shadow: 0 0 10px 1px rgba(0, 60, 189, 0.1);
  will-change: transform;

  &:nth-child(3n) {
    background-color: rgba(25, 196, 128, 0.3);
  }

  &:nth-child(3n+1) {
    background-color: rgba(0, 60, 189, 0.3);
  }

  &:nth-child(3n+2) {
    background-color: rgba(0, 71, 219, 0.2);
  }
}

// Mobile optimizations
@media (max-width: 768px) {
  .hero-section {
    min-height: auto;
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .particle-container .particle {
    opacity: 0.5;
  }

  .logo-background {
    animation: gentle-float 8s ease-in-out infinite alternate;

    .istabot-logo {
      max-width: 90%;
      opacity: 0.1;
    }
  }

  @keyframes gentle-float {
    from {
      transform: translateY(-5px) rotate(-1deg);
    }
    to {
      transform: translateY(5px) rotate(1deg);
    }
  }
}

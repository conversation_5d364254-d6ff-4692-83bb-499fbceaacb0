import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { gsap } from 'gsap';

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  opacity: number;
  element: HTMLDivElement;
}

@Component({
  selector: 'app-hero',
  templateUrl: './hero.component.html',
  styleUrls: ['./hero.component.scss']
})
export class HeroComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('logoContainer') logoContainer!: ElementRef;
  @ViewChild('particleContainer') particleContainer!: ElementRef;
  @ViewChild('istabotLogo') istabotLogo!: ElementRef;

  private logoLoaded = false;
  private particlesCreated = false;
  private initialAnimationDone = false;

  // Mouse tracking
  mouseX: number = 0;
  mouseY: number = 0;
  lastMouseX: number = 0;
  lastMouseY: number = 0;
  windowWidth: number = 0;
  windowHeight: number = 0;

  // Particles
  particles: Particle[] = [];
  particleCount: number = 30; // Reduced count for better performance
  animationFrameId: number = 0;

  constructor() {
    this.preloadLogo();
  }
  private preloadLogo(): void {
    const img = new Image();
    img.src = 'assets/icons/istabot-logo.svg';
    img.onload = () => {
      this.logoLoaded = true;
      if (this.logoContainer && !this.initialAnimationDone) {
        this.initLogoAnimation();
      }
    };
  }

  ngOnInit(): void {
    this.windowWidth = window.innerWidth;
    this.windowHeight = window.innerHeight;

    // Set initial mouse position to center
    this.mouseX = this.windowWidth / 2;
    this.mouseY = this.windowHeight / 2;
    this.lastMouseX = this.mouseX;
    this.lastMouseY = this.mouseY;

    // Add event listeners
    window.addEventListener('mousemove', this.handleMouseMove.bind(this));
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  ngAfterViewInit(): void {
    if (this.logoLoaded && !this.initialAnimationDone) {
      this.initLogoAnimation();
    }

    // Initialize particles with a slight delay to ensure DOM is ready
    setTimeout(() => this.initParticles(), 100);
  }

  ngOnDestroy(): void {
    // Clean up event listeners
    window.removeEventListener('mousemove', this.handleMouseMove.bind(this));
    window.removeEventListener('resize', this.handleResize.bind(this));

    // Cancel animation frame
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    // Clean up GSAP animations
    if (this.logoContainer?.nativeElement) {
      gsap.killTweensOf(this.logoContainer.nativeElement);
    }

    // Clean up particles
    this.particles.forEach(particle => {
      if (particle.element.parentNode) {
        particle.element.parentNode.removeChild(particle.element);
      }
    });
  }

  handleMouseMove(event: MouseEvent): void {
    this.mouseX = event.clientX;
    this.mouseY = event.clientY;

    // Update logo position based on mouse movement
    if (this.logoContainer?.nativeElement && this.logoLoaded) {
      const moveX = (this.mouseX - this.windowWidth / 2) / 30;
      const moveY = (this.mouseY - this.windowHeight / 2) / 30;

      gsap.to(this.logoContainer.nativeElement, {
        x: moveX,
        y: moveY,
        rotation: moveX * 0.05,
        duration: 1,
        ease: 'power2.out'
      });
    }
  }

  handleResize(): void {
    this.windowWidth = window.innerWidth;
    this.windowHeight = window.innerHeight;
  }

  onLogoLoaded(): void {
    if (!this.initialAnimationDone) {
      this.initLogoAnimation();
    }
  }

  initLogoAnimation(): void {
    if (!this.logoContainer?.nativeElement || this.initialAnimationDone) {
      return;
    }

    this.initialAnimationDone = true;

    // Logo elementini seç
    const logoElement = this.istabotLogo.nativeElement;

    // Başlangıç durumunu ayarla
    gsap.set(this.logoContainer.nativeElement, {
      scale: 0,
      rotation: -1
    });

    gsap.set(logoElement, {
      opacity: 0
    });

    // Animasyon sekansını oluştur
    const timeline = gsap.timeline({
      defaults: { ease: 'power2.inOut' }
    });

    timeline
      .to(logoElement, {
        opacity: 0.2,
        duration: 0.5,
        ease: 'power1.in'
      })
      .to(this.logoContainer.nativeElement, {
        scale: 1,
        rotation: 0,
        duration: 1.5,
        ease: 'elastic.out(1, 0.75)'
      }, '-=0.3');

    // Logo animation complete
  }
  initParticles(): void {
    if (!this.particleContainer?.nativeElement) {
      setTimeout(() => this.initParticles(), 200);
      return;
    }

    this.createParticles();

    if (this.particlesCreated) {
      this.animateParticles();
    }
  }

  createParticles(): void {
    if (!this.particleContainer?.nativeElement) return;

    const container = this.particleContainer.nativeElement;

    // Clear any existing particles
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }

    this.particles = [];

    // Create new particles
    for (let i = 0; i < this.particleCount; i++) {
      // Create particle element
      const element = document.createElement('div');
      element.className = 'particle';

      // Generate random properties
      const size = Math.random() * 4 + 2;
      const opacity = Math.random() * 0.5 + 0.3;

      // Style particle
      element.style.width = `${size}px`;
      element.style.height = `${size}px`;
      element.style.opacity = opacity.toString();
      element.style.backgroundColor = i % 3 === 0 ? '#19C480' : '#003CBD';
      element.style.position = 'absolute';
      element.style.borderRadius = '50%';
      element.style.pointerEvents = 'none';

      // Random position
      const x = Math.random() * this.windowWidth;
      const y = Math.random() * this.windowHeight;

      element.style.left = `${x}px`;
      element.style.top = `${y}px`;

      // Random speed
      const speedX = (Math.random() - 0.5) * 0.5;
      const speedY = (Math.random() - 0.5) * 0.5;

      // Add to container
      container.appendChild(element);

      // Add to particles array
      this.particles.push({
        x,
        y,
        size,
        speedX,
        speedY,
        opacity,
        element
      });
    }

    this.particlesCreated = true;
  }

  animateParticles(): void {
    if (!this.particlesCreated || this.particles.length === 0) {
      setTimeout(() => this.animateParticles(), 200);
      return;
    }

    // Update particles
    const mouseSpeedX = this.mouseX - this.lastMouseX;
    const mouseSpeedY = this.mouseY - this.lastMouseY;

    this.particles.forEach(particle => {
      // Basic movement
      particle.x += particle.speedX;
      particle.y += particle.speedY;

      // Add slight influence from mouse movement
      particle.x += mouseSpeedX * 0.1;
      particle.y += mouseSpeedY * 0.1;

      // Wrap around edges
      if (particle.x < -100) particle.x = this.windowWidth + 100;
      if (particle.x > this.windowWidth + 100) particle.x = -100;
      if (particle.y < -100) particle.y = this.windowHeight + 100;
      if (particle.y > this.windowHeight + 100) particle.y = -100;

      // Update position
      if (particle.element) {
        particle.element.style.transform = `translate3d(${particle.x}px, ${particle.y}px, 0)`;
      }
    });

    // Store current mouse position for next frame
    this.lastMouseX = this.mouseX;
    this.lastMouseY = this.mouseY;

    // Continue animation
    this.animationFrameId = requestAnimationFrame(this.animateParticles.bind(this));
  }

}
// Background patterns


// Process line animation styles
.process-line {
  will-change: height;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -4px;
    width: 9px;
    height: 9px;
    background-color: #19C480;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(25, 196, 128, 0.8);
  }
}

// Step number styles
.step-number {
  position: relative;
  z-index: 5;
  box-shadow: 0 5px 15px rgba(0, 60, 189, 0.3);
  will-change: transform, opacity;
  
  &::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(to right, rgba(59, 130, 246, 0.3), rgba(25, 196, 128, 0.3));
    z-index: -1;
    opacity: 0.8;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

// Process step styling for alternating layout
.process-step {
  // Step and line connections for medium screens and above
  @media (min-width: 768px) {
    &:nth-child(odd) .md\:ml-8 {
      margin-left: 2rem;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 1.75rem;
        left: -2rem;
        width: 2rem;
        height: 1px;
        background: linear-gradient(to right, rgba(25, 196, 128, 0.7), rgba(25, 196, 128, 0.2));
      }
    }
    
    &:nth-child(even) {
      flex-direction: row-reverse;
      
      .md\:ml-8 {
        margin-left: 0;
        margin-right: 2rem;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          top: 1.75rem;
          right: -2rem;
          width: 2rem;
          height: 1px;
          background: linear-gradient(to left, rgba(25, 196, 128, 0.7), rgba(25, 196, 128, 0.2));
        }
      }
    }
  }
}

// Analysis options styling
.analysis-option {
  transition: all 0.3s ease;
  will-change: transform;
  border: 1px solid transparent;
  
  &:not(.selected):hover {
    transform: translateY(-5px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
  
  &.selected {
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 2px;
      background-color: #19C480;
      border-radius: 2px;
    }
  }
}

// Result styles
.result-table, .result-comment {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
}

// Report preview effect
.report-preview {
  transition: all 0.3s ease;
  will-change: transform;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 5%;
    width: 90%;
    height: 10px;
    background-color: rgba(0, 0, 0, 0.1);
    filter: blur(5px);
    border-radius: 50%;
    z-index: -1;
    transition: all 0.3s ease;
  }
  
  &:hover::after {
    bottom: -8px;
    filter: blur(8px);
  }
}

// CTA section animation
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}



// Floating elements animation
.floating-element {
  filter: brightness(5) blur(1px);
  opacity: 0.2;
  will-change: transform;
  animation: float 10s ease-in-out infinite alternate;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(10px) rotate(-5deg);
  }
}

// Media queries for responsive design
@media (max-width: 768px) {

  
  .process-step {
    padding-left: 0;
    margin-right: 0;
  }
  
  .floating-element {
    display: none;
  }
}

// Accessibility enhancements
button:focus, 
.analysis-option:focus {
  outline: 2px solid rgba(25, 196, 128, 0.6);
  outline-offset: 2px;
}

// Optional: Support for reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .floating-element,
  .step-number::before,
  .process-step > div:nth-child(2) > div {
    animation: none !important;
    transition: none !important;
  }
}
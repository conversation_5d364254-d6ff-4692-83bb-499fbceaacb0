import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy, ViewChildren, QueryList } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-how-it-works',
  templateUrl: './how-it-works.component.html',
  styleUrls: ['./how-it-works.component.scss']
})
export class HowItWorksComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('processStep1') processStep1!: ElementRef;
  @ViewChild('processStep2') processStep2!: ElementRef;
  @ViewChild('processStep3') processStep3!: ElementRef;
  @ViewChild('processStep4') processStep4!: ElementRef;
  @ViewChild('floatingEl1') floatingEl1!: ElementRef;
  @ViewChild('floatingEl2') floatingEl2!: ElementRef;
  @ViewChild('floatingEl3') floatingEl3!: ElementRef;
  
  constructor() {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initStepAnimations();
      this.initFloatingElements();
      this.initHoverEffects();
    }, 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }
  
  private initStepAnimations(): void {
    // Process line animation
    const processLine = document.querySelector('.process-line');
    if (processLine) {
      gsap.set(processLine, { height: 0 });
      gsap.to(processLine, {
        height: '100%',
        duration: 1.5,
        ease: 'power1.inOut',
        scrollTrigger: {
          trigger: '.how-it-works-section',
          start: 'top center',
          end: 'bottom bottom',
          scrub: true
        }
      });
    }
    
    // Process steps animation
    const steps = [
      this.processStep1?.nativeElement,
      this.processStep2?.nativeElement,
      this.processStep3?.nativeElement,
      this.processStep4?.nativeElement
    ].filter(step => step);
    
    steps.forEach((step, index) => {
      const stepNumber = step.querySelector('.step-number');
      const stepContent = step.querySelector('.md\\:ml-8');
      
      if (stepNumber && stepContent) {
        // Initial state
        gsap.set(stepNumber, { scale: 0.5, opacity: 0 });
        gsap.set(stepContent, { x: 30, opacity: 0 });
        
        // Create ScrollTrigger for each step
        ScrollTrigger.create({
          trigger: step,
          start: 'top bottom-=100',
          onEnter: () => {
            // Number animation
            gsap.to(stepNumber, {
              scale: 1,
              opacity: 1,
              duration: 0.6,
              ease: 'back.out(1.7)',
              delay: index * 0.1
            });
            
            // Content animation
            gsap.to(stepContent, {
              x: 0,
              opacity: 1,
              duration: 0.8,
              ease: 'power2.out',
              delay: index * 0.1 + 0.2
            });
          },
          once: true
        });
      }
    });
    
    // Call to action animation
    const cta = document.querySelector('.max-w-3xl');
    if (cta) {
      gsap.set(cta, { y: 30, opacity: 0 });
      gsap.to(cta, {
        y: 0,
        opacity: 1,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: cta,
          start: 'top bottom-=50'
        }
      });
    }
  }
  
  private initFloatingElements(): void {
    const floatingElements = [
      this.floatingEl1?.nativeElement,
      this.floatingEl2?.nativeElement,
      this.floatingEl3?.nativeElement
    ].filter(el => el);
    
    floatingElements.forEach((element, index) => {
      // Random movement animation
      gsap.to(element, {
        y: '+=20',
        x: '+=10',
        rotation: '+=5',
        duration: 3 + index,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      });
    });
  }
  
  private initHoverEffects(): void {
    // Upload area hover effect
    const uploadVisual = document.querySelector('.upload-visual');
    if (uploadVisual) {
      uploadVisual.addEventListener('mouseenter', () => {
        gsap.to(uploadVisual, {
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(147, 197, 253, 0.5)',
          duration: 0.3
        });
      });
      
      uploadVisual.addEventListener('mouseleave', () => {
        gsap.to(uploadVisual, {
          backgroundColor: 'transparent',
          borderColor: 'rgba(147, 197, 253, 0.3)',
          duration: 0.3
        });
      });
    }
    
    // Report preview hover effect
    const reportPreview = document.querySelector('.report-preview');
    if (reportPreview) {
      reportPreview.addEventListener('mouseenter', () => {
        gsap.to(reportPreview, {
          y: -5,
          boxShadow: '0 15px 30px rgba(0, 0, 0, 0.2)',
          duration: 0.3
        });
      });
      
      reportPreview.addEventListener('mouseleave', () => {
        gsap.to(reportPreview, {
          y: 0,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          duration: 0.3
        });
      });
    }
  }
}
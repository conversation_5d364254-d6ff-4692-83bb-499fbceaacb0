import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  Component,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-landing',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.scss'],
  animations: [
    trigger('shrinkLeft', [
      state('void', style({ transform: 'translateX(100%)', opacity: '0', })),
      state('*', style({ transform: '*', opacity: '*', })),
      transition('* <=> void', [animate('0.2s ease-in-out')]),
    ]),
  ],
})
export class LandingComponent implements OnInit, OnDestroy {
  private tawkInitialized = false;

  constructor(private router: Router) { }

  ngOnInit(): void {
    // Sadece landing sayfasındayken Tawk.to'yu başlat
    if (this.router.url === '/' || this.router.url === '/landing') {
      this.initTawkToSafely();
    }
  }

  ngOnDestroy(): void {
    // Güvenli bir şekilde widget'ı gizle, ama sadece gerektiğinde
    try {
      if (this.tawkInitialized && (window as any).Tawk_API) {
        // Login sayfasına gidiyorsa widget'ı gizleme
        const currentUrl = this.router.url;
        if (!currentUrl.includes('/login') && !currentUrl.includes('/register')) {
          (window as any).Tawk_API?.hideWidget();
        }
      }
    } catch (error) {
      console.error('Tawk.to widget gizleme hatası:', error);
    }
  }

  private initTawkToSafely(): void {
    try {
      if (!(window as any).Tawk_API && !this.tawkInitialized) {
        this.tawkInitialized = true;

        var Tawk_API: any = Tawk_API || {}, Tawk_LoadStart = new Date();
        (function () {
          var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
          s1.async = true;
          s1.src = 'https://embed.tawk.to/669a7cc532dca6db2cb22b5e/1i35nep5c';
          s1.charset = 'UTF-8';
          s1.setAttribute('crossorigin', '*');
          s0.parentNode?.insertBefore(s1, s0);
        })();

        // Tawk.to yüklenme kontrolü
        setTimeout(() => {
          try {
            if ((window as any).Tawk_API) {
              (window as any).Tawk_API?.showWidget();

              // Kullanıcı bilgileri varsa set et
              if (localStorage.getItem('username') && localStorage.getItem('email')) {
                (window as any).Tawk_API?.setAttributes({
                  name: '#' + localStorage.getItem('user_id') + ': ' + localStorage.getItem('username'),
                  email: localStorage.getItem('email') || '<EMAIL>',
                }, function (error: any) {
                  if (error) {
                    console.error('Tawk.to güncelleme hatası:', error);
                  }
                });
              }
            }
          } catch (error) {
            console.error('Tawk.to widget gösterme hatası:', error);
          }
        }, 1000); // Timeout'u artırdık

      } else if ((window as any).Tawk_API) {
        // Tawk.to zaten yüklü, sadece göster
        setTimeout(() => {
          try {
            (window as any).Tawk_API?.showWidget();
          } catch (error) {
            console.error('Tawk.to widget gösterme hatası:', error);
          }
        }, 500);
      }
    } catch (error) {
      console.error('Tawk.to başlatma hatası:', error);
    }
  }
}
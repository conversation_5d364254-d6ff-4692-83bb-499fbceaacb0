<section id="pricing" class="relative px-4 py-16 pt-32 overflow-hidden pricing-section bg-brand-blue-50" *transloco="let t; read:'landing'">

    <!-- <PERSON><PERSON><PERSON><PERSON> Konteyneri -->
    <div class="relative z-10 max-w-6xl mx-auto">
        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON>ğ<PERSON> -->
        <div class="mb-16 text-center">
            <h2 class="mb-2 text-4xl font-bold text-brand-blue-600">{{ t('pricing.title') }}</h2>
            <div class="w-32 h-1 mx-auto mb-6 rounded-full bg-brand-green-500"></div>
            <div class="max-w-2xl mx-auto">
                <p class="text-lg text-gray-700">
                    {{ t('pricing.description') }}
                </p>
            </div>
        </div>

        <!-- Fiyatlandırma Kartları -->
        <div class="grid grid-cols-1 gap-8 pricing-cards md:grid-cols-1 lg:grid-cols-3">
            <!-- Mini Paket -->
            <div class="relative flex flex-col justify-between h-full transition-all duration-300 bg-white shadow-md rounded-3xl pricing-card hover:shadow-xl"
                #pricingCard1>
                <!-- İndirim Etiketi -->
                <div class="absolute top-4 right-4">
                    <div class="px-3 py-1 text-sm font-medium bg-blue-100 rounded-full text-brand-blue-600">
                        {{ t('pricing.packages.mini.discount_label') }}
                    </div>
                </div>

                <!-- Kart İçeriği -->
                <div class="flex-grow p-6">
                    <!-- Orijinal Fiyat -->
                    <div class="flex items-center mb-1">
                        <p class="text-lg text-gray-400 line-through">{{ t('pricing.packages.mini.original_price') }}</p>
                    </div>

                    <!-- Güncel Fiyat -->
                    <div class="flex items-center mb-4">
                        <h3 class="text-4xl font-bold text-brand-blue-600">{{ t('pricing.packages.mini.discounted_price') }}</h3>
                    </div>

                    <!-- Paket Adı -->
                    <h4 class="mb-3 text-2xl font-bold text-gray-900">{{ t('pricing.packages.mini.title') }}</h4>

                    <!-- Krediler -->
                    <div class="flex items-center mb-3">
                        <img src="assets/icons/istacoin.svg" alt="kredi" class="w-8 h-8 mr-2">
                        <span class="text-xl font-semibold text-gray-800">{{ t('pricing.packages.mini.credits') }}</span>
                    </div>

                    <!-- Açıklama -->
                    <p class="mb-6 text-gray-600">{{ t('pricing.packages.mini.description') }}</p>

                    <!-- Özellikler Listesi -->
                    <ul class="space-y-3 ">
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.mini.features')[0] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.mini.features')[1] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.mini.features')[2] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.mini.features')[3] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.mini.features')[4] }}</span>
                        </li>
                    </ul>
                </div>

                <!-- Satın Al Butonu -->
                <div class="p-6 ">

                    <a href="/signup" class="flex items-center justify-center w-full mx-auto primary-blue-button mx">
                        <ng-icon name="lucideArrowRight" class="mr-2"></ng-icon>
                        {{ t('pricing.buy_button') }}
                    </a>
                </div>

            </div>

            <!-- Standart Paket -->
            <div class="relative flex flex-col justify-between h-full transition-all duration-300 bg-white shadow-md rounded-3xl pricing-card hover:shadow-xl"
                #pricingCard2>
                <!-- İndirim Etiketi -->
                <div class="absolute top-4 right-4">
                    <div class="px-3 py-1 text-sm font-medium bg-blue-100 rounded-full text-brand-blue-600">
                        {{ t('pricing.packages.standard.discount_label') }}
                    </div>
                </div>

                <!-- Kart İçeriği -->
                <div class="flex-grow p-6">
                    <!-- Orijinal Fiyat -->
                    <div class="flex items-center mb-1">
                        <p class="text-lg text-gray-400 line-through">{{ t('pricing.packages.standard.original_price') }}</p>
                    </div>

                    <!-- Güncel Fiyat -->
                    <div class="flex items-center mb-4">
                        <h3 class="text-4xl font-bold text-brand-blue-600">{{ t('pricing.packages.standard.discounted_price') }}</h3>
                    </div>

                    <!-- Paket Adı -->
                    <h4 class="mb-3 text-2xl font-bold text-gray-900">{{ t('pricing.packages.standard.title') }}</h4>

                    <!-- Krediler -->
                    <div class="flex items-center mb-3">
                        <img src="assets/icons/istacoin.svg" alt="kredi" class="w-8 h-8 mr-2">
                        <span class="text-xl font-semibold text-gray-800">{{ t('pricing.packages.standard.credits') }}</span>
                    </div>

                    <!-- Açıklama -->
                    <p class="mb-6 text-gray-600">{{ t('pricing.packages.standard.description') }}</p>

                    <!-- Özellikler Listesi -->
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.standard.features')[0] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.standard.features')[1] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.standard.features')[2] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.standard.features')[3] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-brand-blue-500">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-blue-500"></ng-icon>
                            </span>
                            <span class="text-gray-600">{{ t('pricing.packages.standard.features')[4] }}</span>
                        </li>
                    </ul>
                </div>

                <!-- Satın Al Butonu -->
                <div class="p-6 ">

                    <a href="/signup" class="flex items-center justify-center w-full mx-auto primary-blue-button mx">
                        <ng-icon name="lucideArrowRight" class="mr-2"></ng-icon>
                        {{ t('pricing.buy_button') }}
                    </a>
                </div>
            </div>

            <!-- Özel Paket -->
            <div class="relative flex flex-col justify-between h-full text-white transition-all duration-300 shadow-md rounded-3xl pricing-card hover:shadow-xl bg-brand-blue-500"
                #pricingCard3>
                <!-- Öne Çıkan Etiket -->
                <div class="flex items-center justify-center mt-4">
                    <div class="px-4 py-1 text-sm font-medium bg-white rounded-full text-brand-blue-600">
                        {{ t('pricing.packages.custom.title') }}
                    </div>
                </div>

                <!-- Kart İçeriği -->
                <div class="flex-grow p-6">
                    <!-- Güncel Fiyat -->
                    <div class="flex items-center mb-4">
                        <h3 class="text-4xl font-bold text-white">{{ t('pricing.packages.custom.price') }}<span class="text-lg">{{ t('pricing.packages.custom.per_credit') }}</span></h3>
                    </div>


                    <!-- Krediler -->
                    <div class="flex items-center mb-3">
                        <img src="assets/icons/istacoin.svg" alt="kredi" class="w-8 h-8 mr-2">
                        <span class="text-xl font-semibold text-white">{{ t('pricing.packages.custom.credits') }}</span>
                    </div>

                    <!-- Açıklama -->
                    <p class="mb-6 text-blue-100">{{ t('pricing.packages.custom.description') }}</p>

                    <!-- Özellikler Listesi -->
                    <ul class="space-y-3 ">
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-white">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-white"></ng-icon>
                            </span>
                            <span class="text-blue-100">{{ t('pricing.packages.custom.features')[0] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-white">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-white"></ng-icon>
                            </span>
                            <span class="text-blue-100">{{ t('pricing.packages.custom.features')[1] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-white">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-white"></ng-icon>
                            </span>
                            <span class="text-blue-100">{{ t('pricing.packages.custom.features')[2] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-white">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-white"></ng-icon>
                            </span>
                            <span class="text-blue-100">{{ t('pricing.packages.custom.features')[3] }}</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 w-5 h-5 mr-2 text-white">
                                <ng-icon name="lucideCheck" class="w-5 h-5 text-white"></ng-icon>
                            </span>
                            <span class="text-blue-100">{{ t('pricing.packages.custom.features')[4] }}</span>
                        </li>
                    </ul>
                </div>

                <!-- Satın Al Butonu -->
                <div class="p-6">

                    <a href="/signup" class="flex items-center justify-center w-full mx-auto secondary-blue-button mx">
                        <ng-icon name="lucideArrowRight" class="mr-2"></ng-icon>
                        {{ t('pricing.buy_button') }}
                    </a>
                </div>
            </div>
        </div>

        <!-- İletişim Kutusu -->
        <div class="p-1 mt-16 rounded-3xl bg-gradient-to-r from-brand-blue-400 to-brand-blue-500">
            <div
                class="flex flex-col items-center p-8 text-center bg-white md:flex-row md:text-left md:justify-between rounded-3xl">
                <div class="flex items-start">
                    <ng-icon name="lucideBuilding2" class="mr-3 text-3xl text-brand-blue-500"></ng-icon>
                    <div>
                        <h3 class="mb-2 text-2xl font-bold text-gray-900">{{ t('pricing.corporate.title') }}</h3>
                        <p class="text-gray-600">{{ t('pricing.corporate.description') }}</p>
                    </div>
                </div>
                <button
                    (click)="openContactUs()"
                    class="px-6 py-3 mt-4 font-medium text-white transition-all duration-300 rounded-3xl md:mt-0 bg-brand-blue-500 hover:bg-brand-blue-600 hover:shadow-lg hover:shadow-blue-200">
                    {{ t('pricing.corporate.contact_button') }}
                </button>
            </div>
        </div>
    </div>
</section>
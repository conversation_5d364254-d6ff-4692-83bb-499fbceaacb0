// Pricing Section Styles

// Card styles
.pricing-card {
    position: relative;
    isolation: isolate;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    &:hover {
      transform: translateY(-7px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    // Highlight effect for the featured card
    &.featured-card {
      position: relative;

      
      &::after {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          90deg,
          rgba(255,255,255,0) 0%,
          rgba(255,255,255,0.05) 25%,
          rgba(255,255,255,0.1) 50%,
          rgba(255,255,255,0.05) 75%,
          rgba(255,255,255,0) 100%
        );
        transform: rotate(30deg);
        animation: shine 8s linear infinite;
        z-index: -1;
      }
    }
  }
  
  @keyframes shine {
    0% {
      transform: rotate(30deg) translateX(-100%);
    }
    100% {
      transform: rotate(30deg) translateX(100%);
    }
  }
  
  // Original price strike-through style
  .line-through {
    position: relative;
    display: inline-block;
    
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      width: 100%;
      height: 1px;
      background-color: currentColor;
    }
  }
  
  // Feature icons shine effect
  .pricing-card:hover ng-icon {
    animation: iconPulse 1.5s ease infinite;
  }
  
  @keyframes iconPulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  // Badge styling
  .px-3.py-1.rounded-full {
    position: relative;
    
    &::before {
      content: "";
      position: absolute;
      inset: -2px;
      border-radius: 9999px;
      background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.3));
      z-index: -1;
      opacity: 0.3;
      animation: badgePulse 2s ease-in-out infinite;
    }
  }
  
  @keyframes badgePulse {
    0% {
      transform: scale(1);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.15;
    }
    100% {
      transform: scale(1);
      opacity: 0.3;
    }
  }
  
  // Button hover effects
  .pricing-section button.w-full {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%, -50%);
      transform-origin: 50% 50%;
    }
    
    &:focus:not(:active)::after {
      animation: ripple 1s ease-out;
    }
  }
  
  @keyframes ripple {
    0% {
      transform: scale(0, 0);
      opacity: 0.5;
    }
    20% {
      transform: scale(25, 25);
      opacity: 0.3;
    }
    100% {
      opacity: 0;
      transform: scale(40, 40);
    }
  }
  
  // FAQ accordion styling
  .faq-item {
    overflow: hidden;
    
    .faq-question {
      cursor: pointer;
    }
    
    .faq-answer {
      overflow: hidden;
      will-change: height, opacity;
    }
    
    .faq-icon {
      transition: transform 0.3s ease;
    }
    
    &:hover {
      .faq-question h4 {
        color: #2563EB; // brand-blue-600
      }
    }
  }
  
  // Calculator button styles
  .calculator-button {
    position: relative;
    transition: all 0.3s ease;
    
    &::before {
      content: "";
      position: absolute;
      inset: -3px;
      border-radius: 0.5rem;
      background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.3));
      z-index: -1;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover::before {
      opacity: 1;
    }
  }
  
  // Package badge positioning
  .transform.-translate-x-1\/2.-translate-y-1\/2 {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  
  // Responsive styles
  @media (max-width: 768px) {
    .pricing-cards {
      grid-template-columns: 1fr;
    }
    
    .pricing-card {
      margin-bottom: 1.5rem;
      
      &.featured-card {
        order: -1; // Move featured card to the top on mobile
      }
    }
  }
  
  // Accessibility enhancements
  button:focus, 
  .faq-question:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
  
  // Optional: Support for reduced motion preferences
  @media (prefers-reduced-motion: reduce) {
    .pricing-card,
    .featured-card::after,
    .calculator-button,
    button::after {
      transition: none !important;
      animation: none !important;
    }
  }
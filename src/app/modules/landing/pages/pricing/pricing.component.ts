import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Dialog } from '@angular/cdk/dialog';
import { ContactUsComponent } from '@app/modules/payment/dialogs/contact-us/contact-us.component';

@Component({
  selector: 'app-pricing',
  templateUrl: './pricing.component.html',
  styleUrls: ['./pricing.component.scss']
})
export class PricingComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('pricingCard1') pricingCard1!: ElementRef;
  @ViewChild('pricingCard2') pricingCard2!: ElementRef;
  @ViewChild('pricingCard3') pricingCard3!: ElementRef;
  @ViewChild('pricingCard4') pricingCard4!: ElementRef;
  @ViewChild('faqItem1') faqItem1!: ElementRef;
  @ViewChild('faqItem2') faqItem2!: ElementRef;
  @ViewChild('faqItem3') faqItem3!: ElementRef;
  @ViewChild('faqItem4') faqItem4!: ElementRef;

  constructor(private dialog: Dialog) {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initPricingCardsAnimation();
      this.initFaqAccordion();
      this.setupCalculatorButton();
    }, 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }

  // Open contact us dialog
  openContactUs(): void {
    this.dialog.open(ContactUsComponent);
  }

  private initPricingCardsAnimation(): void {
    const pricingCards = [
      this.pricingCard1?.nativeElement,
      this.pricingCard2?.nativeElement,
      this.pricingCard3?.nativeElement,
      this.pricingCard4?.nativeElement
    ].filter(card => card);

    // Initial state
    gsap.set(pricingCards, { y: 50, opacity: 0 });

    // Staggered entrance animation
    ScrollTrigger.create({
      trigger: '.pricing-cards',
      start: "top bottom-=100",
      onEnter: () => {
        gsap.to(pricingCards, {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: "power2.out"
        });
      },
      once: true
    });

    // Hover effects for featured card
    const featuredCard = this.pricingCard3?.nativeElement;
    if (featuredCard) {
      featuredCard.addEventListener('mouseenter', () => {
        gsap.to(featuredCard, {
          y: -10,
          scale: 1.02,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      featuredCard.addEventListener('mouseleave', () => {
        gsap.to(featuredCard, {
          y: 0,
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    }

    // Shine effect animation for the featured card
    if (featuredCard) {
      const shineAnimation = () => {
        const tl = gsap.timeline({ repeat: -1, repeatDelay: 5 });

        tl.to(featuredCard, {
          backgroundImage: 'linear-gradient(135deg, rgba(59, 130, 246, 1) 0%, rgba(37, 99, 235, 1) 100%)',
          duration: 2,
          ease: "sine.inOut"
        });

        tl.to(featuredCard, {
          backgroundImage: 'linear-gradient(135deg, rgba(37, 99, 235, 1) 0%, rgba(59, 130, 246, 1) 100%)',
          duration: 2,
          ease: "sine.inOut"
        });

        return tl;
      };

      shineAnimation();
    }
  }

  private initFaqAccordion(): void {
    const faqItems = [
      this.faqItem1?.nativeElement,
      this.faqItem2?.nativeElement,
      this.faqItem3?.nativeElement,
      this.faqItem4?.nativeElement
    ].filter(item => item);

    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      const answer = item.querySelector('.faq-answer');
      const icon = item.querySelector('.faq-icon');

      if (question && answer && icon) {
        // Set initial state
        gsap.set(answer, { height: 0, opacity: 0, display: 'none' });

        // Add click event
        question.addEventListener('click', () => {
          const isOpen = answer.classList.contains('active');

          // Close all other answers
          faqItems.forEach(otherItem => {
            const otherAnswer = otherItem.querySelector('.faq-answer');
            const otherIcon = otherItem.querySelector('.faq-icon');

            if (otherAnswer && otherAnswer !== answer && otherAnswer.classList.contains('active')) {
              otherAnswer.classList.remove('active');
              gsap.to(otherAnswer, {
                height: 0,
                opacity: 0,
                duration: 0.3,
                ease: "power2.out",
                onComplete: () => {
                  gsap.set(otherAnswer, { display: 'none' });
                }
              });

              // Rotate icon back
              gsap.to(otherIcon, {
                rotation: 0,
                duration: 0.3,
                ease: "power2.out"
              });
            }
          });

          // Toggle current answer
          if (!isOpen) {
            answer.classList.add('active');

            // First set display to block but keep height 0
            gsap.set(answer, { display: 'block', height: 'auto' });

            // Get the height of the content
            const height = answer.offsetHeight;

            // Set height back to 0 and then animate to the full height
            gsap.fromTo(answer,
              { height: 0, opacity: 0 },
              {
                height: height,
                opacity: 1,
                duration: 0.3,
                ease: "power2.out"
              }
            );

            // Rotate icon
            gsap.to(icon, {
              rotation: 180,
              duration: 0.3,
              ease: "power2.out"
            });
          } else {
            answer.classList.remove('active');

            // Animate to height 0
            gsap.to(answer, {
              height: 0,
              opacity: 0,
              duration: 0.3,
              ease: "power2.out",
              onComplete: () => {
                gsap.set(answer, { display: 'none' });
              }
            });

            // Rotate icon back
            gsap.to(icon, {
              rotation: 0,
              duration: 0.3,
              ease: "power2.out"
            });
          }
        });
      }
    });

    // Animate FAQ items on scroll
    ScrollTrigger.create({
      trigger: '.mt-20', // FAQ section container
      start: "top bottom-=100",
      onEnter: () => {
        gsap.fromTo(faqItems,
          { y: 30, opacity: 0 },
          {
            y: 0,
            opacity: 1,
            stagger: 0.1,
            duration: 0.6,
            ease: "power2.out"
          }
        );
      },
      once: true
    });
  }

  private setupCalculatorButton(): void {
    const calculatorButton = document.querySelector('.calculator-button');

    if (calculatorButton) {
      // Pop animation on hover
      calculatorButton.addEventListener('mouseenter', () => {
        gsap.to(calculatorButton, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      calculatorButton.addEventListener('mouseleave', () => {
        gsap.to(calculatorButton, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      // Pulse animation when button is visible
      ScrollTrigger.create({
        trigger: calculatorButton,
        start: "top bottom-=100",
        onEnter: () => {
          const tl = gsap.timeline({ repeat: 2 });

          tl.to(calculatorButton, {
            scale: 1.05,
            boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.4)",
            duration: 0.5,
            ease: "power2.out"
          });

          tl.to(calculatorButton, {
            scale: 1,
            boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
            duration: 0.5,
            ease: "power2.in"
          });
        },
        once: true
      });

      // Add click handler for calculator modal
      calculatorButton.addEventListener('click', () => {

        // Click animation
        gsap.to(calculatorButton, {
          scale: 0.95,
          duration: 0.1,
          yoyo: true,
          repeat: 1,
          ease: "power2.inOut"
        });
      });
    }

    // Animate contact box
    const contactBox = document.querySelector('.p-1.mt-16');
    if (contactBox) {
      ScrollTrigger.create({
        trigger: contactBox,
        start: "top bottom-=50",
        onEnter: () => {
          gsap.fromTo(contactBox,
            { y: 30, opacity: 0 },
            {
              y: 0,
              opacity: 1,
              duration: 0.8,
              ease: "power2.out"
            }
          );
        },
        once: true
      });
    }
  }
}
// 3D Stats Section Styles

// Perspective container for 3D effect
.perspective-container {
  perspective: 1000px;
}

// 3D Grid background
.grid-bg {
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.5) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.5) 1px, transparent 1px);
  background-size: 40px 40px;
  transform: perspective(1000px) rotateX(60deg);
  transform-origin: center top;
  opacity: 0.3;
}

// Stat card styling
.tilt-card-wrapper {
  transform-style: preserve-3d;

  .tilt-card {
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backface-visibility: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 5px rgba(0, 71, 219, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.2);

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
      border-radius: inherit;
      pointer-events: none;
      z-index: 1;
    }

    // 3D icon style
    .stat-icon-3d {
      position: relative;
      transform: translateZ(20px);
      filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.3));
    }

    // Value style with text shadow for 3D effect
    .stat-value {
      position: relative;
      transform: translateZ(15px);

      span {
        text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);

        &.counting {
          animation: glow 2s ease-out forwards;
        }
      }
    }

    // Label with subtle 3D effect
    .stat-label {
      position: relative;
      transform: translateZ(10px);
    }
  }
}

// Glow animation for counting numbers
@keyframes glow {
  0% {
    text-shadow: 0 0 5px rgba(25, 196, 128, 0);
    opacity: 0.5;
  }
  50% {
    text-shadow: 0 0 15px rgba(25, 196, 128, 0.7);
    opacity: 1;
  }
  100% {
    text-shadow: 0 0 5px rgba(25, 196, 128, 0.3);
    opacity: 1;
  }
}

// Slow ping animation for icon in feature highlight
@keyframes ping-slow {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

.animate-ping-slow {
  animation: ping-slow 3s ease-in-out infinite;
}

// Media queries for responsive design
@media (max-width: 768px) {
  .stats-3d-section {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .tilt-card-wrapper {
    margin-bottom: 1rem;

    .tilt-card {
      // Disable 3D effect on mobile for better performance
      transform: none !important;

      .stat-value, .stat-label, .stat-icon-3d {
        transform: none !important;
      }
    }
  }

  .grid-bg {
    background-size: 20px 20px;
  }
}

// Interactive hover effects
.tilt-card-wrapper:hover {
  z-index: 10;

  .stat-icon-3d {
    animation: bounce 1s ease infinite alternate;
  }
}

@keyframes bounce {
  from { transform: translateZ(20px); }
  to { transform: translateZ(30px); }
}

// Feature highlight
.max-w-4xl > div {
  position: relative;
  overflow: hidden;
}
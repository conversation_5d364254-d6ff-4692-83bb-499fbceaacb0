import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-stats',
  templateUrl: './stats.component.html',
  styleUrls: ['./stats.component.scss']
})
export class StatsComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('statCounter1') statCounter1!: ElementRef;
  @ViewChild('statCounter2') statCounter2!: ElementRef;
  @ViewChild('statCounter3') statCounter3!: ElementRef;
  @ViewChild('statCounter4') statCounter4!: ElementRef;

  private statValues = [
    { element: 'statCounter1', start: 0, end: 85, suffix: '%', duration: 3 },
    { element: 'statCounter2', start: 0, end: 5000, suffix: '+', duration: 4 },
    { element: 'statCounter3', start: 0, end: 50000, suffix: '+', duration: 3 },
    { element: 'statCounter4', start: 0, end: 100, suffix: '%', duration: 4 }
  ];

  constructor() {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => this.initCounterAnimations(), 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }

  private initCounterAnimations(): void {
    this.statValues.forEach(stat => {
      const el = this[stat.element];
      if (!el?.nativeElement) return;

      // Create scroll trigger for each counter
      ScrollTrigger.create({
        trigger: el.nativeElement,
        start: "top bottom-=100",
        onEnter: () => {
          // Number counter animation
          let value = { val: stat.start };
          gsap.to(value, {
            val: stat.end,
            duration: stat.duration,
            ease: "power2.out",
            onUpdate: () => {
              // Format the number appropriately
              const formatted = Number.isInteger(stat.end)
                ? Math.floor(value.val).toLocaleString()
                : value.val.toFixed(1);
              el.nativeElement.textContent = formatted;
            }
          });

          // Add counting class for CSS animation
          el.nativeElement.classList.add('counting');
        },
        once: true
      });
    });
  }
}
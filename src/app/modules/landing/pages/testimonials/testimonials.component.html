<section class="relative px-4 py-16 overflow-hidden testimonials-section bg-brand-blue-600">
    <!-- Decorative Elements -->
    <div class="absolute w-64 h-64 rounded-full -top-24 -left-24 bg-brand-blue-400/20 blur-3xl"></div>
    <div class="absolute rounded-full -bottom-32 -right-32 w-96 h-96 bg-brand-green-500/10 blur-3xl"></div>
    
    <!-- Mesh Grid Background -->
    <div class="absolute inset-0 z-0 opacity-10 grid-bg-testimonials"></div>
    
    <!-- Content Container -->
    <div class="relative z-10 max-w-6xl mx-auto">
        <!-- Section Header -->
        <div class="mb-16 text-center">
            <h2 class="mb-2 text-4xl font-bold text-white">Kullanıcılarımız Ne Diyor?</h2>
            <div class="w-32 h-1 mx-auto mb-6 rounded-full bg-brand-green-500"></div>
            <div class="max-w-2xl mx-auto">
                <p class="text-lg text-blue-100">
                    Akademisyenler ve araştırmacıların istabot deneyimleri
                </p>
            </div>
        </div>

        <!-- Testimonial Slider -->
        <div class="relative testimonial-slider">
            <!-- Slider Navigation Arrows -->
            <button class="absolute left-0 z-20 flex items-center justify-center w-10 h-10 -ml-5 -translate-y-1/2 bg-white rounded-full shadow-lg text-brand-blue-600 top-1/2 hover:bg-brand-blue-50 slider-prev" #sliderPrev>
                <ng-icon name="lucideChevronLeft" class="text-xl"></ng-icon>
            </button>
            
            <button class="absolute right-0 z-20 flex items-center justify-center w-10 h-10 -mr-5 -translate-y-1/2 bg-white rounded-full shadow-lg text-brand-blue-600 top-1/2 hover:bg-brand-blue-50 slider-next" #sliderNext>
                <ng-icon name="lucideChevronRight" class="text-xl"></ng-icon>
            </button>
            
            <!-- Slider Track -->
            <div class="overflow-hidden slider-track">
                <div class="flex transition-transform duration-500 slider-container" #sliderContainer>
                    <!-- Testimonial 1 -->
                    <div class="flex-shrink-0 w-full md:px-4 lg:w-1/2 testimonial-slide">
                        <div class="h-full p-1 rounded-2xl bg-gradient-to-r from-brand-blue-400 to-brand-green-500">
                            <div class="flex flex-col h-full p-8 bg-white rounded-xl">
                                <div class="flex flex-col mb-6 md:flex-row md:items-center">
                                    <div class="flex-shrink-0 mb-4 md:mb-0 md:mr-4">
                                        <div class="relative">
                                            <div class="w-16 h-16 overflow-hidden rounded-full">
                                                <img src="https://picsum.photos/seed/istabot1/120/120" alt="Dr. Ahmet Yılmaz" class="object-cover w-full h-full">
                                            </div>
                                            <div class="absolute bottom-0 right-0 flex items-center justify-center w-6 h-6 rounded-full bg-brand-blue-500">
                                                <ng-icon name="lucideCheck" class="text-xs text-white"></ng-icon>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-900">Dr. Ahmet Yılmaz</h3>
                                        <p class="text-sm text-gray-600">Kardiyoloji Uzmanı, Ankara Üniversitesi Tıp Fakültesi</p>
                                    </div>
                                </div>
                                
                                <div class="flex mb-4">
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                </div>
                                
                                <p class="mb-6 text-gray-700 grow">
                                    "İstabot ile tez araştırmam için gereken tüm analizleri tek günde tamamladım. SPSS ile uğraşırken sadece veri hazırlama bile günlerimi alıyordu. Platform son derece kullanıcı dostu ve sonuçları akademik yayınlar için mükemmel formatta sunuyor. Kesinlikle tüm meslektaşlarıma tavsiye ediyorum."
                                </p>
                                
                                <div class="pt-4 mt-auto text-sm text-gray-500 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <ng-icon name="lucideCalendar" class="mr-1 text-brand-blue-500"></ng-icon>
                                        <span>8 ay boyunca aktif kullanıcı</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Testimonial 2 -->
                    <div class="flex-shrink-0 w-full md:px-4 lg:w-1/2 testimonial-slide">
                        <div class="h-full p-1 rounded-2xl bg-gradient-to-r from-brand-blue-400 to-brand-green-500">
                            <div class="flex flex-col h-full p-8 bg-white rounded-xl">
                                <div class="flex flex-col mb-6 md:flex-row md:items-center">
                                    <div class="flex-shrink-0 mb-4 md:mb-0 md:mr-4">
                                        <div class="relative">
                                            <div class="w-16 h-16 overflow-hidden rounded-full">
                                                <img src="https://picsum.photos/seed/istabot2/120/120" alt="Doç. Dr. Ayşe Kaya" class="object-cover w-full h-full">
                                            </div>
                                            <div class="absolute bottom-0 right-0 flex items-center justify-center w-6 h-6 rounded-full bg-brand-blue-500">
                                                <ng-icon name="lucideCheck" class="text-xs text-white"></ng-icon>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-900">Doç. Dr. Ayşe Kaya</h3>
                                        <p class="text-sm text-gray-600">Psikoloji Bölümü, İstanbul Üniversitesi</p>
                                    </div>
                                </div>
                                
                                <div class="flex mb-4">
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                </div>
                                
                                <p class="mb-6 text-gray-700 grow">
                                    "İstabot'un en sevdiğim özelliği APA formatında hazır raporlar sunması. Öğrencilerime de tavsiye ediyorum çünkü verileri analize hazırlamak ve sonuçları yorumlamak için gerekli teknik bilgiye sahip olmasalar bile araştırmalarını profesyonel şekilde tamamlayabiliyorlar. Kullanım kolaylığı açısından piyasadaki en iyi çözüm."
                                </p>
                                
                                <div class="pt-4 mt-auto text-sm text-gray-500 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <ng-icon name="lucideCalendar" class="mr-1 text-brand-blue-500"></ng-icon>
                                        <span>1 yıldır aktif kullanıcı</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Testimonial 3 -->
                    <div class="flex-shrink-0 w-full md:px-4 lg:w-1/2 testimonial-slide">
                        <div class="h-full p-1 rounded-2xl bg-gradient-to-r from-brand-blue-400 to-brand-green-500">
                            <div class="flex flex-col h-full p-8 bg-white rounded-xl">
                                <div class="flex flex-col mb-6 md:flex-row md:items-center">
                                    <div class="flex-shrink-0 mb-4 md:mb-0 md:mr-4">
                                        <div class="relative">
                                            <div class="w-16 h-16 overflow-hidden rounded-full">
                                                <img src="https://picsum.photos/seed/istabot3/120/120" alt="Prof. Dr. Mehmet Demir" class="object-cover w-full h-full">
                                            </div>
                                            <div class="absolute bottom-0 right-0 flex items-center justify-center w-6 h-6 rounded-full bg-brand-blue-500">
                                                <ng-icon name="lucideCheck" class="text-xs text-white"></ng-icon>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-900">Prof. Dr. Mehmet Demir</h3>
                                        <p class="text-sm text-gray-600">Halk Sağlığı Anabilim Dalı, Ege Üniversitesi</p>
                                    </div>
                                </div>
                                
                                <div class="flex mb-4">
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                </div>
                                
                                <p class="mb-6 text-gray-700 grow">
                                    "30 yıldır akademideyim ve istatistik analiz araçlarının gelişimini yakından takip ettim. İstabot, kullanım kolaylığı ve verimlilik açısından devrim niteliğinde. Çok sayıda öğrencim ve meslektaşım artık rutin analizleri için istabot kullanıyor. Sonuçların doğruluğunu SPSS ile karşılaştırdık ve tamamen güvenilir olduğunu gördük."
                                </p>
                                
                                <div class="pt-4 mt-auto text-sm text-gray-500 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <ng-icon name="lucideCalendar" class="mr-1 text-brand-blue-500"></ng-icon>
                                        <span>1.5 yıldır aktif kullanıcı</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Testimonial 4 -->
                    <div class="flex-shrink-0 w-full md:px-4 lg:w-1/2 testimonial-slide">
                        <div class="h-full p-1 rounded-2xl bg-gradient-to-r from-brand-blue-400 to-brand-green-500">
                            <div class="flex flex-col h-full p-8 bg-white rounded-xl">
                                <div class="flex flex-col mb-6 md:flex-row md:items-center">
                                    <div class="flex-shrink-0 mb-4 md:mb-0 md:mr-4">
                                        <div class="relative">
                                            <div class="w-16 h-16 overflow-hidden rounded-full">
                                                <img src="https://picsum.photos/seed/istabot4/120/120" alt="Dr. Zeynep Özkan" class="object-cover w-full h-full">
                                            </div>
                                            <div class="absolute bottom-0 right-0 flex items-center justify-center w-6 h-6 rounded-full bg-brand-blue-500">
                                                <ng-icon name="lucideCheck" class="text-xs text-white"></ng-icon>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-900">Dr. Zeynep Özkan</h3>
                                        <p class="text-sm text-gray-600">İç Hastalıkları Uzmanı, Hacettepe Üniversitesi</p>
                                    </div>
                                </div>
                                
                                <div class="flex mb-4">
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStar" class="text-yellow-500"></ng-icon>
                                    <ng-icon name="lucideStarHalf" class="text-yellow-500"></ng-icon>
                                </div>
                                
                                <p class="mb-6 text-gray-700 grow">
                                    "Klinik araştırmalarım için istatistik analizlerim çok zaman alıyordu. İstabot'u keşfettikten sonra analizlerimi çok daha hızlı tamamlıyorum. Özellikle hasta verilerinin korunmasına yönelik güvenlik özellikleri ve KVKK uyumluluğu klinik araştırmalar için ideal. Müşteri destek ekibi de sorularıma çok hızlı yanıt veriyor."
                                </p>
                                
                                <div class="pt-4 mt-auto text-sm text-gray-500 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <ng-icon name="lucideCalendar" class="mr-1 text-brand-blue-500"></ng-icon>
                                        <span>6 aydır aktif kullanıcı</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Slider Pagination Dots -->
            <div class="flex justify-center mt-8 space-x-2 pagination-dots" #paginationDots>
                <button class="w-3 h-3 bg-blue-200 rounded-full active" data-index="0"></button>
                <button class="w-3 h-3 rounded-full bg-blue-200/50" data-index="1"></button>
                <button class="w-3 h-3 rounded-full bg-blue-200/50" data-index="2"></button>
            </div>
        </div>

        <!-- Stats Counter Section -->
        <div class="grid grid-cols-1 gap-6 px-4 py-10 mt-16 -mx-4 md:grid-cols-3 stats-section bg-blue-900/40 backdrop-blur-sm">
            <!-- Stat 1: Happy Users -->
            <div class="flex flex-col items-center text-center">
                <div class="flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-blue-800/50">
                    <ng-icon name="lucideSmile" class="text-3xl text-brand-green-500"></ng-icon>
                </div>
                <h3 class="mb-1 text-3xl font-bold text-white counter-element" #counterHappyUsers>5000+</h3>
                <p class="text-blue-100">Mutlu Kullanıcı</p>
            </div>
            
            <!-- Stat 2: Universities -->
            <div class="flex flex-col items-center text-center">
                <div class="flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-blue-800/50">
                    <ng-icon name="lucideGraduationCap" class="text-3xl text-brand-green-500"></ng-icon>
                </div>
                <h3 class="mb-1 text-3xl font-bold text-white counter-element" #counterUniversities>150+</h3>
                <p class="text-blue-100">Üniversite ve Kurum</p>
            </div>
            
            <!-- Stat 3: Completed Analyses -->
            <div class="flex flex-col items-center text-center">
                <div class="flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-blue-800/50">
                    <ng-icon name="lucideFileCheck" class="text-3xl text-brand-green-500"></ng-icon>
                </div>
                <h3 class="mb-1 text-3xl font-bold text-white counter-element" #counterAnalyses>250000+</h3>
                <p class="text-blue-100">Tamamlanan Analiz</p>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="p-1 mt-16 rounded-2xl bg-gradient-to-r from-brand-blue-400 to-brand-green-500">
            <div class="p-8 bg-white rounded-xl">
                <div class="flex flex-col items-center text-center">
                    <h3 class="mb-4 text-2xl font-bold text-brand-blue-600">Binlerce Akademisyen Gibi Siz de Katılın</h3>
                    <p class="mb-6 text-gray-700">
                        İstabot ile istatistik analizlerinizi hızlandırın ve zamandan tasarruf edin
                    </p>
                    <div class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                        <button class="px-6 py-3 font-medium text-white transition-transform duration-300 rounded-lg bg-brand-blue-500 hover:bg-brand-blue-600 hover:scale-105">
                            <span>Ücretsiz Hesap Oluştur</span>
                        </button>
                        <button class="px-6 py-3 font-medium transition-transform duration-300 border rounded-lg text-brand-blue-500 border-brand-blue-200 hover:bg-brand-blue-50 hover:scale-105">
                            <span>Daha Fazla Bilgi</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
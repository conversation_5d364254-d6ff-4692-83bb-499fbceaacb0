// Testimonials Section Styles

// Background grid
.grid-bg-testimonials {
    background-image: 
      linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 40px 40px;
    transform: perspective(1000px) rotateX(60deg) scale(1.2);
    transform-origin: center top;
  }
  
  // Testimonial slider styles
  .testimonial-slider {
    position: relative;
    overflow: visible;
    
    .slider-track {
      overflow: hidden;
      margin: 0 20px; // Space for navigation arrows
    }
    
    .slider-container {
      display: flex;
      will-change: transform;
    }
    
    .testimonial-slide {
      padding: 15px;
      flex-shrink: 0;
      
      // Ensure all cards are the same height
      > div {
        height: 100%;
        
        > div {
          display: flex;
          flex-direction: column;
          height: 100%;
        }
      }
    }
    
    // Navigation buttons hover effect
    .slider-prev, .slider-next {
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      
      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
    
    // Pagination dots
    .pagination-dots {
      button {
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.2);
        }
        
        &.active {
          transform: scale(1.2);
        }
      }
    }
  }
  
  // Animation for slide entrance
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .testimonial-slide {
    animation: slideIn 0.5s ease forwards;
  }
  
  // Card hover effects
  .testimonial-slide > div {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }
  }
  
  // Counter animation
  .counter-element {
    &.counting {
      background: linear-gradient(90deg, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0.25) 25%, rgba(255,255,255,0.25) 50%, rgba(255,255,255,0.25) 75%, rgba(255,255,255,0.7) 100%);
      background-size: 400% 100%;
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
      animation: shimmer 2s infinite;
    }
  }
  
  @keyframes shimmer {
    0% {
      background-position: 100% 0;
    }
    100% {
      background-position: -100% 0;
    }
  }
  
  // Verified badge pulse effect
  .absolute.bottom-0.right-0.flex.items-center.justify-center.w-6.h-6.rounded-full {
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.3);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }
  
  // Stats section with growing numbers
  .stats-section {
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(45deg, rgba(0, 60, 189, 0.2), rgba(25, 196, 128, 0.2));
      opacity: 0.3;
    }
  }
  
  // Call to action hover effects
  .p-1.mt-16 button {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  // Media queries for responsive design
  @media (max-width: 768px) {
    .testimonial-slider {
      .slider-track {
        margin: 0 30px; // More space for navigation arrows on mobile
      }
      
      .slider-prev {
        left: 10px;
      }
      
      .slider-next {
        right: 10px;
      }
    }
    
    .stats-section {
      grid-template-columns: 1fr;
      
      > div {
        margin-bottom: 2rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  // Accessibility enhancements
  .slider-prev:focus, 
  .slider-next:focus, 
  .pagination-dots button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
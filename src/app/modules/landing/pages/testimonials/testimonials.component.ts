import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
@Component({
  selector: 'app-testimonials',
  templateUrl: './testimonials.component.html',
  styleUrls: ['./testimonials.component.scss']
})
export class TestimonialsComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('sliderContainer') sliderContainer!: ElementRef;
  @ViewChild('sliderPrev') sliderPrev!: ElementRef;
  @ViewChild('sliderNext') sliderNext!: ElementRef;
  @ViewChild('paginationDots') paginationDots!: ElementRef;
  @ViewChild('counterHappyUsers') counterHappyUsers!: ElementRef;
  @ViewChild('counterUniversities') counterUniversities!: ElementRef;
  @ViewChild('counterAnalyses') counterAnalyses!: ElementRef;
  
  private currentSlide: number = 0;
  private slidesPerView: number = 1;
  private totalSlides: number = 4; // Total number of testimonial slides
  private slideWidth: number = 0;
  private autoplayInterval: any;
  
  constructor() {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initSlider();
      this.initCounters();
      this.startAutoplay();
      this.setupResponsive();
    }, 200);
  }

  ngOnDestroy(): void {
    // Clear autoplay interval
    if (this.autoplayInterval) {
      clearInterval(this.autoplayInterval);
    }
    
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }
  
  private initSlider(): void {
    if (!this.sliderContainer || !this.sliderPrev || !this.sliderNext || !this.paginationDots) return;
    
    // Set up responsive slides per view
    this.setupSlidesPerView();
    
    // Calculate slide width
    this.updateSlideWidth();
    
    // Set initial position
    this.goToSlide(0);
    
    // Add click events to navigation buttons
    this.sliderPrev.nativeElement.addEventListener('click', () => {
      this.prevSlide();
    });
    
    this.sliderNext.nativeElement.addEventListener('click', () => {
      this.nextSlide();
    });
    
    // Add click events to pagination dots
    const dots = this.paginationDots.nativeElement.querySelectorAll('button');
    dots.forEach((dot: HTMLElement, index: number) => {
      dot.addEventListener('click', () => {
        this.goToSlide(index);
      });
    });
    
    // Initial animation to fade in the slider
    const testimonialSlides = document.querySelectorAll('.testimonial-slide');
    gsap.fromTo(testimonialSlides, 
      { opacity: 0, y: 30 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8, 
        stagger: 0.2,
        ease: "power2.out",
        scrollTrigger: {
          trigger: '.testimonial-slider',
          start: "top bottom-=100"
        }
      }
    );
  }
  
  private setupSlidesPerView(): void {
    // Determine slides per view based on screen width
    if (window.innerWidth >= 1024) {
      this.slidesPerView = 2; // Desktop
    } else {
      this.slidesPerView = 1; // Mobile and Tablet
    }
    
    // Update pagination dots based on slides per view
    this.updatePaginationDots();
  }
  
  private updatePaginationDots(): void {
    if (!this.paginationDots) return;
    
    const dotsContainer = this.paginationDots.nativeElement;
    dotsContainer.innerHTML = '';
    
    // Calculate number of pages
    const pages = Math.ceil(this.totalSlides / this.slidesPerView);
    
    // Create dots
    for (let i = 0; i < pages; i++) {
      const dot = document.createElement('button');
      dot.className = `w-3 h-3 rounded-full ${i === 0 ? 'bg-blue-200 active' : 'bg-blue-200/50'}`;
      dot.setAttribute('data-index', i.toString());
      
      dot.addEventListener('click', () => {
        this.goToSlide(i);
      });
      
      dotsContainer.appendChild(dot);
    }
  }
  
  private updateSlideWidth(): void {
    if (!this.sliderContainer) return;
    
    const containerWidth = this.sliderContainer.nativeElement.offsetWidth;
    this.slideWidth = containerWidth / this.slidesPerView;
    
    // Update slide widths
    const slides = this.sliderContainer.nativeElement.querySelectorAll('.testimonial-slide');
    slides.forEach((slide: HTMLElement) => {
      slide.style.width = `${this.slideWidth}px`;
    });
  }
  
  private goToSlide(index: number): void {
    if (!this.sliderContainer || !this.paginationDots) return;
    
    // Ensure index is within bounds
    const maxIndex = Math.ceil(this.totalSlides / this.slidesPerView) - 1;
    index = Math.max(0, Math.min(index, maxIndex));
    
    this.currentSlide = index;
    
    // Calculate the translation value
    const translateX = -index * (this.slideWidth * this.slidesPerView);
    
    // Animate the slider
    gsap.to(this.sliderContainer.nativeElement, {
      x: translateX,
      duration: 0.5,
      ease: "power2.out"
    });
    
    // Update pagination dots
    const dots = this.paginationDots.nativeElement.querySelectorAll('button');
    dots.forEach((dot: HTMLElement, i: number) => {
      if (i === index) {
        dot.classList.add('bg-blue-200');
        dot.classList.add('active');
        dot.classList.remove('bg-blue-200/50');
      } else {
        dot.classList.remove('bg-blue-200');
        dot.classList.remove('active');
        dot.classList.add('bg-blue-200/50');
      }
    });
  }
  
  private nextSlide(): void {
    const maxIndex = Math.ceil(this.totalSlides / this.slidesPerView) - 1;
    const nextIndex = this.currentSlide === maxIndex ? 0 : this.currentSlide + 1;
    this.goToSlide(nextIndex);
  }
  
  private prevSlide(): void {
    const maxIndex = Math.ceil(this.totalSlides / this.slidesPerView) - 1;
    const prevIndex = this.currentSlide === 0 ? maxIndex : this.currentSlide - 1;
    this.goToSlide(prevIndex);
  }
  
  private startAutoplay(): void {
    this.autoplayInterval = setInterval(() => {
      this.nextSlide();
    }, 5000); // Change slide every 5 seconds
    
    // Pause autoplay on hover
    const slider = document.querySelector('.testimonial-slider');
    if (slider) {
      slider.addEventListener('mouseenter', () => {
        if (this.autoplayInterval) {
          clearInterval(this.autoplayInterval);
          this.autoplayInterval = null;
        }
      });
      
      slider.addEventListener('mouseleave', () => {
        if (!this.autoplayInterval) {
          this.startAutoplay();
        }
      });
    }
  }
  
  private setupResponsive(): void {
    // Handle window resize
    window.addEventListener('resize', () => {
      this.setupSlidesPerView();
      this.updateSlideWidth();
      this.goToSlide(0); // Reset to first slide on resize
    });
  }
  
  private initCounters(): void {
    const counters = [
      { element: this.counterHappyUsers, end: 5000, suffix: '+' },
      { element: this.counterUniversities, end: 150, suffix: '+' },
      { element: this.counterAnalyses, end: 250000, suffix: '+' }
    ];
    
    counters.forEach(counter => {
      if (!counter.element) return;
      
      // Set up counter animation on scroll
      ScrollTrigger.create({
        trigger: counter.element.nativeElement,
        start: "top bottom-=100",
        onEnter: () => {
          // Extract current number
          const currentText = counter.element.nativeElement.textContent;
          const currentNum = parseInt(currentText.replace(/\D/g, '') || '0');
          
          // Animate counter
          gsap.to({ value: currentNum }, {
            value: counter.end,
            duration: 2,
            ease: "power2.out",
            onUpdate: function() {
              const value = Math.round(this.targets()[0].value);
              counter.element.nativeElement.textContent = value.toLocaleString() + counter.suffix;
            }
          });
          
          // Add counting class for CSS animation
          counter.element.nativeElement.classList.add('counting');
        },
        once: true
      });
    });
  }
}
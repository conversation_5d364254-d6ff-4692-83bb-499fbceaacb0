// Use Cases Section Styles

// Card hover effects
.use-case-card {
    perspective: 1000px;
    height: 100%;
    
    &:hover {
      .rounded-2xl {
        transform: translateY(-5px);
      }
    }
    
    // Image hover effect with zoom
    .bg-cover {
      transition: transform 0.7s ease;
      will-change: transform;
    }
    
    // Content transition
    p {
      transition: color 0.3s ease;
    }
  }
  
  // Testimonial section styles
  .testimonial-avatar {
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      border-radius: 0.75rem;
      background: linear-gradient(45deg, rgba(59, 130, 246, 0.5), rgba(25, 196, 128, 0.5));
      z-index: -1;
      opacity: 0.7;
    }
  }
  
  // Quote style
  svg {
    filter: drop-shadow(0 4px 3px rgba(0, 0, 0, 0.07));
    will-change: transform;
  }
  
  // Industry items hover effects
  .industry-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .size-16 {
      transition: all 0.3s ease;
      will-change: transform, background-color;
    }
    
    h4 {
      transition: color 0.3s ease;
    }
  }
  
  // Gradient border animation for testimonial
  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  .p-1.mb-8, .p-1.rounded-2xl {
    background-size: 200% auto;
    animation: gradient-shift 3s ease infinite;
  }
  
  // Media queries for responsive design
  @media (max-width: 768px) {
    .use-case-card {
      margin-bottom: 1.5rem;
    }
    
    .industry-item {
      width: 50%;
    }
  }
  
  // Accessibility enhancements
  .industry-item:focus, 
  .use-case-card:focus-within {
    outline: 2px solid rgba(25, 196, 128, 0.6);
    outline-offset: 2px;
  }
  
  // Optional: Support for reduced motion preferences
  @media (prefers-reduced-motion: reduce) {
    .use-case-card, .industry-item, svg, .p-1.rounded-2xl {
      transition: none !important;
      animation: none !important;
      transform: none !important;
    }
  }
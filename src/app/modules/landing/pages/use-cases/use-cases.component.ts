import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy, ViewChildren, QueryList } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

@Component({
  selector: 'app-use-cases',
  templateUrl: './use-cases.component.html',
  styleUrls: ['./use-cases.component.scss']
})
export class UseCasesComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('useCase1') useCase1!: ElementRef;
  @ViewChild('useCase2') useCase2!: ElementRef;
  @ViewChild('useCase3') useCase3!: ElementRef;
  @ViewChild('useCase4') useCase4!: ElementRef;
  
  constructor() {
    gsap.registerPlugin(ScrollTrigger);
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Delay to ensure all elements are rendered
    setTimeout(() => {
      this.initUseCasesAnimation();
      this.initIndustryItemsAnimation();
      this.initTestimonialAnimation();
    }, 200);
  }

  ngOnDestroy(): void {
    // Kill all GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }
  
  private initUseCasesAnimation(): void {
    const useCaseCards = document.querySelectorAll('.use-case-card');
    
    // Initial state
    gsap.set(useCaseCards, { y: 40, opacity: 0 });
    
    // Staggered entry animation
    ScrollTrigger.create({
      trigger: '.use-cases-section',
      start: "top bottom-=100",
      onEnter: () => {
        gsap.to(useCaseCards, {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: "power2.out"
        });
      },
      once: true
    });
    
    // Add hover animations for each card
    useCaseCards.forEach(card => {
      const content = card.querySelector('p');
      const border = card.querySelector('.border');
      
      if (content && border) {
        card.addEventListener('mouseenter', () => {
          gsap.to(content, {
            color: '#2563EB', // brand-blue-600
            duration: 0.3
          });
          
          gsap.to(border, {
            borderColor: '#DBEAFE', // brand-blue-200
            duration: 0.3
          });
        });
        
        card.addEventListener('mouseleave', () => {
          gsap.to(content, {
            color: '#4B5563', // gray-600
            duration: 0.3
          });
          
          gsap.to(border, {
            borderColor: '#F3F4F6', // gray-100
            duration: 0.3
          });
        });
      }
    });
  }
  
  private initIndustryItemsAnimation(): void {
    const industryItems = document.querySelectorAll('.industry-item');
    
    // Initial state
    gsap.set(industryItems, { y: 20, opacity: 0 });
    
    // Staggered entry animation
    ScrollTrigger.create({
      trigger: '.industry-item',
      start: "top bottom-=50",
      onEnter: () => {
        gsap.to(industryItems, {
          y: 0,
          opacity: 1,
          duration: 0.5,
          stagger: 0.1,
          ease: "power2.out"
        });
      },
      once: true
    });
    
    // Add hover effects
    industryItems.forEach(item => {
      const icon = item.querySelector('.size-16');
      const title = item.querySelector('h4');
      
      if (icon && title) {
        item.addEventListener('mouseenter', () => {
          gsap.to(icon, {
            backgroundColor: '#DBEAFE', // brand-blue-200
            scale: 1.1,
            duration: 0.3
          });
          
          gsap.to(title, {
            color: '#2563EB', // brand-blue-600
            duration: 0.3
          });
        });
        
        item.addEventListener('mouseleave', () => {
          gsap.to(icon, {
            backgroundColor: '#EBF8FF', // brand-blue-100
            scale: 1,
            duration: 0.3
          });
          
          gsap.to(title, {
            color: '#1F2937', // gray-800
            duration: 0.3
          });
        });
      }
    });
  }
  
  private initTestimonialAnimation(): void {
    const testimonial = document.querySelector('.p-1.mb-8');
    if (!testimonial) return;
    
    // Initial state
    gsap.set(testimonial, { y: 30, opacity: 0 });
    
    // Animation on scroll
    ScrollTrigger.create({
      trigger: testimonial,
      start: "top bottom-=100",
      onEnter: () => {
        gsap.to(testimonial, {
          y: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out"
        });
      },
      once: true
    });
    
    // Quote icon gentle floating animation
    const quoteIcon = testimonial.querySelector('svg');
    if (quoteIcon) {
      gsap.to(quoteIcon, {
        rotation: 5,
        y: '-=5',
        duration: 2,
        ease: "sine.inOut",
        repeat: -1,
        yoyo: true
      });
    }
  }
}
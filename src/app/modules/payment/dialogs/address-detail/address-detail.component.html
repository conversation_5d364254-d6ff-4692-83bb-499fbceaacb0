<div class="w-full max-w-2xl p-8 pb-6 bg-white shadow-md rounded-3xl max-h-[calc(100vh-80px)] flex flex-col "
    *transloco="let t; read: 'shared.payment.address_detail'" [@slideInOut]="animationState">
    <!-- Header -->
    <div class="flex-none mb-4">
        <div class="flex items-center justify-between">
            <h2 class="text-2xl font-bold text-gray-900">
                {{isUpdate ? t('update_title') : t('new_title')}}
            </h2>
            <button (click)="close()" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                <ng-icon name="lucideX" size="24"></ng-icon>
            </button>
        </div>
        <p class="mt-2 text-gray-600">{{t('desc')}}</p>
    </div>

    <form [formGroup]="addressForm" (ngSubmit)="onSubmit()" class="space-y-2 overflow-auto grow">
        <!-- Invoice Type Selection -->
        <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">{{t('address_type')}}</label>
            <div class="flex gap-4">
                <label [for]="'individual'" class="flex-1">
                    <input type="radio" [id]="'individual'" class="hidden" formControlName="address_type"
                        value="individual">
                    <div [class]="'flex items-center gap-2 px-4 py-3 border-2 rounded-3xl cursor-pointer transition-all ' + 
                        (addressForm.get('address_type').value === 'individual' ? 
                        'border-brand-blue-600 bg-brand-blue-50 text-brand-blue-600' : 
                        'border-gray-200 hover:bg-gray-50')">
                        <ng-icon name="lucideUser" class="text-xl"></ng-icon>
                        <span class="font-medium">{{t('individual')}}</span>
                    </div>
                </label>

                <label [for]="'corporate'" class="flex-1">
                    <input type="radio" [id]="'corporate'" class="hidden" formControlName="address_type"
                        value="corporate">
                    <div [class]="'flex items-center gap-2 px-4 py-3 border-2 rounded-3xl cursor-pointer transition-all ' + 
                        (addressForm.get('address_type').value === 'corporate' ? 
                        'border-brand-blue-600 bg-brand-blue-50 text-brand-blue-600' : 
                        'border-gray-200 hover:bg-gray-50')">
                        <ng-icon name="lucideBuilding2" class="text-xl"></ng-icon>
                        <span class="font-medium">{{t('corporate')}}</span>
                    </div>
                </label>
            </div>
            <div *ngIf="submitted && addressForm.get('address_type').hasError('required')"
                class="mt-1 text-xs text-red-500">
                {{t('address_type_required')}}
            </div>
        </div>

        <!-- Address Title -->
        <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">{{t('address_title')}}</label>
            <input type="text" formControlName="title" [placeholder]="t('address_title_placeholder')"
                class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                [ngClass]="{'border-red-500': submitted && addressForm.get('title').hasError('required')}">
            <div *ngIf="submitted && addressForm.get('title').hasError('required')" class="mt-1 text-xs text-red-500">
                {{t('address_title_required')}}
            </div>
        </div>

        <!-- Full Name / Company Name -->
        <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
                {{addressForm.get('address_type').value === 'individual' ? t('full_name') : t('company_name')}}
            </label>
            <input type="text" formControlName="full_name" [placeholder]="addressForm.get('address_type').value === 'individual' ? 
                    t('full_name_placeholder') : t('company_name_placeholder')"
                class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                [ngClass]="{'border-red-500': submitted && addressForm.get('full_name').hasError('required')}">
            <div *ngIf="submitted && addressForm.get('full_name').hasError('required')"
                class="mt-1 text-xs text-red-500">
                {{addressForm.get('address_type').value === 'individual' ?
                t('full_name_required') : t('company_name_required')}}
            </div>
        </div>

        <!-- City and District -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">{{t('city')}}</label>
                <input type="text" formControlName="city" [placeholder]="t('city_placeholder')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                    [ngClass]="{'border-red-500': submitted && addressForm.get('city').hasError('required')}">
                <div *ngIf="submitted && addressForm.get('city').hasError('required')"
                    class="mt-1 text-xs text-red-500">
                    {{t('city_required')}}
                </div>
            </div>

            <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">{{t('district')}}</label>
                <input type="text" formControlName="district" [placeholder]="t('district_placeholder')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                    [ngClass]="{'border-red-500': submitted && addressForm.get('district').hasError('required')}">
                <div *ngIf="submitted && addressForm.get('district').hasError('required')"
                    class="mt-1 text-xs text-red-500">
                    {{t('district_required')}}
                </div>
            </div>
        </div>

        <!-- Address -->
        <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">{{t('address')}}</label>
            <textarea formControlName="address" [placeholder]="t('address_placeholder')" rows="4"
                class="w-full px-4 py-3 border-2 border-gray-200 resize-y rounded-3xl focus:border-brand-blue-600 focus:ring-2 max-h-40"
                [ngClass]="{'border-red-500': submitted && addressForm.get('address').hasError('required')}">
            </textarea>
            <div *ngIf="submitted && addressForm.get('address').hasError('required')" class="mt-1 text-xs text-red-500">
                {{t('address_required')}}
            </div>
        </div>

        <!-- Corporate Fields -->
        <div *ngIf="addressForm.get('address_type').value === 'corporate'"
            [@heightToggle]="addressForm.get('address_type').value" class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">{{t('tax_office')}}</label>
                <input type="text" formControlName="tax_office" [placeholder]="t('tax_office_placeholder')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                    [ngClass]="{'border-red-500': submitted && addressForm.get('tax_office').hasError('required')}">
                <div *ngIf="submitted && addressForm.get('tax_office').hasError('required')"
                    class="mt-1 text-xs text-red-500">
                    {{t('tax_office_required')}}
                </div>
            </div>

            <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">{{t('tax_number')}}</label>
                <input type="number" formControlName="tax_number" [placeholder]="t('tax_number_placeholder')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2 
                        [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                    [ngClass]="{'border-red-500': submitted && addressForm.get('tax_number').hasError('required')}">
                <div *ngIf="submitted && addressForm.get('tax_number').hasError('required')"
                    class="mt-1 text-xs text-red-500">
                    {{t('tax_number_required')}}
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-end w-full gap-4 p-2">
            <button type="button" (click)="close()" class="neutral-button">
                <ng-icon name="lucideX" size="20"></ng-icon>
                {{t('close')}}
            </button>

            <button type="submit" class="primary-blue-button">
                <ng-icon name="lucideCheck" size="20"></ng-icon>
                {{t('save')}}
            </button>
        </div>
    </form>
</div>
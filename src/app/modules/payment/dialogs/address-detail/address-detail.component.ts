import { animate, state, style, transition, trigger } from '@angular/animations';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PaymentService } from '@app/data/services/payment.service';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
@Component({
  selector: 'app-address-detail',
  templateUrl: './address-detail.component.html',
  styleUrls: ['./address-detail.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ opacity: 100 })),
      state('out', style({ opacity: 100 })),
      transition('void => in', [
        style({ opacity: 0 }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ opacity: 0 }))
      ])
    ]),
    trigger('heightToggle', [
      state('corporate', style({ height: '*' })),
      state('individual', style({ height: '0px', overflow: 'hidden' })),
      transition('individual <=> corporate', [
        animate('300ms ease-in-out')]),
      transition('void => *', [
        style({ height: '0px', overflow: 'hidden' }),
        animate('300ms ease-in-out')
      ]),
      transition('* => void', [
        animate('300ms ease-in-out', style({ height: '0px', overflow: 'hidden' }))
      ])
    ])
  ]
})
export class AddressDetailComponent implements OnInit {
  constructor(@Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private formBuilder: FormBuilder,
    private paymentService: PaymentService,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService
  ) {
    if (data) {
      this.address = {
        ...data.address,
        full_name: data.address.company_name ? data.address.company_name : data.address.full_name,
      }
      this.isUpdate = true;
    }
  }
  isUpdate = false;
  address = {
    id: null,
    title: '',
    full_name: '',
    address_type: 'individual',
    city: '',
    district: '',
    address: '',
    active: null,
    tax_number: '',
    tax_office: ''
  }

  animationState = 'in';
  ngOnInit(): void {
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => {
      this.close();
    }
    );
    this.addressForm = this.formBuilder.group(
      {
        title: [this.address.title, [Validators.required]],
        full_name: [this.address.full_name, [Validators.required]],
        address_type: [this.address.address_type, [Validators.required]],
        city: [this.address.city, [Validators.required]],
        district: [this.address.district, [Validators.required]],
        address: [this.address.address, [Validators.required]],
        tax_number: [this.address.tax_number, [this.address.address_type === 'individual' ? Validators.nullValidator : Validators.required]],
        tax_office: [this.address.tax_office, [this.address.address_type === 'individual' ? Validators.nullValidator : Validators.required]]
      }
    );
    this.addressForm.get('address_type').valueChanges.subscribe(value => {
      if (value === 'individual') {
        // tax_number and tax_office are not required for individual
        this.addressForm.get('tax_number').clearValidators();
        this.addressForm.get('tax_number').setValue('');
        this.addressForm.get('tax_office').clearValidators();
        this.addressForm.get('tax_office').setValue('');
        this.addressForm.get('tax_number').updateValueAndValidity();
        this.addressForm.get('tax_office').updateValueAndValidity();
      } else {
        // tax_number and tax_office are required for corporate
        this.addressForm.get('tax_number').setValidators([Validators.required]);
        this.addressForm.get('tax_office').setValidators([Validators.required]);
        this.addressForm.get('tax_number').updateValueAndValidity();
        this.addressForm.get('tax_office').updateValueAndValidity();

      }
    }
    );
  }
  addressForm = new FormGroup({
    title: new FormControl(''),
    full_name: new FormControl(''),
    address_type: new FormControl(''),
    city: new FormControl(''),
    district: new FormControl(''),
    address: new FormControl(''),
    tax_number: new FormControl(''),
    tax_office: new FormControl('')
  });
  get f() { return this.addressForm.controls; }
  submitted = false;
  onSubmit(): void {
    this.submitted = true;
    if (this.addressForm.invalid) {
      return;
    }
    if (this.isUpdate) {
      this.updateInvoiceAddress();
    } else {
      this.addInvoiceAddress();
    }
  }

  addInvoiceAddress() {
    let address = {
     ...this.addressTypeController(this.addressForm.value),
     active: true
    }

    this.paymentService.addInvoiceAddress(address).subscribe(
      {
        next: (data) => {
          this.close(
            {
              data: data
            }
          );
        },
        error: (error) => {
          if (error.error.title) {
            this.snotifyService.error(this.transloco.translate('notification.payment.address_detail.error.message'), this.transloco.translate('notification.payment.address_detail.error.title'), {
              timeout: 3000,
              showProgressBar: true,
              closeOnClick: true,
              pauseOnHover: true,
              position: 'centerBottom'
            });
          }
        }
      }
    );
  }
  updateInvoiceAddress() {
    let address = this.addressTypeController(this.addressForm.value)
    address.id = this.address.id;
    this.paymentService.updateInvoiceAddress(address).subscribe(
      (data) => {
        this.close(data);
      }
    );
  }
  addressTypeController(address: any) {
    let temp
    if (address.address_type === 'individual') {
      temp = {
        id: address.id,
        title: address.title,
        full_name: address.full_name,
        company_name: null,
        address_type: address.address_type,
        city: address.city,
        district: address.district,
        address: address.address,
        active: address.active,
        tax_number: null,
        tax_office: null
      }
    } else {
      temp = {
        id: address.id,
        title: address.title,
        company_name: address.full_name,
        full_name: null,
        address_type: address.address_type,
        city: address.city,
        district: address.district,
        address: address.address,
        active: address.active,
        tax_number: address.tax_number,
        tax_office: address.tax_office
      }
    }
    return temp;
  }
  close(data?: any) {
    this.animationState = 'out';
    if (data) {
      setTimeout(() => this.dialogRef.close(
        data
      ), 300);
    }
    else {
      setTimeout(() => this.dialogRef.close(), 300);
    }
  }
  @HostListener('window:keyup.esc') onKeyUp() {
    this.close();
  }

  @HostListener("window:beforeunload", ["$event"]) unloadHandler(event: Event) {
    this.close();
  }
}

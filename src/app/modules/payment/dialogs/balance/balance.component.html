<div [@slideInOut]="animationState" class="flex justify-center h-[calc(100dvh-12rem)]"
    *transloco="let t;read:'shared.balance';">
    <div class="flex flex-col bg-white size-full rounded-3xl">
        <!-- Header -->
        <div class="flex justify-between w-full p-6 border-b-2">
            <span class="text-2xl font-medium">{{ t('titles') }}</span>
            <button (click)="closeModal()">
                <ng-icon name="lucideX" class="text-2xl text-neutral-600"></ng-icon>
            </button>
        </div>

        <!-- Skeleton Loader -->
        <div *ngIf="isLoading" class="grid grid-cols-4 gap-4 p-4 bg-white border-b-2 animate-pulse">
            <div class="flex flex-col h-20 p-6 bg-gray-200 rounded-lg"></div>
            <div class="flex flex-col h-20 p-6 bg-gray-200 rounded-lg"></div>
            <div class="flex flex-col h-20 p-6 bg-gray-200 rounded-lg"></div>
            <div class="flex flex-col h-20 p-6 bg-gray-200 rounded-lg"></div>
        </div>

        <!-- Credit Information -->
        <div *ngIf="!isLoading" class="grid grid-cols-4 gap-4 p-4 bg-white border-b-2">
            <div class="flex flex-col p-6 text-black rounded-lg bg-neutral-100">
                <span class="text-base">{{ t('cells.total') }}</span>
                <span class="text-lg font-medium">{{ userCredits.merged.total_credit | number }} </span>
            </div>
            <div class="flex flex-col p-6 text-black rounded-lg bg-neutral-100">
                <span class="text-base">{{ t('cells.available') }}</span>
                <span class="text-lg font-medium text-brand-green-500">{{ userCredits.merged.total_remaining_credit |
                    number }}</span>
            </div>
            <div class="flex flex-col p-6 text-black rounded-lg bg-neutral-100">
                <span class="text-base">{{ t('cells.used') }}</span>
                <span class="text-lg font-medium text-brand-blue-500">{{ usedCredit | number }}</span>
            </div>
            <div class="flex flex-col p-6 text-black rounded-lg bg-neutral-100">
                <span class="text-base">{{ t('cells.active_packs') }}</span>
                <span class="text-lg font-medium">{{ activePackages | number }}</span>
            </div>
        </div>

        <!-- Tabs -->
        <div class="flex bg-white border-b-2">
            <button class="flex px-2 py-3 transition-all duration-300 border-b-2"
                [ngClass]="selectedTab === 'activePackages' ? 'border-brand-blue-500 text-brand-blue-500' : 'border-white text-neutral-600'"
                (click)="selectedTab = 'activePackages'">
                <span>{{ t('tabs.active_packages') }}</span>
            </button>
            <button class="flex px-2 py-3 transition-all duration-300 border-b-2"
                [ngClass]="selectedTab === 'usageHistory' ? 'border-brand-blue-500 text-brand-blue-500' : 'border-white text-neutral-600'"
                (click)="selectedTab = 'usageHistory'">
                <span>{{ t('tabs.usage_history') }}</span>
            </button>
        </div>

        <!-- Active Packages Skeleton -->
        <div *ngIf="isLoading && selectedTab === 'activePackages'"
            class="flex flex-col gap-6 p-6 overflow-auto animate-pulse">
            <div class="h-24 bg-gray-200 rounded-3xl"></div>
            <div class="h-24 bg-gray-200 rounded-3xl"></div>
            <div class="h-24 bg-gray-200 rounded-3xl"></div>
        </div>

        <!-- Active Packages -->
        <div *ngIf="!isLoading && selectedTab === 'activePackages'" class="flex flex-col gap-6 p-6 overflow-auto">
            <div *ngFor="let creditItem of userCredits.data">
                <div class="flex justify-between w-full p-6 border-2 rounded-3xl">
                    <div class="flex flex-col">
                        <div class="flex items-center gap-1">
                            <img src="assets/icons/istacoin.svg" alt="istacoin Logo" class="h-5">
                            <span class="text-lg">{{ creditItem.credit | number }} {{ t('credits') }}</span>

                            <!-- Add this badge for demo credits -->
                            <span *ngIf="creditItem.usage_type === 'demo'"
                                class=" ml-1 inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                Demo
                            </span>
                        </div>
                        <span class="text-neutral-600">{{ t('available') }} {{ creditItem.remaining_credit | number
                            }}</span>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-neutral-600">{{ t('expire') }} {{ creditItem.expires_at | date:'dd/MM/yyyy'
                            }}</span>
                        <div class="flex items-center justify-end gap-1">
                            <ng-icon name="lucidePackage" class="text-lg text-neutral-600"></ng-icon>
                            <span class="text-neutral-600">{{ creditItem.id }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage History Skeleton -->
        <div *ngIf="isLoading && selectedTab === 'usageHistory'"
            class="flex flex-col gap-6 p-6 overflow-auto animate-pulse">
            <div class="h-24 bg-gray-200 rounded-3xl"></div>
            <div class="h-24 bg-gray-200 rounded-3xl"></div>
            <div class="h-24 bg-gray-200 rounded-3xl"></div>
        </div>

        <!-- Usage History -->
        <div *ngIf="!isLoading && selectedTab === 'usageHistory'"
            class="flex flex-col gap-3 p-3 pb-0 overflow-auto rounded-b-3xl">
            <div *ngFor="let creditItem of userCredits.data" class="flex flex-col gap-3">
                <div class="flex justify-between w-full p-3 bg-neutral-100 rounded-3xl"
                    *ngFor="let usage of creditItem.credit_usages">
                    <div class="flex flex-col w-full gap-3 p-3">
                        <div class="flex items-center justify-between w-full">
                            <span class="font-medium">{{ usage.project_name | titlecase }}</span>
                            <span class="text-neutral-600">{{ usage.used_credit | number }} {{ t('credits') }}</span>
                        </div>
                        <div class="flex gap-3">
                            <div class="flex items-center justify-center gap-1">
                                <ng-icon name="lucideClock4" class="text-lg text-neutral-600"></ng-icon>
                                <span class="text-xs font-medium text-neutral-600">{{ usage.created_at |
                                    date:'dd/MM/yyyy'}}</span>
                            </div>
                            <div class="flex items-center justify-center gap-1">
                                <ng-icon name="lucidePackage" class="text-lg text-neutral-600"></ng-icon>
                                <span class="text-xs font-medium text-neutral-600"> {{ usage.project_id }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Buy More Credits -->
        <div class="flex items-center justify-center w-full p-6">
            <button (click)="openPaymentDialog()" [ngClass]="'primary-blue-button'"
                class="flex items-center justify-center w-full gap-2">
                <ng-icon name="lucideShoppingCart" class="text-xl text-white"></ng-icon>
                {{ t('buy_credits') }}
            </button>
        </div>
    </div>
</div>
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { KeyValue } from '@angular/common';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Overlay } from '@angular/cdk/overlay';
import { TranslocoService } from '@ngneat/transloco';
import { PaymentComponent } from '../payment/payment.component';
import { BalanceHelperService } from '@app/data/helper/balance.helper.service';
import { ReportHelperService } from '@app/data/helper/report.helper.service';

@Component({
  selector: 'app-balance',
  templateUrl: './balance.component.html',
  styleUrls: ['./balance.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ],
})
export class BalanceComponent implements OnInit {
  isLoading = true;
  selectedTab: 'activePackages' | 'usageHistory' = 'activePackages';
  animationState = 'in';
  userCredits: any;
  usedCredit: number = 0;
  activePackages: number = 0;
  demoCredits: number = 0;
  demoCreditRemaining: number = 0;

  constructor(
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<string>,
    private dialog: Dialog,
    private overlay: Overlay,
    private langService: TranslocoService,
    private balanceHelperService: BalanceHelperService
  ) { }

  ngOnInit(): void {
    this.dialogRef.disableClose = true;

    // Servisten verileri çekiyoruz
    this.balanceHelperService.getUserCredits().subscribe({
      next: (result) => {
        this.userCredits = result;
        const totalCredit = this.userCredits.merged.total_credit;
        const availableCredit = this.userCredits.merged.total_remaining_credit;
        this.usedCredit = totalCredit - availableCredit;
        this.activePackages = this.userCredits.data.length;

        // Find demo credits in the data array
        const demoCredit = this.userCredits.data.find(credit => credit.usage_type === 'demo');
        if (demoCredit) {
          this.demoCredits = demoCredit.credit || 0;
          this.demoCreditRemaining = demoCredit.remaining_credit || 0;
        }

        this.isLoading = false;
      },
      error: (err) => {
        console.error('Hata:', err);
      }
    });
  }

  // Template'te kullanacağımız diğer metodlar
  closeModal() {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(), 300);
  }
  openPaymentDialog() {
    const positionStrategy = this.overlay.position()
      .global()
      .right('0')
      .top('0');
    this.dialog.open(PaymentComponent, {
      data: this.data,
      width: '100%',
      positionStrategy: positionStrategy
    });
  }

  @HostListener('window:keyup.esc') onKeyUp() {
    this.closeModal();
  }

  @HostListener('window:beforeunload', ['$event']) unloadHandler(event: Event) {
    this.closeModal();
  }

  originalOrder(a: KeyValue<number, string>, b: KeyValue<number, string>): number {
    return 0;
  }
}
<div class="w-full max-w-5xl p-8 bg-white shadow-md min-w-96 rounded-3xl"
    *transloco="let t; read: 'shared.payment.calculate_credit'" [@bouncyScale]="animationState">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-900">{{t('title')}}</h2>
        <button class="text-gray-400 hover:text-gray-600 focus:outline-none" (click)="close()">
            <ng-icon name="matCloseRound" size="24"></ng-icon>
        </button>
    </div>

    <div class="flex flex-col gap-6 lg:flex-row">
        <!-- Analysis Section -->
        <div class="flex-1 space-y-4 overflow-y-auto h-[600px] pr-4">
            <div *ngFor="let analysis of analyses; let i = index"
                class="overflow-hidden transition-all duration-200 bg-white border-2 rounded-3xl w-96"
                [ngClass]="{'border-brand-blue-600': analysis.open, 'border-gray-200': !analysis.open}">

                <!-- Analysis Header -->
                <div (click)="togglePanel(i)"
                    class="flex items-center justify-between w-full p-4 cursor-pointer hover:bg-gray-50"
                    [ngClass]="{'bg-brand-blue-50': analysis.open}">
                    <div class="flex items-center gap-3">
                        <img [src]="'assets/icons/'+ analysis.title +'.svg'" [alt]="analysis.title" class="size-5">
                        <span class="font-medium text-gray-900">
                            {{ t(analysis.title)}}
                            {{analysis.title == 'single'|| analysis.title == 'multi' ? t('independent') : ''}}
                        </span>
                    </div>

                    <div class="flex items-center gap-3">
                        <div *ngIf="analysis.credits > 0" class="flex items-center gap-1">
                            <span class="font-medium text-gray-700">{{ analysis.credits | thousandSeparator}}</span>
                            <img src="assets/icons/istacoin.svg" alt="credit" class="size-5">
                        </div>
                        <ng-icon [class.rotate-180]="analysis.open" name="featherChevronRight"
                            class="transition-transform duration-200">
                        </ng-icon>
                    </div>
                </div>

                <!-- Analysis Content -->
                <div *ngIf="analysis.open" class="p-4 space-y-4 border-t" [@shrinkOut]>
                    <!-- Input Fields -->
                    <div *ngIf="analysis.variables" class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">{{t('variable_count_input')}}</label>
                        <input type="number" [(ngModel)]="analysis.variables.count" min="0"
                            (blur)="analysis.variables.count = analysis.variables.count < 0 ? 0 : analysis.variables.count"
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2">
                    </div>

                    <div *ngIf="analysis.factor" class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">{{t('factor_count_input')}}</label>
                        <input type="number" [(ngModel)]="analysis.factor.count" min="0"
                            (blur)="analysis.factor.count = analysis.factor.count < 0 ? 0 : analysis.factor.count"
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2">
                    </div>

                    <div *ngIf="analysis.rowVariables" class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">{{t('row_variable_count_input')}}</label>
                        <input type="number" [(ngModel)]="analysis.rowVariables.count" min="0"
                            (blur)="analysis.rowVariables.count = analysis.rowVariables.count < 0 ? 0 : analysis.rowVariables.count"
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2">
                    </div>

                    <div *ngIf="analysis.colVariables" class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">{{t('col_variable_count_input')}}</label>
                        <input type="number" [(ngModel)]="analysis.colVariables.count" min="0"
                            (blur)="analysis.colVariables.count = analysis.colVariables.count < 0 ? 0 : analysis.colVariables.count"
                            class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2">
                    </div>

                    <!-- Split Variable Section -->
                    <div class="pt-2 space-y-4">
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="checkbox" [(ngModel)]="analysis.useSplit"
                                class="w-4 h-4 border-2 rounded text-brand-blue-600 focus:ring-brand-blue-600">
                            <span class="text-sm font-medium text-gray-700">{{t('use_split_variable')}}</span>
                        </label>

                        <div *ngIf="analysis.useSplit" class="space-y-2">
                            <label class="text-sm font-medium text-gray-700">{{t('split_label_count')}}</label>
                            <input type="number" [(ngModel)]="analysis.splitLabels.count" min="0"
                                (blur)="analysis.splitLabels.count = analysis.splitLabels.count < 0 ? 0 : analysis.splitLabels.count"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2">
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-between pt-2">
                        <button (click)="clearAnalysis(i)" class="secondary-status-error-button">
                            {{t('clear')}}
                        </button>
                        <button (click)="saveAnalysis(i)" class="primary-blue-button"
                            [disabled]="(analysis.variables && !analysis.variables.count) || (analysis.useSplit && !analysis.splitLabels.count) || (analysis.factor && !analysis.factor.count) || (analysis.rowVariables && !analysis.rowVariables.count) || (analysis.colVariables && !analysis.colVariables.count)">
                            {{t('save')}}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Credits Section -->
        <div class="flex flex-col items-center justify-center flex-none p-6 text-center bg-gray-50 rounded-3xl min-w-56">
            <h3 class="mb-4 text-xl font-semibold text-gray-900">{{t('total_credit')}}</h3>
            <div class="flex items-center justify-center gap-2 mb-6">
                <span class="text-4xl font-bold text-gray-900">{{ totalCredits | thousandSeparator }}</span>
                <img src="assets/icons/istacoin.svg" alt="credit" class="size-8">
            </div>
            <button *ngIf="totalCredits > 0" (click)="clearAll()" class="secondary-status-error-button">
                {{t('clear_all')}}
            </button>
        </div>
    </div>
</div>
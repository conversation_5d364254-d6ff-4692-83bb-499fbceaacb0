import { animate, state, style, transition, trigger } from '@angular/animations';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, HostListener, Inject } from '@angular/core';
import { AnalysisService } from '@app/data/services/analysis.service';


@Component({
  selector: 'app-calculate-credit',
  templateUrl: './calculate-credit.component.html',
  styleUrls: ['./calculate-credit.component.scss'],
  animations: [
    trigger('shrinkOut', [
      state('void', style({ height: '0', opacity: '0' })),
      state('*', style({ height: '*' })),
      transition('void => *', [animate('0.5s ease-in-out')]),
      transition('* => void', [animate('0.1s ease-in-out')]),
    ]),
    trigger('bouncyScale', [
      state('in', style({ transform: 'scale(1)' })),
      state('out', style({ transform: 'scale(0)' })),
      transition('void => in', [
        style({ transform: 'scale(0)' }),
        animate('300ms cubic-bezier(0.68, -0.55, 0.27, 1.55)')
      ]),
      transition('in => out', [
        animate('300ms cubic-bezier(0.68, -0.55, 0.27, 1.55)', style({ transform: 'scale(0)' }))
      ])
    ])
  ]
})
export class CalculateCreditComponent {
  constructor(@Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private a: AnalysisService
  ) {

  }
  totalCredits: number = 0;
  animationState: 'in' | 'out' = 'in';
  close() {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(), 300);
  }
  analyses = [
    { title: 'descriptive', open: true, variables: { count: 0 }, useSplit: false, splitLabels: { count: 0 }, credits: 0 },
    { title: 'single', open: false, variables: { count: 0 }, useSplit: false, splitLabels: { count: 0 }, credits: 0 },
    { title: 'multi', open: false, factor: { count: 0 }, variables: { count: 0 }, useSplit: false, splitLabels: { count: 0 }, credits: 0 },
    { title: 'dependent', open: false, variables: { count: 0 }, useSplit: false, splitLabels: { count: 0 }, credits: 0 },
    { title: 'chisq', open: false, rowVariables: { count: 0 }, colVariables: { count: 0 }, useSplit: false, splitLabels: { count: 0 }, credits: 0 },
    { title: 'correlation', open: false, variables: { count: 0 }, useSplit: false, splitLabels: { count: 0 }, credits: 0 }
  ];

  togglePanel(index: number) {
    this.analyses[index].open = !this.analyses[index].open;
    for (let i = 0; i < this.analyses.length; i++) {
      if (i !== index) {
        this.analyses[i].open = false;
      }
    }
  }

  clearAnalysis(index: number) {
    for (const key in this.analyses[index]) {
      if (key === 'title' || key === 'open' || key === 'useSplit' || key === 'credits') continue;
      this.analyses[index][key].count = 0;
    }
    this.analyses[index].credits = 0;
    this.updateTotalCredits();
  }
  clearAll() {
    for (let i = 0; i < this.analyses.length; i++) {
      this.clearAnalysis(i);
    }
  }

  saveAnalysis(index: number) {
    var variables_count = 0;
    if (this.analyses[index].title === 'chisq') {
      variables_count = this.analyses[index].rowVariables.count + this.analyses[index].colVariables.count;
    }else{
      variables_count = this.analyses[index].variables.count
    }
    const payload = {
      product_code: this.analyses[index].title,
      variables_count: variables_count,
      split_value_labels_count: this.analyses[index].splitLabels.count
    };
    if (this.analyses[index].title === 'multi') {
      payload['factor_groups_count'] = this.analyses[index].factor.count;
    }

    this.a.getCalculatedCredts(payload).subscribe((res) => {
      this.analyses[index].credits = res.credits;
      this.updateTotalCredits();
    }
    );
  }

  updateTotalCredits() {
    this.totalCredits = this.analyses.reduce((acc, analysis) => acc + analysis.credits, 0);
  }

  @HostListener('window:keyup.esc') onKeyUp() {
    this.close();
  }

  @HostListener("window:beforeunload", ["$event"]) unloadHandler(event: Event) {
    this.close();
  }
}

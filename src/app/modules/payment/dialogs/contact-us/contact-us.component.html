<div class="w-full max-w-4xl p-8 bg-white shadow-md rounded-3xl h-[calc(100vh-6rem)] overflow-auto" *transloco="let t ; read: 'shared.payment.contact'"
    [@bouncyScale]="animationState">
    <!-- Header -->
    <div class="mb-2 text-center">
        <h2 class="text-4xl font-bold text-gray-900">
            {{t('title')}}
        </h2>
        <p class="mt-2 text-gray-600">{{t('description')}}</p>
    </div>

    <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="space-y-4">
        <!-- Name -->
        <div class="relative flex flex-col w-full">
            <label class="block mb-1 text-sm font-medium text-gray-700">{{t('name')}}</label>
            <div class="relative">
                <input type="text" id="name" formControlName="name" [placeholder]="t('name_input')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                    [ngClass]="{'border-red-500': contactForm.get('name')?.invalid && contactForm.get('name')?.touched}">
            </div>
            <div *ngIf="contactForm.get('name')?.invalid && contactForm.get('name')?.touched"
                class="mt-1 text-xs text-red-500">
                {{t('name_required')}}
            </div>
        </div>

        <!-- Email -->
        <div class="relative flex flex-col w-full">
            <label class="block mb-1 text-sm font-medium text-gray-700">{{t('email')}}</label>
            <div class="relative">
                <input type="email" id="email" formControlName="email" [placeholder]="t('email_input')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                    [ngClass]="{'border-red-500': contactForm.get('email')?.invalid && contactForm.get('email')?.touched}">
            </div>
            <div *ngIf="contactForm.get('email')?.invalid && contactForm.get('email')?.touched"
                class="mt-1 text-xs text-red-500">
                {{t('email_required')}}
            </div>
        </div>

        <!-- Phone -->
        <div class="relative flex flex-col w-full">
            <label class="block mb-1 text-sm font-medium text-gray-700">{{t('phone')}}</label>
            <div class="relative">
                <input type="text" id="phone" formControlName="phone" [placeholder]="t('phone_input')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2">
            </div>
        </div>

        <!-- Company -->
        <div class="relative flex flex-col w-full">
            <label class="block mb-1 text-sm font-medium text-gray-700">{{t('company')}}</label>
            <div class="relative">
                <input type="text" id="company" formControlName="company" [placeholder]="t('company_input')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2"
                    [ngClass]="{'border-red-500': contactForm.get('company')?.invalid && contactForm.get('company')?.touched}">
            </div>
            <div *ngIf="contactForm.get('company')?.invalid && contactForm.get('company')?.touched"
                class="mt-1 text-xs text-red-500">
                {{t('company_required')}}
            </div>
        </div>

        <!-- Message -->
        <div class="relative flex flex-col w-full">
            <label class="block mb-1 text-sm font-medium text-gray-700">{{t('message')}}</label>
            <div class="relative">
                <textarea id="message" formControlName="message" rows="4" [placeholder]="t('message_input')"
                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-3xl focus:border-brand-blue-600 focus:ring-2 max-h-40">
                </textarea>
            </div>
        </div>

        <!-- Submit Button -->
        <button type="submit" [disabled]="contactForm.invalid" class="text-white bg-brand-blue-500 w-full
            shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(0,60,189,0.3)] hover:bg-brand-blue-600
            hover:shadow-[0_6px_12px_-5px_rgba(0,60,189,0.3)] active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.2)]
            transition-all px-6 py-3 flex items-center gap-2 font-medium rounded-full disabled:opacity-50 justify-center
            disabled:cursor-not-allowed disabled:hover:bg-brand-blue-500
            disabled:hover:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
            disabled:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
            disabled:active:shadow-[inset_0_2px_4px_rgba(0,0,0,0)]">
            {{t('send')}}
        </button>
    </form>

    <!-- Close button -->
    <button class="absolute text-gray-400 hover:text-gray-600 focus:outline-none top-6 right-6" (click)="close()">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
    </button>
</div>
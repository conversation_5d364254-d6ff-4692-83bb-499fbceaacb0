import { animate, state, style, transition, trigger } from '@angular/animations';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import emailjs from '@emailjs/browser';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
const publicKey = 'o9TqDVlONwom86VF2'
@Component({
  selector: 'app-contact-us',
  templateUrl: './contact-us.component.html',
  styleUrls: ['./contact-us.component.scss'],
  animations: [
    trigger('bouncyScale', [
      state('in', style({ transform: 'scale(1)' })),
      state('out', style({ transform: 'scale(0)' })),
      transition('void => in', [
        style({ transform: 'scale(0)' }),
        animate('300ms cubic-bezier(0.68, -0.55, 0.27, 1.55)')
      ]),
      transition('in => out', [
        animate('300ms cubic-bezier(0.68, -0.55, 0.27, 1.55)', style({ transform: 'scale(0)' }))
      ])
    ])
  ]
})
export class ContactUsComponent {

  contactForm: FormGroup;

  constructor(@Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private fb: FormBuilder,
    private snotifyService: SnotifyService,
    private translocoService: TranslocoService
  ) {
    this.contactForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      company: ['', Validators.required],
      message: ['']
    });
  }
  animationState: 'in' | 'out' = 'in';
  close() {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(), 300);
  }
  onSubmit() {
    if (this.contactForm.valid) {
      emailjs.send("service_v1wfhxn", "template_vna2l49",
        {
          name: this.contactForm.value.name,
          email: this.contactForm.value.email,
          phone: this.contactForm.value.phone,
          company: this.contactForm.value.company,
          message: this.contactForm.value.message
        },
        {
          publicKey: publicKey
        }
      );
      this.snotifyService.success(this.translocoService.translate('notification.payment.contact_us.message'),this.translocoService.translate('notification.payment.contact_us.title'),
    {
      position: 'centerBottom',
    });
      this.close();
    }
  }
}

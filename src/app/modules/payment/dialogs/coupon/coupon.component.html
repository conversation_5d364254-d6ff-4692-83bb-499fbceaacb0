<div [@slideInOut]="animationState"
    class="relative flex flex-col items-start justify-center gap-4 p-6 overflow-auto bg-white border max-h-dvh rounded-3xl border-zinc-200"
    *transloco="let t ; read: 'shared.payment'">
    <div class="flex items-center justify-between w-full ">
        <h2 class="text-lg font-semibold text-gray-900">{{t('title')}}</h2>
        <button (click)="close()" class="text-gray-400 transition-colors hover:text-gray-600">
            <ng-icon name="lucideX" class="text-xl"></ng-icon>
        </button>
    </div>
    <div class="flex flex-col w-full h-auto gap-2 max-h-128">
        <div class="flex gap-2 group">
            <input *ngIf="!couponUsed" type="text" [(ngModel)]="couponCode" [disabled]="couponUsed" (keydown.enter)="useCoupon(couponCode)"
                [ngClass]="{'border-brand-blue-600':couponCode!=null && couponCode!=''}"
                class="w-full p-2 text-sm border rounded-3xl border-zinc-100" [placeholder]="t('coupon_placeholder')">
            <div *ngIf="couponUsed"
                class="w-full p-2 text-sm border rounded-3xl border-zinc-100">
                {{t('coupon_used')}}
            </div>
            <button (click)="couponUsed?removeCoupon() : useCoupon(couponCode);pMethod==null"
                [ngClass]="couponUsed ? 'bg-red-500' : 'bg-brand-blue-500'"
                class="p-2 px-4 text-sm text-white transition-all rounded-3xl disabled:bg-zinc-400">
                {{couponUsed? t('remove'): t('use')}}
            </button>

        </div>
        <p class="w-full pb-1 text-sm border-b-2 text-brand-blue-600">
            {{t('defined_coupons')}}
        </p>
        <div class="flex flex-col flex-grow gap-2 overflow-auto min-h-64">
            <div *ngIf="coupons.length==0" class="flex items-center justify-center w-full h-64">
                <div class="flex flex-col items-center gap-2">
                    <ng-icon name="lucideTicketX" class="text-3xl text-brand-blue-500"></ng-icon>
                    <p class="text-sm">
                        {{t('no_coupon')}}
                    </p>
                </div>
            </div>
            <div class="relative flex w-full group" *ngFor="let item of coupons">
                <div [ngClass]="{'bg-brand-blue-500 text-white border-brand-blue-600 border-2':couponUsed && couponCode==item.code, ' border-brand-blue-600/50 border':!couponUsed}"
                    class="flex flex-col gap-2 p-2 text-xs bg-white border rounded-3xl  group-has-[:disabled]:border-zinc-400 group-has-[:disabled]:opacity-70 grow">
                    <p *ngIf="item.product_id" class="text-brand-blue-500 ">
                        {{t(item.product)}} : {{ item.ctype=== 'net' ? item.amount + '₺' : item.amount + '%'
                        }} {{t('discount') }}
                    </p>
                    <p *ngIf="item.users.length==1 && !item.product_id&& item.dataset_id==null && !item.product_category_id"
                        class="text-brand-blue-500 ">
                        {{currentLang=='tr' ? t('for_you')+ ' '+ item.amount +(item.ctype=== 'net' ? '₺'
                        : '%')+ ' ' + t('discount'): item.amount + ' ' + (item.ctype=== 'net' ? '₺' : '%') + ' '
                        + t('discount') + ' ' + t('for_you')}}
                    </p>
                    <p *ngIf="(item.users.length>1 && !item.product_id&& item.dataset_id==null) || item.product_category_id"
                        class="text-brand-brand-blue-500 ">
                        {{currentLang=='tr' ? t('for_analysis_group') + ' ' + item.amount + (item.ctype=== 'net'
                        ? '₺' :
                        '%') + ' ' + t('discount') : t('discount') + ' ' + item.amount + ' ' + (item.ctype===
                        'net' ? '₺' : '%') + ' ' + t('for_analysis_group') }}
                    </p>
                    <p class=" text-zinc-500">
                        {{currentLang == 'tr' ? item.valid_from + ' ' + t('until') : t('until') + ' ' +
                        item.valid_from }}
                    </p>
                </div>
                <hr class="h-16 mt-2 border-r group-has-[:disabled]:border-zinc-400  border-brand-blue-600 border-dashed">
                <button (click)="couponUsed ? removeCoupon(item) : useCoupon(item)"
                    [disabled]="couponUsed && couponCode!=item.code"
                    [ngClass]="{'bg-red-500 hover:bg-red-500/70':couponUsed && couponCode==item.code, 'bg-[#D3D3D3] hover:bg-[#D3D3D3]':couponUsed && couponCode!=item.code, 'bg-brand-blue-500 hover:bg-brand-blue-500/70':!couponUsed}"
                    class="flex items-center justify-center w-24  text-sm font-medium text-white transition-all group-has-[:disabled]:border-zinc-400  border rounded-3xl ">
                    {{
                    couponUsed == false ? t('use'): couponCode==item.code ?t('remove') : t('use')
                    }}
                </button>
            </div>
        </div>
    </div>
</div>
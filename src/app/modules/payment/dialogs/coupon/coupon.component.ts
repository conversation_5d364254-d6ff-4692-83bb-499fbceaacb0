import { animate, state, style, transition, trigger } from '@angular/animations';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { CouponService } from '@app/data/services/coupon.service';
import { PaymentService } from '@app/data/services/payment.service';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';

@Component({
  selector: 'app-coupon',
  templateUrl: './coupon.component.html',
  styleUrls: ['./coupon.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ opacity: 100 })),
      state('out', style({ opacity: 100 })),
      transition('void => in', [
        style({ opacity: 0 }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ opacity: 0 }))
      ])
    ]),
  ]
})
export class CouponComponent implements OnInit {
  constructor(
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private c: CouponService,
    private payment: PaymentService,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
  ) {
    this.dialogRef.disableClose = true;
    this.currentLang = this.transloco.getActiveLang();
  }
  currentLang
  animationState = 'in';
  couponInput = false;
  couponUsed = false;
  coupons = [];
  ngOnInit(): void {
    this.getCoupons();
  }
  getCoupons() {
    this.c.getCoupons().subscribe({
      next: (coupons: any) => {
        this.coupons = coupons;
        this.coupons = this.coupons.map(coupon => {
          return {
            ...coupon,
            product: null ,
            valid_from: new Date(coupon.valid_from).toLocaleDateString(),
            valid_until: new Date(coupon.valid_until).toLocaleDateString(),
          }
        }
        );
        if (this.data.couponCode)
          this.couponUsed = true;
        this.discount = this.data.discount_amount;
        this.couponCode = this.data.couponCode;
      }
    });
  }
  ////TODO
  removeCoupon() {
    this.couponUsed = false;
    this.couponCode = '';
    this.snotifyService.success(
      this.transloco.translate('notification.payment.coupon.remove.message'),
      this.transloco.translate('notification.payment.coupon.remove.title'), {
      timeout: 1500,
      showProgressBar: true,
      closeOnClick: true,
      pauseOnHover: false,
      position: 'centerBottom',
    });
  }
  /**
* 
* @param coupon {
"original_price": 3417,
"discount_amount": 512.55,
"total_price": 2904.45
}¨ƒ
*/
  couponCode: any;
  discount = 0;
  useCoupon(coupon: any) {
    this.couponCode = coupon.code ? coupon.code : coupon;
    var order = {
      coupon_code: coupon.code ? coupon.code : coupon,
      price: this.data.total,
      order_items_data: this.data.selectedProducts
    }
    this.couponUsed = true
    this.c.applyCoupon({ order }).subscribe({
      next: (data: any) => {
        this.snotifyService.success(this.transloco.translate('notification.payment.coupon.success.message'), this.transloco.translate('notification.payment.coupon.success.title'), {
          timeout: 1500,
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: false,
          position: 'centerBottom',
        });
        this.close({ coupon_code: coupon.code ? coupon.code : coupon, total_price: Number(data.total_price), discount_amount: Number(data.discount_amount) });
      },
      error: (error) => {
        this.snotifyService.error(this.transloco.translate('notification.payment.coupon.error.message'), this.transloco.translate('notification.payment.coupon.error.title'), {
          timeout: 1500,
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: false,
          position: 'centerBottom',
        });
        this.couponUsed = false;
        this.couponCode = null;
      }
    });
  }

  close(data?: any) {
    this.animationState = 'out';
    if (data) {
      setTimeout(() => this.dialogRef.close(
        data
      ), 300);
    }
    else {
      if (this.couponUsed) {
        setTimeout(() => this.dialogRef.close({
          coupon_code: this.couponCode,
          total_price: this.data.total,
          discount_amount: this.discount
        }), 300);
      } else {
        this.dialogRef.close();
      }
    }
  }
}

<div class="relative w-full p-6 overflow-hidden rounded-lg h-fit bg-zinc-100">
    <button class="absolute z-30 scale-125 right-4 top-4" (click)="closeModal()">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 21 24" fill="none">
            <g clip-path="url(#clip0_1799_2357)">
                <path
                    d="M7.74037 6.3395L17.797 16.3961C18.1406 16.7397 18.1406 17.3096 17.797 17.6532C17.4534 17.9968 16.8835 17.9968 16.5399 17.6532L6.48329 7.59658C6.13971 7.25299 6.13968 6.68309 6.48329 6.33949C6.82689 5.99589 7.39679 5.99591 7.74037 6.3395Z"
                    fill="currentColor" />
                <path
                    d="M17.7985 6.34096C18.1421 6.68456 18.142 7.25446 17.7984 7.59804L7.74182 17.6547C7.39822 17.9983 6.82835 17.9983 6.48475 17.6547C6.14114 17.3111 6.14115 16.7412 6.48475 16.3976L16.5414 6.34097C16.885 5.99738 17.4549 5.99736 17.7985 6.34096Z"
                    fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_1799_2357">
                    <rect width="16" height="16" fill="white" transform="translate(12.1401 23.3101) rotate(-135)" />
                </clipPath>
            </defs>
        </svg>
    </button>

    <!-- Başarılı Modal -->
    <div *ngIf="showSuccessModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="w-full max-w-md p-8 mx-4 bg-white rounded-lg">
            <div class="text-center">
                <div class="flex justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none"
                        stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                </div>
                <h2 class="mb-4 text-2xl font-bold">İşlem Durumu</h2>

                <!-- Başarılı durumda gelen kod ve mesajı göster -->
                <div *ngIf="responseCode" class="inline-block px-3 py-1 mb-2 text-green-600 bg-green-100 rounded-lg">
                    Durum Kodu: {{ responseCode }}
                </div>
                <p class="mb-6">{{ responseMessage || 'Ödeme işleminiz tamamlandı.' }}</p>

                <button class="px-6 py-2 text-white transition-colors bg-gray-500 rounded-lg hover:bg-gray-600"
                    (click)="closeSuccessModal()">
                    Kapat
                </button>
            </div>
        </div>
    </div>

    <!-- Başarısız Modal -->
    <div *ngIf="showFailureModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="w-full max-w-md p-8 mx-4 bg-white rounded-lg">
            <div class="text-center">
                <div class="flex justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none"
                        stroke="#F44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                </div>
                <h2 class="mb-4 text-2xl font-bold">İşlem Durumu</h2>

                <!-- Hata kodu ve mesajını göster -->
                <div *ngIf="paymentErrorCode" class="inline-block px-3 py-1 mb-2 text-red-600 bg-red-100 rounded-lg">
                    Durum Kodu: {{ paymentErrorCode }}
                </div>
                <p class="mb-6">{{ paymentErrorMessage || 'İşlem tamamlanamadı.' }}</p>

                <button class="px-6 py-2 text-white transition-colors bg-gray-500 rounded-lg hover:bg-gray-600"
                    (click)="closeFailureModal()">
                    Kapat
                </button>
            </div>
        </div>
    </div>

    <!-- Yükleniyor Spinner -->
    <div *ngIf="isLoading" class="fixed inset-0 z-40 flex items-center justify-center bg-white bg-opacity-75">
        <div class="flex flex-col items-center">
            <div
                class="w-16 h-16 mb-4 border-4 border-t-4 border-blue-500 rounded-full animate-spin border-t-transparent">
            </div>
            <p class="text-gray-600">Ödeme işlemi hazırlanıyor...</p>
        </div>
    </div>

    <!-- Bilgi Mesajı - iframe kaldırıldı, yeni sayfada açıldığında gösterilecek -->
    <div *ngIf="!isLoading" class="flex flex-col items-center justify-center p-10 bg-white rounded-lg shadow-sm">
        <div class="mb-4 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
                stroke="#4285F4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="16"></line>
                <line x1="12" y1="16" x2="12" y2="16"></line>
            </svg>
        </div>
        <h2 class="mb-2 text-xl font-semibold text-gray-800">Ödeme İşlemi Devam Ediyor</h2>
        <p class="mb-4 text-center text-gray-600">
            Ödeme işleminiz yeni bir pencerede açıldı. Lütfen açılan penceredeki işlemleri tamamlayın.
            <br>Pencere açılmadıysa, tarayıcınızın pop-up engelleyici ayarlarını kontrol edin.
        </p>
        <p class="mb-6 text-sm text-center text-gray-500">
            İşlem durumu otomatik olarak kontrol edilmektedir. İşlem tamamlandığında buradan bilgilendirileceksiniz.
        </p>

        <button (click)="closeModal()"
            class="px-6 py-2 text-white transition-colors bg-gray-500 rounded-lg hover:bg-gray-600">
            İptal Et
        </button>
    </div>

    <!-- Gizli form - artık iframe'de değil, yeni pencerede açılacak -->
    <form id="payment-form" [attr.action]="formAction" method="POST" target="payment_window" style="display: none;">
        <ng-container *ngFor="let key of formKeys">
            <input type="hidden" [attr.name]="key" [attr.value]="formData[key]">
        </ng-container>
    </form>
</div>
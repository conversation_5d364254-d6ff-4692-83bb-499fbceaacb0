.loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;
  }
  
  .spinner {
    width: 50px;
    height: 50px;
    border: 8px solid rgba(0, 0, 0, 0.1);
    border-top-color: #333;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  /* credit-card.component.scss */
:host {
  display: block;
  width: 100%;
  height: 100%;
}

.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 123, 255, 0.2);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* İframe içeriğinin görünürlüğünü iyileştirmek için */
#an_iframe {
  border: none;
  width: 100%;
  min-height: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Modal stilleri */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
}

.modal-content {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 90%;
  width: 450px;
}

.success-icon {
  color: #4CAF50;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-icon {
  color: #F44336;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-code {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: #FFEBEE;
  color: #D32F2F;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-family: monospace;
  font-size: 0.9rem;
}

.btn {
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
  outline: none;
}

.btn-success {
  background-color: #4CAF50;
  color: white;
}

.btn-success:hover {
  background-color: #388E3C;
}

.btn-error {
  background-color: #F44336;
  color: white;
}

.btn-error:hover {
  background-color: #D32F2F;
}

.btn-secondary {
  background-color: #9E9E9E;
  color: white;
}

.btn-secondary:hover {
  background-color: #757575;
}

.btn-retry {
  background-color: #2196F3;
  color: white;
}

.btn-retry:hover {
  background-color: #1976D2;
}
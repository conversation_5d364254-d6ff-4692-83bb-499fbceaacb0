import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit, NgZone, OnDestroy } from '@angular/core';
import { PaymentService } from '@app/data/services/payment.service';
import { akbank } from '@env/environment';
import { HttpClient } from '@angular/common/http';
import { environment } from '@env/environment';

@Component({
  selector: 'app-credit-card',
  templateUrl: './credit-card.component.html',
  styleUrls: ['./credit-card.component.scss']
})
export class CreditCardComponent implements OnInit, OnDestroy {
  constructor(
    @Inject(DIALOG_DATA) public data,
    public dialogRef: DialogRef<any>,
    private payment: PaymentService,
    private zone: NgZone,
    private http: HttpClient
  ) {
    this.formAction = akbank.url;
    console.log('[Constructor] CreditCardComponent constructor called');
  }
  response: any;
  formData: any = {};
  formKeys: string[] = [];
  formAction: string;
  isLoading: boolean = true;
  orderId: string;
  pollingInterval: any;
  paymentWindow: Window | null = null;

  // Ödeme durumu takibi için değişkenler
  showSuccessModal: boolean = false;
  showFailureModal: boolean = false;
  paymentErrorMessage: string = '';
  paymentErrorCode: string = '';

  // Başarılı durum için de kod ve mesaj
  responseCode: string = '';
  responseMessage: string = '';

  ngOnInit(): void {
    console.log('[ngOnInit] CreditCardComponent initialization');
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => {
      this.closeModal();
    });

    // Sipariş oluştur ve formu gönder
    this.sendCredentials();
  }

  ngOnDestroy(): void {
    // Polling'i durdur
    this.stopPolling();

    // Ödeme penceresi açıksa kapat
    if (this.paymentWindow && !this.paymentWindow.closed) {
      this.paymentWindow.close();
    }

    console.log('[ngOnDestroy] Polling stopped and component destroyed');
  }

  // Polling başlat - 3 saniyede bir sipariş durumunu kontrol et
  startPolling() {
    console.log('[startPolling] Starting polling for order status');

    if (!this.orderId) {
      console.error('[startPolling] Order ID is missing, cannot start polling');
      return;
    }

    // Eğer zaten çalışan bir interval varsa temizle
    this.stopPolling();

    // İlk kontrolü hemen yap
    this.checkOrderStatus(0);

    let pollCount = 1;
    console.log(`[startPolling] Will poll ${environment.apiUrl}/orders/${this.orderId} every 3 seconds`);

    this.pollingInterval = setInterval(() => {
      this.checkOrderStatus(pollCount);
      pollCount++;
    }, 3000);
  }

  // Sipariş durumunu kontrol et
  checkOrderStatus(pollCount: number) {
    if (!this.orderId) {
      console.error('[checkOrderStatus] Order ID is missing');
      return;
    }

    const url = `${environment.apiUrl}/orders/${this.orderId}`;
    console.log(`[Polling #${pollCount}] GET ${url}`);

    this.http.get(url).subscribe(
      (result: any) => {
        console.log(`[Polling #${pollCount}] Response:`, result);

        // Status bilgilerini logla
        if (result) {
          console.log(`[Polling #${pollCount}] Order ID: ${result.id}`);
          console.log(`[Polling #${pollCount}] Order status: ${result.status}`);
          console.log(`[Polling #${pollCount}] Payment status: ${result.payment?.status}`);

          // Response params varsa logla
          if (result.payment?.credit_card_payment?.response_params) {
            console.log(`[Polling #${pollCount}] Response params:`, result.payment.credit_card_payment.response_params);
          }

          // Ödeme durumunu kontrol et ve uygun modalı göster
          this.checkPaymentStatus(result);
        }
      },
      error => {
        console.error(`[Polling #${pollCount}] Error:`, error);
      }
    );
  }

  // Ödeme durumunu kontrol et ve uygun modalı göster
  checkPaymentStatus(orderData: any) {
    // Status değerlerini kontrol et (case insensitive)
    const orderStatus = (orderData.status || '').toLowerCase();
    const paymentStatus = (orderData.payment?.status || '').toLowerCase();

    console.log(`[checkPaymentStatus] Checking status: Order=${orderStatus}, Payment=${paymentStatus}`);

    // Daha önce modal gösterilmişse tekrar gösterme
    if (this.showSuccessModal || this.showFailureModal) {
      console.log('[checkPaymentStatus] Modal already shown, skipping status check');
      return;
    }

    // İşlem tamamlandıysa payment penceresini kapat
    if (orderStatus !== 'draft' && this.paymentWindow && !this.paymentWindow.closed) {
      console.log('[checkPaymentStatus] Transaction finalized, closing payment window');
      this.paymentWindow.close();
    }

    // Başarılı durumlar
    if (orderStatus === 'approved' ||
      orderStatus === 'completed' ||
      orderStatus === 'paid' ||
      orderStatus === 'success' ||
      paymentStatus === 'approved' ||
      paymentStatus === 'completed' ||
      paymentStatus === 'paid' ||
      paymentStatus === 'success') {

      console.log('[checkPaymentStatus] SUCCESS detected, showing success modal');

      // Ödeme penceresini kapat
      if (this.paymentWindow && !this.paymentWindow.closed) {
        this.paymentWindow.close();
      }

      // Polling'i durdur
      this.stopPolling();

      // Başarılı durumda kod ve mesajı al
      this.responseCode = orderData.payment?.credit_card_payment?.response_params?.responseCode || '';
      this.responseMessage = orderData.payment?.credit_card_payment?.response_params?.responseMessage || 'Ödeme işleminiz başarıyla tamamlandı.';

      // NgZone içinde modal göster (UI güncellemesi için)
      this.zone.run(() => {
        // Success modal göster
        this.showSuccessModal = true;

        // 5 saniye sonra sayfayı yenile
        setTimeout(() => {
          console.log('[autoRefresh] Auto refreshing page after 5 seconds');
          window.location.reload();
        }, 5000);
      });
    }
    // Başarısız durumlar
    else if (orderStatus === 'failed' ||
      orderStatus === 'cancelled' ||
      orderStatus === 'canceled' ||
      orderStatus === 'declined' ||
      orderStatus === 'error' ||
      paymentStatus === 'failed' ||
      paymentStatus === 'cancelled' ||
      paymentStatus === 'canceled' ||
      paymentStatus === 'declined' ||
      paymentStatus === 'rejected' ||
      paymentStatus === 'error') {

      console.log('[checkPaymentStatus] FAILURE detected, showing failure modal');

      // Ödeme penceresini kapat
      if (this.paymentWindow && !this.paymentWindow.closed) {
        this.paymentWindow.close();
      }

      // Polling'i durdur
      this.stopPolling();

      // Hata detaylarını al
      this.paymentErrorCode = orderData.payment?.credit_card_payment?.response_params?.responseCode || '';
      this.paymentErrorMessage = orderData.payment?.credit_card_payment?.response_params?.responseMessage || 'Ödeme işlemi başarısız oldu.';

      // NgZone içinde modal göster (UI güncellemesi için)
      this.zone.run(() => {
        // Failure modal göster
        this.showFailureModal = true;

        // 5 saniye sonra sayfayı yenile
        setTimeout(() => {
          console.log('[autoRefresh] Auto refreshing page after 5 seconds');
          window.location.reload();
        }, 5000);
      });
    }
    // Diğer durumlarda polling devam eder
    else {
      console.log(`[checkPaymentStatus] Status '${orderStatus}' is not terminal, continuing to poll`);
    }
  }

  // Polling'i durdur
  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      console.log('[stopPolling] Polling stopped');
    }
  }

  // Başarılı modalı kapat
  closeSuccessModal() {
    this.showSuccessModal = false;
    this.dialogRef.close({ success: true });
    console.log('[closeSuccessModal] Success modal closed, dialog closed with success=true');

    // Sayfayı yenile
    window.location.reload();
  }

  // Başarısız modalı kapat
  closeFailureModal() {
    this.showFailureModal = false;
    this.dialogRef.close({ success: false });
    console.log('[closeFailureModal] Failure modal closed, dialog closed with success=false');

    // Sayfayı yenile
    window.location.reload();
  }

  // Modal kapatıldığında
  closeModal() {
    this.stopPolling();

    // Ödeme penceresi açıksa kapat
    if (this.paymentWindow && !this.paymentWindow.closed) {
      this.paymentWindow.close();
    }

    this.dialogRef.close({ success: false });
    console.log('[closeModal] Dialog closed with success=false');
  }

  // Ödeme için sipariş oluştur
  sendCredentials() {
    console.log('[sendCredentials] Creating order with data:', this.data.order);

    // Eğer data.order yoksa veya eksikse
    if (!this.data.order) {
      console.error('[sendCredentials] Order data is missing!', this.data);
      this.isLoading = false;
      return;
    }

    // Use the order object that was passed in the data
    this.payment.createOrder(this.data.order).subscribe(
      res => {
        console.log('[sendCredentials] Order created successfully. Response:', res);

        // Null kontrolü yap
        if (!res) {
          console.error('[sendCredentials] Response is null or undefined!');
          this.isLoading = false;
          return;
        }

        this.response = res;

        // orderId, res.order.id yerine doğrudan res.id olarak alınıyor
        // çünkü response objesi zaten siparişin kendisi
        if (res.id) {
          this.orderId = res.id.toString();
          console.log(`[sendCredentials] Order ID set: ${this.orderId}`);
        } else {
          console.error('[sendCredentials] order ID is missing in the response!', res);
          // Alternatif olarak ID'yi ararız
          if (typeof res === 'object') {
            console.log('[sendCredentials] Searching for ID in response object');
            for (const key in res) {
              if (key === 'id' || key === 'order_id') {
                this.orderId = res[key].toString();
                console.log(`[sendCredentials] Found ID as ${key}: ${this.orderId}`);
                break;
              } else if (res[key] && typeof res[key] === 'object' && res[key].id) {
                this.orderId = res[key].id.toString();
                console.log(`[sendCredentials] Found nested ID in ${key}.id: ${this.orderId}`);
                break;
              }
            }
          }
        }

        // form_data var mı kontrol et
        if (res.payment?.credit_card_payment?.form_data) {
          this.formData = res.payment.credit_card_payment.form_data;
          this.formKeys = Object.keys(this.formData);
          console.log('[sendCredentials] Form data keys:', this.formKeys);

          // Form hazır, ödeme sayfasını yeni pencerede aç
          setTimeout(() => this.openPaymentWindow(), 100);
        } else {
          console.error('[sendCredentials] form_data is missing in the response!');
          this.isLoading = false;
        }
      },
      error => {
        console.error('[sendCredentials] Error creating order:', error);
        this.isLoading = false;
      }
    );
  }

  // Ödeme sayfasını yeni pencerede aç
  openPaymentWindow() {
    console.log('[openPaymentWindow] Opening payment in new window...');

    // Form elementini bul
    const form = document.getElementById('payment-form') as HTMLFormElement;
    if (!form) {
      console.error('[openPaymentWindow] payment-form element not found!');
      this.isLoading = false;
      return;
    }

    // Yeni pencere aç (pencere ayarlarını ihtiyaca göre değiştirin)
    this.paymentWindow = window.open('', 'payment_window',
      'width=800,height=700,resizable=yes,scrollbars=yes,status=yes');

    if (!this.paymentWindow) {
      console.error('[openPaymentWindow] Failed to open payment window. Pop-up may be blocked.');
      alert('Ödeme sayfasının açılabilmesi için lütfen pop-up engelleyiciyi devre dışı bırakın.');
      this.isLoading = false;
      return;
    }

    // Form hedefini yeni pencereye ayarla
    form.target = 'payment_window';

    // Form gönder
    form.submit();
    console.log('[openPaymentWindow] Payment form submitted to new window');

    // Yükleniyor göstergesini kapat
    this.isLoading = false;

    // Polling başlat
    if (this.orderId) {
      console.log(`[openPaymentWindow] Starting polling with orderId: ${this.orderId}`);
      this.startPolling();
    } else {
      console.log('[openPaymentWindow] OrderId missing, checking response object');

      // Response objesinden orderId'yi almayı dene
      if (this.response && this.response.id) {
        this.orderId = this.response.id.toString();
        console.log(`[openPaymentWindow] Found order ID in response: ${this.orderId}, starting polling`);
        this.startPolling();
      }
    }

    // Pencere kapandığında kontrol et
    this.paymentWindow.onbeforeunload = () => {
      console.log('[paymentWindow.onbeforeunload] Payment window is closing');
    };
  }
}
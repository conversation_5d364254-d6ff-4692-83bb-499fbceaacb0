<div [@slideInOut]="animationState" class="w-full max-w-2xl bg-white shadow-xl rounded-3xl h-[calc(95vh-4rem)] overflow-auto"
    *transloco="let t ; read: 'shared.payment.eft_detail'">
    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center gap-2">
            <div class="flex items-center justify-center w-10 h-10 rounded-full bg-brand-blue-100">
                <ng-icon name="lucideCaptions" class="text-xl text-brand-blue-500"></ng-icon>
            </div>
            <h2 class="text-lg font-semibold text-gray-900">{{t('title')}}</h2>
        </div>
        <button (click)="close()" [disabled]="isSending" class="text-gray-400 transition-colors hover:text-gray-600">
            <ng-icon name="lucideX" class="text-xl"></ng-icon>
        </button>
    </div>

    <!-- Content -->
    <div class="p-6 space-y-2">
        <!-- Price Info -->
        <div class="flex items-center justify-between px-4 py-3 text-white bg-brand-blue-500 rounded-3xl">
            <span class="font-medium">{{t('price')}}</span>
            <span class="text-lg font-semibold">{{price | thousandSeparator}} ₺</span>
        </div>

        <!-- Bank Details -->
        <div class="space-y-1">
            <h3 class="text-base font-medium text-gray-900">{{t('eft_info')}}</h3>
            <div class="space-y-1 text-sm">
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">{{t('account_name')}}</span>
                    <span class="font-medium text-gray-900">Naci Murat</span>
                </div>
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">{{t('bank_name')}}</span>
                    <span class="font-medium text-gray-900">Akbank(0046)</span>
                </div>
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">{{t('branch')}}</span>
                    <span class="font-medium text-gray-900">{{t('branch_value')}}</span>
                </div>
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">{{t('account_number')}}</span>
                    <span class="font-medium text-gray-900">0014316</span>
                </div>
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">{{t('iban')}}</span>
                    <span class="font-medium text-gray-900">TR64 0004 6013 8988 8000 0143 16</span>
                </div>
            </div>
        </div>
       <!-- Info Alert -->
       <div class="p-4 border rounded-3xl bg-brand-blue-50 border-brand-blue-200">
        <p class="text-sm text-brand-blue-700">{{t('eft_subinfo')}}</p>
    </div>
        <!-- Receipt Upload -->
        <div class="space-y-2">
            <h3 class="font-medium text-gray-900">{{t('upload_receipt')}}</h3>
            <label
                [ngClass]="receipt ? 'border-brand-blue-500 bg-brand-blue-50' : 'border-gray-200 hover:border-brand-blue-500'"
                class="block p-6 transition-all border-2 border-dashed cursor-pointer rounded-3xl">
                <input (change)="fileInput($event)" type="file" id="dataset" class="hidden"
                    accept="image/png, image/jpg, image/jpeg, application/pdf">

                <div *ngIf="!receipt" class="flex flex-col items-center gap-2">
                    <ng-icon name="lucideUpload" class="w-8 h-8 text-brand-blue-500"></ng-icon>
                    <p class="text-xs text-gray-500">PNG, JPG, PDF</p>
                </div>

                <div *ngIf="receipt" class="relative group">
                    <img [src]="receipt" alt="Receipt preview" class="object-contain w-full h-32 rounded">
                    <div
                        class="absolute inset-0 flex items-center justify-center text-sm text-white transition-opacity opacity-0 bg-black/50 group-hover:opacity-100">
                        {{t('click_to_change')}}
                    </div>
                </div>
            </label>
        </div>

 
    </div>

    <!-- Footer -->
    <div class="p-6 border-t border-gray-200">
        <button [disabled]="receipt==null || isSending" (click)="createOrder()"
            class="flex items-center justify-center w-full gap-2 px-6 py-3 font-medium text-white transition-all rounded-full bg-brand-blue-500 hover:bg-brand-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
            <ng-icon *ngIf="!isSending" name="lucideCheckCircle" class="w-5 h-5"></ng-icon>
            <ng-icon *ngIf="isSending" name="lucideLoader" class="w-5 h-5 animate-spin"></ng-icon>
            <span>{{isSending ? t('sending') : t('send')}}</span>
        </button>
    </div>
</div>
import { DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { DIALOG_DATA } from '@angular/cdk/dialog';
import { PaymentService } from '@app/data/services/payment.service';

@Component({
  selector: 'app-package-details',
  template: `
    <div class="w-full max-w-lg p-8 bg-white shadow-xl rounded-2xl" *transloco="let t;read 'shared.payment.package_details'">
      <div class="flex items-start justify-between mb-6">
        <h3 class="text-xl font-bold text-brand-blue-700">
          {{data.package.title}} {{ t('details') }}
        </h3>
        <button
          (click)="dialogRef.close()"
          class="text-gray-500 hover:text-gray-700"
        >
          <ng-icon name="lucideX" class="w-6 h-6"></ng-icon>
        </button>
      </div>

      <div class="grid grid-cols-2 gap-4 mb-6">
        <div>
          <div class="text-sm text-gray-500">{{ t('credit_amount') }}</div>
          <div class="text-lg font-semibold text-brand-blue-700">
            <ng-container *ngIf="data.package.isCustom">
              <div class="flex items-center gap-2">
                <input type="number" 
                       [(ngModel)]="data.package.customCredits" 
                       (ngModelChange)="updateFlexPackage()"
                       (blur)="validateAndUpdateFlexPackage()"
                       min="1" 
                       [max]="maxAvailableCredits"
                       [ngClass]="{'border-red-500': !isValidCreditAmount()}"
                       class="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-blue-500 focus:border-transparent">
                <span>{{ t('credits') }}</span>
              </div>
              <div *ngIf="!isValidCreditAmount()" class="text-xs mt-1 text-red-500">
                <span *ngIf="data.package.customCredits < 1">{{ t('min_credit_error') }}</span>
                <span *ngIf="data.package.customCredits > maxAvailableCredits">
                  {{ t('max_credit_error', {max: maxAvailableCredits}) }}
                </span>
              </div>
            </ng-container>
            <ng-container *ngIf="!data.package.isCustom">
              {{data.package.credits}} {{ t('credits') }}
            </ng-container>
          </div>
        </div>

        <div>
          <div class="text-sm text-gray-500">{{ t('price') }}</div>
          <div class="text-lg font-semibold text-brand-blue-700">
            {{data.package.isCustom ? 
              getFlexPackagePrice() + ' ₺' : 
              data.package.price + ' ₺'}}
          </div>
        </div>
      </div>

      <div class="mb-6 space-y-3">
        <div class="flex items-start gap-2" *ngFor="let feature of features">
          <ng-icon name="lucideCheck" class="w-5 h-5 text-brand-green-500 flex-shrink-0 mt-0.5"></ng-icon>
          <div>
            <div class="font-medium text-gray-800">{{ t(feature.titleKey) }}</div>
            <div class="text-gray-600">{{ t(feature.descriptionKey) }}</div>
          </div>
        </div>
      </div>

      <div class="flex justify-end gap-3">
        <button
          (click)="dialogRef.close()"
          class="px-6 py-3 font-medium text-gray-600 transition-colors hover:text-brand-blue-500"
        >
          {{ t('close') }}
        </button>

        <ng-container *ngIf="data.package.type !== 'corporate'; else corporateButton">
          <button
            (click)="addToCart()"
            [disabled]="data.package.isCustom && !isValidCreditAmount()"
            class="px-6 py-3 rounded-full text-white font-medium bg-brand-blue-500 shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(0,60,189,0.3)] hover:bg-brand-blue-600 hover:-translate-y-0.5 transition-all disabled:bg-gray-300 disabled:cursor-not-allowed disabled:shadow-none"
          >
            {{ t('add_to_cart') }}
          </button>
        </ng-container>

        <ng-template #corporateButton>
          <button class="px-6 py-3 rounded-full text-white font-medium bg-brand-blue-500 shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(0,60,189,0.3)] hover:bg-brand-blue-600 hover:-translate-y-0.5 transition-all">
            {{ t('contact_us') }}
          </button>
        </ng-template>
      </div>
    </div>
  `
})
export class PackageDetailsComponent implements OnInit {
  features = [
    {
      titleKey: 'validity_period_title',
      descriptionKey: 'validity_period_desc'
    },
    {
      titleKey: 'report_languages_title',
      descriptionKey: 'report_languages_desc'
    },
    {
      titleKey: 'report_export_title',
      descriptionKey: 'report_export_desc'
    },
    {
      titleKey: 'repurchase_title',
      descriptionKey: 'repurchase_desc'
    },
    ...(this.data.package.type === 'corporate' ? [{
      titleKey: 'corporate_support_title',
      descriptionKey: 'corporate_support_desc'
    }] : [])
  ];

  maxAvailableCredits: number = 1000;
  existingTotalCredits: number = 0;

  constructor(
    public dialogRef: DialogRef<any>,
    @Inject(DIALOG_DATA) public data: { package: any },
    private paymentService: PaymentService
  ) {}

  ngOnInit() {
    // Calculate existing total credits from selected packages
    this.calculateExistingCredits();
  }

  calculateExistingCredits() {
    const state = this.paymentService.getState();
    if (state.selectedPackages && state.selectedPackages.length > 0) {
      // Calculate credits from other packages, excluding any flex package
      this.existingTotalCredits = state.selectedPackages
        .filter(pkg => !pkg.isCustom)
        .reduce((total, pkg) => total + (pkg.credits * pkg.count), 0);
      
      this.maxAvailableCredits = Math.max(1, 1000 - this.existingTotalCredits);
    } else {
      this.maxAvailableCredits = 1000;
    }

    // Ensure initial customCredits value is valid
    this.validateAndUpdateFlexPackage();
  }

  isValidCreditAmount(): boolean {
    if (this.data.package.customCredits === undefined || 
        this.data.package.customCredits === null || 
        isNaN(Number(this.data.package.customCredits))) {
      return false;
    }
    
    const creditValue = Number(this.data.package.customCredits);
    return creditValue >= 1 && creditValue <= this.maxAvailableCredits;
  }

  updateFlexPackage() {
    if (this.data.package.isCustom) {
      // Ensure credit value is a number
      if (isNaN(Number(this.data.package.customCredits))) {
        this.data.package.customCredits = 1;
      }
    }
  }

  validateAndUpdateFlexPackage() {
    if (!this.data.package.isCustom) return;

    // First ensure it's a valid number
    if (this.data.package.customCredits === undefined || 
        this.data.package.customCredits === null || 
        isNaN(Number(this.data.package.customCredits))) {
      this.data.package.customCredits = 1;
    }

    // Convert to number
    this.data.package.customCredits = Number(this.data.package.customCredits);
    
    // Apply limits
    if (this.data.package.customCredits < 1) {
      this.data.package.customCredits = 1;
    } else if (this.data.package.customCredits > this.maxAvailableCredits) {
      this.data.package.customCredits = this.maxAvailableCredits;
    }

    // Force integer value
    this.data.package.customCredits = Math.floor(this.data.package.customCredits);

    // Update credits property to match custom credits
    if (this.data.package.isCustom) {
      this.data.package.credits = this.data.package.customCredits;
    }
  }

  getFlexPackagePrice(): number {
    if (!this.data.package.isCustom || !this.isValidCreditAmount()) {
      return 0;
    }
    return Number(this.data.package.customCredits) * this.data.package.pricePerCredit;
  }

  addToCart() {
    if (this.data.package.isCustom) {
      this.validateAndUpdateFlexPackage();
      if (!this.isValidCreditAmount()) {
        return; // Don't close dialog if invalid
      }
    }
    
    // Return both action and updated package data
    this.dialogRef.close({
      action: 'add',
      packageData: this.data.package
    });
  }
}

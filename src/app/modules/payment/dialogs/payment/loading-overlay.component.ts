import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-loading-overlay',
  template: `
    <div *ngIf="show" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div class="p-6 bg-white rounded-lg shadow-lg">
        <div class="flex items-center space-x-4">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-brand-blue-500"></div>
          <p class="text-lg font-medium text-gray-700">
            {{ 'loading' | transloco }}
          </p>
        </div>
      </div>
    </div>
  `
})
export class LoadingOverlayComponent {
  @Input() show: boolean = false;
}

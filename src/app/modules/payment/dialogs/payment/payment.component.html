<app-loading-overlay [show]="showModal"></app-loading-overlay>

<div class="flex flex-col h-[95vh]  w-[98vw]  bg-gray-50 rounded-3xl overflow-auto"
    *transloco="let t; read: 'shared.payment'">
    <div class="flex flex-col h-full overflow-hidden ">
        <!-- Header -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-start justify-between mb-4">
                <div>
                    <h1 class="mb-2 text-2xl font-bold text-brand-blue-700">
                        {{t('title')}}
                    </h1>
                    <p class="text-gray-600">
                        {{ getStepDescription() }}
                    </p>
                </div>
                <div class="flex items-center gap-6">
                    <button (click)="showVideo()" class="text-sm transition-colors text-brand-blue-400 hover:text-brand-blue-600">
                            {{t('helper_video')}}
                    </button>
                    <button (click)="closeDialog()" class="text-gray-400 transition-colors hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Step Indicator -->
            <div class="flex items-center w-full">
                <ng-container *ngFor="let step of steps; let i = index">
                    <!-- Step Circle -->
                    <div class="flex items-center">
                        <div [ngClass]="getStepClasses(i + 1)"
                            class="flex items-center justify-center w-8 h-8 rounded-full">
                            <ng-container *ngIf="state.currentStep > i + 1; else stepNumber">
                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                            </ng-container>
                            <ng-template #stepNumber>
                                <span [ngClass]="state.currentStep === i + 1 ? 'text-white' : 'text-gray-400'">
                                    {{i + 1}}
                                </span>
                            </ng-template>
                        </div>
                        <div class="ml-2">
                            <p [ngClass]="state.currentStep >= i + 1 ? 'text-brand-blue-700' : 'text-gray-400'"
                                class="font-medium">
                                {{t(step.title)}}
                            </p>
                        </div>
                    </div>

                    <!-- Connector Line -->
                    <div *ngIf="i < steps.length - 1"
                        [ngClass]="state.currentStep > i + 1 ? 'bg-brand-blue-500' : 'bg-gray-200'"
                        class="flex-1 h-0.5 mx-4">
                    </div>
                </ng-container>
            </div>
        </div>

        <!-- Content -->
        <div class="flex-1 overflow-auto">
            <ng-container [ngSwitch]="state.currentStep">
                <app-package-selection *ngSwitchCase="1" (packageSelected)="onPackageSelected($event)">
                </app-package-selection>

                <app-billing-info *ngSwitchCase="2" [packageData]="state.packageData"
                    (billingSubmitted)="onBillingSubmitted($event)">
                </app-billing-info>

                <app-payment-summary *ngSwitchCase="3" [packageData]="state.packageData"
                    [billingInfo]="state.billingInfo" (paymentCompleted)="onPaymentCompleted($event)">
                </app-payment-summary>
            </ng-container>
        </div>


        <!-- Step navigation buttons -->
        <div class="flex items-center justify-between p-4 pr-20 border-t border-gray-200">
            <!-- Back Button -->
            <button *ngIf="state.currentStep > 1" (click)="previousStep()"
                class="flex items-center px-4 py-2 text-sm font-medium transition-colors border rounded-full text-brand-blue-700 border-brand-blue-200 hover:bg-brand-blue-50">
                <ng-icon name="lucideChevronLeft" class="mr-2"></ng-icon>
                {{t('buttons.back')}}
            </button>
            <div *ngIf="state.currentStep === 1"></div>

            <!-- Action Button for Step 3 -->
            <button *ngIf="state.currentStep === 3" [disabled]="!canProceed()" (click)="completePayment()"
                class="flex items-center px-4 py-2 text-sm font-medium text-white transition-colors rounded-full"
                [class.bg-brand-blue-500]="getPaymentMethod() === 'creditCard'"
                [class.hover:bg-brand-blue-600]="getPaymentMethod() === 'creditCard'"
                [class.bg-brand-green-500]="getPaymentMethod() === 'bankTransfer'"
                [class.hover:bg-brand-green-600]="getPaymentMethod() === 'bankTransfer'"
                [class.bg-brand-purple-500]="getOrderTotal() === 0"
                [class.hover:bg-brand-purple-600]="getOrderTotal() === 0" [class.disabled:bg-gray-300]="!canProceed()"
                [class.disabled:cursor-not-allowed]="!canProceed()">
                {{getActionButtonText()}}
                <ng-icon [name]="getActionButtonIcon()" class="ml-2"></ng-icon>
            </button>

            <!-- Continue Button for Steps 1-2 -->
            <button *ngIf="state.currentStep < 3" [disabled]="!canProceedToNextStep()" (click)="nextStep()"
                class="flex items-center px-4 py-2 text-sm font-medium text-white transition-colors rounded-full bg-brand-blue-500 hover:bg-brand-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed">
                {{t('buttons.continue')}}
                <ng-icon name="lucideChevronRight" class="ml-2"></ng-icon>
            </button>
        </div>
    </div>
</div>
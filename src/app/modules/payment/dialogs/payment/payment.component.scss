input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}


/* Animasyonlar */
@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out forwards;
}

/* Focus stillerini özelleştirme */
input:focus, textarea:focus {
  outline: none;
}

/* Hover efektleri */
.hover-shadow {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px -5px rgba(0, 60, 189, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* Responsive düzenlemeler */
@media (max-width: 768px) {
  .stepper-text {
    display: none;
  }
  
  .stepper-mobile {
    display: flex;
  }
}

@media (min-width: 769px) {
  .stepper-text {
    display: block;
  }
  
  .stepper-mobile {
    display: none;
  }
}
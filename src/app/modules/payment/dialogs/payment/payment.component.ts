import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, OnInit, Inject } from '@angular/core';
import { PaymentService } from '@app/data/services/payment.service';
import { CreditCardComponent } from '../credit-card/credit-card.component';
import { EftComponent } from '../eft/eft.component';
import { Dialog } from '@angular/cdk/dialog';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
import { VideoEmbedComponent } from '@app/shared/components/video-embed/video-embed.component';

interface PaymentState {
  currentStep: number;
  packageData?: any;
  billingInfo?: any;
  paymentInfo?: any;
  appliedCoupon?: { code: string } | null;
}

@Component({
  selector: 'app-payment-dialog',
  templateUrl: './payment.component.html',
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class PaymentComponent implements OnInit {
  state: PaymentState = {
    currentStep: 1
  };

  steps = [
    { title: '' },
    { title: '' },
    { title: '' }
  ];

  showModal: boolean = false;

  constructor(
    public dialogRef: DialogRef<any>,
    @Inject(DIALOG_DATA) public data: any,
    private paymentService: PaymentService,
    private dialog: Dialog,
    private transloco: TranslocoService,
    private snotifyService: SnotifyService
  ) { }

  ngOnInit() {

    // Initialize step titles with translations
    this.steps = [
      { title: this.transloco.translate('steps.step1') },
      { title: this.transloco.translate('steps.step2') },
      { title: this.transloco.translate('steps.step3') }
    ];

    // Dialog başlangıç durumu
    if (this.data?.initialStep) {
      this.state.currentStep = this.data.initialStep;
    }
  }

  getStepDescription(): string {
    return this.transloco.translate(`shared.payment.step_descriptions.step${this.state.currentStep}`,);
  }

  getStepClasses(stepNumber: number): string {
    if (this.state.currentStep > stepNumber) {
      return 'bg-brand-blue-500'; // Completed step
    } else if (this.state.currentStep === stepNumber) {
      return 'bg-brand-blue-500'; // Current step
    } else {
      return 'bg-white border-2 border-gray-300'; // Future step
    }
  }

  canProceedToStep(step: number): boolean {
    switch (step) {
      case 1:
        return true; // Always can go to first step
      case 2:
        return !!this.paymentService.getState().selectedPackages?.length;
      case 3:
        return !!this.state.billingInfo?.address;
      default:
        return false;
    }
  }

  nextStep() {
    const nextStepNumber = this.state.currentStep + 1;
    if (nextStepNumber <= this.steps.length && this.canProceedToStep(nextStepNumber)) {
      this.state.currentStep = nextStepNumber;
      this.paymentService.updateState({ currentStep: nextStepNumber });
    }
  }

  previousStep() {
    const prevStepNumber = this.state.currentStep - 1;

    // Check if on summary step and has coupon applied
    if (this.state.currentStep === 3 &&
      this.paymentService.getState().appliedCoupon) {

      // Show confirmation dialog for losing coupon
      const confirmDialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('shared.confirm.back_with_coupon.title'),
          content: this.transloco.translate('shared.confirm.back_with_coupon.content'),
          confirm: this.transloco.translate('shared.confirm.back_with_coupon.confirm'),
          cancel: this.transloco.translate('shared.confirm.back_with_coupon.cancel')
        }
      });

      confirmDialog.closed.subscribe((result) => {
        if (result) {
          // User confirmed to go back and lose coupon
          this.navigateToPrevStep(prevStepNumber);
        }
      });
    } else if (prevStepNumber >= 1) {
      // No coupon to lose or not on summary step
      this.navigateToPrevStep(prevStepNumber);
    }
  }

  // Helper method to navigate to previous step
  private navigateToPrevStep(prevStepNumber: number) {
    this.state.currentStep = prevStepNumber;

    // Only update the current step, don't modify any other state
    this.paymentService.updateState({
      currentStep: prevStepNumber
    });

    // Keep any previously selected packages intact
    const currentState = this.paymentService.getState();

  }

  goToStep(stepNumber: number) {
    if (stepNumber >= 1 &&
      stepNumber <= this.steps.length &&
      this.canProceedToStep(stepNumber)) {
      this.state.currentStep = stepNumber;
      this.paymentService.updateState({ currentStep: stepNumber });
    }
  }

  // Step event handlers
  onPackageSelected(packageData: any) {
    this.state.packageData = packageData;
  }

  onBillingSubmitted(billingInfo: any) {
    this.state.billingInfo = billingInfo;
    // State'i güncelle
    this.paymentService.updateState({
      ...this.paymentService.getState(),
      billingInfo: billingInfo
    });

  }

  // Handle payment completion from payment-summary component
  onPaymentCompleted(paymentInfo: any) {
    this.state.paymentInfo = paymentInfo;

    if (!paymentInfo.success && paymentInfo.action === 'back') {
      this.previousStep();
      return;
    }

    // Process based on payment method
    if (paymentInfo.success) {
      switch (paymentInfo.method) {
        case 'creditCard':
          this.processCreditCardPayment(paymentInfo.data);
          break;
        case 'bankTransfer':
          this.processBankTransferPayment(paymentInfo.data);
          break;
        case 'zero':
          this.processZeroAmountPayment(paymentInfo.data);
          break;
      }
    }
  }

  // Methods to handle different payment flows
  private processCreditCardPayment(paymentData: any) {
    const dialogRef = this.dialog.open(CreditCardComponent, {
      data: paymentData,
      width: '700px'
    });

    dialogRef.closed.subscribe((result: { success: boolean }) => {
      if (result && result.success) {
        this.completePaymentFlow(result);
      }
    });
  }

  private processBankTransferPayment(paymentData: any) {
    const dialogRef = this.dialog.open(EftComponent, {
      data: paymentData
    });

    dialogRef.closed.subscribe((result: { success: boolean }) => {
      if (result && result.success) {
        this.completePaymentFlow(result);
      }
    });
  }


  private processZeroAmountPayment(paymentData: any) {
    this.showModal = true;

    // Use the createOrder method directly as in previous implementation
    this.paymentService.createOrder(paymentData.order).subscribe(
      data => {
        this.showModal = false;

        // Show success notification
        this.snotifyService.success(
          this.transloco.translate('notification.payment.success.message',),
          this.transloco.translate('notification.payment.success.title',),
          {
            timeout: 1500,
            showProgressBar: true,
            closeOnClick: true,
            pauseOnHover: false,
            position: 'centerBottom',
          }
        );

        // Clear the cart
        const state = this.paymentService.getState();
        if (state.selectedPackages) {
          state.selectedPackages.forEach(pkg => {
            pkg.count = 0;
          });
        }

        // Reset payment state
        this.paymentService.resetState();

        // Complete payment flow
        this.completePaymentFlow({
          success: true,
          method: 'zero',
          data: paymentData
        });
      },
      error => {
        this.showModal = false;

        // Show error notification
        this.snotifyService.error(
          this.transloco.translate('notification.payment.error.message',),
          this.transloco.translate('notification.payment.error.title',),
          {
            timeout: 1500,
            showProgressBar: true,
            closeOnClick: true,
            pauseOnHover: false,
            position: 'centerBottom',
          }
        );

        console.error('İşlem başarısız:', error);
      }
    );
  }

  private completePaymentFlow(result: any) {
    // Close the dialog with success result
    this.dialogRef.close({
      success: true,
      data: {
        ...this.state,
        paymentInfo: result
      }
    });
  }

  closeDialog() {
    this.dialogRef.close();
  }

  // Simplified method to check if payment can proceed
  canProceed(): boolean {
    const paymentState = this.paymentService.getState();
    return paymentState.agreed === true &&
      (paymentState.total === 0 || !!paymentState.paymentMethod);
  }

  // Remove getSummaryComponent method as it's no longer needed

  canProceedToNextStep(): boolean {
    if (this.state.currentStep === 1) {
      return !!this.state.packageData?.packages?.length;
    } else if (this.state.currentStep === 2) {
      return !!this.state.billingInfo?.address;
    }
    return false;
  }

  // Updated methods to use payment service state directly
  getPaymentMethod(): string {
    return this.paymentService.getState().paymentMethod || 'creditCard';
  }

  getOrderTotal(): number {
    return this.paymentService.getState().total || 0;
  }

  getActionButtonText(): string {
    if (this.getOrderTotal() === 0) {
      return this.transloco.translate('shared.payment.buttons.complete_free_order',);
    }
    return this.getPaymentMethod() === 'creditCard'
      ? this.transloco.translate('shared.payment.buttons.continue_to_payment',)
      : this.transloco.translate('shared.payment.buttons.view_eft_details',);
  }

  getActionButtonIcon(): string {
    if (this.getOrderTotal() === 0) {
      return 'lucideCheck';
    }
    return this.getPaymentMethod() === 'creditCard'
      ? 'lucideCreditCard'
      : 'lucideBuilding2';
  }

  // Updated method to handle payment completion
  completePayment(): void {
    if (!this.canProceed()) return;

    const state = this.paymentService.getState();

    // Create order items data in the required format
    const order_items_data = state.selectedPackages
      .filter(item => item.count > 0)
      .map(item => {
        // Basic order item structure
        const orderItem: any = {
          credit_package_id: item.id,
          quantity: item.isCustom && item.customCredits ? item.customCredits :item.count,
        };

        return orderItem;
      });

    // For zero amount, always use 'eft' as payment method
    const paymentMethod = state.total === 0 ? 'eft' :
      (state.paymentMethod === 'creditCard' ? 'credit_card' : 'eft');

    // Construct order object according to previous structure
    const order = {
      coupon_code: state.appliedCoupon ? state.appliedCoupon.code : '',
      discount: state.discount || 0,
      price: state.subTotal || 0,
      total_price: state.total || 0,
      payment_method: paymentMethod,
      order_items_data,
      invoice_address_id: this.state.billingInfo?.address?.id
    };

    // Create payload for different payment methods
    const paymentData = {
      order,
      billingAddress: this.state.billingInfo?.address,
      totals: {
        subTotal: state.subTotal,
        discount: state.discount,
        total: state.total
      }
    };

    // Process based on payment method
    if (state.total === 0) {
      this.processZeroAmountPayment(paymentData);
    } else if (state.paymentMethod === 'creditCard') {
      this.processCreditCardPayment(paymentData);
    } else {
      this.processBankTransferPayment(paymentData);
    }
  }

  showVideo() {
    this.dialog.open(VideoEmbedComponent, {
     data:{
       from: 'payment',
     } 
    });
  }
}

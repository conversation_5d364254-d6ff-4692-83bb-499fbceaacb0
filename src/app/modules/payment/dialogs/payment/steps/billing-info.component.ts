import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PaymentService } from '@app/data/services/payment.service';
import { Dialog } from '@angular/cdk/dialog';
import { AddressDetailComponent } from '../../address-detail/address-detail.component';
import { TranslocoService } from '@ngneat/transloco';

interface Address {
    id: number;
    title: string;
    full_name: string | null;
    city: string;
    district: string;
    address: string;
    address_type: 'individual' | 'corporate';
    company_name: string | null;
    tax_office: string | null;
    tax_number: string | null;
    active: boolean | null;
    user_id: number;
    created_at: string;
    updated_at: string;
}

@Component({
    selector: 'app-billing-info',
    template: `
    <div class="grid grid-cols-1 gap-6 p-6 md:grid-cols-3" *transloco="let t;read 'shared.payment.billing_info'">
      <!-- Sol Panel - Fatura Bilgileri -->
      <div class="col-span-2 space-y-6">
        <div class="bg-white border border-gray-200 rounded-xl">
          <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-800">{{ t('billing_addresses') }}</h3>
              <button
                (click)="openAddressDialog()"
                class="flex items-center gap-2 px-3 py-2 text-sm transition-colors rounded-lg text-brand-blue-500 hover:bg-brand-blue-50">
                <ng-icon name="lucidePlus"></ng-icon>
                <span>{{ t('add_new_address') }}</span>
              </button>
            </div>
          </div>

          <!-- Adres Listesi -->
          <div class="p-4 max-h-[calc(100vh-24rem)]" style=" overflow-y: auto;">
            <div class="space-y-4">
              <div *ngFor="let address of addresses"
                   (click)="selectAddress(address)"
                   [class.border-brand-blue-500]="selectedAddress?.id === address.id"
                   [class.bg-brand-blue-50]="selectedAddress?.id === address.id"
                   class="border rounded-xl p-4 cursor-pointer transition-all">

                <div class="flex items-start justify-between mb-3">
                  <div class="flex items-center gap-3">
                    <!-- Radio Button -->
                    <div [class.border-brand-blue-500]="selectedAddress?.id === address.id"
                         class="w-5 h-5 rounded-full border-2 flex items-center justify-center">
                      <div *ngIf="selectedAddress?.id === address.id"
                           class="w-3 h-3 rounded-full bg-brand-blue-500">
                      </div>
                    </div>

                    <!-- Adres Başlığı -->
                    <div class="flex items-center gap-2">
                      <h3 class="font-medium text-gray-800">{{address.title}}</h3>
                      <div class="flex items-center gap-1">
                        <span class="px-2 py-0.5 bg-brand-blue-50 text-brand-blue-500 text-xs rounded-full">
                          {{address.address_type === 'individual' ? t('individual') : t('corporate')}}
                        </span>
                        <span *ngIf="address.active"
                              class="px-2 py-0.5 bg-brand-green-50 text-brand-green-500 text-xs rounded-full">
                          {{ t('default') }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Düzenle/Sil Butonları -->
                  <div class="flex items-center gap-2">
                    <button (click)="editAddress($event, address)"
                            class="text-gray-500 transition-colors hover:text-brand-blue-500">
                      <ng-icon name="lucideSquarePen"></ng-icon>
                    </button>
                    <button (click)="deleteAddress($event, address.id)"
                            class="text-gray-500 transition-colors hover:text-red-500">
                      <ng-icon name="lucideTrash2"></ng-icon>
                    </button>
                  </div>
                </div>

                <!-- Adres Detayları -->
                <div class="grid grid-cols-1 gap-4 mt-2 sm:grid-cols-2">
                  <div>
                    <ng-container *ngIf="address.address_type === 'corporate'">
                      <div class="text-sm font-medium text-gray-800">
                        {{address.company_name}}
                      </div>
                      <div class="text-sm text-gray-600">
                        {{ t('tax_no') }}: {{address.tax_number}}
                      </div>
                      <div class="text-sm text-gray-600">
                        {{address.tax_office}} {{ t('tax_office') }}
                      </div>
                    </ng-container>

                    <div class="text-sm font-medium text-gray-800">
                      {{address.full_name}}
                    </div>
                    <div class="text-sm text-gray-600">{{address.phone}}</div>
                  </div>

                  <div>
                    <div class="text-sm text-gray-600">{{address.address}}</div>
                    <div class="text-sm text-gray-600">
                      {{address.district}} / {{address.city}}
                    </div>
                    <div class="text-sm text-gray-600">{{address.zipCode}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sağ Panel - Sepet Özeti -->
      <div>
        <h2 class="flex items-center gap-2 mb-6 text-xl font-semibold text-brand-blue-700">
          <ng-icon name="lucideShoppingCart"></ng-icon>
          <span>{{ t('cart_summary') }}</span>
        </h2>

        <div class="mb-6 bg-white border border-gray-200 rounded-3xl">
          <div class="p-4 border-b border-gray-200">
            <h3 class="font-medium text-gray-800">{{ t('selected_packages') }}</h3>
          </div>

          <div class="p-4 space-y-2 min-h-[12rem]">
            <ng-container *ngFor="let pkg of selectedPackages">
              <div class="flex items-center justify-between p-2 rounded-3xl bg-gray-50">
                <div class="flex items-center gap-2">
                  <div class="flex items-center justify-center w-8 h-8 rounded-full bg-brand-blue-100">
                    <ng-icon name="lucidePackage" class="text-brand-blue-700"></ng-icon>
                  </div>
                  <div>
                    <div class="font-medium text-gray-800">{{pkg.title}}</div>
                    <div class="text-xs text-gray-500">
                      <ng-container *ngIf="!pkg.isCustom">{{pkg.credits}} {{ t('credits') }} × {{pkg.count}} {{ t('piece') }}</ng-container>
                      <ng-container *ngIf="pkg.isCustom">{{pkg.customCredits}} {{ t('credits') }}</ng-container>
                    </div>
                  </div>
                </div>
                <div class="font-semibold text-brand-blue-700">
                  <ng-container *ngIf="!pkg.isCustom">{{pkg.price * pkg.count | number:'1.0-0'}} ₺</ng-container>
                  <ng-container *ngIf="pkg.isCustom">{{calculateFlexPackagePrice(pkg) | number:'1.0-0'}} ₺</ng-container>
                </div>
              </div>
            </ng-container>
          </div>

          <div class="p-4 border-t border-gray-200">
            <div class="flex items-center justify-between mb-1">
              <span class="text-gray-600">{{ t('total_credits') }}</span>
              <span class="font-medium text-gray-800">{{packageData.totalCredits}} {{ t('credits_label') }}</span>
            </div>

            <div class="flex items-center justify-between pt-2 mt-2 border-t border-gray-200">
              <span class="font-medium text-gray-800">{{ t('total') }}</span>
              <span class="text-xl font-bold text-brand-blue-500">
                {{packageData.totalPrice | number:'1.0-0'}} ₺
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class BillingInfoComponent implements OnInit {
    @Input() packageData: any;
    @Output() billingSubmitted = new EventEmitter<any>();

    selectedAddress: Address | null = null;
    addresses: Address[] = [];
    selectedPackages: any[] = [];

    // Calculate price for flex package based on custom credits
    calculateFlexPackagePrice(pkg: any): number {
        if (!pkg.isCustom || !pkg.customCredits) {
            return 0;
        }
        return pkg.pricePerCredit * Number(pkg.customCredits);
    }

    constructor(
        private paymentService: PaymentService,
        private dialog: Dialog,
        private transloco: TranslocoService
    ) { }

    ngOnInit() {
        if (this.packageData) {
            this.selectedPackages = this.packageData.packages;
        }
        this.loadAddresses();
    }

    loadAddresses() {
        this.paymentService.getInvoiceAddresses().subscribe(
            addresses => {
                this.addresses = addresses;
                // Select default address
                const defaultAddress = addresses.find(a => a.active);
                if (defaultAddress) {
                    this.selectAddress(defaultAddress);
                }
            }
        );
    }

    selectAddress(address: Address) {
        this.selectedAddress = address;
        this.billingSubmitted.emit({
            address: this.selectedAddress
        });
    }

    openAddressDialog() {
        const dialogRef = this.dialog.open(AddressDetailComponent);
        dialogRef.closed.subscribe(result => {
            if (result) {
                this.loadAddresses();
            }
        });
    }

    editAddress(event: Event, address: Address) {
        event.stopPropagation();
        const dialogRef = this.dialog.open(AddressDetailComponent, {
            data: { address }
        });
        dialogRef.closed.subscribe(result => {
            if (result) {
                this.loadAddresses();
            }
        });
    }

    deleteAddress(event: Event, id: number) {
        event.stopPropagation();
        if (confirm(this.transloco?.translate('shared.payment.billing_info.confirm_delete_address'))) {
            this.paymentService.deleteInvoiceAddress(id.toString()).subscribe(() => {
                // Find the index of deleted address
                const deletedIndex = this.addresses.findIndex(a => a.id === id);
                // If there are other addresses and the deleted address was selected
                if (this.addresses.length > 1 && this.selectedAddress?.id === id) {
                    // Select the previous address if exists, otherwise select the next one
                    const newSelectedIndex = deletedIndex > 0 ? deletedIndex + 2 : 0;
                    const filteredAddresses = this.addresses.filter((_, index) => index !== deletedIndex);
                    this.addresses = filteredAddresses;
                    const newSelectedAddress = this.addresses[newSelectedIndex];
                    // Update the address with active: true
                    if (newSelectedAddress) {
                        const updatedAddress = { ...newSelectedAddress, active: true };
                        this.paymentService.updateInvoiceAddress(updatedAddress).subscribe(() => {
                            this.loadAddresses();
                        });
                    }
                } else {
                    this.loadAddresses();
                    if (this.selectedAddress?.id === id) {
                        this.selectedAddress = null;
                    }
                }
            });
        }
    }
}
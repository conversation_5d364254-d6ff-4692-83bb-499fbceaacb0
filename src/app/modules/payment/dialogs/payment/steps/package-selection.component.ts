import { Dialog } from '@angular/cdk/dialog';
import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { PaymentService } from '@app/data/services/payment.service';
import { CalculateCreditComponent } from '../../calculate-credit/calculate-credit.component';
import { PackageDetailsComponent } from '../../package-details/package-details.component';
import { ContactUsComponent } from '../../contact-us/contact-us.component';

interface PackageResponse {
    id: number;
    name: string;
    credit: number;
    price: string;
    original_price: string;
    validity_period: number;
    position: number;
    deleted_at: null;
}

interface Package {
    type: string;
    title: string;
    credits: number;
    originalPrice: number;
    discountPercent: number;
    pricePerCredit?: number;
    price: number;
    count: number;
    name: string;
    isCustom?: boolean;
    customCredits?: number;
    id: number;
}

@Component({
    selector: 'app-package-selection',
    template: `
    <div class="grid grid-cols-1 gap-6 p-6 md:grid-cols-3" *transloco="let t;read 'shared.payment.package_selection'">
      <!-- Sol Panel - Paketler -->
      <div class="col-span-2 space-y-6">
        <h2 class="flex items-center gap-2 text-xl font-semibold text-brand-blue-700">
          <ng-icon name="lucideBoxes" class="text-brand-blue-500 text-xl"></ng-icon>
          <span>{{ t('packages') }}</span>
        </h2>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <!-- Paket Kartları -->
          <ng-container *ngFor="let pkg of packagesArray">
            <div class="border-2 transition-all hover:shadow-md  border-gray-200 "
                 [ngClass]="pkg.count > 0 ? 'border-brand-blue-300 bg-brand-blue-50' : 'border-gray-300 bg-white'"
                 class="rounded-3xl p-4">
              <div class="flex justify-between">
                <div>
                  <h3 class="text-lg font-semibold text-brand-blue-700">{{pkg.title}}</h3>
                  <div class="flex items-center gap-2 mt-2">
                    <ng-container *ngIf="pkg.isCustom; else standardPackage">
                      <div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-green-50 text-brand-green-500">
                        {{ t('flexible') }}
                      </div>
                    </ng-container>
                    <ng-template #standardPackage>
                      <div *ngIf="pkg.type != 'corporate' " class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-blue-50 text-brand-blue-500">
                        {{ pkg.credits}} {{ t('credits') }}
                      </div>
                      <div *ngIf="pkg.type == 'corporate' " class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-blue-50 text-brand-blue-500">
                            {{ t('corporate_custom') }}
                      </div>
                      <div *ngIf="pkg.discountPercent > 0" 
                           class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-brand-blue-50 text-brand-blue-700">

                        <span>{{pkg.discountPercent}}% {{ t('discount') }}</span>
                      </div>
                    </ng-template>
                  </div>
                </div>

                <div class="text-right">
                  <div *ngIf="pkg.originalPrice !== pkg.price && pkg.originalPrice && !pkg.isCustom && pkg.type != 'corporate'" 
                       class="text-xs text-gray-500 line-through">
                    {{pkg.originalPrice | number:'1.0-0'}} ₺
                  </div>
                  <div class="text-lg font-bold text-brand-blue-500" *ngIf="pkg.type != 'corporate'">
                    {{pkg.isCustom ? (pkg.pricePerCredit + ' ₺/' + t('credit')) : (pkg.price | number:'1.0-0') + ' ₺'}}
                  </div>
                </div>
              </div>

              <!-- Esnek paket için kredi giriş alanı -->
              <div *ngIf="pkg.isCustom && pkg.count > 0" class="mt-1">
                <label class="block mb-1 text-sm font-medium text-gray-700">{{ t('credit_amount') }}</label>
                <div class="flex items-center gap-2">
                  <input 
                    type="number" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-brand-blue-500" 
                    [ngClass]="{'border-red-500': !isValidCreditAmount(pkg.customCredits)}"
                    [(ngModel)]="pkg.customCredits" 
                    min="1" 
                    max="1000"
                    (change)="updateFlexPackage()" 
                    (blur)="validateAndUpdateFlexPackage()"
                    [placeholder]="t('credit_placeholder')">
                  <span class="text-sm text-gray-500">{{ t('credits_label') }}</span>
                </div>
                <div *ngIf="!isValidCreditAmount(pkg.customCredits)" class="mt-1 text-xs text-red-500">
                  <span *ngIf="pkg.customCredits < 1">{{ t('min_credit_error') }}</span>
                  <span *ngIf="pkg.customCredits > getMaxAvailableFlexCredits()">{{ t('max_credit_error', {max: getMaxAvailableFlexCredits()}) }}</span>
                </div>
                <div *ngIf="isValidCreditAmount(pkg.customCredits)" class="mt-1 text-sm text-brand-blue-700">
                  {{ t('total') }}: {{calculateFlexPackagePrice(pkg) | number:'1.0-0'}} ₺
                </div>
              </div>

              <div class="flex items-center justify-between mt-2">
                <button (click)="showDetails(pkg)" *ngIf="pkg.type != 'corporate'"
                        class="flex items-center gap-1 text-sm transition-colors text-brand-blue-500 hover:text-brand-blue-700">
                  <span>{{ t('details') }}</span>
                  <ng-icon name="lucideArrowRight"></ng-icon>
                </button>
                <div *ngIf="pkg.type == 'corporate'">

                </div>
                <div class="flex items-center gap-2">
                  <ng-container *ngIf="pkg.type !== 'corporate'">
                    <div class="flex items-center overflow-hidden border-2 border-gray-200 rounded-3xl">
                      <button class="flex items-center justify-center w-8 h-8 bg-gray-50 hover:bg-gray-100"
                              (click)="updatePackageCount(pkg.type, -1)"
                              [disabled]="pkg.count === 0">
                        <ng-icon name="lucideMinus" class="text-gray-600"></ng-icon>
                      </button>
                      <div class="flex items-center justify-center w-8 h-8 text-sm font-medium" *ngIf="pkg.type != 'corporate'">
                        {{pkg.count}}
                      </div>
                      <button class="flex items-center justify-center w-8 h-8 bg-gray-50 hover:bg-gray-100" 
                              [disabled]="!canAddPackage(pkg.type)"
                              (click)="updatePackageCount(pkg.type, 1)">
                        <ng-icon name="lucidePlus" class="text-gray-600"></ng-icon>
                      </button>
                    </div>

                    <button (click)="updatePackageCount(pkg.type, 1)" [disabled]="!canAddPackage(pkg.type)"
                            class="px-3 py-2 text-sm text-white transition-colors rounded-3xl bg-brand-blue-500 hover:bg-brand-blue-600 disabled:cursor-not-allowed disabled:bg-gray-300">
                      {{ t('add') }}
                    </button>
                  </ng-container>

                  <button *ngIf="pkg.type === 'corporate'" (click)="showContactUs(pkg)"
                          class="px-3 py-2 text-sm text-white transition-colors rounded-3xl bg-brand-blue-500 hover:bg-brand-blue-600 disabled:cursor-not-allowed disabled:bg-gray-300">
                    {{ t('contact_us') }}
                  </button>
                </div>
              </div>
            </div>
          </ng-container>

          <!-- Kredi Hesaplayıcı -->
          <button (click)="openCreditCalculator()"
                  class="flex items-center justify-center w-full gap-2 px-4 py-2 transition-all border md:justify-start rounded-3xl text-brand-blue-500 bg-brand-blue-50 border-brand-blue-300 hover:bg-brand-blue-100">
            <ng-icon name="lucideCalculator"></ng-icon>
            <span>{{ t('credit_calculator') }}</span>
          </button>
        </div>
      </div>

      <!-- Sağ Panel - Sepet -->
      <div>
        <h2 class="flex items-center gap-2 mb-6 text-xl font-semibold text-brand-blue-700">
          <ng-icon name="lucideShoppingCart"></ng-icon>
          <span>{{ t('cart_summary') }}</span>
        </h2>

        <div class="mb-6 bg-white border border-gray-200 rounded-3xl">
          <div class="p-4 border-b border-gray-200">
            <h3 class="font-medium text-gray-800">{{ t('selected_packages') }}</h3>
          </div>

          <div class="p-4 space-y-2 min-h-[12rem]">
            <ng-container *ngFor="let pkg of getSelectedPackages()">
              <div class="flex items-center justify-between p-2 rounded-3xl bg-gray-50">
                <div class="flex items-center gap-2">
                  <div class="flex items-center justify-center w-8 h-8 rounded-full bg-brand-blue-100">
                    <ng-icon name="lucidePackage" class="text-brand-blue-700"></ng-icon>
                  </div>
                  <div>
                    <div class="font-medium text-gray-800">{{pkg.title}}</div>
                    <div class="text-xs text-gray-500">
                      <span *ngIf="!pkg.isCustom">{{pkg.credits}} {{ t('credits_label') }} × {{pkg.count}} {{ t('piece') }}</span>
                      <span *ngIf="pkg.isCustom">{{pkg.customCredits}} {{ t('credits_label') }}</span>
                    </div>
                  </div>
                </div>
                <div class="font-semibold text-brand-blue-700">
                  <span *ngIf="!pkg.isCustom">{{pkg.price * pkg.count | number:'1.0-0'}} ₺</span>
                  <span *ngIf="pkg.isCustom">{{calculateFlexPackagePrice(pkg) | number:'1.0-0'}} ₺</span>
                </div>
              </div>
            </ng-container>
          </div>

          <div class="p-4 border-t border-gray-200">
            <div class="flex items-center justify-between mb-1">
              <span class="text-gray-600">{{ t('total_credits') }}</span>
              <span class="font-medium text-gray-800">{{totalCredits}} {{ t('credits_label') }}</span>
            </div>

            <div class="flex items-center justify-between pt-2 mt-2 border-t border-gray-200">
              <span class="font-medium text-gray-800">{{ t('total') }}</span>
              <span class="text-xl font-bold text-brand-blue-500">
                {{totalPrice | number:'1.0-0'}} ₺
              </span>
            </div>
          </div>
        </div>


      </div>
    </div>
  `,
})
export class PackageSelectionComponent implements OnInit {
    @Output() packageSelected = new EventEmitter<any>();

    packagesArray: Package[] = [];
    corporatePackage = {
        type: 'corporate',
        title: 'Kurumsal Paket',
        credits: 0,
        originalPrice: 0,
        discountPercent: 0,
        price: 0,
        count: 0,
        name: 'Kurumsal Paket',
        id: 0
    };

    totalCredits: number = 0;
    totalPrice: number = 0;

    constructor(
        private paymentService: PaymentService,
        private dialog: Dialog
    ) { }

    ngOnInit() {
        // Get existing packages from state first, before loading
        const savedState = this.paymentService.getState();
        const savedPackages = savedState.selectedPackages || [];
        
        this.loadPackages(savedPackages);
    }

    loadPackages(savedPackages: any[] = []) {
        this.paymentService.getCreditPackages().subscribe({
            next: (packages: PackageResponse[]) => {
                // Map packages
                this.packagesArray = packages.map(pkg => {
                    // Find if this package was previously selected in the saved state
                    const savedPkg = savedPackages.find(p => p.id === pkg.id);
                    
                    const packageObj: Package = {
                        id: pkg.id,
                        type: pkg.name,
                        title: this.getPackageTitle(pkg.name),
                        credits: pkg.credit,
                        originalPrice: parseFloat(pkg.original_price),
                        price: parseFloat(pkg.price),
                        discountPercent: this.calculateDiscount(pkg.original_price, pkg.price),
                        // Use saved count or default to 0
                        count: savedPkg ? savedPkg.count : 0,
                        name: pkg.name,
                        isCustom: pkg.name === 'flex',
                        pricePerCredit: pkg.name === 'flex' ? parseFloat(pkg.price) : undefined,
                        customCredits: savedPkg && pkg.name === 'flex' ? savedPkg.customCredits : 1 // Default to minimum
                    };
                    
                    return packageObj;
                });

                // Add corporate package
                this.packagesArray.push({
                    ...this.corporatePackage,
                    // Restore count if it was in saved state
                    count: savedPackages.find(p => p.type === 'corporate')?.count || 0
                });

                // Calculate totals based on restored packages
                this.calculateTotals();
            },
            error: (err) => {
                console.error('Failed to load packages:', err);
            }
        });
    }

    private getPackageTitle(name: string): string {
        const titles: { [key: string]: string } = {
            'mini': 'Mini Paket',
            'standart': 'Standart Paket',
            'flex': 'Esnek Paket'
        };
        return titles[name] || name;
    }

    private calculateDiscount(originalPrice: string, price: string): number {
        const original = parseFloat(originalPrice);
        const current = parseFloat(price);
        if (original === current) return 0;
        return Math.round(((original - current) / original) * 100);
    }


    updatePackageCount(packageType: string, change: number, customCredits?: number) {

        const pkg = this.packagesArray.find(p => p.type === packageType);
        
        if (!pkg) return;

        // Special handling for flex package - can only have max 1
        if (pkg.isCustom && pkg.count + change > 1) {
            return;
        }
        
        // If we're adding a flex package with specified customCredits, update it first
        if (pkg.isCustom && change > 0 && customCredits !== undefined) {
            pkg.customCredits = customCredits;

        }
        
        // Calculate new total credits with the potentially updated custom credits
        const newTotalCredits = this.calculateTotalCredits(packageType, change);
        
        // Check against credit limit
        if (newTotalCredits > 1000) {
            alert('Toplam kredi miktarı 1000\'i geçemez!');
            return;
        }

        pkg.count += change;
        
        // If flex package is being added for the first time, initialize with default credits
        // ONLY if customCredits wasn't provided
        if (pkg.isCustom && pkg.count === 1 && customCredits === undefined) {
            // Initialize with maximum available, up to a default of 1
            const maxAvailable = this.getMaxAvailableFlexCredits();
            pkg.customCredits = Math.min(maxAvailable, 1); // Default to 1 credits

        }
        
        // If a standard package was changed, we need to adjust flex package credits if needed
        if (!pkg.isCustom && change > 0) {
            const flexPackage = this.packagesArray.find(p => p.isCustom && p.count > 0);
            if (flexPackage && flexPackage.customCredits) {
                // Get new maximum available for flex
                const maxAvailable = this.getMaxAvailableFlexCredits();
                // If current flex credits exceed new max, reduce it
                if (Number(flexPackage.customCredits) > maxAvailable) {
                    flexPackage.customCredits = maxAvailable;

                }
            }
        }
        
        this.calculateTotals();
    }

    // Check if the package can be added
    canAddPackage(packageType: string): boolean {
        const pkg = this.packagesArray.find(p => p.type === packageType);
        
        if (!pkg) return false;
        
        // For flex package, only allow adding if count is 0
        if (pkg.isCustom) {
            return pkg.count === 0;
        }
        
        // For standard packages, check credit limit
        const totalCredits = this.calculateTotalCredits();
        return totalCredits + pkg.credits <= 1000;
    }

    // Calculate total credits including the flex package
    calculateTotalCredits(changedPackage?: string, change: number = 0): number {
        let totalCredits = 0;
        
        // First calculate standard package credits
        this.packagesArray.forEach(pkg => {
            if (!pkg.isCustom && pkg.type !== 'corporate') {
                const count = pkg.type === changedPackage ? pkg.count + change : pkg.count;
                totalCredits += pkg.credits * count;
            }
        });
        
        // Then add flex package credits if any
        const flexPackage = this.packagesArray.find(p => p.isCustom);
        if (flexPackage) {
            const count = flexPackage.type === changedPackage ? 
                          flexPackage.count + change : 
                          flexPackage.count;
                          
            if (count > 0 && flexPackage.customCredits !== undefined) {
                // Ensure we're adding a valid number
                const flexCredits = Number(flexPackage.customCredits);
                if (!isNaN(flexCredits)) {
                    totalCredits += flexCredits;
                }
            }
        }
        
        return totalCredits;
    }

    // Calculate and update totals
    calculateTotals() {
        // Calculate total credits including flex package
        this.totalCredits = this.calculateTotalCredits();
        
        // Calculate total price
        this.totalPrice = this.packagesArray
            .filter(pkg => pkg.type !== 'corporate')
            .reduce((total, pkg) => {
                if (pkg.isCustom && pkg.count > 0) {
                    // For flex package, calculate price based on custom credits
                    return total + this.calculateFlexPackagePrice(pkg);
                } else {
                    // For standard packages, use price * count
                    return total + (pkg.price * pkg.count);
                }
            }, 0);

        if (this.totalCredits > 0) {
            this.packageSelected.emit({
                packages: this.getSelectedPackages(),
                totalCredits: this.totalCredits,
                totalPrice: this.totalPrice
            });
        }

        this.paymentService.updateState({
            selectedPackages: this.getSelectedPackages(),
            totalCredits: this.totalCredits,
            totalPrice: this.totalPrice
        });
    }

    // Calculate total credits from standard packages only (excluding flex)
    private getTotalCreditsExcludingFlex(): number {
        return this.packagesArray
            .filter(pkg => !pkg.isCustom && pkg.type !== 'corporate')
            .reduce((total, pkg) => total + (pkg.credits * pkg.count), 0);
    }

    // Get maximum available credits for flex package
    getMaxAvailableFlexCredits(): number {
        const standardPackageCredits = this.getTotalCreditsExcludingFlex();
        return Math.max(1, 1000 - standardPackageCredits); // Ensure at least 1 credits minimum
    }

    // Validate custom credit amount for flex package considering total cart limit
    isValidCreditAmount(credits: number | undefined): boolean {
        if (credits === undefined || credits === null || isNaN(Number(credits))) return false;
        
        // Ensure we're working with a number
        const creditValue = Number(credits);
        
        // Check minimum credit value
        if (creditValue < 1) return false;
        
        // Calculate max available credits
        const maxAvailable = this.getMaxAvailableFlexCredits();
        
        // Check if requested credits exceed available limit
        return creditValue <= maxAvailable;
    }

    // Calculate price for flex package based on custom credits
    calculateFlexPackagePrice(pkg: Package): number {
        if (!pkg.isCustom || pkg.count === 0 || !this.isValidCreditAmount(pkg.customCredits)) {
            return 0;
        }
        return pkg.pricePerCredit! * Number(pkg.customCredits!);
    }

    // Update flex package credits and recalculate totals
    updateFlexPackage() {
        const flexPackage = this.packagesArray.find(p => p.isCustom);
        if (!flexPackage || flexPackage.count === 0) return;

        // Validate and sanitize the input first
        this.validateAndUpdateFlexPackage();
        
        // Recalculate totals (already done in validateAndUpdateFlexPackage)
    }

    // Validate and enforce limits when input loses focus or changes
    validateAndUpdateFlexPackage() {
        const flexPackage = this.packagesArray.find(p => p.isCustom);
        if (!flexPackage || flexPackage.count === 0) return;

        // First ensure it's a valid number
        if (flexPackage.customCredits === undefined || 
            flexPackage.customCredits === null || 
            isNaN(Number(flexPackage.customCredits))) {
            flexPackage.customCredits = 1; // Default to minimum if invalid
        }

        // Convert to number to ensure proper comparisons
        flexPackage.customCredits = Number(flexPackage.customCredits);

        // Apply min/max constraints
        const maxAvailable = this.getMaxAvailableFlexCredits();
        if (flexPackage.customCredits < 1) {
            flexPackage.customCredits = 1;
        } else if (flexPackage.customCredits > maxAvailable) {
            flexPackage.customCredits = maxAvailable;
        }

        // Force integer value
        flexPackage.customCredits = Math.floor(flexPackage.customCredits);
        
        // Update totals
        this.calculateTotals();
    }

    getSelectedPackages() {
        // Return packages with count > 0, including necessary custom properties for flex package
        return this.packagesArray.filter(pkg => pkg.count > 0 && pkg.type !== 'corporate')
            .map(pkg => {
                if (pkg.isCustom) {
                    // For flex package, include customCredits in the result
                    return {
                        ...pkg,
                        credits: pkg.customCredits || 0 // Use custom credits instead of default credits
                    };
                }
                return pkg;
            });
    }

    showDetails(pkg: Package) {
        // Create a clone of the package to avoid directly modifying the original
        const packageCopy = JSON.parse(JSON.stringify(pkg));
        
        const dialogRef = this.dialog.open(PackageDetailsComponent, {
            data: { package: packageCopy }
        });

        dialogRef.closed.subscribe((result: any) => {
            // Check if the result is an object with action and packageData
            if (typeof result === 'object' && result?.action === 'add') {

                
                // Use the returned package data with customCredits for flex packages
                if (result.packageData.isCustom && result.packageData.customCredits) {
                    // Call updatePackageCount with custom credits value
                    this.updatePackageCount(
                        'flex', 
                        1, 
                        Number(result.packageData.customCredits)
                    );
                } else {
                    // For standard packages, just add it
                    this.updatePackageCount(pkg.type, 1);
                }
            } else if (result === 'add') {
                // Handle legacy string result for backward compatibility
                this.updatePackageCount(pkg.type, 1);
            }
        });
    }

    openCreditCalculator() {
        this.dialog.open(CalculateCreditComponent)
    }
    
    showContactUs(pkg: any){
        this.dialog.open(ContactUsComponent)
    }
}
import { Dialog } from '@angular/cdk/dialog';
import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { PaymentService } from '@app/data/services/payment.service';
import { CouponService } from '@app/data/services/coupon.service';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { AgreementComponent } from '@app/shared/components/agreement/agreement.component';
@Component({
    selector: 'app-payment-summary',
    template: `
<div class="flex flex-col justify-between h-full" *transloco="let t;read 'shared.payment.payment_summary'">

    <div class="grid grid-cols-1 gap-4 p-4 lg:grid-cols-2">
        <!-- Sol Panel: Sipari<PERSON> Ö<PERSON>ti -->
        <div class="grid grid-cols-1 gap-3">
            <!-- Seçilen Paketler -->
            <div class="bg-white border border-gray-200 rounded-3xl h-min">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-base font-medium text-gray-800">{{ t('selected_packages') }}</h3>
                </div>

                <div class="p-4 space-y-2">
                    <div *ngFor="let pkg of selectedPackages" class="flex items-center justify-between py-1">
                        <div class="flex items-center gap-2">
                            <div class="flex items-center justify-center w-6 h-6 rounded-full bg-brand-blue-100">
                                <ng-icon name="lucidePackage" class="text-brand-blue-500"></ng-icon>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">{{pkg.title}}</div>
                                <div class="text-sm text-gray-500">
                                    <ng-container *ngIf="!pkg.isCustom">{{pkg.credits}} {{ t('credits') }} ×
                                        {{pkg.count}}</ng-container>
                                    <ng-container *ngIf="pkg.isCustom">{{pkg.customCredits}} {{ t('credits')
                                        }}</ng-container>
                                </div>
                            </div>
                        </div>
                        <div class="text-sm font-medium text-brand-blue-700">
                            <ng-container *ngIf="!pkg.isCustom">{{pkg.price * pkg.count | number:'1.0-0'}}
                                ₺</ng-container>
                            <ng-container *ngIf="pkg.isCustom">{{calculateFlexPackagePrice(pkg) | number:'1.0-0'}}
                                ₺</ng-container>
                        </div>
                    </div>

                    <!-- Fiyat Detayları -->
                    <div class="pt-2 mt-2 border-t border-gray-200">
                        <div class="flex items-center justify-between text-base">
                            <span class="text-gray-600">{{ t('products_total') }}</span>
                            <span class="font-medium text-gray-800">{{subTotal | number:'1.0-0'}} ₺</span>
                        </div>
                        <div *ngIf="appliedCoupon" class="flex items-center justify-between text-base">

                            <div class="flex items-center gap-1 text-status-success-500">
                                <ng-icon name="lucidePercent" class="text-status-success-500"></ng-icon>
                                <span>{{ t('discount') }} ({{appliedCoupon.code}})</span>
                            </div>

                            <div class="flex items-center justify-between text-base">
                                <span class="font-medium text-status-success-500">
                                    -{{discount | number:'1.0-0'}} ₺
                                </span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between pt-2 mt-2 border-t border-gray-200">
                            <span class="font-medium text-gray-800">{{ t('total') }}</span>
                            <span class="text-xl font-bold text-brand-blue-500">
                                {{total | number:'1.0-0'}} ₺
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fatura Adresi -->
            <div class="bg-white border border-gray-200 rounded-3xl h-min">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-800">{{ t('billing_address') }}</h3>
                </div>

                <div class="p-4">
                    <div class="flex items-start justify-between mb-1">
                        <div class="text-sm font-medium text-gray-800">{{billingAddress?.title}}</div>
                        <span class="px-1.5 py-0.5 bg-brand-blue-50 text-brand-blue-500 text-xs rounded-full">
                            {{billingAddress?.type === 'individual' ? t('individual') : t('corporate')}}
                        </span>
                    </div>

                    <div class="text-sm">
                        <ng-container *ngIf="billingAddress?.type === 'corporate'">
                            <div class="font-medium text-gray-800">{{billingAddress?.companyName}}</div>
                            <div class="text-gray-600">{{ t('tax_no') }}: {{billingAddress?.taxNumber}}</div>
                            <div class="text-gray-600">{{billingAddress?.taxOffice}}</div>
                        </ng-container>

                        <div class="font-medium text-gray-800">{{billingAddress?.name}}</div>
                        <div class="text-gray-600">{{billingAddress?.phone}}</div>
                        <div class="mt-1 text-gray-600">{{billingAddress?.address}}</div>
                        <div class="text-gray-600">
                            {{billingAddress?.district}} / {{billingAddress?.city}} - {{billingAddress?.zipCode}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sağ Panel: Ödeme Yöntemi -->
        <div class="bg-white border border-gray-200 rounded-3xl">
            <!-- Kupon Kodu -->
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-sm font-medium text-gray-800">{{ t('coupon_code') }}</h3>
            </div>

            <div class="p-2">
                <ng-container *ngIf="appliedCoupon; else couponInput">
                    <div
                        class="flex items-center justify-between p-2 border rounded-3xl bg-status-success-100 border-status-success-500">
                        <div class="flex items-center gap-2">
                            <ng-icon name="lucidePercent" class="text-status-success-500"></ng-icon>
                            <div>
                                <span class="text-sm font-medium text-gray-800">{{appliedCoupon.code}}</span>
                                <p class="text-sm text-gray-600">
                                    {{appliedCoupon.type === 'percent' ?
                                    appliedCoupon.discount + '% ' + t('discount') :
                                    (appliedCoupon.discount | number:'1.0-0') + ' ₺ ' + t('discount')}}
                                </p>
                            </div>
                        </div>
                        <button (click)="removeCoupon()"
                            class="text-sm text-red-500 transition-colors hover:text-red-700">
                            {{ t('remove') }}
                        </button>
                    </div>
                </ng-container>

                <ng-template #couponInput>
                    <div class="flex items-center gap-1">
                        <input type="text" [(ngModel)]="couponCode"
                            class="flex-grow px-3 py-1.5 text-sm rounded-3xl border border-gray-200 focus:border-brand-blue-500 focus:ring-1 focus:ring-brand-blue-100"
                            [placeholder]="t('coupon_placeholder')">
                        <button (click)="applyCoupon()"
                            class="px-3 py-1.5 text-sm text-white rounded-3xl bg-brand-blue-500 hover:bg-brand-blue-600 transition-colors">
                            {{ t('apply') }}
                        </button>
                    </div>
                </ng-template>
            </div>

            <!-- Conditional Payment Method Section -->
            <ng-container *ngIf="total > 0; else zeroPaymentMessage">
                <!-- Ödeme Yöntemi Seçimi -->
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-800">{{ t('payment_method') }}</h3>
                </div>

                <div class="p-3 space-y-3">
                    <!-- Kredi Kartı Seçeneği -->
                    <div (click)="selectPaymentMethod('creditCard')"
                        [class.border-brand-blue-500]="selectedPaymentMethod === 'creditCard'"
                        [class.bg-brand-blue-50]="selectedPaymentMethod === 'creditCard'"
                        class="border rounded-3xl p-4 cursor-pointer transition-all">
                        <div class="flex items-center gap-2">
                            <div [class.border-brand-blue-500]="selectedPaymentMethod === 'creditCard'"
                                class="w-4 h-4 rounded-full border-2 flex items-center justify-center">
                                <div *ngIf="selectedPaymentMethod === 'creditCard'"
                                    class="w-2 h-2 rounded-full bg-brand-blue-500">
                                </div>
                            </div>
                            <div class="flex items-center gap-1">
                                <ng-icon name="lucideCreditCard" class="text-brand-blue-500"></ng-icon>
                                <span class="text-sm font-medium text-gray-800">{{ t('credit_card') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- EFT/Havale Seçeneği -->
                    <div (click)="selectPaymentMethod('bankTransfer')"
                        [class.border-brand-blue-500]="selectedPaymentMethod === 'bankTransfer'"
                        [class.bg-brand-blue-50]="selectedPaymentMethod === 'bankTransfer'"
                        class="border rounded-3xl p-4 cursor-pointer transition-all">
                        <div class="flex items-center gap-2">
                            <div [class.border-brand-blue-500]="selectedPaymentMethod === 'bankTransfer'"
                                class="w-4 h-4 rounded-full border-2 flex items-center justify-center">
                                <div *ngIf="selectedPaymentMethod === 'bankTransfer'"
                                    class="w-2 h-2 rounded-full bg-brand-blue-500">
                                </div>
                            </div>
                            <div class="flex items-center gap-1">
                                <ng-icon name="lucideBuilding2" class="text-brand-blue-500"></ng-icon>
                                <span class="text-sm font-medium text-gray-800">{{ t('bank_transfer') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Ödeme Yöntemi Bilgilendirmesi -->
                    <div class="flex items-start gap-2 p-4 rounded-3xl bg-status-info-100">
                        <ng-icon name="lucideInfo" class="text-brand-blue-500 mt-0.5"></ng-icon>
                        <p class="text-sm text-gray-700">
                            {{ selectedPaymentMethod === 'creditCard' ? t('credit_card_info') : t('bank_transfer_info')
                            }}
                        </p>
                    </div>
                </div>
            </ng-container>

            <ng-template #zeroPaymentMessage>
                <!-- Zero Amount Message -->
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-800">{{ t('payment_info') }}</h3>
                </div>

                <div class="p-4">
                    <div class="flex items-center gap-2 p-4 rounded-3xl bg-status-success-100">
                        <ng-icon name="lucideCheck" class="text-status-success-500 mt-0.5"></ng-icon>
                        <div>
                            <p class="text-sm font-medium text-gray-800">{{ t('free_order') }}</p>
                            <p class="text-sm text-gray-600">{{ t('free_order_info') }}</p>
                        </div>
                    </div>
                </div>
            </ng-template>
        </div>
    </div>
    <!-- Sözleşme Onayı -->
    <div class="flex items-center p-4 w-full justify-end">
        <input type="checkbox" [(ngModel)]="agreed" (click)="onAgreementCheckboxClick($event)" id="agreement"
            class="size-5 mt-0.5 border-gray-300 rounded-lg text-brand-blue-500 focus:ring-brand-blue-500">
        <label for="agreement" class="ml-1.5 text-sm text-gray-700">
            {{ t('agreement_text') }}
        </label>
    </div>
</div>
  `
})
export class PaymentSummaryComponent implements OnInit {
    @Input() packageData: any;
    @Input() billingInfo: any;
    @Output() paymentCompleted = new EventEmitter<any>();

    selectedPackages: any[] = [];
    billingAddress: any;
    selectedPaymentMethod: 'creditCard' | 'bankTransfer' = 'creditCard';
    agreed: boolean = false;

    couponCode: string = '';
    appliedCoupon: any = null;

    subTotal: number = 0;
    discount: number = 0;
    total: number = 0;

    isDialogOpen = false;
    isAccepted = false;

    constructor(
        private paymentService: PaymentService,
        private couponService: CouponService,
        private snotifyService: SnotifyService,
        private transloco: TranslocoService,
        private dialog: Dialog
    ) { }

    ngOnInit() {
        if (this.packageData && this.billingInfo) {
            this.selectedPackages = this.packageData.packages;
            this.billingAddress = this.billingInfo.address;
            this.calculateTotals();

            // Set default payment method - for zero amount, we'll use bankTransfer
            this.selectedPaymentMethod = this.total === 0 ? 'bankTransfer' : 'creditCard';

            this.paymentService.updateState({
                paymentMethod: this.selectedPaymentMethod
            });
        }
    }

    get canProceed(): boolean {
        return this.agreed && !!this.selectedPaymentMethod;
    }

    calculateTotals() {
        this.subTotal = this.selectedPackages.reduce((total, pkg) => {
            if (pkg.isCustom) {
                // For flex package, calculate price based on custom credits
                return total + this.calculateFlexPackagePrice(pkg);
            } else {
                // For standard packages, use price * count
                return total + (pkg.price * pkg.count);
            }
        }, 0);

        this.discount = this.appliedCoupon ?
            (this.appliedCoupon.type === 'percent' ?
                this.subTotal * (this.appliedCoupon.discount / 100) :
                this.appliedCoupon.discount) : 0;

        const discountedTotal = this.subTotal - this.discount;
        this.total = discountedTotal

        // After setting this.total, update the payment method if total is 0
        if (this.total === 0 && this.selectedPaymentMethod !== 'bankTransfer') {
            this.selectedPaymentMethod = 'bankTransfer';
            this.paymentService.updateState({
                paymentMethod: 'bankTransfer'
            });
        }

        // Update state
        this.paymentService.updateState({
            subTotal: this.subTotal,
            discount: this.discount,
            total: this.total,
            appliedCoupon: this.appliedCoupon
        });
    }

    // Calculate price for flex package based on custom credits
    calculateFlexPackagePrice(pkg: any): number {
        if (!pkg.isCustom || !pkg.customCredits) {
            return 0;
        }
        return pkg.pricePerCredit * Number(pkg.customCredits);
    }

    selectPaymentMethod(method: 'creditCard' | 'bankTransfer') {
        this.selectedPaymentMethod = method;
        // Update the payment service state
        this.paymentService.updateState({
            paymentMethod: method
        });
    }

    applyCoupon() {
        if (!this.couponCode.trim()) return;

        const order = {
            order: {
                coupon_code: this.couponCode,
                price: this.subTotal,
                order_items_data: this.selectedPackages.map(pkg => {
                    // Basic order item structure
                    const orderItem: any = {
                        credit_package_id: pkg.id,
                        quantity: pkg.isCustom && pkg.customCredits ? pkg.customCredits : pkg.count 
                    };

                    return orderItem;
                })
            }
        };

        this.couponService.applyCoupon(order).subscribe({
            next: (data: any) => {
                this.appliedCoupon = {
                    code: this.couponCode,
                    type: 'amount',
                    discount: Number(data.discount_amount)
                };
                this.calculateTotals();
                this.couponCode = '';

                this.snotifyService.success(
                    this.transloco.translate('notification.payment.coupon.success.message'),
                    this.transloco.translate('notification.payment.coupon.success.title'),
                    {
                        timeout: 1500,
                        showProgressBar: true,
                        closeOnClick: true,
                        pauseOnHover: false,
                        position: 'centerBottom',
                    }
                );

                this.paymentService.updateState({
                    appliedCoupon: this.appliedCoupon
                });
            },
            error: (error) => {
                this.snotifyService.error(
                    this.transloco.translate('notification.payment.coupon.error.message'),
                    this.transloco.translate('notification.payment.coupon.error.title'),
                    {
                        timeout: 1500,
                        showProgressBar: true,
                        closeOnClick: true,
                        pauseOnHover: false,
                        position: 'centerBottom',
                    }
                );
            }
        });
    }

    removeCoupon() {
        this.appliedCoupon = null;
        this.calculateTotals();

        this.snotifyService.success(
            this.transloco.translate('notification.payment.coupon.remove.message'),
            this.transloco.translate('notification.payment.coupon.remove.title'),
            {
                timeout: 1500,
                showProgressBar: true,
                closeOnClick: true,
                pauseOnHover: false,
                position: 'centerBottom',
            }
        );

        this.paymentService.updateState({
            appliedCoupon: null
        });
    }

    goToPreviousStep() {
        this.paymentCompleted.emit({
            success: false,
            action: 'back'
        });
    }

    showAgreementModal() {
        this.isDialogOpen = true;
        const agreement = this.dialog.open(AgreementComponent, {
            data: {
                payment_method: this.selectedPaymentMethod,
                type: 'distance_sale_agreement'
            }
        });

        agreement.closed.subscribe((result: boolean) => {
            this.isDialogOpen = false;
            this.agreed = result;
            this.isAccepted = result;
            // Update the payment service state
            this.paymentService.updateState({
                agreed: result
            });
        });
    }

    onAgreementCheckboxClick(event: Event) {
        event.preventDefault(); // Checkbox'ın default davranışını engelle
        if (!this.agreed) {
            this.showAgreementModal();
        } else {
            this.agreed = false;
            this.isAccepted = false;
        }
    }
}

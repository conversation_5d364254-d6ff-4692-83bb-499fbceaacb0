<div *ngIf="status" class="flex items-center justify-center w-full h-screen ">
    <div class="flex flex-col items-center justify-center w-1/3 gap-4 p-2 bg-white rounded-md"
        *transloco="let t; read 'shared.credit_card_result'">
        <div *ngIf="status === 'approved'">
            <svg viewBox="0 0 512 512" version="1.1" height="124" width="124" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" stroke="#000000">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="add-copy" fill="#22C55E" transform="translate(42.666667, 42.666667)">
                        <path
                            d="M213.333333,3.55271368e-14 C95.51296,3.55271368e-14 3.55271368e-14,95.51296 3.55271368e-14,213.333333 C3.55271368e-14,331.153707 95.51296,426.666667 213.333333,426.666667 C331.153707,426.666667 426.666667,331.153707 426.666667,213.333333 C426.666667,95.51296 331.153707,3.55271368e-14 213.333333,3.55271368e-14 Z M213.333333,384 C119.227947,384 42.6666667,307.43872 42.6666667,213.333333 C42.6666667,119.227947 119.227947,42.6666667 213.333333,42.6666667 C307.43872,42.6666667 384,119.227947 384,213.333333 C384,307.43872 307.438933,384 213.333333,384 Z M293.669333,137.114453 L323.835947,167.281067 L192,299.66912 L112.916693,220.585813 L143.083307,190.4192 L192,239.335893 L293.669333,137.114453 Z"
                            id="Shape"> </path>
                    </g>
                </g>
            </svg>
        </div>
        <div *ngIf="status !== 'approved' && status != null">
            <svg height="124" width="124" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns"
                fill="#000000">
                <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
                    <g id="Icon-Set" sketch:type="MSLayerGroup" transform="translate(-568.000000, -1087.000000)"
                        fill="#f50000">
                        <path
                            d="M584,1117 C576.268,1117 570,1110.73 570,1103 C570,1095.27 576.268,1089 584,1089 C591.732,1089 598,1095.27 598,1103 C598,1110.73 591.732,1117 584,1117 L584,1117 Z M584,1087 C575.163,1087 568,1094.16 568,1103 C568,1111.84 575.163,1119 584,1119 C592.837,1119 600,1111.84 600,1103 C600,1094.16 592.837,1087 584,1087 L584,1087 Z M589.717,1097.28 C589.323,1096.89 588.686,1096.89 588.292,1097.28 L583.994,1101.58 L579.758,1097.34 C579.367,1096.95 578.733,1096.95 578.344,1097.34 C577.953,1097.73 577.953,1098.37 578.344,1098.76 L582.58,1102.99 L578.314,1107.26 C577.921,1107.65 577.921,1108.29 578.314,1108.69 C578.708,1109.08 579.346,1109.08 579.74,1108.69 L584.006,1104.42 L588.242,1108.66 C588.633,1109.05 589.267,1109.05 589.657,1108.66 C590.048,1108.27 590.048,1107.63 589.657,1107.24 L585.42,1103.01 L589.717,1098.71 C590.11,1098.31 590.11,1097.68 589.717,1097.28 L589.717,1097.28 Z"
                            id="cross-circle" sketch:type="MSShapeGroup"> </path>
                    </g>
                </g>
            </svg>
        </div>

        <p *ngIf="status === 'approved' ">
            {{t('approved')}}
        </p>
        <p *ngIf="status !== 'approved' && status != null">
            {{t('declined')}}
        </p>
        <p>
            {{t('info')}}
        </p>
        <p *ngIf="orderId">Order ID: {{ orderId }}</p>
        <button *ngIf="status !== 'approved' && status != null" (click)="close()"
            class="p-2 font-medium text-white bg-red-500 rounded-lg">
            {{t('close')}}
        </button>
    </div>
</div>
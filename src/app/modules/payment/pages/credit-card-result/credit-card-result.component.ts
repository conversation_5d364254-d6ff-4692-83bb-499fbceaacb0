import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PaymentService } from '@app/data/services/payment.service';
import { SnotifyService } from 'ng-alt-snotify';

@Component({
  selector: 'app-credit-card-result',
  templateUrl: './credit-card-result.component.html',
  styleUrls: ['./credit-card-result.component.scss']
})
export class CreditCardResultComponent implements OnInit {
  status: string | null = null;
  orderId: string | null = null;
  orderResponse: any = null;

  constructor(private route: ActivatedRoute,
    private paymentService: PaymentService,
    private snotifyService: SnotifyService,
  ) { }
  orderProducts
  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.orderId = params['order_id'];
      this.paymentService.getOrder(this.orderId).subscribe(res => {
        this.orderResponse = res;
        this.status = res.status;
        this.orderProducts = res.order_items;
        setTimeout(() => {
          this.close();
        }, 3000);
      }
      );
    }
    );
  }
  close() {
    window.parent.location.reload();
  }
}


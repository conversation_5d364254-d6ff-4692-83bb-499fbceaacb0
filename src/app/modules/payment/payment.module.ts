import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@app/shared/shared.module';
import { PaymentComponent } from './dialogs/payment/payment.component';
import { EftComponent } from './dialogs/eft/eft.component';
import { CreditCardComponent } from './dialogs/credit-card/credit-card.component';
import { BalanceComponent } from './dialogs/balance/balance.component';
import { AddressDetailComponent } from './dialogs/address-detail/address-detail.component';
import { CalculateCreditComponent } from './dialogs/calculate-credit/calculate-credit.component';
import { ContactUsComponent } from './dialogs/contact-us/contact-us.component';
import { CouponComponent } from './dialogs/coupon/coupon.component';
import { BillingInfoComponent } from './dialogs/payment/steps/billing-info.component';
import { PackageSelectionComponent } from './dialogs/payment/steps/package-selection.component';
import { PaymentSummaryComponent } from './dialogs/payment/steps/payment-summary.component';
import { PackageDetailsComponent } from './dialogs/package-details/package-details.component';
import { LoadingOverlayComponent } from './dialogs/payment/loading-overlay.component';

@NgModule({
  declarations: [
    PaymentComponent,
    EftComponent,
    CreditCardComponent,
    BalanceComponent,
    AddressDetailComponent,
    CalculateCreditComponent,
    ContactUsComponent,
    CouponComponent,
    BillingInfoComponent,
    PackageSelectionComponent,
    PaymentSummaryComponent,
    PackageDetailsComponent,
    LoadingOverlayComponent
  ],
  imports: [
    CommonModule,
    SharedModule
  ]
})
export class PaymentModule { }

// src/app/modules/projects/dialogs/add-member/add-member.component.ts

import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DialogRef, DIALOG_DATA } from '@angular/cdk/dialog';
import { TranslocoService } from '@ngneat/transloco';

export interface AddMemberDialogData {
  projectId: number;
}

export interface AddMemberDialogResult {
  email: string;
}

@Component({
  selector: 'app-add-member',
  template: `
    <div class="p-6 min-w-0 bg-white rounded-3xl max-w-md mx-auto">
      <h2 class="mb-4 text-xl font-bold text-neutral-950">{{transloco.translate('project_detail.add_member.title')}}</h2>
      
      <form [formGroup]="memberForm" (ngSubmit)="onSubmit()">
        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-neutral-700">
            {{transloco.translate('project_detail.add_member.email_label')}} *
          </label>
          <input
            type="email"
            formControlName="email"
            class="w-full px-3 py-2 border border-neutral-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
            [placeholder]="currentPlaceholder"
            [class.border-red-500]="memberForm.get('email')?.invalid && memberForm.get('email')?.touched"
            [class.focus:ring-red-500]="memberForm.get('email')?.invalid && memberForm.get('email')?.touched"
            (focus)="onEmailFocus()"
            (blur)="onEmailBlur()"
          />
          <div *ngIf="memberForm.get('email')?.invalid && memberForm.get('email')?.touched" 
               class="mt-1 text-sm text-red-600">
            <span *ngIf="memberForm.get('email')?.errors?.['required']">
              {{transloco.translate('project_detail.add_member.form_validation.email_required')}}
            </span>
            <span *ngIf="memberForm.get('email')?.errors?.['email']">
              {{transloco.translate('project_detail.add_member.form_validation.email_format')}}
            </span>
          </div>
        </div>

        <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-xl info-box">
          <div class="flex items-start gap-2">
            <ng-icon name="lucideInfo" class="text-blue-600 mt-0.5 flex-shrink-0"></ng-icon>
            <div class="text-sm text-blue-800">
              <p class="font-medium mb-1">{{transloco.translate('project_detail.add_member.info_title')}}</p>
              <p>{{transloco.translate('project_detail.add_member.info_description')}}</p>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-3 mt-6">
          <button
            type="button"
            (click)="onCancel()"
            class="secondary-status-error-button">
            {{transloco.translate('project_detail.add_member.cancel')}}
          </button>
          <button
            type="submit"
            [disabled]="memberForm.invalid || isSubmitting"
            class="primary-blue-button">
            <div *ngIf="isSubmitting" class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            {{isSubmitting ? transloco.translate('project_detail.add_member.submitting') : transloco.translate('project_detail.add_member.submit')}}
          </button>
        </div>
      </form>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      max-width: 500px;
    }

    .form-field {
      transition: all 0.2s ease-in-out;
    }

    .form-field:focus-within {
      transform: translateY(-1px);
    }

    input {
      transition: all 0.2s ease-in-out;
    }

    input:focus {
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    button {
      transition: all 0.2s ease-in-out;
    }

    button:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    button:active:not(:disabled) {
      transform: translateY(0);
    }

    .info-box {
      animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `]
})
export class AddMemberComponent {
  memberForm: FormGroup;
  isSubmitting = false;
  currentPlaceholder = '';

  constructor(
    private fb: FormBuilder,
    private dialogRef: DialogRef<AddMemberDialogResult, AddMemberComponent>,
    @Inject(DIALOG_DATA) public data: AddMemberDialogData,
    public transloco: TranslocoService
  ) {
    this.memberForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });

    // Set initial placeholder
    this.currentPlaceholder = this.transloco.translate('project_detail.add_member.email_placeholder');

    // Setup dialog behavior
    this.setupDialogBehavior();
  }

  private setupDialogBehavior(): void {
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => this.onCancel());
  }

  onSubmit(): void {
    if (this.memberForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      console.log('Adding member with email:', this.memberForm.value.email);

      // Simulate API delay for better UX
      setTimeout(() => {
        const result: AddMemberDialogResult = {
          email: this.memberForm.value.email.trim().toLowerCase()
        };
        console.log('Closing add member dialog with result:', result);
        this.dialogRef.close(result);
      }, 300);
    }
  }

  onCancel(): void {
    console.log('Add member dialog cancelled');
    this.dialogRef.close();
  }

  // Email input'una focus verildiğinde placeholder'ı değiştir
  onEmailFocus(): void {
    this.currentPlaceholder = this.transloco.translate('project_detail.add_member.email_placeholder_focused');
  }

  onEmailBlur(): void {
    const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement;
    if (emailInput && !emailInput.value) {
      this.currentPlaceholder = this.transloco.translate('project_detail.add_member.email_placeholder');
    }
  }
}
<div *transloco="let t ; read: 'shared.create_project'">
    <div class="overflow-hidden bg-white shadow-xl rounded-3xl">
        <!-- Header -->
        <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
            <div class="flex items-center gap-2 text-neutral-950">
                <ng-icon name="lucideFolderPlus" class="text-2xl"></ng-icon>
                <h3 class="text-2xl font-medium">{{data.project!=null ? t('update_project') :
                    t('new_project')}}</h3>
            </div>
            <button class="text-gray-600 hover:text-neutral-950" (click)="checkChanges()">
                <ng-icon name="lucideX" class="text-2xl"></ng-icon>
            </button>
        </div>

        <form [formGroup]="form" (submit)="data.project ==null ? createProject(): updateProject()">
            <div class="p-6 space-y-6">
                <!-- Project Name -->
                <div>
                    <label class="block mb-2 text-xl font-medium text-neutral-950">{{t('name')}}</label>
                    <input required type="text" formControlName="name" placeholder="{{t('name_placeholder')}}"
                        class="w-full p-3 border border-gray-200 rounded-3xl focus:ring-2 focus:ring-blue-100 focus:border-blue-500">
                    <div *ngIf="submitted && form.get('name').hasError('required')" class="mt-1 text-xs text-red-500">
                        {{t('name_is_required')}}
                    </div>
                </div>

                <!-- Dataset Upload -->
                <div>
                    <label class="block mb-2 text-xl font-medium text-neutral-950">{{t('dataset')}}
                        <span class="text-base text-gray-700">({{t('optional')}})</span>
                    </label>

                    <!-- Show Current File If Exists -->
                    <div *ngIf="file"
                        class="flex items-center justify-between p-3 mb-3 border border-blue-200 rounded-3xl bg-blue-50">
                        <div class="flex items-center gap-2">
                            <ng-icon name="lucideFileSpreadsheet" class="text-xl text-brand-blue-400 "></ng-icon>
                            <span class="max-w-xs text-sm truncate text-neutral-950">{{file.name}}</span>
                        </div>
                        <button type="button" class="p-1 text-gray-400 hover:text-red-500" (click)="resetFileInput()">
                            <ng-icon name="lucideTrash2" class="text-xl"></ng-icon>
                        </button>
                    </div>

                    <!-- Upload Area -->
                    <label for="dataset" [ngClass]="{'border-brand-blue-500': isDragging}" *ngIf="!file"
                        class="block p-4 transition-colors border-2 border-gray-200 border-dashed cursor-pointer group rounded-3xl hover:border-brand-blue-500"
                        (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onDrop($event)">
                        <div class="flex flex-col items-center gap-2">
                            <ng-icon name="lucideCloudUpload"
                                class="text-xl text-gray-400 group-hover:text-brand-blue-500"></ng-icon>
                            <div class="text-sm text-gray-500">
                                <span class="text-brand-blue-400 hover:underline">{{t('upload_dataset')}}</span>
                                {{t('or_drag_drop')}}
                            </div>
                            <div class="text-xs text-gray-400">
                                {{t('excel_only')}}
                            </div>
                        </div>
                        <input #fileInput (change)="handleFileInput($event)" type="file" id="dataset" accept=".xlsx"
                            class="hidden">
                    </label>
                </div>

                <!-- Example Section -->
                <div class="flex items-center justify-between gap-2 p-4 rounded-3xl bg-brand-blue-100">
                    <div class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <path
                                d="M19.581 15.3499L8.51196 13.3999V27.8089C8.51183 27.9655 8.54261 28.1206 8.60255 28.2653C8.66248 28.41 8.75038 28.5414 8.86121 28.6521C8.97205 28.7627 9.10363 28.8504 9.24842 28.9101C9.39321 28.9698 9.54835 29.0003 9.70496 28.9999H28.805C28.9617 29.0006 29.1171 28.9702 29.2621 28.9107C29.4071 28.8511 29.539 28.7635 29.65 28.6528C29.7611 28.5421 29.8491 28.4106 29.9092 28.2658C29.9692 28.1209 30.0001 27.9657 30 27.8089V22.4999L19.581 15.3499Z"
                                fill="#185C37" />
                            <path
                                d="M19.581 3H9.70496C9.54835 2.99961 9.39321 3.03013 9.24842 3.08982C9.10363 3.14951 8.97205 3.23719 8.86121 3.34784C8.75038 3.45848 8.66248 3.58992 8.60255 3.73461C8.54261 3.8793 8.51183 4.03439 8.51196 4.191V9.5L19.581 16L25.442 17.95L30 16V9.5L19.581 3Z"
                                fill="#21A366" />
                            <path d="M8.51196 9.5H19.581V16H8.51196V9.5Z" fill="#107C41" />
                            <path opacity="0.1"
                                d="M16.434 8.19995H8.51196V24.45H16.434C16.7496 24.4484 17.052 24.3225 17.2755 24.0996C17.4989 23.8766 17.6256 23.5746 17.628 23.259V9.39095C17.6256 9.07529 17.4989 8.77327 17.2755 8.55034C17.052 8.32741 16.7496 8.20152 16.434 8.19995Z"
                                fill="black" />
                            <path opacity="0.2"
                                d="M15.783 8.8501H8.51196V25.1001H15.783C16.0986 25.0985 16.401 24.9726 16.6245 24.7497C16.848 24.5268 16.9746 24.2248 16.977 23.9091V10.0411C16.9746 9.72544 16.848 9.42342 16.6245 9.20049C16.401 8.97756 16.0986 8.85167 15.783 8.8501Z"
                                fill="black" />
                            <path opacity="0.2"
                                d="M15.783 8.8501H8.51196V23.8001H15.783C16.0986 23.7985 16.401 23.6726 16.6245 23.4497C16.848 23.2268 16.9746 22.9248 16.977 22.6091V10.0411C16.9746 9.72544 16.848 9.42342 16.6245 9.20049C16.401 8.97756 16.0986 8.85167 15.783 8.8501Z"
                                fill="black" />
                            <path opacity="0.2"
                                d="M15.132 8.8501H8.51196V23.8001H15.132C15.4476 23.7985 15.75 23.6726 15.9735 23.4497C16.1969 23.2268 16.3236 22.9248 16.326 22.6091V10.0411C16.3236 9.72544 16.1969 9.42342 15.9735 9.20049C15.75 8.97756 15.4476 8.85167 15.132 8.8501Z"
                                fill="black" />
                            <path
                                d="M3.194 8.8501H15.132C15.4482 8.84984 15.7516 8.97514 15.9755 9.19846C16.1994 9.42179 16.3255 9.72487 16.326 10.0411V21.9591C16.3255 22.2753 16.1994 22.5784 15.9755 22.8017C15.7516 23.0251 15.4482 23.1504 15.132 23.1501H3.194C3.03731 23.1506 2.88205 23.1202 2.73715 23.0606C2.59224 23.0009 2.46054 22.9133 2.3496 22.8026C2.23866 22.692 2.15067 22.5605 2.09068 22.4157C2.03068 22.271 1.99987 22.1158 2 21.9591V10.0411C1.99987 9.88441 2.03068 9.72923 2.09068 9.58448C2.15067 9.43972 2.23866 9.30824 2.3496 9.19758C2.46054 9.08692 2.59224 8.99926 2.73715 8.93963C2.88205 8.88 3.03731 8.84958 3.194 8.8501Z"
                                fill="url(#paint0_linear_2482_13654)" />
                            <path
                                d="M5.69995 19.873L8.21095 15.989L5.91095 12.127H7.75795L9.01295 14.6C9.12895 14.834 9.21295 15.008 9.25095 15.124H9.26795C9.34995 14.9366 9.43662 14.7546 9.52795 14.578L10.87 12.131H12.57L10.211 15.971L12.63 19.876H10.821L9.37095 17.165C9.30382 17.0483 9.24662 16.9262 9.19995 16.8H9.17595C9.13357 16.923 9.0772 17.0408 9.00795 17.151L7.51495 19.873H5.69995Z"
                                fill="white" />
                            <path
                                d="M28.8061 3.00001H19.5811V9.50001H30.0001V4.19101C30.0002 4.03431 29.9694 3.87913 29.9094 3.73438C29.8494 3.58963 29.7614 3.45815 29.6505 3.34749C29.5395 3.23682 29.4078 3.14916 29.2629 3.08953C29.118 3.0299 28.9627 2.99948 28.8061 3.00001Z"
                                fill="#33C481" />
                            <path d="M19.5811 16H30.0001V22.5H19.5811V16Z" fill="#107C41" />
                            <defs>
                                <linearGradient id="paint0_linear_2482_13654" x1="4.494" y1="7.9141" x2="13.832"
                                    y2="24.0861" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#18884F" />
                                    <stop offset="0.5" stop-color="#117E43" />
                                    <stop offset="1" stop-color="#0B6631" />
                                </linearGradient>
                            </defs>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-neutral-950">{{t('table_example')}}</p>
                            <p class="text-xs text-gray-500">{{t('example_info')}}</p>
                        </div>
                    </div>
                    <a [href]="example_url" class="secondary-blue-button" (click)="$event.stopPropagation()"
                        target="_blank">
                        <ng-icon name="lucideDownload" class="text-xl"></ng-icon>
                        {{t('download')}}
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="flex items-center justify-between gap-3 px-6 pb-4 ">

                <button type="button" class="text-blue-button" (click)="openHowToCreateProject()">
                    <ng-icon name="lucidePlay" class="text-xl"></ng-icon>
                    <span>
                        {{t('how_to_create')}}
                    </span>
                </button>
                <button type="submit" [disabled]="form.invalid || isFileUploaded == false || isLoading"
                    class="inline-flex items-center gap-2 disabled:cursor-not-allowed primary-blue-button disabled:opacity-50">
                    <ng-icon *ngIf="!isLoading" name="lucidePlus" class="text-xl"></ng-icon>
                    <ng-icon *ngIf="isLoading" name="lucideLoader" class="text-xl animate-spin"></ng-icon>
                    {{data.project!=null ? (isLoading ? t('updating') : t('update')) : (isLoading ? t('creating') :
                    t('create'))}}
                </button>
            </div>
        </form>
    </div>
</div>
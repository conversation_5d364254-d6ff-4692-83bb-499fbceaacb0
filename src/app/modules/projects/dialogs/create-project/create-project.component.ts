import { Component, ElementRef, HostL<PERSON>ener, Inject, <PERSON>Z<PERSON>, OnInit, ViewChild } from '@angular/core';
import { HttpClient, HttpEventType } from '@angular/common/http';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ProjectService } from '@app/data/services/project.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { DatasetService } from '@app/data/services/dataset.service';
import { SnotifyService } from 'ng-alt-snotify';
import { Observable } from 'rxjs';
import { TranslocoService } from '@ngneat/transloco';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { ExcelService } from '@app/data/services/excel.service';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { DiagnoseComponent } from '@app/modules/diagnose/dialogs/diagnose/diagnose.component';
import { VideoEmbedComponent } from '@app/shared/components/video-embed/video-embed.component';
@Component({
  selector: 'app-create-project',
  templateUrl: './create-project.component.html',
  styleUrls: ['./create-project.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class CreateProjectComponent implements OnInit {
  hasReports: boolean = false;
  project: any = null;
  selectedFile: File | null = null;
  uploadProgress: number | null = null;
  isDiagnoseOpen = false;
  isDragging = false;
  isLoading = false;
  projectType: string = 'normal'; // Default type

  constructor(
    public dialog: Dialog,
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private formBuilder: FormBuilder,
    private d: DatasetService,
    private projectService: ProjectService,
    private snotifyService: SnotifyService,
    private ngZone: NgZone,
    private transloco: TranslocoService,
    private diagnoseHelper: DiagnoseHelperService,
    private excelService: ExcelService,
  ) { }
  form = this.formBuilder.group({
    name: [''],
  });
  submitted
  projectName
  protectId
  datasetId
  file: File
  isFileUploaded = null;
  s3_url = null;
  example_url = '';
  ngOnInit(): void {
    this.example_url = this.transloco.getActiveLang() == 'tr' ? 'https://drive.google.com/uc?export=download&id=1AfACIYat1CoZ-IkNUxutYW6W0ToJ6MIZ' : 'https://drive.google.com/uc?export=download&id=1vRrE2kbX24iSaKGZARpFBQv1U4BcEzA6';
    this.form = new FormGroup({
      name: new FormControl(this.data.project ? this.data.project.name : '')
    });
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => {
      this.checkChanges();
    }
    );
  }
  @ViewChild('fileInput') fileInput: ElementRef
  resetFileInput(): void {
    this.file = null;
    this.fileInput.nativeElement.value = '';
    this.isFileUploaded = null;
    this.s3_url = null;
    this.fileName = '';
    this.actualFileName = '';
  }
  fileName = '';
  actualFileName = '';
  async handleFileInput(event): Promise<void> {
    this.isFileUploaded = false;
    const selectedFile = event.target.files[0];
    this.actualFileName = await this.excelService.readDatasetName(selectedFile);
    try {

      if (!selectedFile) {
        return;
      }
      // Önce dosya validasyonunu yap
      const fileValidation = this.d.validateFile(selectedFile);
      if (!fileValidation.isValid) {
        fileValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }
      // Excel içeriğini oku
      const data = await this.d.parseExcelDataView(selectedFile);

      // İçerik validasyonunu yap
      const contentValidation = this.d.validateDataContent(data);
      if (!contentValidation.isValid) {
        contentValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }
      // Process Excel file
      const processedData = await this.excelService.readExcelFile(selectedFile);
      const processedBlob = await this.excelService.writeExcelFile(processedData, selectedFile.name);

      if (this.file != null && this.file != selectedFile) {
        // Delete existing file logic
        this.d.getDeletePresignedUrl(this.fileName).subscribe({
          next: (data) => {
            this.d.deleteToS3(data.url).subscribe({
              next: async () => {
                await this.uploadProcessedFile(selectedFile, processedBlob);
              },
              error: () => {
                this.isFileUploaded = null;
              }
            });
          },
          error: () => {
            this.isFileUploaded = null;
          }
        });
      } else {
        await this.uploadProcessedFile(selectedFile, processedBlob);
      }
    } catch (error) {
      console.error(error);
      this.snotifyService.error(this.transloco.translate('shared.create_project.file_processing_error'));
      this.isFileUploaded = null;
      this.resetFileInput();
    }
  }

  private async uploadProcessedFile(originalFile: File, processedBlob: Blob): Promise<void> {
    this.file = originalFile;
    this.fileName = this.updateFileName(this.file.name);

    this.d.getUploadPresignedUrl(this.fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      .subscribe({
        next: (data: any) => {
          // Create a File from the Blob
          const processedFile = new File([processedBlob], this.fileName, {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });

          this.d.uploadToS3(data.url, processedFile).subscribe({
            next: () => {
              this.isFileUploaded = true;
              this.s3_url = `https://istabot-development.s3.eu-central-1.amazonaws.com/${this.fileName}`;
            },
            error: () => {
              this.isFileUploaded = null;
            }
          });
        },
        error: () => {
          this.isFileUploaded = null;
        }
      });
  }
  isSuccessFromistabot = null;

  async showDiagnose(file: File) {
    try {
      const diagnoseData = await this.diagnoseHelper.prepareDiagnoseData(
        file,
        this.s3_url,
        this.data.project ? this.data.project.id : this.protectId,
        this.datasetId,
      );

      const dialog = this.dialog.open(DiagnoseComponent, {
        data:
        {
          ...diagnoseData,
          fromCreateProject: true,
        },
        width: '100%'
      });

      dialog.closed.subscribe(async (result: { saved: boolean, projectId: number }) => {
        if (result) {
          this.isDiagnoseOpen = true
          // Her durumda create-project dialogunu kapat
          this.animationState = 'out';
          setTimeout(() => this.dialogRef.close({
            ...result,
            isDiagnoseOpen: this.isDiagnoseOpen
          }), 300);
        }
      });
    } catch (error) {
      this.snotifyService.error('Error processing file');
    }
  }

  async createProject() {
    if (this.isLoading) return;

    this.submitted = true;
    if (this.form.invalid) {
      this.isLoading = false;
      return;
    }

    // File validation check
    if (!this.file || !this.s3_url || !this.isFileUploaded) {
      this.isFileUploaded = null;  // Reset file upload state
      this.s3_url = null;  // Reset S3 URL
    }

    this.isLoading = true;
    if (this.isSuccessFromistabot == null) {
      const status = Observable.create((observer) => {
        // Include project_type in the project creation payload
        // Default is 'normal' for regular user-created projects
        this.projectService.createProject({
          name: this.form.value.name,
          project_type: 'normal' // Default project type
        }).subscribe((data) => {
          this.projectName = data.name;
          this.protectId = data.id;

          // isFileUploaded durumu true VE file ve s3_url değerleri varsa devam et
          if (this.isFileUploaded === true && this.file && this.s3_url) {
            // If this is a demo project, show warning and don't allow dataset upload
            if (data.project_type === 'demo') {
              observer.next({
                title: this.transloco.translate('shared.create_project.demo_warning'),
                body: this.transloco.translate('shared.create_project.demo_dataset_restriction'),
                config: {
                  closeOnClick: true,
                  timeout: 3000,
                  showProgressBar: true,
                },
              });
              observer.complete();
              setTimeout(() => this.dialogRef.close({ saved: true, projectId: this.protectId }), 300);
              return;
            }

            // Normal dataset upload flow
            if (this.isFileUploaded) {
              this.d.addDatasetToProject(this.s3_url, this.actualFileName, data.id).subscribe((data) => {
                this.datasetId = data.id;
                observer.next({
                  title: this.transloco.translate('shared.create_project.success'),
                  body: this.transloco.translate('shared.create_project.diagnose_message'),
                  config: {
                    closeOnClick: true,
                    timeout: 1500,
                    showProgressBar: true,
                  },
                });
                observer.complete();
                this.showDiagnose(this.file);
              }, (error) => {
                let errorMessage = '';
                if (error.code) {
                  errorMessage = error.code;
                } else if (error?.error?.code) {
                  errorMessage = error.error.code;
                }
                this.projectService.deleteProject(data.id).subscribe((data) => {
                  this.isSuccessFromistabot = null;
                  this.d.getDeletePresignedUrl(this.fileName).subscribe({
                    next: (data) => {
                      this.d.deleteToS3(data.url).subscribe(
                        {
                          next: (data) => {
                            this.isFileUploaded = null;
                            this.file = null;
                            this.resetFileInput();
                            observer.error({
                              title: this.transloco.translate('shared.create_project.error'),
                              body: error.error.error ? error.error.error + ' ' + this.transloco.translate('shared.create_project.errors.' + errorMessage) : this.transloco.translate('shared.create_project.errors.' + errorMessage),
                              config: {
                                closeOnClick: true,
                                timeout: 3000,
                                showProgressBar: true,
                              },
                            });
                          },
                          error: (error) => {
                            this.isFileUploaded = null;
                            this.file = null;
                            this.resetFileInput();
                            observer.error({
                              title: this.transloco.translate('shared.create_project.error'),
                              body: error.error.error ? error.error.error + ' ' + this.transloco.translate('shared.create_project.errors.' + errorMessage) : this.transloco.translate('shared.create_project.errors.' + errorMessage),
                              config: {
                                closeOnClick: true,
                                timeout: 3000,
                                showProgressBar: true,
                              },
                            });
                          }
                        }
                      );
                    },
                    error: (error) => {
                      this.isFileUploaded = null;
                      this.file = null;
                      this.resetFileInput();
                      observer.error({
                        title: this.transloco.translate('shared.create_project.error'),
                        body: error.error.error ? error.error.error + ' ' + this.transloco.translate('shared.create_project.errors.' + errorMessage) : this.transloco.translate('shared.create_project.errors.' + errorMessage),
                        config: {
                          closeOnClick: true,
                          timeout: 3000,
                          showProgressBar: true,
                        },
                      });
                    }
                  });
                  this.isFileUploaded = null;
                  this.file = null;
                  this.resetFileInput();
                  observer.error({
                    title: this.transloco.translate('shared.create_project.error'),
                    body: error.error.error ? error.error.error + ' ' + this.transloco.translate('shared.create_project.errors.' + errorMessage) : this.transloco.translate('shared.create_project.errors.' + errorMessage),
                    config: {
                      closeOnClick: true,
                      timeout: 3000,
                      showProgressBar: true,
                    },
                  });
                });
              });
            } else {
              observer.next({
                title: this.transloco.translate('shared.create_project.success'),
                body: this.transloco.translate('shared.create_project.create_message'),
                config: {
                  closeOnClick: true,
                  timeout: 1500,
                  showProgressBar: true,
                },
              });
              observer.complete();
              setTimeout(() => this.dialogRef.close({ saved: true, projectId: this.protectId }), 300);
            }
          } else {
            // No file upload, just create the project
            observer.next({
              title: this.transloco.translate('shared.create_project.success'),
              body: this.transloco.translate('shared.create_project.create_message'),
              config: {
                closeOnClick: true,
                timeout: 1500,
                showProgressBar: true,
              },
            });
            observer.complete();
            setTimeout(() => this.dialogRef.close({ saved: true, projectId: this.protectId }), 300);
          }
        },
          (error) => {
            this.isLoading = false;
            observer.error({
              title: this.transloco.translate('shared.create_project.error'),
              body: error.error && error.error.error ? error.error.error : this.transloco.translate('shared.create_project.general_error'),
              config: {
                closeOnClick: true,
                timeout: 3000,
                showProgressBar: true,
              },
            });
          });
      });
      this.snotifyService.async(this.transloco.translate('shared.create_project.project_creating'), status);
    } else {
      this.updateProject();
      this.isSuccessFromistabot = null;
    }
  }

  isDemoProject(): boolean {
    return this.data?.project?.project_type === 'demo';
  }

  canUpdateDataset(): boolean {
    // If this is a demo project, prevent dataset updates
    if (this.isDemoProject()) {
      return false;
    }

    // Otherwise, use the original logic
    return !this.project?.datasets?.length ||
      (!this.project.datasets[0].diagnosed_s3_url && !this.hasReports);
  }

  updateProject() {
    if (this.isLoading) return;

    this.isLoading = true;
    var tmp;
    const status = Observable.create((observer) => {
      if (this.data.project ? this.form.value.name != this.data.project.name : this.form.value.name != this.projectName) {
        this.projectService.updateProjectName(this.data.project ? this.data.project.id : this.protectId, this.form.value.name).subscribe((data) => {
          observer.next({
            title: this.transloco.translate('shared.create_project.success'),
            body: this.transloco.translate('shared.create_project.update_message'),
            config: {
              closeOnClick: true,
              timeout: 1500,
              showProgressBar: true,
            },
          });
          observer.complete()
        }
        );
      }
      if (this.isFileUploaded) {
        this.d.addDatasetToProject(this.s3_url, this.fileName, this.data.project ? this.data.project.id : this.protectId).subscribe((data) => {
          tmp = data
          this.projectService.getProjectById(this.data.project.id).subscribe((data) => {
            observer.next({
              title: this.transloco.translate('shared.create_project.success'),
              body: this.transloco.translate('shared.create_project.diagnose_message'),
              config: {
                closeOnClick: true,
                timeout: 1500,
                showProgressBar: true,
              },
            })
            observer.complete()
            this.datasetId = tmp.id
            this.showDiagnose(this.file);
          }
          );
        },
          (error) => {
            let errorMessage = '';
            if (error.code) {
              errorMessage = error.code;
            } else if (error?.error?.code) {
              errorMessage = error.error.code;
            }
            observer.error({
              title: this.transloco.translate('shared.create_project.error'),
              // body: error.error.error ? error.error.error + ' ' + this.transloco.translate('shared.create_project.errors.' + errorMessage) : this.transloco.translate('shared.create_project.errors.' + errorMessage),
              config: {
                closeOnClick: true,
                timeout: 3000,
                showProgressBar: true,
              },
            });
            this.d.getDeletePresignedUrl(
              this.fileName
            ).subscribe(
              {
                next: (data) => {
                  this.d.deleteToS3(data.url).subscribe(
                    {
                      next: (data) => {
                        this.isFileUploaded = null;
                        this.file = null;
                        this.resetFileInput();
                      },
                      error: (error) => {
                        this.isFileUploaded = null;
                        this.file = null;
                        this.resetFileInput();
                      }
                    }
                  );
                },
                error: (error) => {
                  this.isFileUploaded = null;
                  this.file = null;
                  this.resetFileInput();
                }
              }
            );
          }
        );
      } else {
        this.hideModal();
      }
    });
    this.snotifyService.async(this.transloco.translate('shared.create_project.project_creating'), status)
  }

  // Add error handlers to reset loading state
  private handleError() {
    this.isLoading = false;
  }

  // Add success handlers to reset loading state
  private handleSuccess() {
    this.isLoading = false;
  }

  animationState = 'in';
  updateFileName(name: string): string {
    const fileName = name
    const extension = fileName.substring(fileName.lastIndexOf('.'));
    const fileNameWithProjectId = fileName.replace(extension, '') + new Date().toISOString();
    return this.slugify(fileNameWithProjectId) + extension;
  }
  slugify(text: string): string {
    const turkishMap = {
      'ş': 's',
      'Ş': 'S',
      'ç': 'c',
      'Ç': 'C',
      'ğ': 'g',
      'Ğ': 'G',
      'ü': 'u',
      'Ü': 'U',
      'ö': 'o',
      'Ö': 'O',
      'ı': 'i',
      'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '') // Remove all non-alphanumeric characters except spaces and dashes
      .replace(/\s+/g, '-') // Replace spaces with dashes
      .replace(/-+/g, '-'); // Replace multiple dashes with a single dash
  }

  openHowToCreateProject() {
    this.dialog.open(VideoEmbedComponent, {
      data: {
        from: 'create-project'
      },
    });
  }
  checkChanges() {
    // First check if fileInput is defined before attempting to access its value
    const hasFileInputValue = this.fileInput && this.fileInput.nativeElement &&
      this.fileInput.nativeElement.value !== '';

    // Check if form is dirty or if a file has been uploaded
    if (this.form.dirty || hasFileInputValue || this.file) {
      const dialogRef = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('shared.confirm.create_project.title'),
          content: this.transloco.translate('shared.confirm.create_project.content'),
          confirm: this.transloco.translate('shared.confirm.create_project.confirm'),
          cancel: this.transloco.translate('shared.confirm.create_project.cancel')
        },
      });
      dialogRef.closed.subscribe((result) => {
        if (result) {
          if (this.file && this.fileName) {
            this.d.getDeletePresignedUrl(
              this.fileName
            ).subscribe(
              {
                next: (data) => {
                  this.d.deleteToS3(data.url).subscribe(
                    {
                      next: (data) => {
                        this.hideModal(result);
                      },
                      error: (error) => {
                        this.hideModal(result);
                      }
                    }
                  );
                },
                error: (error) => {
                  this.hideModal(result);
                }
              }
            );
          } else {
            this.hideModal(result);
          }
        }
      });
    } else {
      this.hideModal();
    }
  }
  hideModal(result?) {
    this.ngZone.run(() => {
      this.animationState = 'out';
      if (result) {
        setTimeout(() => this.dialogRef.close(), 300);
      }
      else {
        setTimeout(() => this.dialogRef.close(result), 300);
      }
    });
  }

  @HostListener('window:keyup.esc') onKeyUp() {
    this.checkChanges()
  }

  @HostListener("window:beforeunload", ["$event"]) unloadHandler(event: Event) {
    this.checkChanges()
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        const event = { target: { files: [file] } };
        this.handleFileInput(event);
      }
    }
  }

  downloadExample() {
    window.open(this.example_url, '_blank');
  }
}

import { animate, state, style, transition, trigger } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ProjectService } from '@app/data/services/project.service';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { Observable, of } from 'rxjs';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';

@Component({
  selector: 'app-edit-project',
  templateUrl: './edit-project.component.html',
  styleUrls: ['./edit-project.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class EditProjectComponent implements OnInit {
  animationState = 'in';
  isLoading = false;
  submitted = false;
  form: FormGroup;
  originalName: string;
  originalDescription: string;

  constructor(
    @Inject(DIALOG_DATA) public data: { project: any },
    public dialogRef: DialogRef<any>,
    private formBuilder: FormBuilder,
    private projectService: ProjectService,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
    private dialog: Dialog
  ) { }

  ngOnInit(): void {
    this.originalName = this.data.project.name;
    this.originalDescription = this.data.project.description || '';

    this.form = new FormGroup({
      name: new FormControl(this.originalName, [Validators.required]),
      description: new FormControl(this.originalDescription, [Validators.maxLength(256)])
    });

    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => {
      this.checkChanges();
    });
  }

  checkChanges(): void {
    const nameChanged = this.form.value.name !== this.originalName;
    const descriptionChanged = this.form.value.description !== this.originalDescription;

    if (nameChanged || descriptionChanged) {
      const dialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('notification.confirm.create_project.title'),
          content: this.transloco.translate('notification.confirm.create_project.content'),
          confirm: this.transloco.translate('notification.confirm.create_project.confirm'),
          cancel: this.transloco.translate('notification.confirm.create_project.cancel')
        }
      });

      dialog.closed.subscribe((result) => {
        if (result) {
          this.closeModal();
        }
      });
    } else {
      this.closeModal();
    }
  }

  closeModal(result?: any): void {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(result), 300);
  }

  updateProject(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    if (this.isLoading) {
      return;
    }

    this.isLoading = true;
    const projectId = this.data.project.id;
    let nameUpdated = false;
    let descriptionUpdated = false;

    const status = new Observable((observer: any) => {
      // Check if name needs to be updated
      if (this.form.value.name !== this.originalName) {
        nameUpdated = true;
        this.projectService.updateProjectName(projectId, this.form.value.name).subscribe({
          next: (data) => {
            // If description also needs to be updated, we'll handle the notification after both updates
            if (!descriptionUpdated) {
              observer.next({
                title: this.transloco.translate('notification.project.update_name.success.title'),
                body: this.transloco.translate('notification.project.update_name.success.message'),
                config: {
                  closeOnClick: true,
                  timeout: 1500,
                  showProgressBar: true,
                },
              });
              observer.complete();
              this.closeModal({ saved: true, nameUpdated: true, name: this.form.value.name });
            }
          },
          error: (error) => {
            observer.error(error);
            this.isLoading = false;
          }
        });
      }

      // Check if description needs to be updated
      if (this.form.value.description !== this.originalDescription) {
        descriptionUpdated = true;
        this.projectService.updateProjectDescription(projectId, this.form.value.description).subscribe({
          next: (data) => {
            // If name was not updated or has already been updated, show notification
            if (!nameUpdated) {
              observer.next({
                title: this.transloco.translate('notification.project.update_description.success.title'),
                body: this.transloco.translate('notification.project.update_description.success.message'),
                config: {
                  closeOnClick: true,
                  timeout: 1500,
                  showProgressBar: true,
                },
              });
              observer.complete();
              this.closeModal({ saved: true, descriptionUpdated: true, description: this.form.value.description });
            } else {
              // Both name and description were updated
              this.closeModal({
                saved: true,
                nameUpdated: true,
                descriptionUpdated: true,
                name: this.form.value.name,
                description: this.form.value.description
              });
            }
          },
          error: (error) => {
            observer.error(error);
            this.isLoading = false;
          }
        });
      }

      // If neither name nor description changed, just close the dialog
      if (!nameUpdated && !descriptionUpdated) {
        this.isLoading = false;
        this.closeModal({ saved: false });
      }
    });

    this.snotifyService.async(
      this.transloco.translate('shared.edit_project.updating'),
      status
    );
  }
}

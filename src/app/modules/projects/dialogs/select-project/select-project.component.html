<div class="p-4 bg-white rounded-3xl" *transloco="let t;read 'project_list.select_project'">
  <h2 class="mb-4 text-lg font-semibold">
    {{t('title')}}
  </h2>
  <div class="mb-4">
    <select [(ngModel)]="selectedProjectId" class="w-full p-2 border rounded">
      <option value="">
        {{t('select')}}
      </option>
      <option *ngFor="let project of projects" [value]="project.id">
        {{project.name}}
      </option>
    </select>
  </div>
  <div class="flex justify-end gap-2">
    <button (click)="cancel()" class="px-4 py-2 text-gray-600 border rounded-3xl hover:bg-gray-100">
        {{t('cancel')}}
    </button>
    <button (click)="selectProject()" [disabled]="!selectedProjectId"
      class="px-4 py-2 text-white bg-blue-600 rounded-3xl disabled:bg-gray-400 hover:bg-blue-700">
        {{t('compare')}}
    </button>
  </div>
</div>

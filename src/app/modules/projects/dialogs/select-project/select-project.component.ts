import { Component, Inject, OnInit } from '@angular/core';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { ProjectService } from '@app/data/services/project.service';

@Component({
  selector: 'app-select-project',
  templateUrl: './select-project.component.html',
})
export class SelectProjectComponent implements OnInit{
  projects: any[];
  selectedProjectId: number;

  constructor(
    public dialogRef: DialogRef<any>,
    @Inject(DIALOG_DATA) public data: {currentProjectId: number},
    private project: ProjectService
  ) {
  }

  ngOnInit(): void {
    this.getProjects();
  }

  getProjects() {
    this.project.getProjectAll().subscribe(projects => {
      this.projects = projects.filter(p => p.id !== this.data.currentProjectId && p.datasets[0]);
    });
  }

  selectProject() {
    const selectedAnalysisId = this.projects.find(p => p.id == this.selectedProjectId).datasets[0].analyses[0].id;
    this.dialogRef.close(
      selectedAnalysisId
    );
  }

  cancel() {
    this.dialogRef.close();
  }
}

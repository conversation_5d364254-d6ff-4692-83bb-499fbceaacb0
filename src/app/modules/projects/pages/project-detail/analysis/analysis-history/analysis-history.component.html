<div class="flex flex-col h-full" *transloco="let t; read 'project_detail.analysis.analysis_history'">
    <!-- Search and Filter Section -->
    <div class="flex-none mb-4 bg-white">
        <div class="flex items-center justify-between">
            <div class="relative flex-1">
                <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="filterHistory()"
                    placeholder="{{t('search_placeholder')}}"
                    class="w-full py-3 pl-10 pr-4 transition-colors border-2 border-gray-200 rounded-full focus:border-brand-green-700 focus:ring-2 focus:ring-brand-green-100" />
                <div class="absolute inset-y-0 flex items-center pointer-events-none left-4">
                    <ng-icon name="lucideSearch" class="text-xl text-gray-400"></ng-icon>
                </div>
                <!-- Clear button (only visible when searchTerm exists) -->
                <button *ngIf="searchTerm" (click)="clearSearch()"
                    class="absolute inset-y-0 flex items-center justify-center h-full gap-1 text-gray-500 right-4 hover:text-gray-700 focus:outline-none">
                    <ng-icon name="lucideX" class="text-xl"></ng-icon>
                    <span class="text-sm">{{t('clear_search')}}</span>
                </button>
            </div>
            <div class="flex items-center gap-2">
                <div class="relative ml-4" appClickOutside (clickOutside)="isFilterDropdownOpen = false">
                    <button (click)="toggleFilterDropdown()" class="secondary-green-button">
                        <ng-icon name="lucideFilter" class="text-2xl text-brand-green-500"></ng-icon>
                        <span class="text-brand-green-500">{{t('filters')}}</span>
                        <span *ngIf="getActiveFilterCount() > 0"
                            class="absolute flex items-center justify-center w-5 h-5 text-xs font-bold text-white rounded-full bg-brand-green-500 -top-1 -right-1">
                            {{getActiveFilterCount()}}
                        </span>
                    </button>
                    <!-- Filter Dropdown -->
                    <div *ngIf="isFilterDropdownOpen"
                        class="absolute right-0 z-50 mt-2 bg-white border shadow-lg rounded-3xl">
                        <div class="p-4 space-y-4">
                            <!-- Sort options -->
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-700">{{t('sort_by')}}</label>
                                <select [(ngModel)]="filters.sortBy" (ngModelChange)="filterHistory()"
                                    class="w-full px-3 py-2 text-sm border rounded-3xl focus:ring-2 focus:ring-green-500">
                                    <option value="date_desc">{{t('newest_first')}}</option>
                                    <option value="date_asc">{{t('oldest_first')}}</option>
                                    <option value="name_asc">{{t('name_asc')}}</option>
                                    <option value="name_desc">{{t('name_desc')}}</option>
                                </select>
                            </div>

                            <!-- Date Range Filter -->
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-700">{{t('date_range')}}</label>
                                <mat-form-field appearance="outline" class="w-full">
                                    <mat-date-range-input [rangePicker]="picker">
                                        <input matStartDate [(ngModel)]="filters.dateRange.start"
                                            (dateChange)="filterHistory()" placeholder="{{t('start_date')}}">
                                        <input matEndDate [(ngModel)]="filters.dateRange.end"
                                            (dateChange)="filterHistory()" placeholder="{{t('end_date')}}">
                                    </mat-date-range-input>
                                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                    <mat-date-range-picker #picker></mat-date-range-picker>
                                </mat-form-field>
                            </div>

                            <!-- Reset Button -->
                            <button (click)="resetFilters()"
                                class="flex items-center justify-center w-full gap-2 px-4 py-2 mt-4 text-sm border text-neutral-950 rounded-3xl hover:bg-gray-50">
                                <ng-icon name="lucideX" class="w-4 h-4"></ng-icon>
                                {{t('reset_filters')}}
                            </button>
                        </div>
                    </div>
                </div>
                <!-- <button *ngIf="isAdmin == true" (click)="toggleCompareMode()" [class.bg-brand-green-500]="isCompareMode"
                    [class.text-white]="isCompareMode" [disabled]="filteredHistory.length < 2"
                    class="secondary-green-button">
                    <ng-icon name="lucideGitCompare" class="text-2xl"></ng-icon>
                    {{t('compare_reports')}}
                </button> -->
            </div>
        </div>
    </div>

    <!-- Comparison mode bar -->
    <div *ngIf="isCompareMode" class="flex items-center justify-between p-4 mt-4 bg-brand-green-50 rounded-xl">
        <div class="flex items-center gap-4">
            <span class="text-sm font-medium">{{t('select_reports')}}</span>
            <div class="flex items-center gap-2">
                <ng-container *ngFor="let report of selectedReportsForComparison">
                    <div class="flex items-center gap-2 p-2 bg-white rounded-lg">
                        <span class="text-sm">{{report.title}}</span>
                        <button (click)="selectForComparison(report)" class="text-gray-400 hover:text-red-500">
                            <ng-icon name="lucideX" class="text-lg"></ng-icon>
                        </button>
                    </div>
                </ng-container>
            </div>
        </div>

        <div class="flex items-center gap-2">
            <button (click)="toggleCompareMode()" class="text-sm hover:underline">
                {{t('cancel')}}
            </button>
            <button (click)="compareReports()" [disabled]="selectedReportsForComparison.length !== 2"
                class="primary-green-button">
                {{t('compare')}}
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isHistoryLoading" class="flex-1">
        <div class="space-y-4">
            <div *ngFor="let item of [1,2,3]"
                class="flex items-center justify-between w-full p-4 bg-white border rounded-3xl animate-pulse">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-gray-200 rounded-xl"></div>
                        <div>
                            <div class="w-48 h-5 mb-2 bg-gray-200 rounded-md"></div>
                            <div class="flex items-center gap-2 mt-2">
                                <div class="w-20 h-3 bg-gray-200 rounded"></div>
                                <div class="w-32 h-3 bg-gray-200 rounded"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                        <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History List with Drag and Drop -->
    <div *ngIf="!isHistoryLoading" class="flex-1">
        <!-- Drag and drop container -->
        <div cdkDropList (cdkDropListDropped)="onHistoryListDrop($event)" class="space-y-4">
            <div *ngFor="let item of filteredHistory" cdkDrag
                class="flex items-center justify-between w-full transition-all bg-white border border-neutral-150 hover:border-brand-green-500 rounded-3xl"
                [class.ring-2]="isCompareMode && selectedReportsForComparison.includes(item)"
                [class.ring-brand-green-500]="isCompareMode && selectedReportsForComparison.includes(item)">

                <!-- Drag handle and content -->
                <div class="relative flex items-center flex-1 group">
                    <button class="flex items-center justify-between w-full p-4"
                        (click)="isCompareMode ? selectForComparison(item) : navigateToDetail(item)">
                        <div class="flex items-center gap-4">
                            <div
                                class="relative flex items-center justify-center w-12 h-12 rounded-xl bg-brand-green-50 group">
                                <!-- Folder icon: visible normally, hidden on hover -->
                                <ng-icon name="lucideFileText"
                                    class="text-2xl transition-opacity duration-200 opacity-100 text-brand-green-500 group-hover:opacity-0"></ng-icon>

                                <!-- Drag handle: hidden normally, shown on hover -->
                                <div cdkDragHandle *ngIf="!isCompareMode"
                                    class="absolute flex items-center justify-center p-2 transition-opacity duration-200 opacity-0 cursor-move left-1 group-hover:opacity-100">
                                    <div class="flex items-center justify-center size-full">
                                        <ng-icon name="lucideMove"
                                            class="text-2xl text-brand-green-500"></ng-icon>
                                    </div>
                                </div>
                            </div>


                            <div>
                                <div class="flex items-center gap-2">
                                    <h3 class="font-semibold text-gray-900 truncate max-w-144 text-start">
                                        {{item.title}}
                                    </h3>
                                    <ng-icon *ngIf="item.is_favorite" name="lucideStar"
                                        class="text-xl text-yellow-500"></ng-icon>
                                </div>
                                <div class="flex items-center gap-2">
                                <p class="flex items-center gap-1 text-sm text-neutral-600">
                                    <ng-icon name="lucideClock4"></ng-icon>
                                    <span>{{formatDate(item.created_at)}}</span>
                                </p>
                                <span *ngIf="item.credit_usage?.used_credit" class="flex text-xs">
                                    <img src="assets/icons/istacoin.svg" alt="" class="mr-1 size-4">
                                    {{item.credit_usage?.used_credit}} {{t('credit_used')}}
                                </span>
                            </div>
                            </div>
                        </div>
                        <div
                            class="flex items-center justify-center ml-2 text-gray-400 transition-colors rounded-full size-12 min-w-12 hover:bg-gray-100 group-hover:text-brand-green-500">
                            <ng-icon name="lucideChevronRight" class="text-2xl"></ng-icon>
                        </div>
                    </button>
                </div>

                <!-- Download button -->
                <button (click)="downloadDocxById(item)"
                    class="flex items-center justify-center mx-2 text-gray-400 rounded-3xl size-12 min-w-12 hover:transition-colors hover:bg-gray-100 hover:text-brand-green-500">
                    <ng-icon name="lucideDownload" class="text-xl"></ng-icon>
                </button>

                <!-- Preview when dragging -->
                <div *cdkDragPreview class="flex items-center gap-2 p-4 bg-white shadow-lg rounded-3xl">
                    <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-brand-green-50">
                        <ng-icon name="lucideFileText" class="text-2xl text-brand-green-600"></ng-icon>
                    </div>
                    <span class="font-medium">{{ item.title }}</span>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="filteredHistory.length === 0" class="flex flex-col items-center justify-center py-8">
            <ng-icon name="lucideInbox" class="text-4xl text-gray-400"></ng-icon>
            <p class="mt-2 text-sm text-gray-500">{{t('no_reports')}}</p>
        </div>
    </div>

    <!-- Favorites Download Progress Modal -->
    <div *ngIf="showModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="p-6 bg-white rounded-lg shadow-xl">
            <div class="flex flex-col items-center">
                <div
                    class="w-16 h-16 mb-4 border-4 border-t-4 rounded-full border-brand-green-500 border-t-transparent animate-spin">
                </div>
                <p class="text-lg font-medium text-gray-800">{{t('downloading')}}</p>
            </div>
        </div>
    </div>
</div>
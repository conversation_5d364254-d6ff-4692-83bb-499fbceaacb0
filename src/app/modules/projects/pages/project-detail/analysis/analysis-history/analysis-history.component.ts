import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AnalysisService } from '@app/data/services/analysis.service';
import { ReportLine } from '@app/data/models/report_line.interface';
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Dialog } from '@angular/cdk/dialog';
import { CompareReportsDialogComponent } from '@app/modules/reports/components/compare-reports-dialog.component';
import { TranslocoService } from '@ngneat/transloco';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { SnotifyService } from 'ng-alt-snotify';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';

@Component({
  selector: 'app-analysis-history',
  templateUrl: './analysis-history.component.html',
})
export class AnalysisHistoryComponent implements OnInit {
  @Input() analysisId: string;

  analysisHistory: any[] = [];
  isHistoryLoading = false;
  searchTerm: string = '';
  isFilterDropdownOpen = false;
  filters = {
    dateRange: {
      start: null,
      end: null
    },
    sortBy: 'date_desc'
  };

  filteredHistory: any[] = [];
  isCompareMode = false;
  selectedReportsForComparison: any[] = [];
  hasFavorites = false;
  showModal = false;
  allReportsLang: string = 'tr';
  isFavoriteDropdownOpen = false;
  disableAllButton = false;
  
  constructor(
    private analysisService: AnalysisService,
    private router: Router,
    private dialog: Dialog,
    private transloco: TranslocoService,
    private ah: AdminHelperService,
    private snotifyService: SnotifyService
  ) {
    if (this.ah.isRole('admin')) {
      this.isAdmin = true;
    }
  }
  isAdmin = false;

  ngOnInit(): void {
    this.loadAnalysisHistory();
    this.allReportsLang = localStorage.getItem('activeLang') || 'tr';
  }

  loadAnalysisHistory(): void {
    this.isHistoryLoading = true;
    this.analysisService.getAnalysisReportLines(this.analysisId).subscribe({
      next: (data) => {
        this.analysisHistory = data.map(analysis => ({
          ...analysis,
          timeAgo: formatDistanceToNow(new Date(analysis.created_at), {
            addSuffix: true,
            locale: tr
          })
        })).sort((a, b) => {
          if (a.position !== undefined && b.position !== undefined) {
            return a.position - b.position;
          }
          return 0;
        });
        
        this.filteredHistory = [...this.analysisHistory];
        // Check if there are any favorite reports
        this.hasFavorites = this.analysisHistory.some(report => report.is_favorite);
        this.isHistoryLoading = false;
      },
      error: (err) => {
        console.error('Error loading analysis history:', err);
        this.isHistoryLoading = false;
      }
    });
  }

  filterHistory(): void {
    let filtered = [...this.analysisHistory];

    if (this.searchTerm.trim()) {
      const searchTermLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(analysis =>
        analysis.title.toLowerCase().includes(searchTermLower)
      );
    }

    if (this.filters.dateRange.start || this.filters.dateRange.end) {
      filtered = filtered.filter(analysis => {
        const analysisDate = new Date(analysis.created_at);
        const start = this.filters.dateRange.start ? new Date(this.filters.dateRange.start) : null;
        const end = this.filters.dateRange.end ? new Date(this.filters.dateRange.end) : null;

        if (start && end) {
          return analysisDate >= start && analysisDate <= end;
        } else if (start) {
          return analysisDate >= start;
        } else if (end) {
          return analysisDate <= end;
        }
        return true;
      });
    }

    filtered = this.sortHistory(filtered);
    this.filteredHistory = filtered;
    
    // Update hasFavorites status based on the full analysis history
    this.hasFavorites = this.analysisHistory.some(report => report.is_favorite);
  }

  private sortHistory(history: any[]): any[] {
    return history.sort((a, b) => {
      switch (this.filters.sortBy) {
        case 'date_desc':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'date_asc':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'name_asc':
          return a.title.localeCompare(b.title);
        case 'name_desc':
          return b.title.localeCompare(a.title);
        default:
          return 0;
      }
    });
  }

  toggleFilterDropdown(): void {
    this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
    if (this.isFilterDropdownOpen) {
      this.isFavoriteDropdownOpen = false; // Close favorite dropdown if open
    }
  }
  
  toggleFavoriteDropdown(): void {
    if (this.hasFavorites) {
      this.isFavoriteDropdownOpen = !this.isFavoriteDropdownOpen;
      if (this.isFavoriteDropdownOpen) {
        this.isFilterDropdownOpen = false; // Close filter dropdown if open
      }
    }
  }

  resetFilters(): void {
    this.filters = {
      dateRange: {
        start: null,
        end: null
      },
      sortBy: 'date_desc'
    };
    this.searchTerm = '';
    this.filterHistory();
  }

  getActiveFilterCount(): number {
    let count = 0;
    if (this.filters.dateRange.start || this.filters.dateRange.end) count++;
    if (this.filters.sortBy !== 'date_desc') count++;
    return count;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR');
  }

  navigateToDetail(report: ReportLine) {
  // report.id yerine report.credit_usage?.creditable_id kullan
  const reportContentId = report.credit_usage?.creditable_id || report.id;
  
  this.router.navigate(['/reports', reportContentId], {
    state: {
      report: report,
      projectName: report.credit_usage?.project_name,
      projectId: report.credit_usage?.project_id,
      fromHistory: true  // Add this flag
    }
  });
}

  downloadDocxById(item: any) {
    const lang = localStorage.getItem('activeLang') || 'tr';
    this.analysisService.getReportContentDocxById(item.credit_usage?.creditable_id, lang).subscribe((data) => {
      const a = document.createElement('a')
      const objectUrl = URL.createObjectURL(data)
      a.href = objectUrl
      a.download = this.slugify(item.credit_usage.project_name) + '_' + this.slugify(item.title) + '.docx';
      a.click();
      URL.revokeObjectURL(objectUrl);
    });
  }

  slugify(text: string): string {
    const turkishMap = {
      'ş': 's', 'Ş': 'S', 'ç': 'c', 'Ç': 'C',
      'ğ': 'g', 'Ğ': 'G', 'ü': 'u', 'Ü': 'U',
      'ö': 'o', 'Ö': 'O', 'ı': 'i', 'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-');
  }

  toggleCompareMode() {
    this.isCompareMode = !this.isCompareMode;
    this.selectedReportsForComparison = [];
  }

  selectForComparison(analysis: any) {
    if (!this.isCompareMode) return;

    if (this.selectedReportsForComparison.includes(analysis)) {
      this.selectedReportsForComparison = this.selectedReportsForComparison.filter(r => r !== analysis);
    } else if (this.selectedReportsForComparison.length < 2) {
      this.selectedReportsForComparison.push(analysis);
    }
  }

  compareReports() {
    if (this.selectedReportsForComparison.length !== 2) return;

    const dialogRef = this.dialog.open(CompareReportsDialogComponent, {
      data: {
        reports: this.selectedReportsForComparison
      }
    });
  }

  downloadFavorites(lang: string = 'tr'): void {
    // Get favorite reports
    const favoriteReports = this.analysisHistory.filter(report => report.is_favorite);
    
    if (favoriteReports.length === 0) {
      return;
    }
    
    this.showModal = true;
    this.isFavoriteDropdownOpen = false; // Close dropdown after selection
    
    // Get the first report's project ID to use for the file name
    const projectName = favoriteReports[0]?.credit_usage?.project_name || 'project';
    const fileName = lang === 'tr' ?
      this.slugify(projectName) + '_favori_raporlar.docx' :
      this.slugify(projectName) + '_favorite_reports.docx';
    
    // Use the first report's analysis ID
    const analysisId = favoriteReports[0]?.credit_usage?.creditable_id;
    
    if (analysisId) {
      this.analysisService.getFavoriteReportsContent(analysisId, lang).subscribe({
        next: (data) => {
          const a = document.createElement('a');
          const objectUrl = URL.createObjectURL(data);
          a.href = objectUrl;
          a.download = fileName;
          a.click();
          
          setTimeout(() => {
            this.showModal = false;
          }, 1000);
          
          URL.revokeObjectURL(objectUrl);
        },
        error: (error) => {
          console.error('Error downloading favorite reports:', error);
          this.showModal = false;
        }
      });
    } else {
      this.showModal = false;
    }
  }

  clearSearch() {
    this.searchTerm = '';
    this.filterHistory();
  }

  // New drag and drop functionality
  onHistoryListDrop(event: CdkDragDrop<any[]>) {
    // Don't allow reordering if we're in the middle of another operation
    if (this.showModal || this.isCompareMode) return;

    // Get the previous order
    const previousList = [...this.filteredHistory];

    // Update the array order
    moveItemInArray(this.filteredHistory, event.previousIndex, event.currentIndex);

    // Get the moved report and its new position
    const movedReport = this.filteredHistory[event.currentIndex];
    const newPosition = event.currentIndex + 1;

    // Call API to update the moved report's position
    this.analysisService.updateReportPosition(movedReport.id.toString(), newPosition).subscribe({
      next: () => {
        // Success notification
        this.snotifyService.success(
          this.transloco.translate('notification.analysis.report_position.success'),
          this.transloco.translate('notification.analysis.report_position.title'),
          {
            timeout: 2000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
        
        // Update the original array too
        this.analysisHistory = [...this.analysisHistory].sort((a, b) => {
          if (a.position !== undefined && b.position !== undefined) {
            return a.position - b.position;
          }
          return 0;
        });
      },
      error: (error) => {
        console.error(error);
        // Revert the UI change if the API call fails
        this.filteredHistory = previousList;

        this.snotifyService.error(
          this.transloco.translate('notification.analysis.report_position.error'),
          this.transloco.translate('notification.analysis.report_position.title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }
}
<div class="h-full">
    <div class="flex flex-col h-full p-8 overflow-y-auto bg-white border-neutral-150 rounded-3xl"
        *transloco="let t;read: 'project_detail.analysis'">
        <div class="flex items-center justify-between">
            <div class="flex flex-col gap-2">
                <h6 class="text-3xl font-bold text-neutral-950">
                    {{ isAnalysisView ? t('header_analysis') : t('header_history') }}
                </h6>
            </div>
            <div class="flex items-center gap-2">
                <div *ngIf="!isAnalysisView && showGetAllReports" class="relative">
                    <button (click)="toggleReportLanguageDropdown()" class="secondary-blue-button">
                        <ng-icon name="lucideDownload" class="text-lg"></ng-icon>
                        {{ t('download_favorites') }}
                    </button>

                    <!-- Language Selection Dropdown -->
                    <div *ngIf="isReportLanguageDropdownOpen"
                        class="absolute right-0 z-50 mt-2 bg-white border shadow-lg rounded-xl">
                        <div class="p-2">
                            <button (click)="downloadAllReportDocx('tr')"
                                class="flex items-center w-full gap-2 px-4 py-2 text-sm text-left transition-colors rounded-lg hover:bg-gray-100">
                                🇹🇷 Türkçe
                            </button>
                            <button (click)="downloadAllReportDocx('en')"
                                class="flex items-center w-full gap-2 px-4 py-2 text-sm text-left transition-colors rounded-lg hover:bg-gray-100">
                                🇬🇧 English
                            </button>
                        </div>
                    </div>
                </div>
                <button (click)="toggleView()" class="primary-blue-button">
                    <ng-icon [name]="isAnalysisView ? 'lucideHistory' : 'lucideLayoutGrid'" class="text-lg"></ng-icon>
                    {{ isAnalysisView ? t('show_history') : t('show_analyses') }}
                </button>
            </div>
        </div>

        <div class="flex-1 pt-2 mt-6 overflow-y-auto">
            <!-- Analysis Grid View -->
            <div *ngIf="isAnalysisView" class="grid gap-6 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2">
                <div *ngFor="let analysis of analyses; let i = index"
                    class="relative overflow-hidden transition-all duration-300 ease-out border-2 cursor-pointer analysis-card group rounded-3xl"
                    [ngClass]="{
                        'border-brand-green-300 bg-gradient-to-br from-brand-green-50 via-white to-brand-green-50 hover:border-brand-green-400 hover:shadow-xl hover:-translate-y-2': analysis.isAvailable,
                        'border-gray-300 bg-gray-50 opacity-60 cursor-not-allowed': !analysis.isAvailable
                    }" [attr.data-index]="i" (click)="handleCardClick(analysis, $event)">

                    <!-- Hover overlay -->
                    <div class="absolute inset-0 transition-opacity duration-300 opacity-0 bg-gradient-to-br from-brand-green-100/20 to-brand-green-200/20 group-hover:opacity-100"
                        [class.group-hover:opacity-0]="!analysis.isAvailable"></div>

                    <!-- Click ripple effect container -->
                    <div
                        class="absolute w-10 h-10 transform -translate-x-1/2 -translate-y-1/2 rounded-full opacity-0 pointer-events-none click-indicator top-1/2 left-1/2 bg-brand-green-500/30">
                    </div>

                    <!-- New Badge -->
                    <div class="absolute top-0 right-0 flex items-center gap-1 px-3 py-1.5 text-xs font-bold text-white shadow-lg bg-gradient-to-r from-purple-500 to-indigo-500 rounded-bl-xl animate-pulse z-10"
                        *ngIf="analysis.isNew">
                        {{ t('new') }}
                        <ng-icon name="lucideSparkles" class="w-3 h-3"></ng-icon>
                    </div>

                    <div class="relative z-10 flex flex-col h-full ">
                        <!-- Header -->
                        <div class="flex items-center gap-4">
                            <div class="flex items-center justify-center w-16 h-16 transition-transform duration-300 shadow-lg icon-container rounded-2xl bg-gradient-to-br from-brand-green-500 to-brand-green-600 group-hover:scale-110 group-hover:rotate-3"
                                [class.group-hover:scale-100]="!analysis.isAvailable"
                                [class.group-hover:rotate-0]="!analysis.isAvailable">
                                <img [src]="'assets/icons/' + analysis.icon + '.svg'"
                                    class="w-8 h-8 filter brightness-0 invert">
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-bold transition-colors duration-300 text-neutral-800 group-hover:text-brand-green-600"
                                    [class.group-hover:text-neutral-800]="!analysis.isAvailable">
                                    {{ t('analyses.' + analysis.type) }}
                                </h3>
                            </div>
                        </div>

                        <!-- Description -->
                        <!-- <div class="flex-1">
                            <p class="text-sm leading-relaxed text-neutral-600 line-clamp-3">
                                {{ getAnalysisDescription(analysis.type) }}
                            </p>


                            <p *ngIf="!analysis.isAvailable && analysis.requirementDescription"
                                class="mt-3 text-xs italic text-red-500">
                                {{ analysis.requirementDescription }}
                            </p>
                        </div> -->
                    </div>
                </div>
            </div>

            <!-- History View -->
            <app-analysis-history *ngIf="!isAnalysisView" [analysisId]="project.datasets[0].analyses[0].id">
            </app-analysis-history>
        </div>
    </div>

    <!-- Download Progress Modal -->
    <div *ngIf="showModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="p-6 bg-white rounded-lg shadow-xl">
            <div class="flex flex-col items-center">
                <div
                    class="w-16 h-16 mb-4 border-4 border-t-4 rounded-full border-brand-green-500 border-t-transparent animate-spin">
                </div>
                <p class="text-lg font-medium text-gray-800">{{ t('downloading_reports') }}</p>
            </div>
        </div>
    </div>
</div>
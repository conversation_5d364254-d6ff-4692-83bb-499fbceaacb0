// Analysis component specific styles
.analysis-card {
  position: relative;
  background: white;
  border-radius: 1.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  // Available state
  &.border-brand-green-300 {
    border-color: #9ae6b4;
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 8%, white 20%);

    &:hover {
      border-color: #68d391;
      background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 12%, white 25%);
    }
  }

  // Disabled state
  &.border-gray-300 {
    border-color: #d2d6dc;
    background: #f9fafb;
    opacity: 0.7;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow:
        0 10px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
  }

  // Icon container animations
  .icon-container {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }
  }

  &:hover .icon-container::before {
    left: 100%;
  }

  // Click ripple effect
  .click-indicator {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
    z-index: 1000;
  }

  // Card title hover effect
  h3 {
    transition: color 0.3s ease;
  }

  // New badge pulse animation
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  // Status indicator
  .w-3.h-3.rounded-full {
    transition: all 0.3s ease;
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);

    &.bg-brand-green-500 {
      animation: statusPulse 2s infinite;
    }
  }

  @keyframes statusPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
    }
    70% {
      box-shadow: 0 0 0 4px rgba(72, 187, 120, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
    }
  }

  // Line clamp utility
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  // Hover effects for text elements
  &:hover {
    .text-neutral-600 {
      color: #4a5568;
    }
  }

  // Disabled card hover effects override
  &.opacity-60:hover {
    .icon-container {
      transform: none !important;
    }

    h3 {
      color: inherit !important;
    }
  }
}

// Grid responsive adjustments
.analysis-grid {
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  @media (min-width: 1025px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

// Custom scrollbar for the main container
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;

    &:hover {
      background: #a0aec0;
    }
  }
}

// Enhanced focus states for accessibility
.analysis-card {
  &:focus {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
  }

  &:focus-visible {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
  }
}

// Loading states
.analysis-card.loading {
  .icon-container {
    background: #e2e8f0;
    animation: shimmer 1.5s ease-in-out infinite;
  }

  h3,
  p {
    background: #e2e8f0;
    color: transparent;
    border-radius: 0.25rem;
    animation: shimmer 1.5s ease-in-out infinite;
  }
}

@keyframes shimmer {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

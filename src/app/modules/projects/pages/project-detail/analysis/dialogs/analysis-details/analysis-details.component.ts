import { Component, Inject, OnInit, ElementRef, Renderer2 } from '@angular/core';
import { DialogRef, DIALOG_DATA } from '@angular/cdk/dialog';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

// GSAP declaration
declare var gsap: any;

@Component({
  selector: 'app-analysis-details',
  template: `
<div class="modern-dialog-container" #dialogContainer>
  <div class="modern-dialog-content" *transloco="let t; read: 'analyses_type_list'">

    <!-- Header Section -->
    <div class="dialog-header">
      <!-- Background decoration -->
      <div class="header-decoration"></div>

      <div class="header-content">
        <div class="icon-title-section">
          <div class="modern-icon-container" #iconContainer>
            <img [src]="'assets/icons/' + data.analysis.icon + '.svg'" class="analysis-icon"
              alt="{{data.analysis.type}} icon">
            <div class="icon-glow"></div>
          </div>

          <div class="title-section">
            <h2 class="dialog-title" #dialogTitle>
              <span>{{t(data.analysis.type)}}</span>

              <p class="description-text text-base">{{t(data.analysis.type + '_info')}}</p>
            </h2>
          </div>

          <button (click)="close()" class="close-button" #closeBtn [attr.aria-label]="'Kapat'">
            <div class="close-icon-container">
              <ng-icon name="lucideX" class="close-icon"></ng-icon>
            </div>
          </button>
        </div>
      </div>

      <!-- Content Section -->
      <div class="dialog-body" #dialogBody>

        <!-- Requirements Card -->
        <div class="requirements-card" #requirementsCard *ngIf="data.analysis.requirementDescription">
          <div class="card-header">
            <div class="card-icon warning">
              <ng-icon name="lucideInfo" class="text-amber-500"></ng-icon>
            </div>
            <h3>Gereksinimler</h3>
          </div>
          <p class="requirements-text">{{data.analysis.requirementDescription}}</p>
          <!-- Video Section -->
          <div #videoCard>
            <div class="video-container">
              <div class="video-wrapper">
                <iframe 
                  class="analysis-video" 
                  [src]="getSafeVideoUrl(data.analysis.type)"
                  [title]="t(data.analysis.type) + ' Eğitimi'" 
                  frameborder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  referrerpolicy="strict-origin-when-cross-origin" 
                  allowfullscreen>
                </iframe>
                <div class="video-overlay" *ngIf="!videoLoaded" (click)="loadVideo()">
                  <div class="play-button">
                    <ng-icon name="lucidePlay" class="play-icon"></ng-icon>
                  </div>
                  <p>Video yüklemek için tıklayın</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Section -->
      <div class="flex justify-end w-full">
        <button (click)="selectAnalysis()" [disabled]="!data.analysis.isAvailable" class="primary-green-button"
          #selectBtn [ngClass]="{'disabled': !data.analysis.isAvailable}">
          <span>{{t('select')}}</span>
          <ng-icon name="lucideArrowRight" class="button-icon"></ng-icon>
          <div class="button-glow" *ngIf="data.analysis.isAvailable"></div>
        </button>
      </div>
    </div>
  </div>
</div>
    `,
  styles: [`
      .modern-dialog-container {
        position: fixed;
        inset: 0;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
        padding: 1rem;
      }

      .modern-dialog-content {
        background: white;
        border-radius: 24px;
        width: 100%;
        max-width: 900px;
        max-height: 90vh;
        overflow: hidden;
        box-shadow: 
          0 25px 50px -12px rgba(0, 0, 0, 0.25),
          0 0 0 1px rgba(255, 255, 255, 0.05);
        position: relative;
      }

      /* Header Styles */
      .dialog-header {
        position: relative;
        background: linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%);
        padding: 2rem;
        border-bottom: 1px solid #e5e7eb;
        overflow: hidden;
      }

      .header-decoration {
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(72, 187, 120, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(50%, -50%);
      }

      .header-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 1.5rem;
      }

      .icon-title-section {
        display: flex;
        align-items: flex-start;
        gap: 1.5rem;
        flex: 1;
      }

      .modern-icon-container {
        position: relative;
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #48bb78, #38a169);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 20px 40px rgba(72, 187, 120, 0.3);
      }

      .analysis-icon {
        width: 40px;
        height: 40px;
        filter: brightness(0) invert(1);
        z-index: 2;
        position: relative;
      }

      .icon-glow {
        position: absolute;
        inset: -4px;
        background: linear-gradient(135deg, #48bb78, #38a169);
        border-radius: 24px;
        opacity: 0;
        filter: blur(8px);
        transition: opacity 0.3s ease;
      }

      .modern-icon-container:hover .icon-glow {
        opacity: 0.7;
      }

      .title-section {
        flex: 1;
      }

      .dialog-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 0.5rem 0;
        line-height: 1.2;
      }

      .title-underline {
        height: 3px;
        background: linear-gradient(90deg, #48bb78, #38a169);
        border-radius: 2px;
        width: 0;
        margin-bottom: 1rem;
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
      }

      .status-badge.available {
        background: rgba(72, 187, 120, 0.1);
        color: #22543d;
      }

      .status-badge.unavailable {
        background: rgba(239, 68, 68, 0.1);
        color: #991b1b;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: currentColor;
        animation: pulse 2s infinite;
      }

      .close-button {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(8px);
      }
      .close-icon {
        font-size: 1.5rem;
        color: #6b7280;
        transition: color 0.3s ease;
      }

      .close-button:hover .close-icon {
        color: #374151;
      }

      /* Body Styles */
      .dialog-body {
        padding: 2rem;
        max-height: 60vh;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e0 #f7fafc;
      }

      .dialog-body::-webkit-scrollbar {
        width: 6px;
      }

      .dialog-body::-webkit-scrollbar-track {
        background: #f7fafc;
        border-radius: 3px;
      }

      .dialog-body::-webkit-scrollbar-thumb {
        background: #cbd5e0;
        border-radius: 3px;
      }

      .description-card,
      .requirements-card,
      .video-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
      }

      .description-card:hover,
      .requirements-card:hover,
      .video-card:hover {
        border-color: #cbd5e0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      .card-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
      }

      .card-icon {
        width: 40px;
        height: 40px;
        background: rgba(72, 187, 120, 0.1);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
      }

      .card-icon.warning {
        background: rgba(245, 158, 11, 0.1);
      }

      .card-header h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #374151;
        margin: 0;
      }

      .description-text,
      .requirements-text {
        color: #6b7280;
        line-height: 1.6;
        margin: 0;
      }

      .requirements-text {
        color: #d97706;
        font-style: italic;
      }

      /* Video Styles */
      .video-container {
        position: relative;
        width: 100%;
        background: #000;
        border-radius: 12px;
        overflow: hidden;
      }

      .video-wrapper {
        position: relative;
        width: 100%;
        height: 300px;
      }

      .analysis-video {
        width: 100%;
        height: 100%;
        border: none;
      }

      .video-overlay {
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .video-overlay:hover {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
      }

      .play-button {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(8px);
      }

      .play-button:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }

      .play-icon {
        font-size: 2rem;
        margin-left: 4px;
      }

      .footer-content {
        display: flex;
        align-items: right;
        justify-content: space-between;
        gap: 1rem;
      }

      .secondary-button,
      .primary-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }

      .secondary-button {
        background: white;
        border: 1px solid #d1d5db;
        color: #374151;
      }

      .secondary-button:hover {
        background: #f9fafb;
        border-color: #9ca3af;
        transform: translateY(-1px);
      }

      .primary-button {
        background: linear-gradient(135deg, #48bb78, #38a169);
        border: none;
        color: white;
        box-shadow: 0 4px 14px rgba(72, 187, 120, 0.3);
      }

      .primary-button:hover:not(.disabled) {
        background: linear-gradient(135deg, #38a169, #2f855a);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
      }

      .primary-button.disabled {
        background: #e5e7eb;
        color: #9ca3af;
        cursor: not-allowed;
        box-shadow: none;
      }

      .button-icon {
        font-size: 1rem;
      }

      .button-glow {
        position: absolute;
        inset: -2px;
        background: linear-gradient(135deg, #48bb78, #38a169);
        border-radius: 14px;
        opacity: 0;
        filter: blur(8px);
        z-index: -1;
        transition: opacity 0.3s ease;
      }

      .primary-button:hover .button-glow {
        opacity: 0.7;
      }

      /* Animations */
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      /* Responsive */
      @media (max-width: 768px) {
        .modern-dialog-content {
          margin: 1rem;
          max-width: calc(100% - 2rem);
        }

        .dialog-header {
          padding: 1.5rem;
        }

        .icon-title-section {
          flex-direction: column;
          gap: 1rem;
        }

        .modern-icon-container {
          width: 60px;
          height: 60px;
        }

        .analysis-icon {
          width: 30px;
          height: 30px;
        }

        .dialog-title {
          font-size: 1.5rem;
        }

        .dialog-body {
          padding: 1.5rem;
        }

        .video-wrapper {
          height: 200px;
        }

        .footer-content {
          flex-direction: column-reverse;
          gap: 0.75rem;
        }

        .secondary-button,
        .primary-button {
          width: 100%;
          justify-content: center;
        }
      }
    `]
})
export class AnalysisDetailsComponent implements OnInit {
  videoLoaded = false;

  constructor(
    private dialogRef: DialogRef<string>,
    @Inject(DIALOG_DATA) public data: { analysis: any },
    private el: ElementRef,
    private renderer: Renderer2,
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    // Initialize animations after view loads
    setTimeout(() => {
      this.initializeAnimations();
    }, 50);
  }

  initializeAnimations(): void {
    if (typeof gsap !== 'undefined') {
      const container = this.el.nativeElement.querySelector('.modern-dialog-content');
      const iconContainer = this.el.nativeElement.querySelector('.modern-icon-container');
      const titleUnderline = this.el.nativeElement.querySelector('.title-underline');
      const cards = this.el.nativeElement.querySelectorAll('.description-card, .requirements-card, .video-card');
      const footer = this.el.nativeElement.querySelector('.dialog-footer');

      // Container entrance
      gsap.fromTo(container,
        {
          opacity: 0,
          scale: 0.9,
          y: 30
        },
        {
          opacity: 1,
          scale: 1,
          y: 0,
          duration: 0.5,
          ease: "back.out(1.7)"
        }
      );

      // Icon animation
      gsap.fromTo(iconContainer,
        {
          scale: 0,
          rotation: -180
        },
        {
          scale: 1,
          rotation: 0,
          duration: 0.8,
          delay: 0.2,
          ease: "back.out(2)"
        }
      );

      // Title underline animation
      gsap.to(titleUnderline, {
        width: '100px',
        duration: 0.8,
        delay: 0.4,
        ease: "power2.out"
      });

      // Cards stagger animation
      gsap.fromTo(cards,
        {
          opacity: 0,
          y: 20
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.6,
          delay: 0.3,
          stagger: 0.1,
          ease: "power2.out"
        }
      );

      // Footer animation
      gsap.fromTo(footer,
        {
          opacity: 0,
          y: 20
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.5,
          delay: 0.6,
          ease: "power2.out"
        }
      );
    }
  }

  getVideoUrl(type: string): string {
    const videoUrls = {
      'descriptive': 'https://www.youtube.com/embed/SrpBGvnDmqo?si=9Ms-b4tHWF49kGbz',
      'single': 'https://www.youtube.com/embed/19GiPTLOcUg?si=KzIRls2HKjVF7F_R',
      'multi': 'https://www.youtube.com/embed/aANHllnl6vQ?si=GPyzxJ830cp1ZwZU',
      'dependent': 'https://www.youtube.com/embed/Zi-oUSgBxcI?si=6TlZDztMXrhh18nz',
      'correlation': 'https://www.youtube.com/embed/gn3lfdLzx-o?si=wszUguCBzdjRAs8n',
      'chisq': 'https://www.youtube.com/embed/NgdXEQnzxx4?si=r6tltKFWuR1ysrCV',
      'comean': 'https://www.youtube.com/embed/1Z9v6Jjv9Z0?si=6TlZDztMXrhh18nz',
      'logistic_cox': 'https://www.youtube.com/embed/example?si=example',
      'survival': 'https://www.youtube.com/embed/example?si=example',
      'roc': 'https://www.youtube.com/embed/example?si=example',
      'linear': 'https://www.youtube.com/embed/example?si=example'
    };

    return videoUrls[type] || 'https://www.youtube.com/embed/dQw4w9WgXcQ';
  }

  getSafeVideoUrl(type: string): SafeResourceUrl {
    const url = this.getVideoUrl(type);
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  loadVideo(): void {
    this.videoLoaded = true;
  }

  close(): void {
    if (typeof gsap !== 'undefined') {
      const container = this.el.nativeElement.querySelector('.modern-dialog-content');

      gsap.to(container, {
        opacity: 0,
        scale: 0.9,
        y: 30,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          this.dialogRef.close();
        }
      });
    } else {
      this.dialogRef.close();
    }
  }

  selectAnalysis(): void {
    if (!this.data.analysis.isAvailable) return;

    if (typeof gsap !== 'undefined') {
      const selectBtn = this.el.nativeElement.querySelector('.primary-button');

      // Button click animation
      gsap.to(selectBtn, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut",
        onComplete: () => {
          this.dialogRef.close(this.data.analysis.type);
        }
      });
    } else {
      this.dialogRef.close(this.data.analysis.type);
    }
  }
}
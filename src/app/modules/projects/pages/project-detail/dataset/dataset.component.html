<div class="p-4 overflow-hidden bg-white border border-neutral-150 size-full rounded-3xl"
    *transloco="let t;read 'project_detail.dataset'">

    <div class="flex items-start w-full gap-1 px-4 pt-4 ">
        <div class="flex flex-col flex-1 gap-3 truncate">
            <div class="flex items-center gap-2">
                <span class="text-xl font-semibold truncate" [title]="dataset.name">
                    {{dataset.name}}
                </span>
                <!-- Toplam değişken sayısı -->
                <span *ngIf="totalVariables"
                    class="px-2 py-1 text-sm font-medium rounded-full bg-brand-blue-100 text-brand-blue-600">
                    {{ totalVariables }} {{ t('values') }}
                </span>
            </div>
            <span class="flex flex-wrap items-center gap-2 text-sm text-neutral-600">
                {{ t('last_updated') }} {{dataset.updated_at | date: 'dd/MM/yyyy HH:mm'}}
            </span>
        </div>
        <div class="flex items-start flex-none gap-3">

            <div class="flex gap-4">
                <button (click)="downloadDataset()"
                    class="items-center justify-center transition-all secondary-blue-button">
                    <ng-icon name="lucideDownload" class="text-xl"></ng-icon>
                    <span>{{ t('download') }}</span>
                </button>

                <button (click)="showDiagnose()" class="items-center justify-center transition-all primary-blue-button">
                    <ng-icon name="lucideSettings" class="text-xl"></ng-icon>
                    <span>{{ t('edit_dataset') }}</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Arama ve Filtreleme Alanı -->
    <div class="flex gap-4 px-4 mt-6">
        <div class="relative flex-1">
            <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="filterVariables()"
                placeholder="{{ t('placeholder') }}"
                class="w-full py-3 pl-10 pr-4 transition-colors border-2 border-gray-200 rounded-full focus:border-brand-green-700 focus:ring-2 focus:ring-brand-green-100" />
            <div class="absolute inset-y-0 flex items-center pointer-events-none left-4">
                <ng-icon name="lucideSearch" class="text-xl text-gray-400"></ng-icon>
            </div>
            <!-- Clear butonu (sadece searchTerm varsa görünür) -->
            <button *ngIf="searchTerm" (click)="clearSearch()"
                class="absolute inset-y-0 flex items-center justify-center h-full gap-1 text-gray-500 right-4 hover:text-gray-700 focus:outline-none">
                <ng-icon name="lucideX" class="text-xl"></ng-icon>
                <span class="text-sm">{{t('clear_search')}}</span>
            </button>
        </div>
        <div class="relative" appClickOutside (clickOutside)="isFilterDropdownOpen = false">
            <button (click)="toggleFilterDropdown()" class="secondary-blue-button">
                <ng-icon name="lucideFilter" class="text-2xl text-brand-blue-500"></ng-icon>
                <span class="text-brand-blue-500">{{ t('filter.title') }}</span>
                <span *ngIf="getActiveFilterCount() > 0"
                    class="absolute flex items-center justify-center w-5 h-5 text-xs font-bold text-white rounded-full bg-brand-green-500 -top-1 -right-1">
                    {{getActiveFilterCount()}}
                </span>
            </button>
            <!-- Filtreleme Menüsü -->
            <div *ngIf="isFilterDropdownOpen"
                class="absolute right-0 z-50 w-64 p-4 mt-2 space-y-4 bg-white border shadow-lg rounded-3xl">

                <!-- Tür Seçimi -->
                <div>
                    <label class="block mb-2 text-sm font-medium text-gray-700">{{ t('filter.type') }}</label>
                    <select [(ngModel)]="filters.type" (ngModelChange)="filterVariables()"
                        class="w-full px-3 py-2 text-sm border rounded-3xl focus:ring-2 focus:ring-blue-500">
                        <option value="">{{ t('filter.all') }}</option>
                        <option value="Scale">Scale</option>
                        <option value="Ordinal">Ordinal</option>
                        <option value="Nominal">Nominal</option>
                    </select>
                </div>

                <!-- Filtreyi Sıfırla -->
                <button (click)="resetFilters()"
                    class="flex items-center justify-center w-full gap-2 px-4 py-2 mt-4 text-sm border text-neutral-950 rounded-3xl hover:bg-gray-50">
                    <ng-icon name="lucideX" class="w-4 h-4"></ng-icon>
                    {{ t('filter.reset_filter') }}
                </button>
            </div>
        </div>
    </div>


    <div *ngIf="diagnoseData?.variables_json" class="px-4 py-4">
        <div class="bg-white border shadow-sm rounded-2xl">

            <!-- Tablo Başlığı -->
            <div class="sticky top-0 z-10 grid gap-3 px-4 py-3 font-semibold shadow-sm bg-brand-blue-50 rounded-t-2xl"
                style="grid-template-columns: 2fr 1.5fr 2fr;">
                <div>{{ t('table.headers.variable') }}</div>
                <div>{{ t('table.headers.type') }}</div>
                <div>{{ t('table.headers.variable_label') }}</div>
            </div>

            <!-- Tablo Gövdesi -->
            <div class="overflow-auto max-h-[calc(92vh-525px)]">
                <div *ngFor="let variable of filteredVariables" class="group">
                    <div class="grid gap-3 px-4 py-3" style="grid-template-columns: 2fr 1.5fr 2fr;">
                        <!-- Değişken Adı -->
                        <div class="flex items-center truncate">
                            <span class="w-8 font-medium text-gray-800">{{ variable.header }}</span>
                        </div>
                        <!-- Türü -->
                        <div class="flex items-center">
                            <span class="px-2 py-1 font-medium rounded-full" [ngClass]="{
                                'text-blue-600': variable.measure === 'Scale',
                                'text-amber-600': variable.measure === 'Ordinal',
                                'text-purple-600': variable.measure === 'Nominal',
                                'bg-blue-100': variable.measure === 'Scale',
                                'bg-amber-100': variable.measure === 'Ordinal',
                                'bg-purple-100': variable.measure === 'Nominal'
                                }">
                                {{ variable.measure }}
                            </span>
                        </div>
                        <div>
                            <!-- Değişken Etiketi - Value Labels, formatValueLabels fonksiyonu kullanılıyor -->
                            <div>
                                <span class="text-gray-700">{{ formatValueLabels(variable.value_labels) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Skeleton Loader - Veri yükleniyor göstergesi -->
    <div *ngIf="!diagnoseData?.variables_json" class="px-4 py-4">
        <div class="bg-white border shadow-sm rounded-2xl">
            <!-- Skeleton Header -->
            <div class="sticky top-0 z-10 grid gap-3 px-4 py-3 shadow-sm bg-brand-blue-50 rounded-t-2xl"
                style="grid-template-columns: 2fr 1.5fr 2fr;">
                <div class="h-6 bg-gray-200 rounded-md animate-pulse"></div>
                <div class="h-6 bg-gray-200 rounded-md animate-pulse"></div>
                <div class="h-6 bg-gray-200 rounded-md animate-pulse"></div>
            </div>

            <!-- Skeleton Rows -->
            <div class="overflow-auto max-h-[calc(100vh-525px)]">
                <div *ngFor="let i of [1,2,3,4,5,6,7,8,9,10]" class="grid gap-3 px-4 py-3"
                    style="grid-template-columns: 2fr 1.5fr 2fr;">
                    <div class="h-8 bg-gray-100 rounded-md animate-pulse"></div>
                    <div class="w-24 h-8 bg-gray-100 rounded-full animate-pulse"></div>
                    <div class="h-8 bg-gray-100 rounded-md animate-pulse"></div>
                </div>
            </div>
        </div>
    </div>
</div>
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { DataTransferService } from '@app/data/services/data-transfer.service';
import { DatasetService } from '@app/data/services/dataset.service';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { Dialog } from '@angular/cdk/dialog';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { DiagnoseComponent } from '@app/modules/diagnose/dialogs/diagnose/diagnose.component';
import { Project } from '@app/data/models/project.interface';
import { ProjectStateService } from '@app/modules/projects/services/project-state.service';
import { ProjectService } from '@app/data/services/project.service';

@Component({
  selector: 'app-dataset',
  templateUrl: './dataset.component.html',
  styleUrls: ['./dataset.component.scss']
})
export class DatasetComponent implements OnInit {
  project: Project | null = null;
  dataset: any;
  diagnoseData: any = null; // Diagnose component'in kullandığı data formatı 
  totalVariables: number | null = null;
  totalNullValues: number | null = null;
  missingValueLabels: any[] = [];
  duplicateValueLabels: any[] = [];
  validValues: number = 0;
  searchTerm: string = '';
  isFilterDropdownOpen: boolean = false;
  isDiagnoseLoading: boolean = false;
  filters = { type: '' };
  filteredVariables: any[] = [];
  lastProjectId: number | null = null;
  notImportedVariables: number = 0;

  constructor(
    private dt: DataTransferService,
    private ds: DatasetService,
    private dh: DiagnoseHelperService,
    private dialog: Dialog,
    private snotifyService: SnotifyService,
    public transloco: TranslocoService, // public yapıldı - template'te erişim için
    private route: ActivatedRoute,
    private router: Router,
    private ps: ProjectStateService,
    private p: ProjectService
  ) { }

  ngOnInit(): void {
    // Proje bilgisini state üzerinden al; state boşsa API'den çek
    this.ps.currentProject$.subscribe(project => {
      if (project) {

        this.project = project;
        this.loadDataset(); // Proje geldikten sonra dataset yükle
      } else {
        this.loadProjectFromAPI();
      }
    });

    // Dataset state kontrolü; eğer dataset state boşsa, API'den çek
    this.dt.dataset$.subscribe(dataset => {
      if (dataset) {
        this.dataset = dataset;
        // Dataset aldıktan sonra Excel dosyasını da yükle (diagnose comp. gibi)
        this.loadExcelData();
      } else if (this.project) {
        console.warn("⚠️ Dataset state boş, API'den alınıyor...");
        this.loadDatasetFromAPI();
      }
    });

    this.dt.totalVariables$.subscribe(total => {
      this.totalVariables = total;
      this.updateValidValues();
    });

    this.dt.totalNullValues$.subscribe(nullValues => {
      this.totalNullValues = nullValues;
      this.updateValidValues();
    });

    this.dt.notImportedVariables$.subscribe(notImported => {
      this.notImportedVariables = notImported;
      this.updateValidValues();
    });
  }

  // Proje bilgisi yoksa API'den çek
  private loadProjectFromAPI(): void {
    this.route.params.subscribe((params: Params) => {
      const projectId = params['pid'];
      if (!projectId) {
        console.error("❌ Proje ID eksik!");
        this.router.navigate(['/projects']);
        return;
      }
      this.p.getProjectById(projectId).subscribe({
        next: (data) => {
          if (data) {

            this.project = data;
            this.ps.setProject(data);
            this.loadDataset(); // Proje geldikten sonra dataset yükle
          } else {
            console.error("❌ Proje bulunamadı!");
            this.router.navigate(['/projects']);
          }
        },
        error: () => {
          console.error("❌ Proje yüklenirken hata oluştu!");
          this.router.navigate(['/projects']);
        }
      });
    });
  }

  // Proje geldikten sonra dataset'i yükle
  private loadDataset(): void {
    if (!this.project?.datasets?.length) {
      console.error("❌ Proje dataseti yok!");
      return;
    }
    this.loadDatasetFromAPI();
  }

  // API'den dataset'i çek ve state'i güncelle
  private async loadDatasetFromAPI() {
    try {
      const datasetId = this.project?.datasets?.[0]?.id;
      if (!datasetId) {
        console.error("❌ Dataset ID eksik!");
        return;
      }
      // datasetId'yi string olarak gönderiyoruz
      const dataset = await this.ds.getDatasetById(datasetId.toString()).toPromise();
      if (dataset) {
        this.dt.setDataset(dataset);
        this.dataset = dataset;

        // Dataset'i aldıktan sonra Excel dosyasını yükleyerek diagnose componentin yaptığı gibi verileri alalım
        this.loadExcelData();

      } else {
        console.error("❌ API'den dataset çekilemedi!");
      }
    } catch (error) {
      console.error("❌ Dataset yüklenirken hata oluştu:", error);
    }
  }

  // Excel dosyasını yükle (diagnose component'in kullandığı gibi)
  private async loadExcelData() {
    if (!this.dataset) return;

    try {
      const fileUrl = this.dataset.diagnosed_s3_url ? this.dataset.diagnosed_s3_url : this.dataset.s3_url;
      const fileName = fileUrl.split('/').pop();
      const fileNameDecoded = decodeURIComponent(fileName.replace(/\+/g, " "));
      const blob = await this.ds.getDataset(fileNameDecoded);

      // DiagnoseHelperService'i kullanarak aynı şekilde veriyi hazırla
      this.diagnoseData = await this.dh.prepareDiagnoseData(
        blob,
        this.dataset.s3_url,
        this.dataset.project_id.toString(),
        this.dataset.id
      );

      if (this.diagnoseData && this.diagnoseData.variables_json && this.diagnoseData.variables_json.variable_list) {
        // Diagnose component'in kullandığı variable_list ile çalışalım
        this.filteredVariables = [...this.diagnoseData.variables_json.variable_list];
        this.filterVariables();

        // Analiz sonuçlarını da hesaplayalım
        const analysisResults = await this.dh.analyzeVariableData(this.filteredVariables);
        this.totalVariables = this.filteredVariables.length;
        this.totalNullValues = analysisResults.totalNullValues;
        this.notImportedVariables = this.filteredVariables.filter((variable: any) => !variable.import).length;

        this.dt.setTotalVariables(this.totalVariables);
        this.dt.setTotalNullValues(this.totalNullValues);
        this.dt.setNotImportedVariables(this.notImportedVariables);
        this.updateValidValues();
      } else {
        console.error("❌ Excel verisi doğru formatta hazırlanamadı!");
      }
    } catch (error) {
      console.error("❌ Excel dosyasından veri yüklenirken hata oluştu:", error);
    }
  }

  private updateValidValues() {
    if (this.totalVariables !== null && this.notImportedVariables !== null) {
      this.validValues = this.totalVariables - this.notImportedVariables;
    }
  }

  filterVariables() {
    if (!this.diagnoseData?.variables_json?.variable_list) return;

    this.filteredVariables = this.diagnoseData.variables_json.variable_list.filter(variable => {
      return (
        (!this.filters.type || variable.measure === this.filters.type) &&
        (!this.searchTerm || variable.header?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          variable.label?.en?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          variable.label?.tr?.toLowerCase().includes(this.searchTerm.toLowerCase()))
      );
    });
  }

  toggleFilterDropdown() {
    this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
  }

  resetFilters() {
    this.filters = { type: '' };
    this.searchTerm = '';
    this.filterVariables();
  }

  getActiveFilterCount(): number {
    return this.filters.type ? 1 : 0;
  }

  // Value Labels'ı formatlamak için yardımcı fonksiyon
  formatValueLabels(valueLabels: any): string {
    // Value labels null/undefined veya boş ise boş string döndür
    if (!valueLabels) return '';

    try {
      // Aktif dile göre değerleri al
      const currentLang = this.transloco.getActiveLang();
      const labels = valueLabels[currentLang];

      // Eğer labels objesi yoksa veya boşsa boş string döndür
      if (!labels || typeof labels !== 'object') return '';

      // Etikette hiç değer yoksa boş string döndür
      const entries = Object.entries(labels);
      if (entries.length === 0) return '';

      // Tüm değerler boş mu kontrol et
      const allValuesEmpty = entries.every(([_, value]) => value === '' || value === null || value === undefined);
      if (allValuesEmpty) return '';

      // "1=Evet, 2=Hayır" şeklinde formatla
      return entries
        .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
        .map(([key, value]) => `${key}=${value}`)
        .join(', ');
    } catch (error) {
      console.error('Value label formatlanırken hata oluştu:', error);
      return '';
    }
  }

  // Value labels'ın boş olup olmadığını kontrol eden fonksiyon
  hasValueLabels(valueLabels: any): boolean {
    return !!this.formatValueLabels(valueLabels);
  }

  async showDiagnose() {
    if (this.isDiagnoseLoading || !this.dataset) return;

    try {
      this.isDiagnoseLoading = true;

      // Eğer diagnoseData zaten hazırlandıysa, doğrudan kullan
      if (this.diagnoseData) {
        const dialog = this.dialog.open(DiagnoseComponent, {
          data: { ...this.diagnoseData, datasetName: this.dataset.name },
          width: '100%'
        });
        dialog.closed.subscribe((result: any) => {
          if (result) {
            this.lastProjectId = result.projectId;
            // Diagnose component'ten döndükten sonra verileri yeniden yükle
            this.loadDatasetFromAPI();
          }
        });
      } else {
        // Henüz hazırlanmadıysa, yeniden hazırla
        const fileUrl = this.dataset.diagnosed_s3_url ? this.dataset.diagnosed_s3_url : this.dataset.s3_url;
        const fileName = fileUrl.split('/').pop();
        const fileNameDecoded = decodeURIComponent(fileName.replace(/\+/g, " "));
        const blob = await this.ds.getDataset(fileNameDecoded);
        const diagnoseData = await this.dh.prepareDiagnoseData(
          blob,
          this.dataset.s3_url,
          this.dataset.project_id.toString(),
          this.dataset.id
        );

        const dialog = this.dialog.open(DiagnoseComponent, {
          data: { ...diagnoseData, datasetName: this.dataset.name },
          width: '100%'
        });
        dialog.closed.subscribe((result: any) => {
          if (result) {
            this.lastProjectId = result.projectId;
            this.loadDatasetFromAPI();
          }
        });
      }
    } catch (error) {
      this.snotifyService.error(this.transloco.translate('notification.diagnose.error.message'));
    } finally {
      this.isDiagnoseLoading = false;
    }
  }

  downloadDataset() {
    let fileUrl = this.project.datasets[0].diagnosed_s3_url === null ? this.project.datasets[0].s3_url : this.project.datasets[0].diagnosed_s3_url
    const fileNameDecoded = decodeURIComponent(fileUrl.replace(/\+/g, " "));
    const fileName = fileNameDecoded.split('/').pop()
    this.ds.getGetPresignedUrl(fileName).subscribe({
      next: (url) => {
        window.open(url.url, '_blank');
      },
      error: (error) => {
        console.log(error);
        this.snotifyService.error(
          this.transloco.translate('notification.dataset.download.error.message'),
          this.transloco.translate('notification.dataset.download.error.title'),
          {
            timeout: 5000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }
  clearSearch() {
    this.searchTerm = '';
    this.filterVariables();
  }
}
<div *transloco="let t;read 'project_detail.members'" class="flex flex-col h-full bg-gray-50">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex items-center justify-center h-64">
        <div class="flex flex-col items-center gap-4">
            <div class="relative">
                <div class="w-12 h-12 border-4 border-blue-200 rounded-full border-t-blue-600 animate-spin"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <ng-icon name="lucideUsers" class="text-lg text-blue-600"></ng-icon>
                </div>
            </div>
            <p class="text-sm text-gray-600 animate-pulse">{{t('loading')}}</p>
        </div>
    </div>

    <!-- Main Content -->
    <div *ngIf="!isLoading" class="flex flex-col gap-8">
        <!-- Compact Header -->
        <div class="p-4 bg-white border border-gray-100 shadow-sm rounded-3xl">
            <div class="flex flex-col gap-4 mb-4 lg:flex-row lg:items-center lg:justify-between">
                <div class="space-y-1">
                    <div class="flex items-center gap-2">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">{{t('title')}} ( {{getTotalMemberCount()}} )
                            </h1>
                            <p class="text-sm text-gray-600">{{t('description')}}</p>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col items-stretch gap-2 sm:flex-row sm:items-center">
                    <!-- History Toggle Button - Only for owners -->
                    <button *ngIf="isOwner()" (click)="toggleHistory()" class="secondary-blue-button">
                        <ng-icon name="lucideHistory" class="text-sm"></ng-icon>
                        <span class="font-medium">{{showHistory ? t('hide_history') : t('show_history')}}</span>
                    </button>

                    <!-- Add Member Button (Only for owners) -->
                    <button *ngIf="isOwner()" (click)="addMember()" class="primary-blue-button">
                        <ng-icon name="lucideUserPlus" class="text-sm"></ng-icon>
                        <span class="font-medium">{{t('add_member')}}</span>
                    </button>
                </div>
            </div>
            <!-- Search Bar - Compact -->
            <div class="flex w-full gap-4">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <ng-icon name="lucideSearch" class="text-lg text-gray-400"></ng-icon>
                    </div>
                    <input type="text" [(ngModel)]="searchTerm" (input)="filterMembers()"
                        [placeholder]="t('search_placeholder')"
                        class="w-full py-2 pl-10 pr-10 text-sm transition-all duration-200 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 bg-gray-50 hover:bg-white" />
                    <div *ngIf="searchTerm" (click)="clearSearch()"
                        class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                        [title]="t('clear_search')">
                        <ng-icon name="lucideX" class="text-sm text-gray-400 hover:text-gray-600"></ng-icon>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Share History -->
        <div *ngIf="showHistory && isOwner()"
            class="overflow-hidden bg-white border border-gray-100 shadow-sm rounded-3xl">
            <div class="p-6 bg-white border-b">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-white rounded-xl">
                        <ng-icon name="lucideHistory" class="text-xl text-gray-600"></ng-icon>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900">{{t('share_history')}}</h3>
                </div>
            </div>

            <div class="p-6">
                <div *ngIf="shareHistories.length === 0" class="py-12 text-center">
                    <div class="flex flex-col items-center gap-4">
                        <div class="p-4 bg-gray-100 rounded-full">
                            <ng-icon name="lucideFileText" class="text-4xl text-gray-400"></ng-icon>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-700">{{t('history.no_history')}}</h4>
                            <p class="mt-1 text-sm text-gray-500">{{t('history.no_history_description')}}</p>
                        </div>
                    </div>
                </div>

                <div *ngIf="shareHistories.length > 0" class="space-y-3">
                    <div *ngFor="let history of shareHistories; let i = index"
                        class="flex items-center gap-4 p-4 transition-colors duration-200 bg-gray-50 rounded-xl hover:bg-gray-100"
                        [style.animation-delay]="i * 100 + 'ms'">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full">
                                <ng-icon [name]="getHistoryIcon(history.action_type)"
                                    class="text-lg text-blue-600"></ng-icon>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-semibold text-gray-900 truncate">
                                {{getActionTypeText(history.action_type)}}
                            </p>
                            <p class="mt-1 text-xs text-gray-600">
                                <span class="font-medium">{{history.actor.full_name}}</span>
                                →
                                <span class="font-medium">{{history.target_user.full_name}}</span>
                            </p>
                            <p class="mt-1 text-xs text-gray-500">
                                {{formatDate(history.created_at)}}
                            </p>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="w-3 h-3 rounded-full" [ngClass]="{
                                   'bg-green-400': history.action_type === 'add_researcher',
                                   'bg-red-400': history.action_type === 'remove_researcher', 
                                   'bg-blue-400': history.action_type === 'transfer_ownership'
                                 }"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Members List -->
        <div *ngIf="!showHistory" class="space-y-6">
            <!-- Loading Researchers -->
            <div *ngIf="isLoadingResearchers" class="p-8 bg-white border border-gray-100 shadow-sm rounded-2xl">
                <div class="flex items-center justify-center py-8">
                    <div class="flex flex-col items-center gap-4">
                        <div class="relative">
                            <div class="w-8 h-8 border-4 border-blue-200 rounded-full border-t-blue-600 animate-spin">
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 animate-pulse">{{t('loading_researchers')}}</p>
                    </div>
                </div>
            </div>

            <div *ngIf="!isLoadingResearchers" class="space-y-4">
                <!-- Owner Card - Universal (supports both API formats) -->
                <div *ngIf="project?.owner || (project?.owner_name && project?.owner_email)"
                    class="p-4 border border-blue-200 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl member-card">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="relative">
                                <div
                                    class="flex items-center justify-center w-10 h-10 rounded-full shadow-lg bg-gradient-to-br from-blue-500 to-blue-600 avatar">
                                    <ng-icon name="lucideCrown" class="text-sm text-white"></ng-icon>
                                </div>
                                <div
                                    class="absolute -top-0.5 -right-0.5 w-3 h-3 bg-yellow-400 rounded-full border border-white flex items-center justify-center">
                                    <ng-icon name="lucideStar" class="text-xs text-yellow-800"></ng-icon>
                                </div>
                            </div>
                            <div>
                                <!-- Display name: use project.owner if available, otherwise use owner_name -->
                                <h3 class="text-sm font-bold text-gray-900">
                                    <ng-container *ngIf="project?.owner; else ownerNameTemplate">
                                        {{project.owner.name}} {{project.owner.surname}}
                                    </ng-container>
                                    <ng-template #ownerNameTemplate>
                                        {{project?.owner_name}}
                                    </ng-template>
                                </h3>

                                <!-- Display email: use project.owner.email if available, otherwise use owner_email -->
                                <p class="text-xs font-medium text-blue-700">
                                    {{project?.owner?.email || project?.owner_email}}
                                </p>

                                <!-- <p class="text-xs text-blue-600 mt-0.5">{{t('owner_info.title')}}</p> -->
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <span
                                class="flex items-center justify-center px-2 py-1 text-xs font-bold text-blue-800 bg-blue-200 rounded-full role-badge owner">
                                <ng-icon name="lucideCrown" class="inline mr-1 text-xs"></ng-icon>
                                {{t('roles.owner')}}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Researchers Cards -->
                <div *ngFor="let researcher of filteredResearchers; let i = index"
                    class="p-4 transition-all duration-200 bg-white border border-gray-100 shadow-sm rounded-xl hover:shadow-md member-card"
                    [style.animation-delay]="i * 100 + 'ms'">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div
                                class="flex items-center justify-center w-10 h-10 rounded-full shadow-lg bg-gradient-to-br from-green-500 to-emerald-600 avatar">
                                <ng-icon name="lucideUser" class="text-sm text-white"></ng-icon>
                            </div>
                            <div>
                                <h3 class="text-sm font-bold text-gray-900">{{researcher.full_name}}</h3>
                                <p class="text-xs font-medium text-gray-600">{{researcher.email}}</p>
                                <!-- <p class="text-xs text-gray-500 mt-0.5">{{t('researcher_info.title')}}</p> -->
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <span
                                class="flex items-center justify-center px-2 py-1 text-xs font-bold text-green-800 bg-green-200 rounded-full role-badge researcher">
                                <ng-icon name="lucideFlaskConical" class="inline mr-1 text-xs"></ng-icon>
                                {{t('roles.researcher')}}
                            </span>

                            <!-- Owner Actions - Only show for owners -->
                            <div *ngIf="isOwner()" class="flex items-center gap-1">
                                <!-- Transfer Ownership Button -->
                                <button (click)="transferOwnership(researcher)"
                                    class="p-2 text-orange-600 transition-all duration-200 rounded-full bg-orange-50 hover:bg-orange-100 hover:scale-105 action-button group"
                                    [matTooltip]="t('actions.transfer_ownership')">
                                    <div class="flex items-center justify-center size-6">
                                        <ng-icon name="lucideCrown"
                                            class="text-sm transition-transform group-hover:scale-110"></ng-icon>
                                    </div>
                                </button>

                                <!-- Remove Member Button -->
                                <button (click)="removeMember(researcher)"
                                    class="p-2 text-red-600 transition-all duration-200 rounded-full bg-red-50 hover:bg-red-100 hover:scale-105 action-button group"
                                    [matTooltip]="t('actions.remove_member')">
                                    <div class="flex items-center justify-center size-6">
                                        <ng-icon name="lucideUserMinus"
                                            class="text-sm transition-transform group-hover:scale-110"></ng-icon>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div *ngIf="filteredResearchers.length === 0 && !isLoadingResearchers"
                    class="p-8 text-center bg-white border border-gray-100 shadow-sm rounded-xl empty-state">
                    <div class="flex flex-col items-center gap-4">
                        <div class="relative">
                            <div class="p-4 bg-gray-100 rounded-full">
                                <ng-icon name="lucideUsers" class="text-3xl text-gray-400 empty-icon"></ng-icon>
                            </div>
                        </div>
                        <div class="space-y-1">
                            <h3 class="text-lg font-bold text-gray-700">
                                {{searchTerm ? t('empty_state.no_search_results') : t('empty_state.no_researchers')}}
                            </h3>
                            <p class="max-w-md text-sm text-gray-500">
                                {{searchTerm ? t('empty_state.no_search_results_description') :
                                t('empty_state.no_researchers_description')}}
                            </p>
                        </div>
                        <button *ngIf="isOwner() && !searchTerm" (click)="addMember()" class="primary-blue-button">
                            <ng-icon name="lucideUserPlus" class="text-sm"></ng-icon>
                            <span class="text-sm font-medium">{{t('actions.add_first_researcher')}}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
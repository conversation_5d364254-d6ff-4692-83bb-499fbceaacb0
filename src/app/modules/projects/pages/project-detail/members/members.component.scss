// src/app/modules/projects/pages/project-detail/members/members.component.scss

// Enhanced skeleton loader
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Pulse animation for skeleton elements
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Enhanced member cards
.member-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

// Enhanced avatar animations
.avatar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.05) rotate(5deg);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
}

// Enhanced action buttons
.action-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.95);
  }
}

// Enhanced stats cards
.stats-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);

    .stats-icon {
      transform: scale(1.1) rotate(5deg);
    }
  }
}

.stats-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// Enhanced empty state
.empty-state {
  transition: all 0.3s ease-in-out;
}

.empty-icon {
  transition: all 0.3s ease-in-out;
  opacity: 0.5;
}

// Enhanced role badges
.role-badge {
  transition: all 0.2s ease-in-out;

  &.owner {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    @apply text-blue-800 border border-blue-300;
  }

  &.researcher {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    @apply text-green-800 border border-green-300;
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// Improved loading animations
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Slide in animations for cards
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Pulse animation for loading text
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// Enhanced history animations
.history-item {
  transition: all 0.2s ease-in-out;
  animation: fadeInLeft 0.5s ease-out forwards;
  opacity: 0;

  &:hover {
    transform: translateX(4px);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Enhanced button hover effects
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

// Gradient button enhancements
.btn-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  
  &:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }
}

// Responsive enhancements
@media (max-width: 768px) {
  .member-card {
    .member-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .member-actions {
      margin-top: 1rem;
      justify-content: flex-start;
      width: 100%;
      gap: 0.5rem;
    }
  }

  .stats-card {
    text-align: center;
    
    .stats-icon {
      margin: 0 auto;
    }
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;

    button {
      width: 100%;
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .member-card {
    padding: 1.25rem;

    .avatar {
      width: 3rem;
      height: 3rem;
    }

    h3 {
      font-size: 1rem;
    }

    p {
      font-size: 0.875rem;
    }
  }

  .action-button {
    padding: 0.75rem;

    ng-icon {
      font-size: 1rem;
    }
  }

  .stats-card {
    padding: 1.25rem;

    .stats-icon {
      padding: 0.75rem;
    }

    p:nth-child(2) {
      font-size: 2rem;
    }
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .member-card {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.3);
    
    &:hover {
      background: rgba(31, 41, 55, 0.9);
    }
  }
}

// Print styles
@media print {
  .action-button,
  button {
    display: none;
  }
  
  .member-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .stats-icon {
    transition: transform 0.2s ease-in-out;
  }

  &:hover .stats-icon {
    transform: scale(1.1);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .members-container {
    .member-card {
      .member-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }

      .member-actions {
        margin-top: 1rem;
        justify-content: flex-start;
        width: 100%;
      }
    }

    .header-actions {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;

      button {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .share-history {
    .history-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .members-container {
    padding: 1rem;

    .member-card {
      padding: 1rem;

      .avatar {
        width: 2.5rem;
        height: 2.5rem;
      }

      .member-name {
        font-size: 0.875rem;
      }

      .member-email {
        font-size: 0.75rem;
      }
    }

    .action-button {
      padding: 0.5rem;

      ng-icon {
        font-size: 1rem;
      }
    }
  }
}

// Empty state styles
.empty-state {
  transition: all 0.3s ease-in-out;

  .empty-icon {
    opacity: 0.5;
    transition: opacity 0.3s ease-in-out;
  }

  &:hover .empty-icon {
    opacity: 0.7;
  }
}

// Button variations
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.btn-outline {
  @apply border border-blue-600 text-blue-600 hover:bg-blue-50;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  }
}

// Loading states
.loading-overlay {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
  }
}

// Fade in animation for lists
.fade-in-list {
  .list-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.3s ease-out forwards;

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
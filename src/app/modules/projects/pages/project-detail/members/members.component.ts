import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ProjectStateService } from '@app/modules/projects/services/project-state.service';
import { ProjectService } from '@app/data/services/project.service';
import { Project } from '@app/data/models/project.interface';
import { <PERSON>er, ShareHistory } from '@app/data/models/researcher.interface';
import { Subscription } from 'rxjs';
import { Dialog } from '@angular/cdk/dialog';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { AddMemberComponent, AddMemberDialogResult } from '@app/modules/projects/dialogs/add-member/add-member.component';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';

export interface AddMemberDialogData {
  projectId: number;
}

@Component({
  selector: 'app-members',
  templateUrl: './members.component.html',
  styleUrls: ['./members.component.scss']
})
export class MembersComponent implements OnInit, OnDestroy {
  project: Project | null = null;
  researchers: Researcher[] = [];
  filteredResearchers: Researcher[] = [];
  shareHistories: ShareHistory[] = [];
  isLoading = true;
  isLoadingResearchers = false;
  showHistory = false;

  // Search functionality
  searchTerm = '';

  // Owner control
  userIsOwner = false;
  ownerCheckCompleted = false;

  private projectSubscription: Subscription;

  constructor(
    private projectState: ProjectStateService,
    private projectService: ProjectService,
    private dialog: Dialog,
    private snotify: SnotifyService,
    private transloco: TranslocoService
  ) { }

  ngOnInit(): void {
    // Listen to project state
    this.projectSubscription = this.projectState.currentProject$.subscribe(project => {
      if (project) {
        this.project = project;
        this.isLoading = false;
        this.checkOwnershipAndLoadData();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.projectSubscription) {
      this.projectSubscription.unsubscribe();
    }
  }

  /**
   * Check ownership and load data
   */
  private checkOwnershipAndLoadData(): void {
    if (!this.project?.id) return;

    this.isLoadingResearchers = true;

    // First check ownership using current user data
    this.checkOwnershipFromUserData();

    // Then load researchers
    this.loadResearchers();
  }

  /**
   * Check ownership from current user data in localStorage
   */
  private checkOwnershipFromUserData(): void {
    if (!this.project) return;

    const currentUserId = this.getCurrentUserId();
    const currentUserEmail = this.getCurrentUserEmail();

    // Check if user is owner based on project.owner data
    if (this.project.owner) {
      this.userIsOwner = this.project.owner.id === currentUserId ||
        this.project.owner.email.toLowerCase() === currentUserEmail?.toLowerCase();
    }
    // Fallback to owner_name/owner_email if project.owner is not available
    else if (this.project.owner_email) {
      this.userIsOwner = this.project.owner_email.toLowerCase() === currentUserEmail?.toLowerCase();
    }

    this.ownerCheckCompleted = true;
    console.log('Owner check completed:', this.userIsOwner);
  }

  /**
   * Load researchers for the project
   */
  private loadResearchers(): void {
    if (!this.project?.id) return;

    this.isLoadingResearchers = true;

    this.projectService.getResearchers(this.project.id).subscribe({
      next: (researchers) => {
        this.researchers = researchers;
        this.filteredResearchers = [...researchers];
        this.isLoadingResearchers = false;
        console.log('✅ Loaded researchers:', researchers.length);
      },
      error: (error) => {
        console.error('❌ Error loading researchers:', error);
        this.researchers = [];
        this.filteredResearchers = [];
        this.isLoadingResearchers = false;

        // Only show error if user should have access
        if (this.userIsOwner) {
          this.snotify.error(
            this.transloco.translate('project_detail.members.messages.load_researchers_error')
          );
        }
      }
    });
  }

  /**
   * Load share histories (owner-only)
   */
  private loadShareHistories(): void {
    if (!this.project?.id || !this.userIsOwner) return;

    this.projectService.getShareHistories(this.project.id).subscribe({
      next: (histories) => {
        this.shareHistories = histories;
        console.log('✅ Loaded share histories:', histories.length);
      },
      error: (error) => {
        console.error('❌ Error loading share histories:', error);
        this.snotify.error(
          this.transloco.translate('project_detail.members.messages.load_history_error')
        );
      }
    });
  }

  /**
   * Add member modal (owner-only)
   */
  addMember(): void {
    if (!this.userIsOwner) {
      this.snotify.warning(
        this.transloco.translate('project_detail.members.messages.no_permission')
      );
      return;
    }

    console.log('Opening add member dialog for project:', this.project?.id);

    const dialogRef = this.dialog.open<AddMemberDialogResult>(AddMemberComponent, {
      width: '500px',
      data: { projectId: this.project?.id }
    });

    dialogRef.closed.subscribe((result: AddMemberDialogResult | undefined) => {
      console.log('Add member dialog result:', result);
      if (result && result.email) {
        this.addResearcher(result.email);
      } else {
        console.log('Add member operation cancelled or no email provided');
      }
    });
  }

  /**
   * Add researcher process
   */
  private addResearcher(email: string): void {
    if (!this.project?.id || !this.userIsOwner) return;

    this.projectService.addResearcher(this.project.id, { researcher: { email } }).subscribe({
      next: (response) => {
        this.snotify.success(
          response.message || this.transloco.translate('project_detail.members.messages.member_added_success')
        );
        this.loadResearchers(); // Refresh list
        if (this.showHistory) {
          this.loadShareHistories(); // Refresh history
        }
      },
      error: (error) => {
        console.error('Error adding researcher:', error);
        let messageKey = 'project_detail.members.messages.add_member_error';

        if (error.status === 404) {
          messageKey = 'project_detail.members.messages.user_not_found';
        } else if (error.status === 422) {
          messageKey = 'project_detail.members.messages.member_already_exists';
        } else if (error.status === 403 || error.status === 401) {
          messageKey = 'project_detail.members.messages.no_permission';
        }

        // Use error message from API if available, otherwise use translation
        const message = error.error?.message || this.transloco.translate(messageKey);
        this.snotify.error(message);
      }
    });
  }

  /**
   * Remove member process - With confirmation dialog
   */
  removeMember(researcher: Researcher): void {
    if (!this.project?.id || !this.userIsOwner) {
      this.snotify.warning(
        this.transloco.translate('project_detail.members.messages.no_permission')
      );
      return;
    }

    console.log('Opening remove confirmation for researcher:', researcher.id);

    const dialogRef = this.dialog.open<boolean>(ConfirmComponent, {
      width: '400px',
      data: {
        title: this.transloco.translate('project_detail.members.confirmations.remove_member.title'),
        content: this.transloco.translate('project_detail.members.confirmations.remove_member.content', { name: researcher.full_name }),
        confirm: this.transloco.translate('project_detail.members.confirmations.remove_member.confirm'),
        cancel: this.transloco.translate('project_detail.members.confirmations.remove_member.cancel')
      }
    });

    dialogRef.closed.subscribe((result: boolean | undefined) => {
      console.log('Remove dialog result:', result);
      if (result === true) {
        console.log('Confirmed: Removing researcher:', researcher.id, 'from project:', this.project!.id);

        this.projectService.removeResearcher(this.project!.id, researcher.id).subscribe({
          next: (response) => {
            console.log('✅ Remove researcher success:', response);
            this.snotify.success(
              response.message || this.transloco.translate('project_detail.members.messages.member_removed_success')
            );
            this.loadResearchers();
            if (this.showHistory) {
              this.loadShareHistories();
            }
          },
          error: (error) => {
            console.error('❌ Error removing researcher:', error);
            let messageKey = 'project_detail.members.messages.remove_member_error';

            if (error.status === 403 || error.status === 401) {
              messageKey = 'project_detail.members.messages.no_permission';
            } else if (error.status === 422) {
              messageKey = 'project_detail.members.messages.remove_member_error';
            }

            const message = error.error?.message || this.transloco.translate(messageKey);
            this.snotify.error(message);
          }
        });
      } else {
        console.log('Remove operation cancelled');
      }
    });
  }

  /**
   * Transfer ownership process - With confirmation dialog
   */
  transferOwnership(researcher: Researcher): void {
    if (!this.project?.id || !this.userIsOwner) {
      this.snotify.warning(
        this.transloco.translate('project_detail.members.messages.no_permission')
      );
      return;
    }

    console.log('Opening transfer confirmation for researcher:', researcher.id);

    const dialogRef = this.dialog.open<boolean>(ConfirmComponent, {
      width: '500px',
      data: {
        title: this.transloco.translate('project_detail.members.confirmations.transfer_ownership.title'),
        content: this.transloco.translate('project_detail.members.confirmations.transfer_ownership.content', { name: researcher.full_name }),
        confirm: this.transloco.translate('project_detail.members.confirmations.transfer_ownership.confirm'),
        cancel: this.transloco.translate('project_detail.members.confirmations.transfer_ownership.cancel')
      }
    });

    dialogRef.closed.subscribe((result: boolean | undefined) => {
      console.log('Transfer dialog result:', result);
      if (result === true) {
        console.log('Confirmed: Transferring ownership to researcher:', researcher.id, 'project:', this.project!.id);

        this.projectService.transferOwnership(this.project!.id, researcher.id).subscribe({
          next: (response) => {
            console.log('✅ Transfer ownership success:', response);
            this.snotify.success(
              response.message || this.transloco.translate('project_detail.members.messages.ownership_transferred_success')
            );
            // After transfer, user is no longer owner, refresh page
            this.userIsOwner = false;
            this.projectState.refreshCurrentProject();
            this.checkOwnershipAndLoadData();
          },
          error: (error) => {
            console.error('❌ Error transferring ownership:', error);
            let messageKey = 'project_detail.members.messages.transfer_ownership_error';

            if (error.status === 403 || error.status === 401) {
              messageKey = 'project_detail.members.messages.no_permission';
            } else if (error.status === 422) {
              messageKey = 'project_detail.members.messages.transfer_ownership_error';
            }

            const message = error.error?.message || this.transloco.translate(messageKey);
            this.snotify.error(message);
          }
        });
      } else {
        console.log('Transfer operation cancelled');
      }
    });
  }

  /**
   * Toggle history display
   */
  toggleHistory(): void {
    if (!this.userIsOwner) {
      this.snotify.warning(
        this.transloco.translate('project_detail.members.messages.no_permission')
      );
      return;
    }

    this.showHistory = !this.showHistory;
    if (this.showHistory && this.shareHistories.length === 0) {
      this.loadShareHistories();
    }
  }

  /**
   * Get total member count including owner
   */
  getTotalMemberCount(): number {
    let count = this.researchers.length;

    // Add owner to count if owner exists
    if (this.project?.owner || this.project?.owner_name) {
      count += 1;
    }

    return count;
  }

  /**
   * Check if user is owner
   */
  isOwner(): boolean {
    return this.ownerCheckCompleted && this.userIsOwner;
  }

  /**
   * Get current user ID from localStorage
   */
  private getCurrentUserId(): number | null {
    // Try different possible keys for user ID
    const userId = localStorage.getItem('user_id') || localStorage.getItem('USER_ID');
    return userId ? parseInt(userId, 10) : null;
  }

  /**
   * Get current user email from localStorage
   */
  private getCurrentUserEmail(): string | null {
    return localStorage.getItem('email') || null;
  }

  /**
   * Filter members based on search term
   */
  filterMembers(): void {
    if (!this.searchTerm.trim()) {
      this.filteredResearchers = [...this.researchers];
      return;
    }

    const term = this.searchTerm.toLowerCase().trim();
    this.filteredResearchers = this.researchers.filter(researcher =>
      researcher.full_name.toLowerCase().includes(term) ||
      researcher.email.toLowerCase().includes(term) ||
      (researcher.name && researcher.name.toLowerCase().includes(term)) ||
      (researcher.surname && researcher.surname.toLowerCase().includes(term))
    );
  }

  /**
   * Clear search term
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.filterMembers();
  }

  /**
   * Get icon for history action type
   */
  getHistoryIcon(actionType: string): string {
    const icons: { [key: string]: string } = {
      'add_researcher': 'lucideUserPlus',
      'remove_researcher': 'lucideUserMinus',
      'transfer_ownership': 'lucideCrown'
    };
    return icons[actionType] || 'lucideHistory';
  }

  /**
   * Get Turkish text for action type
   */
  getActionTypeText(actionType: string): string {
    const actionKey = `project_detail.members.history.actions.${actionType}`;
    return this.transloco.translate(actionKey);
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
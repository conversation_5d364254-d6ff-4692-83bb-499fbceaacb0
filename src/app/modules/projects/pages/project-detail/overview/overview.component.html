<div *transloco="let t;read 'project_detail.overview'" class="flex items-center justify-center size-full">
    <div *ngIf="!project?.datasets || project.datasets.length !== 0"
        class="flex flex-col gap-6 overflow-hidden size-full">
        <!-- New 2-column Layout -->
        <div class="flex flex-col h-full gap-4">
            <div *ngIf="project?.project_type === 'demo'"
                class="inline-flex items-center p-2 text-sm font-medium text-blue-800 bg-blue-100 rounded-full">
                <ng-icon name="lucideInfo" class="mr-2 text-lg"></ng-icon>
                {{t('demo_info')}}
            </div>
            <div class="flex h-full gap-6">
                <!-- Left Column: Last Reports (Full Height) -->
                <div class="w-1/2 h-full">
                    <!-- Last Reports Card -->

                    <div *ngIf="project.datasets.length > 0 && project.datasets[0].diagnosed_s3_url"
                        class="flex flex-col w-full h-full overflow-hidden bg-white border border-neutral-150 rounded-3xl">

                        <div class="flex items-center justify-between p-6 pb-3">
                            <span class="w-full text-2xl font-bold">
                                {{ t('last_reports.title') }}
                            </span>
                            <button *ngIf="!isLoading && reports.length !== 0"
                                class="flex items-center justify-center gap-1 text-sm text-nowrap text-brand-green-500"
                                (click)="navigateToAnalysisHistory()">
                                <span>{{ t('last_reports.view_all') }}</span>
                                <ng-icon name="lucideArrowRight" class="text-lg text-brand-green-500"></ng-icon>
                            </button>
                        </div>
                        <!-- Skeleton Loader for Reports -->
                        <div *ngIf="isLoading && project.datasets.length > 0 && project.datasets[0].diagnosed_s3_url"
                            class="flex flex-col flex-grow gap-3 p-3 overflow-y-auto">
                            <div *ngFor="let item of [1,2,3,4,5]"
                                class="flex items-center gap-1 bg-white border rounded-3xl animate-pulse">
                                <div class="flex justify-between w-full gap-3 p-4">
                                    <div class="flex gap-3">
                                        <div class="flex items-center justify-center bg-gray-200 rounded-xl size-12">
                                        </div>
                                        <div class="flex flex-col justify-start gap-2">
                                            <div class="w-48 h-5 bg-gray-200 rounded-md"></div>
                                            <div class="flex items-center gap-2">
                                                <div class="w-4 h-4 bg-gray-200 rounded-full"></div>
                                                <div class="w-32 h-4 bg-gray-200 rounded-md"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex gap-1">
                                        <div class="bg-gray-200 rounded-full size-12 min-w-12"></div>
                                    </div>
                                </div>
                                <div
                                    class="flex items-center justify-center mx-2 bg-gray-200 rounded-3xl size-12 min-w-12">
                                </div>
                            </div>
                        </div>

                        <!-- Actual Reports List -->
                        <div *ngIf="!isLoading && reports.length > 0"
                            class="flex flex-col flex-grow gap-3 p-3 overflow-y-auto">
                            <div class="flex items-center gap-1 bg-white border rounded-3xl hover:border-brand-green-500 group"
                                *ngFor="let report of reports">
                                <button (click)="navigateToDetail(report)"
                                    class="flex justify-between w-full gap-3 p-4 transition-all">
                                    <div class="flex gap-3">
                                        <div
                                            class="flex items-center justify-center rounded-xl bg-status-success-100 size-12">
                                            <ng-icon name="lucideFileText"
                                                class="text-2xl text-brand-green-500"></ng-icon>
                                        </div>
                                        <div class="flex flex-col justify-start">
                                            <span
                                                class="w-full font-semibold text-left text-gray-800 truncate max-w-64">
                                                {{ report.label }}
                                            </span>
                                            <div class="flex items-center gap-1 text-sm text-gray-500">
                                                <ng-icon name="lucideClock4"></ng-icon>
                                                <span class="text-sm">
                                                    {{ report.created_at | date:'dd/MM/yyyy HH:mm' }}
                                                </span>
                                                <span *ngIf="report.credit_usage?.used_credit" class="flex text-xs">
                                                    <img src="assets/icons/istacoin.svg" alt="" class="mr-1 size-4">
                                                    {{report.credit_usage?.used_credit}}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex gap-1">
                                        <button matTooltip="Raporu görüntüle"
                                            class="flex items-center justify-center ml-2 text-gray-400 transition-colors rounded-full size-12 min-w-12 hover:bg-gray-100 group-hover:text-brand-green-500">
                                            <ng-icon name="lucideChevronRight"
                                                class="text-2xl group-hover:text-brand-green-500"></ng-icon>
                                        </button>
                                    </div>
                                </button>
                                <button (click)="downloadDocxById(report)" matTooltip="Raporu indir"
                                    class="flex items-center justify-center mx-2 text-gray-400 rounded-3xl size-12 min-w-12 hover:transition-colors hover:bg-gray-100 hover:text-brand-green-500">
                                    <ng-icon name="lucideDownload" class="text-xl"></ng-icon>
                                </button>
                            </div>
                        </div>

                        <!-- Skeleton Loader for No Reports Message -->
                        <div *ngIf="isLoading && project.datasets.length > 0 && project.datasets[0].diagnosed_s3_url && reports.length === 0"
                            class="flex items-center justify-center flex-grow">
                            <div class="w-48 h-5 bg-gray-200 rounded-md animate-pulse"></div>
                        </div>

                        <!-- No Reports Message -->
                        <div *ngIf="!isLoading && reports.length === 0"
                            class="flex items-center justify-center flex-grow text-gray-500">
                            {{ t('last_reports.no_report') }}
                        </div>
                    </div>

                    <!-- Placeholder when no reports card is displayed -->
                    <div *ngIf="!(project.datasets.length > 0 && project.datasets[0].diagnosed_s3_url)"
                        class="flex flex-col w-full h-full p-6 bg-white border border-neutral-200 rounded-3xl">
                        <div class="flex flex-col items-center justify-center h-full gap-4 text-center">
                            <ng-icon name="lucideFileText" class="text-4xl text-neutral-300"></ng-icon>
                            <span class="text-xl font-medium text-neutral-400">{{ t('last_reports.title') }}</span>
                            <p class="text-sm text-neutral-400">
                                {{ t('last_reports.no_report') }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Dataset Info and Start Analysis (Stacked) -->
                <div class="flex flex-col w-1/2 h-full gap-6">
                    <!-- Dataset Info Card -->
                    <div class="flex flex-col w-full gap-6 p-6 bg-white h-fit rounded-3xl">
                        <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold">
                                {{ t('dataset_info') }}
                            </span>
                            <button (click)="showDiagnose()" [disabled]="isDiagnoseLoading"
                                *ngIf="project.datasets.length > 0 && project.datasets[0].diagnosed_s3_url"
                                class="flex items-center justify-center gap-1 text-sm text-status-info-500">
                                <span>{{ t('edit_dataset') }}</span>
                                <ng-icon *ngIf="!isDiagnoseLoading" name="lucideArrowRight"
                                    class="text-lg text-status-info-500"></ng-icon>
                                <ng-icon *ngIf="isDiagnoseLoading" name="hugeLoading03"
                                    class="text-xl animate-spin"></ng-icon>
                            </button>
                        </div>

                        <!-- Skeleton Loader for Dataset Info -->
                        <div *ngIf="isLoading && project.datasets.length > 0 && project.datasets[0].diagnosed_s3_url"
                            class="flex flex-col gap-6">
                            <div class="flex items-center justify-between p-6 bg-neutral-100 rounded-3xl animate-pulse">
                                <div class="flex items-center justify-center gap-1">
                                    <div class="w-6 h-6 bg-gray-200 rounded-full"></div>
                                    <div class="w-32 h-4 bg-gray-200 rounded-md"></div>
                                </div>
                                <div class="flex items-center justify-center gap-1">
                                    <div class="w-24 h-4 bg-gray-200 rounded-md"></div>
                                    <div class="w-6 h-6 bg-gray-200 rounded-full"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Actual Dataset Info -->
                        <div *ngIf="!isLoading && project.datasets.length > 0 && project.datasets[0].diagnosed_s3_url"
                            class="flex flex-col gap-6">
                            <button (click)="navigateToDataset()"
                                class="flex items-center justify-between p-6 bg-neutral-100 rounded-3xl">
                                <div class="flex items-center justify-center gap-1 text-sm text-status-info-500">
                                    <ng-icon name="lucideDatabase" class="text-lg text-neutral-600"></ng-icon>
                                    <span class="w-full max-w-xs truncate text-neutral-600">
                                        {{ project.datasets[0].name }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-center gap-1 text-sm text-status-info-500">
                                    <span>{{ t('view_dataset') }}</span>
                                    <ng-icon name="lucideEye" class="text-lg text-status-info-500"></ng-icon>
                                </div>
                            </button>
                        </div>

                        <div *ngIf="project.datasets.length > 0 && !project.datasets[0].diagnosed_s3_url"
                            class="flex flex-col gap-6">
                            <div class="flex items-center justify-between p-6 bg-status-warning-25 rounded-3xl">
                                <div class="flex items-center justify-center gap-1">
                                    <ng-icon name="lucideDatabase" class="text-lg text-neutral-600"></ng-icon>
                                    <span class="w-full max-w-xs truncate text-neutral-600">
                                        {{ project.datasets[0].name }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-center gap-1 text-status-warning-700">
                                    <ng-icon name="lucideTriangleAlert" class="text-lg"></ng-icon>
                                    <span>{{ t('dataset_not_diagnosed.warning') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skeleton Loader for Start Analysis Card -->
                    <div *ngIf="isLoading && project.datasets.length > 0"
                        class="flex flex-col w-full h-full p-6 rounded-3xl bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse">
                        <div class="flex flex-col h-full">
                            <div>
                                <div class="w-48 h-8 mb-4 bg-gray-200 rounded-md"></div>
                                <div class="w-full h-6 mb-2 bg-gray-200 rounded-md"></div>
                                <div class="w-3/4 h-6 bg-gray-200 rounded-md"></div>
                            </div>
                            <div class="flex justify-end mt-auto">
                                <div class="w-full h-12 bg-gray-200 rounded-3xl"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Start Analysis Card -->
                    <div *ngIf="!isLoading && project.datasets.length > 0"
                        class="flex flex-col w-full h-full p-6 rounded-3xl" [ngClass]="project.datasets[0].diagnosed_s3_url
                        ? 'bg-gradient-to-br from-brand-blue-100 to-brand-blue-200'
                        : 'bg-gradient-to-br from-white to-status-warning-25'">
                        <div class="flex flex-col h-full">
                            <div>
                                <span class="w-full text-2xl font-semibold" [ngClass]="project.datasets[0].diagnosed_s3_url
                                    ? 'text-brand-blue-500'
                                    : 'text-black'">
                                    {{ project.datasets[0].diagnosed_s3_url
                                    ? t('dataset_ready.title')
                                    : t('dataset_not_diagnosed.title') }}
                                </span>
                                <p class="mt-3 text-xl font-medium" [ngClass]="project.datasets[0].diagnosed_s3_url
                                    ? 'text-data-blue-500'
                                    : 'text-neutral-600'">
                                    {{ project.datasets[0].diagnosed_s3_url
                                    ? t('dataset_ready.info')
                                    : t('dataset_not_diagnosed.info') }}
                                </p>
                            </div>
                            <div class="flex justify-end mt-auto">
                                <button *ngIf="project.datasets[0].diagnosed_s3_url" (click)="navigateToAnalysis()"
                                    class="flex items-center justify-center w-full gap-2 primary-blue-button">
                                    <span>{{ t('dataset_ready.button') }}</span>
                                    <ng-icon name="lucideArrowRight" class="text-xl text-white"></ng-icon>
                                </button>
                                <button *ngIf="!project.datasets[0].diagnosed_s3_url" (click)="showDiagnose()"
                                    [disabled]="isDiagnoseLoading"
                                    class="items-center justify-center transition-all secondary-status-warning-button">
                                    <ng-icon *ngIf="isDiagnoseLoading" name="hugeLoading03"
                                        class="text-xl animate-spin"></ng-icon>
                                    <ng-icon *ngIf="!isDiagnoseLoading" name="lucideSettings" class="text-xl"></ng-icon>
                                    <span>{{ t('dataset_not_diagnosed.button') }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- no dataset view - unchanged -->
    <div *ngIf="!project?.datasets || project.datasets.length === 0"
        class="flex flex-col items-center justify-center size-full">
        <!-- Skeleton Loader for No Dataset View -->
        <div *ngIf="isLoading"
            class="flex flex-col items-center justify-center gap-6 bg-white shadow-md rounded-3xl size-full animate-pulse">
            <div class="flex flex-col items-center justify-center gap-3 p-6">
                <div class="flex items-center justify-center p-4 bg-gray-200 rounded-full size-16"></div>
                <div class="w-48 h-6 bg-gray-200 rounded-md"></div>
                <div class="w-64 h-4 bg-gray-200 rounded-md"></div>
            </div>
            <div class="h-12 bg-gray-200 w-80 rounded-3xl"></div>
        </div>

        <!-- Actual No Dataset View -->
        <div *ngIf="!isLoading"
            class="flex flex-col items-center justify-center bg-white shadow-md rounded-3xl size-full">

            <div class="flex flex-col items-center justify-center gap-3 p-6">
                <div class="flex items-center justify-center p-4 rounded-full bg-data-blue-100">
                    <ng-icon name="lucideCloudUpload" class="text-3xl text-brand-blue-500"></ng-icon>
                </div>

                <span class="text-xl font-semibold">{{ t('no_dataset.title') }}</span>
                <span class="text-neutral-600">{{ t('no_dataset.info') }}</span>
            </div>

            <div class="flex flex-col gap-6">
                <div *ngIf="selectedFile"
                    class="flex items-center justify-between w-full p-3 border border-blue-200 rounded-3xl bg-blue-50">
                    <div class="flex items-center gap-2">
                        <ng-icon name="lucideFileSpreadsheet" class="text-xl text-brand-blue-400"></ng-icon>
                        <span class="text-sm text-neutral-950">{{ selectedFile.name }}</span>
                    </div>
                    <button type="button" class="text-gray-400 hover:text-red-500" (click)="resetFileInput()">
                        <ng-icon name="lucideTrash2" class="text-xl"></ng-icon>
                    </button>
                </div>

                <div class="flex items-center justify-between">
                    <ng-container *ngIf="canUpdateDataset()">
                        <div class="flex items-center gap-2">
                            <input #fileInput type="file" accept=".xlsx" class="hidden"
                                (change)="handleFileInput($event)">

                            <ng-container *ngIf="selectedFile">
                                <button type="button" (click)="updateDataset()" [disabled]="isFileUploading"
                                    class="primary-blue-button">
                                    <ng-icon *ngIf="isFileUploading" name="lucideLoader" class="animate-spin"></ng-icon>
                                    <ng-icon *ngIf="!isFileUploading" name="lucideSave"></ng-icon>
                                    {{t('save_dataset')}}
                                </button>
                                <button type="button" (click)="resetFileInput()" [disabled]="isFileUploading"
                                    class="secondary-blue-button">
                                    <ng-icon name="lucideX"></ng-icon>
                                    {{t('cancel')}}
                                </button>
                            </ng-container>
                        </div>
                    </ng-container>
                </div>
            </div>

            <!-- Upload Area -->
            <label for="dataset" [ngClass]="{'border-brand-blue-500': isDragging}" *ngIf="!selectedFile"
                class="flex items-center justify-center px-12 py-6 transition-colors border-2 border-gray-200 border-dashed cursor-pointer w-fit group rounded-3xl hover:border-brand-blue-500"
                (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onDrop($event)">
                <div class="flex flex-col items-center gap-2">
                    <div class="text-sm text-gray-500">
                        {{ t('no_dataset.area_text') }}
                        <span class="text-brand-blue-400 hover:underline">{{ t('no_dataset.area_text_2') }}</span>

                    </div>
                    <div class="text-xs text-gray-400">{{ t('no_dataset.area_info') }}</div>
                </div>
                <input #fileInput (change)="handleFileInput($event)" type="file" id="dataset" accept=".xlsx"
                    class="hidden">
            </label>
        </div>
    </div>
</div>
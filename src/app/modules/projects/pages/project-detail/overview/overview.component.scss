// .animate__animated {
//     animation-duration: 0.3s;
//     animation-fill-mode: both;
// }

// .animate__fadeIn {
//     animation-name: fadeIn;
// }

// .animate__fadeOut {
//     animation-name: fadeOut;
// }

// @keyframes fadeIn {
//     from {
//         opacity: 0;
//         transform: translateY(-5px);
//     }

//     to {
//         opacity: 1;
//         transform: translateY(0);
//     }
// }

// @keyframes fadeOut {
//     from {
//         opacity: 1;
//         transform: translateY(0);
//     }

//     to {
//         opacity: 0;
//         transform: translateY(-5px);
//     }
// }

// /* Buton hover animasyonları */
// button {
//     transition: all 0.2s ease-in-out;
// }

// button:hover {
//     transform: translateY(-2px);
//     box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
// }

// /* Textarea animasyonu */
// textarea {
//     transition: all 0.3s ease;
// }

// textarea:focus {
//     transform: scale(1.01);
// }
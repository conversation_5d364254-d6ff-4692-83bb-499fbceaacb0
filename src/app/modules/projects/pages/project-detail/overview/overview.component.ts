import { SnotifyService } from 'ng-alt-snotify';
import { DatasetService } from '@app/data/services/dataset.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { ProjectService } from '@app/data/services/project.service';
import { Dialog } from '@angular/cdk/dialog';
import { TranslocoService } from '@ngneat/transloco';
import { ReportHelperService } from '@app/data/helper/report.helper.service';
import { ReportLine } from '@app/data/models/report_line.interface';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Location } from '@angular/common';
import { ProjectStateService } from '../../../services/project-state.service';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { DiagnoseComponent } from '@app/modules/diagnose/dialogs/diagnose/diagnose.component';
import { EmailJsService } from '@app/data/services/emailjs.service';
import { AnalysisService } from '@app/data/services/analysis.service';
import { ExcelService } from '@app/data/services/excel.service';
import { DataTransferService } from '@app/data/services/data-transfer.service';
import { Project } from '@app/data/models/project.interface';
import { FormsModule } from '@angular/forms';
import { environment } from '@env/environment';

@Component({
  selector: 'app-project-overview',
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.scss']
})
export class OverviewComponent implements OnInit {

  project: Project;
  isLoading = true;
  reports: ReportLine[] = [];
  totalVariables: number = 0;
  totalNullValues: number = 0;
  notImportedVariables: number = 0;
  isDiagnoseLoading: boolean = false;
  lastProjectId: number;
  selectedFile: File = null;
  fileName = '';
  actualFileName = '';
  isFileUploading = false;
  @ViewChild('fileInput') fileInput: ElementRef;
  hasReports = true;

  constructor(
    private b: BreadcrumbService,
    private rh: ReportHelperService,
    private router: Router,
    private route: ActivatedRoute,
    private p: ProjectService,
    private location: Location,
    private projectState: ProjectStateService,
    private ds: DatasetService,
    private dh: DiagnoseHelperService,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
    private dialog: Dialog,
    private emailJsService: EmailJsService,
    private a: AnalysisService,
    private es: ExcelService,
    private dt: DataTransferService
  ) {
  }

  ngOnInit(): void {
    this.projectState.currentProject$.subscribe(project => {
      if (project) {
        this.project = project;
      }
    });
    this.route.params.subscribe((params: Params) => {

      if (!this.project) {
        const projectId = params['pid'];
        this.loadProject(projectId);
      } else {
        this.loadProjectData();
      }
    });

    this.loadReports();
    this.loadDatasetAnalysis();
    if (this.project?.datasets?.length > 0) {
      this.dt.setDataset(this.project.datasets[0]);
    }
  }

  loadReports() {
    if (!this.project || !this.project.datasets || this.project.datasets.length === 0) {
      return;
    }

    const firstDataset = this.project.datasets[0];
    if (!firstDataset.analyses || firstDataset.analyses.length === 0) {
      return;
    }

    const analysisId = firstDataset.analyses[0].id;

    this.isLoading = true;
    // Sadece en son 4 raporu çekmek için limit parametresi ekliyoruz
    this.rh.getAnalysisReportLines(analysisId.toString(), 5).subscribe(
      (data) => {
        if (data && data.length > 0) {
          // 📌 Tarihe göre sıralayarak en yeni raporu en üste alıyoruz
          this.reports = data.sort((a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          console.log(this.reports)
        } else {
          this.reports = [];
        }

        this.isLoading = false;
      },
      (error) => {
        this.isLoading = false;
      }
    );
  }

  navigateToDataset() {
    this.router.navigate([`/projects/${this.project.id}/dataset`]);
  }

  navigateToAnalysis() {
    this.router.navigate([`/projects/${this.project.id}/analysis`]);
  }

  async loadDatasetAnalysis() {
    if (!this.project?.datasets?.length) return;

    const dataset = await this.ds.getDatasetById(this.project.datasets[0].id.toString()).toPromise();

    if (!dataset || !dataset.variables || dataset.variables.length === 0) {
      return;
    }

    const analysisResults = await this.dh.analyzeVariableData(dataset.variables);
    this.totalVariables = analysisResults.totalVariables;
    this.totalNullValues = analysisResults.totalNullValues;

    // Import değeri false olan değişkenleri say
    this.notImportedVariables = dataset.variables.filter(variable => !variable.import).length;

    // DataTransferService ile veri paylaşımı
    this.dt.setDataset(dataset);
    this.dt.setTotalVariables(this.totalVariables);
    this.dt.setTotalNullValues(this.totalNullValues);
    this.dt.setNotImportedVariables(this.notImportedVariables);
  }

  async showDiagnose() {
    if (this.isDiagnoseLoading) return;
    try {
      this.isDiagnoseLoading = true;
      const fileUrl = this.project.datasets[0].diagnosed_s3_url || this.project.datasets[0].s3_url;
      const fileName = decodeURIComponent(fileUrl.split('/').pop().replace(/\+/g, " "));
      const blob = await this.ds.getDataset(fileName);
      const diagnoseData = await this.dh.prepareDiagnoseData(
        blob,
        this.project.datasets[0].s3_url,
        this.project.id.toString(),
        this.project.datasets[0].id.toString()
      );

      const dialog = this.dialog.open(DiagnoseComponent, {
        data: {
          ...diagnoseData,
          datasetName: this.project.datasets[0].name,
          fromCreateProject: true,
        },
        width: '100%'
      });

      dialog.closed.subscribe((data: number) => {
        if (data) {
          this.lastProjectId = data;
          this.refreshDataset();
        }
      });

    } catch (error) {
      this.snotifyService.error(this.transloco.translate('notification.diagnose.error.message'));
    } finally {
      this.isDiagnoseLoading = false;
    }
  }

  refreshDataset() {
    this.ds.getDatasetById(this.project.datasets[0].id.toString()).subscribe({
      next: (updatedDataset) => {
        if (updatedDataset) {
          this.project.datasets[0] = updatedDataset;
          this.dt.setDataset(updatedDataset);
          this.loadDatasetAnalysis();
        }
      },
      error: (err) => {
        console.error("❌ Dataset yenileme hatası:", err);
      }
    });
  }

  loadProject(projectId: string) {
    this.isLoading = true;
    this.p.getProjectById(Number(projectId)).subscribe({
      next: (data) => {
        if (data) {
          this.project = data;
          this.loadProjectData();
        }
      },
      error: () => {
        console.error("❌ Proje yüklenirken hata oluştu!");
      }
    });
  }

  loadProjectData() {
    this.loadDatasetAnalysis();
    this.isLoading = false;
  }

  navigateToDetail(report: ReportLine) {
    if (!report || !report.id) {
      return;
    }
    this.router.navigate(['/reports', report.id]);
  }

  downloadDocxById(report: any) {
    if (!report || !report.id) {
      return;
    }

    const reportId = report.credit_usage?.creditable_id ?? report.id;

    if (!reportId) {
      return;
    }

    const lang = localStorage.getItem('activeLang') || 'tr';

    this.a.getReportContentDocxById(reportId, lang).subscribe(
      (data) => {
        this.emailJsService.sendReportDownloadNotification(report.id);

        const a = document.createElement('a');
        const objectUrl = URL.createObjectURL(data);
        a.href = objectUrl;

        const fileName = this.slugify(report.label || report.title || `report_${reportId}`) + '.docx';
        a.download = fileName;

        a.click();
        URL.revokeObjectURL(objectUrl);
      },
      (error) => {
      }
    );
  }

  slugify(text: string): string {
    return text
      .toString()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  }

  async handleFileInput(event: any): Promise<void> {
    const file = event.target.files[0];
    if (!file) return;

    try {
      // Dosya validasyonu
      const fileValidation = this.ds.validateFile(file);
      if (!fileValidation.isValid) {
        fileValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }

      // Excel içerik validasyonu
      const data = await this.ds.parseExcelDataView(file);
      const contentValidation = this.ds.validateDataContent(data);
      if (!contentValidation.isValid) {
        contentValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }

      this.selectedFile = file;
      this.actualFileName = await this.es.readDatasetName(file);
      this.fileName = this.updateFileName(file.name);

    } catch (error) {
      this.snotifyService.error(this.transloco.translate('project_detail.settings.file_processing_error'));
      this.resetFileInput();
    }
  }

  private updateFileName(name: string): string {
    const extension = name.substring(name.lastIndexOf('.'));
    const fileNameWithProjectId = name.replace(extension, '') + new Date().toISOString();
    return this.slugify(fileNameWithProjectId) + extension;
  }

  async updateDataset(): Promise<void> {
    if (!this.selectedFile || this.isFileUploading) return;

    this.isFileUploading = true;
    try {
      const processedData = await this.es.readExcelFile(this.selectedFile);
      const processedBlob = await this.es.writeExcelFile(processedData, this.selectedFile.name);

      const uploadData = await this.ds.getUploadPresignedUrl(
        this.fileName,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ).toPromise();

      const processedFile = new File([processedBlob], this.fileName, {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      await this.ds.uploadToS3(uploadData.url, processedFile).toPromise();

      const s3_url = `https://istabot-development.s3.eu-central-1.amazonaws.com/${this.fileName}`;

      if (this.project.datasets?.length) {
        // Update existing dataset
        this.ds.updateDatasetUrl(
          this.project.datasets[0].id.toString(),
          s3_url,
          this.actualFileName
        );

        // Fetch fresh project data to get updated timestamp
        const freshProjectData = await this.p.getProjectById(this.project.id).toPromise();

        // Update local project state with new dataset and updated project data
        const updatedProject = {
          ...freshProjectData,
          datasets: [{
            ...this.project.datasets[0],
            s3_url,
            name: this.actualFileName
          }]
        };
        this.projectState.setProject(updatedProject);
      } else {
        // Add new dataset
        const response = await this.ds.addDatasetToProject(
          s3_url,
          this.actualFileName,
          String(this.project.id)
        ).toPromise();

        // Fetch fresh project data to get updated timestamp
        const freshProjectData = await this.p.getProjectById(this.project.id).toPromise();

        // Update project with new dataset and updated project data
        const updatedProject = {
          ...freshProjectData,
          datasets: [{
            id: response.id,
            name: response.name,
            s3_url: response.s3_url,
            project_id: response.project_id,
            variables_json: response.variables_json,
            diagnosed_s3_url: response.diagnosed_s3_url,
            created_at: response.created_at,
            updated_at: response.updated_at,
            analyses: [],
            variables: []
          }]
        };
        this.projectState.setProject(updatedProject);
      }
      this.showDiagnose();
      // Show success message
      this.snotifyService.success(
        this.transloco.translate(
          this.project.datasets?.length
            ? 'project_detail.settings.dataset_update_success'
            : 'project_detail.settings.dataset_add_success'
        )
      );
      this.resetFileInput();


    } catch (error) {
      this.snotifyService.error(
        this.transloco.translate(
          this.project.datasets?.length
            ? 'project_detail.settings.dataset_update_error'
            : 'project_detail.settings.dataset_add_error'
        )
      );
    } finally {
      this.isFileUploading = false;
    }
  }

  resetFileInput(): void {
    this.selectedFile = null;
    this.fileInput.nativeElement.value = '';
  }

  canUpdateDataset(): boolean {
    // Eğer hiç dataset yoksa true döndür
    if (!this.project?.datasets?.length) return true;
    this.showDiagnose()
    // Eğer diagnosed_s3_url veya rapor varsa güncellemeye izin verme
    return !this.project.datasets[0].diagnosed_s3_url && !this.hasReports;
  }

  navigateToAnalysisHistory() {
    this.router.navigate([`/projects/${this.project.id}/analysis/history`]);
  }
}
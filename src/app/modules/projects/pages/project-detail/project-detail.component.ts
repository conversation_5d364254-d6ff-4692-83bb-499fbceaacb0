import { EditProjectComponent } from '@app/modules/projects/dialogs/edit-project/edit-project.component';
import { Location } from '@angular/common';
import { Component, OnDestroy, OnInit, ChangeDetectorRef, HostListener } from '@angular/core';
import { ActivatedRoute, Params, Router, NavigationEnd, NavigationStart } from '@angular/router';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { ProjectService } from '@app/data/services/project.service';
import { ProjectStateService } from '../../services/project-state.service';
import { filter } from 'rxjs/operators';
import { Project } from '@app/data/models/project.interface';
import { Breadcrumb } from '@app/data/models/breadcrumb.interface';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { Dialog } from '@angular/cdk/dialog';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';
import { AdminService } from '@app/data/services/admin.service';
import { SelectProjectComponent } from '@app/modules/projects/dialogs/select-project/select-project.component';

type ProjectTab = 'overview' | 'dataset' | 'analysis' | 'settings' | 'members' | 'history';

@Component({
  selector: 'app-project-detail',
  templateUrl: './project-detail.component.html',
  styleUrls: ['./project-detail.component.scss']
})
export class ProjectDetailComponent implements OnInit, OnDestroy {
  isLoading = true;
  initializing = true;
  project: Project | null = null;
  activeTab: ProjectTab = 'overview';
  projectName: string;
  hasDataset: boolean = false;
  hasDiagnosedDataset: boolean = false;
  isUpdatingType = false;

  isCloning: boolean = false;
  isRecalculating: boolean = false;
  isComparing: boolean = false;
  isAdmin = false;

  adminDropdownOpen = false;
  showProjectTypes = false;
  isChangingType = false;

  constructor(
    private b: BreadcrumbService,
    private router: Router,
    private route: ActivatedRoute,
    private p: ProjectService,
    private location: Location,
    private projectState: ProjectStateService,
    private cdr: ChangeDetectorRef,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
    private dialog: Dialog,
    private ah: AdminHelperService,
    private admin: AdminService
  ) {
    // Check if user is admin
    if (this.ah.isRole('admin')) {
      this.isAdmin = true;
    }

    // Router olaylarını dinle - proje detay sayfasından ayrılırken başlığı sıfırla
    this.router.events
      .pipe(filter(event => event instanceof NavigationStart))
      .subscribe((event: NavigationStart) => {
        // Proje detay sayfasından başka bir sayfaya geçiyorsa başlığı sıfırla
        if (this.router.url.includes('/projects/') && !event.url.includes('/projects/')) {
          document.title = 'istabot';
        }
      });

    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras.state as {
      project: Project;
    };
    if (state) {
      this.project = state.project;
      this.projectState.setProject(this.project);
      this.initializing = false;
    }

    if (this.router.url.includes('analysis/history')) {
      this.activeTab = 'analysis';
    }
  }

  toggleAdminDropdown(): void {
    this.adminDropdownOpen = !this.adminDropdownOpen;
    this.showProjectTypes = false; // Reset sub-menu
  }

  showProjectTypeOptions(): void {
    this.showProjectTypes = !this.showProjectTypes;
  }

  selectProjectType(type: string): void {
    this.isChangingType = true;
    this.adminDropdownOpen = false;
    this.showProjectTypes = false;

    // Mevcut selectOption metodunuzu çağırın
    this.selectOption(type);

    // Loading state'i reset etmek için
    setTimeout(() => {
      this.isChangingType = false;
    }, 2000);
  }

  // Host listener for clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (this.adminDropdownOpen && !target.closest('.relative')) {
      this.adminDropdownOpen = false;
      this.showProjectTypes = false;
    }
  }


  isAdminUser(): boolean {
    try {
      const roles = JSON.parse(localStorage.getItem('roles') || '[]');
      // Check if any role has name "admin"
      return roles.some(role => role.name === 'admin');
    } catch (error) {
      console.error('Error parsing roles:', error);
      return false;
    }
  }

  // Admin functions moved from overview component
  cloneProject() {
    this.adminDropdownOpen = false;
    this.showProjectTypes = false;
    if (!this.project) return;

    this.isCloning = true;
    this.admin.cloneProject(this.project.id).subscribe({
      next: (data) => {
        this.snotifyService.success(
          this.transloco.translate('notification.project.clone.success.message'),
          this.transloco.translate('notification.project.clone.success.title'),
          {
            timeout: 5000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
        this.router.navigate(['/projects']);
      },
      error: (error) => {
        console.log(error);
        this.snotifyService.error(
          this.transloco.translate('notification.project.clone.error.message'),
          this.transloco.translate('notification.project.clone.error.title'),
          {
            timeout: 5000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      },
      complete: () => {
        this.isCloning = false;
      }
    });
  }

  recalculateProject() {
    this.adminDropdownOpen = false;
    this.showProjectTypes = false;
    if (!this.project?.datasets?.[0]?.analyses?.[0]?.id) return;

    this.isRecalculating = true;
    this.admin.recalculateProject(this.project.datasets[0].analyses[0].id).subscribe({
      next: (data) => {
        this.snotifyService.success(
          this.transloco.translate('notification.project.recalculate.success.message'),
          this.transloco.translate('notification.project.recalculate.success.title'),
          {
            timeout: 5000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
        window.location.reload();
      },
      error: (error) => {
        console.log(error);
        this.snotifyService.error(
          this.transloco.translate('notification.project.recalculate.error.message'),
          this.transloco.translate('notification.project.recalculate.error.title'),
          {
            timeout: 5000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      },
      complete: () => {
        this.isRecalculating = false;
      }
    });
  }

  compareReports() {
    this.adminDropdownOpen = false;
    this.showProjectTypes = false;
    if (!this.project) return;

    const dialog = this.dialog.open(SelectProjectComponent, {
      data: {
        currentProjectId: this.project.id
      },
      width: '400px'
    });

    dialog.closed.subscribe((selectedAnalysisId: number) => {
      if (selectedAnalysisId && this.project?.datasets?.[0]?.analyses?.[0]?.id) {
        this.isComparing = true;
        const currentAnalysisId = this.project.datasets[0].analyses[0].id;

        this.admin.checkAll(currentAnalysisId, selectedAnalysisId).subscribe({
          next: (data) => {
            this.snotifyService.success(
              this.transloco.translate('notification.project.compare.success.message'),
              this.transloco.translate('notification.project.compare.success.title'),
              {
                timeout: 5000,
                showProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                position: 'centerBottom'
              }
            );
            window.location.reload();
          },
          error: (error) => {
            console.log(error);
            this.snotifyService.error(
              this.transloco.translate('notification.project.compare.error.message'),
              this.transloco.translate('notification.project.compare.error.title'),
              {
                timeout: 5000,
                showProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                position: 'centerBottom'
              }
            );
          },
          complete: () => {
            this.isComparing = false;
          }
        });
      }
    });
  }

  // Helper method to check if admin functions can be used
  canUseAdminFunctions(): boolean {
    return this.isAdmin &&
      this.project?.datasets?.length > 0 &&
      this.project.datasets[0].diagnosed_s3_url &&
      this.project.datasets[0].analyses?.length > 0;
  }

  updateProjectType(newType: string): void {
    // First check if the user is an admin
    if (!this.isAdminUser()) {
      // Non-admins shouldn't be able to call this method
      this.snotifyService.error(
        this.transloco.translate('project_detail.type_update.unauthorized'),
        {
          timeout: 3000,
          showProgressBar: true,
          closeOnClick: true
        }
      );
      return;
    }

    if (!this.project || newType === this.project.project_type || this.isUpdatingType) {
      return;
    }

    this.isUpdatingType = true;
    this.p.updateProjectType(this.project.id.toString(), newType)
      .subscribe({
        next: (response) => {
          // Update the project type in the local project object
          if (this.project) {
            this.project.project_type = newType as 'demo' | 'normal' | 'demo_template';

            // Update the project in the project state service
            this.projectState.setProject(this.project);
          }

          this.isUpdatingType = false;

          // Show success notification
          this.snotifyService.success(
            this.transloco.translate('project_detail.type_update.success'),
            {
              timeout: 3000,
              showProgressBar: true,
              closeOnClick: true
            }
          );
        },
        error: (error) => {
          this.isUpdatingType = false;
          console.error('Error updating project type:', error);

          // Show error notification
          this.snotifyService.error(
            this.transloco.translate('project_detail.type_update.error'),
            {
              timeout: 3000,
              showProgressBar: true,
              closeOnClick: true
            }
          );
        }
      });
  }

  selectOption(value: string) {
    this.dropdownOpen = false
    this.updateProjectType(value)
  }

  setActiveTab(tab: ProjectTab): void {
    this.activeTab = tab;

    // Sayfa başlığını güncelle
    this.updatePageTitle();
  }

  /**
   * Sayfa başlığını günceller
   */
  private updatePageTitle(): void {
    if (!this.project) return;

    this.transloco.selectTranslate('tabs.project.' + this.activeTab).subscribe(tabTitle => {
      // "route adı | istabot" formatını uygula
      const title = `${tabTitle} - ${this.project?.name} | istabot`;
      document.title = title;
    });
  }

  toggleFavorite(event: Event) {
    if (!this.project) return;

    event.stopPropagation();

    const previousStatus = this.project.favorite;
    this.project.favorite = !this.project.favorite;

    this.p.toggleProjectFavorite(this.project.id.toString()).subscribe({
      next: () => {
        if (!this.project) return;

        this.snotifyService.success(
          this.transloco.translate(this.project.favorite ? 'notification.project.favorite.added' : 'notification.project.favorite.removed'),
          this.transloco.translate('notification.project.favorite.title'),
          {
            timeout: 2000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );

        // Update project state
        this.projectState.setProject(this.project);
      },
      error: (error) => {
        console.error(error);
        if (this.project) {
          this.project.favorite = previousStatus;
        }

        this.snotifyService.error(
          this.transloco.translate('notification.project.favorite.error'),
          this.transloco.translate('notification.project.favorite.error_title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }

  ngOnInit(): void {
    this.projectState.currentProject$.subscribe(project => {
      if (project) {
        this.project = project;
        this.updateBreadcrumbs();
        // Update project breadcrumb with project name
        this.checkDatasetStatus();
        // Sayfa başlığını güncelle
        this.updatePageTitle();
      } else {
        this.route.params.subscribe((params: Params) => {
          const projectId = params['pid'];
          this.p.getProjectById(projectId).subscribe({
            next: (data) => {
              if (data) {
                this.project = data;
                this.checkDatasetStatus();
                // Sayfa başlığını güncelle
                this.updatePageTitle();
              } else {
                this.router.navigate(['/projects']);
              }
            },
            error: () => {
              this.router.navigate(['/projects']);
            }
          });
        });
      }
    });

    const handleTabFromUrl = () => {
      const urlSegments = this.router.url.split('/');
      let tabFromUrl = urlSegments[urlSegments.length - 1] as ProjectTab;

      if (urlSegments.includes('analysis')) {
        this.activeTab = 'analysis';
        return;
      }

      if (this.isValidTab(tabFromUrl)) {
        this.activeTab = tabFromUrl;
      }

      this.redirectIfNoDataset(); //dataset durumu değiştiğinde yönlendirme yap
    };

    handleTabFromUrl();

    this.router.events.pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        handleTabFromUrl();
        this.updateBreadcrumbs();
        this.updatePageTitle();
      });

    this.route.params.subscribe((params: Params) => {
      this.initializing = true;

      if (!this.project) {
        const projectId = params['pid'];
        this.p.getProjectById(projectId).subscribe({
          next: (data) => {
            if (data) {
              this.project = data;
              this.projectState.setProject(this.project);

              this.checkDatasetStatus();
              setTimeout(() => {
                this.initializing = false;
              }, 1000);
            } else {
              this.router.navigate(['/projects']);
            }
          },
          error: () => {
            this.router.navigate(['/projects']);
          }
        });
      } else {

        this.loadProject();
        setTimeout(() => {
          this.initializing = false;
        }, 1000);
      }
    });
  }

  ngOnDestroy(): void {
    if (history.state) {
      history.state.project = undefined;
    }
    this.projectState.clearProject();

    // Sayfa başlığını sıfırla
    // Bu component'ten ayrılırken başlığı sıfırla
    // NOT: Bu kod çalışıyor, ancak NavigationStart olayı daha erken tetiklendiği için
    // App component'teki kod daha önce çalışıyor. Bu kod yedek olarak burada duruyor.
    document.title = 'istabot';
  }

  private isValidTab(tab: string): tab is ProjectTab {
    return ['overview', 'dataset', 'analysis', 'settings', 'members', 'history'].includes(tab);
  }

  goBack(): void {
    this.location.back();
  }

  loadProject() {
    this.isLoading = true;
    this.p.getProjectById(this.project?.id!).subscribe((data) => {
      this.projectState.setProject(data);
      this.checkDatasetStatus();
      this.isLoading = false;
    }, (error) => {
      console.error('Proje yüklenirken hata oluştu:', error);
      this.isLoading = false;
    });
  }

  /** Dataset var mı, diagnosed edilmiş mi kontrol eden fonksiyon */
  private checkDatasetStatus(): void {
    if (this.project?.datasets && this.project.datasets.length > 0) {
      this.hasDataset = true;
      this.hasDiagnosedDataset = !!this.project.datasets[0].diagnosed_s3_url;
    } else {
      this.hasDataset = false;
      this.hasDiagnosedDataset = false;
    }

    this.redirectIfNoDataset();
    this.cdr.detectChanges();
  }

  private redirectIfNoDataset(): void {
    if (
      this.project && // Proje tanımlıysa
      ((!this.hasDataset || !this.hasDiagnosedDataset) &&
        (this.activeTab === 'dataset' || this.activeTab === 'analysis'))
    ) {
      this.router.navigate([`/projects/${this.project.id}/overview`]).then(() => {
        this.activeTab = 'overview';
        this.cdr.detectChanges();
      });
    }
  }

  private updateBreadcrumbs() {
    if (!this.project) return;

    const urlSegments = this.router.url.split('/');
    const currentTab = urlSegments[urlSegments.length - 1];

    const breadcrumbs: Breadcrumb[] = [
      {
        label: 'navigation.projects',
        link: '/projects',
        icon: 'lucideLayers',
        shortLabel: 'navigation.projects_short'
      },
      {
        label: this.project.name,
        link: `/projects/${this.project.id}`,
        icon: 'lucideFolderOpen',
        shortLabel: this.project.name?.length > 15 ? this.project.name.substring(0, 12) + '...' : this.project.name,
        tooltip: this.project.name
      }
    ];

    if (this.isValidTab(currentTab)) {
      let icon = 'lucideLayoutDashboard'; // Default icon

      // Tab'a göre ikon seç
      switch (currentTab) {
        case 'overview':
          icon = 'lucideLayoutDashboard';
          break;
        case 'dataset':
          icon = 'lucideDatabase';
          break;
        case 'analysis':
          icon = 'lucideChartBar';
          break;
        case 'settings':
          icon = 'lucideSettings';
          break;
        case 'members':
          icon = 'lucideUsers'; // veya 'lucideUserGroup'
          break;
        case 'history':
          icon = 'lucideHistory';
          break;
      }

      breadcrumbs.push({
        label: `navigation.project.${currentTab}`,
        link: `/projects/${this.project.id}/${currentTab}`,
        icon: icon,
        shortLabel: `navigation.project.${currentTab}_short`
      });
    }

    // Breadcrumb'ları ayarla ve localStorage'a kaydet
    // Bu, sayfa yenilendiğinde veya başka bir sayfaya gidip geri döndüğünde breadcrumb'ların korunmasını sağlar
    this.b.setBreadcrumbs(breadcrumbs);

    // Proje sayfasında olduğumuzu belirten bir bayrak ayarla
    // Bu, BreadcrumbService'in NavigationStart olayında kullanılabilir
    localStorage.setItem('currentPage', 'project');
  }

  projectList = [];

  openEditProjectDialog() {
    if (!this.project) return;
    const dialog = this.dialog.open(EditProjectComponent, {
      data: { project: this.project },
    });

    dialog.closed.subscribe((result: any) => {
      if (result && result.saved) {
        // Update the project in the list and current view if name was updated
        if (result.nameUpdated) {
          this.project.name = result.name;
          if (this.projectList && this.projectList.length > 0) {
            const projectInList = this.projectList.find(p => p.id == this.project.id);
            if (projectInList) {
              projectInList.name = result.name;
            }
          }

          // Proje adı değiştiğinde sayfa başlığını güncelle
          this.updatePageTitle();
        }

        // Update the project description if it was updated
        if (result.descriptionUpdated) {
          this.project.description = result.description;
        }
      }
    });
  }

  dropdownOpen = false;

  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  selectType(type: string) {
    this.dropdownOpen = false;
    this.updateProjectType(type);
  }

  getLabel(type: string): string {
    switch (type) {
      case 'normal': return 'Normal Proje';
      case 'demo_template': return 'Demo Şablonu';
      case 'demo': return 'Demo Proje';
      default: return type;
    }
  }

  showInfoDialog = false;

  showProjectInfo(event: Event): void {
    event.stopPropagation();
    this.showInfoDialog = true;
  }

  closeInfoDialog(): void {
    this.showInfoDialog = false;
  }
}
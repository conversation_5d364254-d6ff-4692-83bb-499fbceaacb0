<div class="h-full">
    <div class="flex flex-col h-full p-8 overflow-y-auto bg-white border-neutral-150 rounded-3xl"
        *transloco="let t;read 'project_detail.settings'">
        <h5 class="mb-4 text-xl font-semibold text-gray-800">{{ t('title') }}</h5>

        <!-- Loading Skeleton -->
        <ng-container *ngIf="isPageLoading">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="space-y-2">
                        <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                        <div class="w-48 h-6 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                    <div class="w-24 h-10 bg-gray-200 rounded-3xl animate-pulse"></div>
                </div>

                <div class="flex items-center justify-between mt-8">
                    <div class="space-y-2">
                        <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
                        <div class="w-48 h-6 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                    <div class="w-24 h-10 bg-gray-200 rounded-3xl animate-pulse"></div>
                </div>

                <div class="mt-8">
                    <div class="overflow-hidden border border-gray-200 rounded-3xl">
                        <div class="bg-gray-200 h-14 animate-pulse"></div>
                        <div class="p-4 space-y-4">
                            <div class="w-48 h-6 bg-gray-200 rounded animate-pulse"></div>
                            <div class="w-full h-16 bg-gray-200 rounded animate-pulse"></div>
                            <div class="flex justify-end">
                                <div class="w-32 h-10 bg-gray-200 rounded-3xl animate-pulse"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>

        <!-- Main Content -->
        <ng-container *ngIf="!isPageLoading">

            <!-- Dataset Update Section -->
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="max-w-96">
                        <label class="block text-sm font-medium text-gray-700">{{ t('dataset_name') }}</label>
                        <span class="block mt-2 text-lg truncate">{{ getCurrentDatasetName() || t('no_dataset')
                            }}</span>
                    </div>

                    <ng-container *ngIf="canUpdateDataset(); else datasetLocked">
                        <div class="flex items-center gap-2">
                            <input #fileInput type="file" accept=".xlsx" class="hidden"
                                (change)="handleFileInput($event)">

                            <button *ngIf="!selectedFile" type="button" (click)="fileInput.click()"
                                class="secondary-blue-button">
                                <ng-icon *ngIf="project.datasets?.length" name="lucideUpload"></ng-icon>
                                <ng-icon *ngIf="!project.datasets?.length" name="lucidePlus"></ng-icon>
                                {{ project.datasets?.length ? t('update_dataset') : t('add_dataset') }}
                            </button>

                            <ng-container *ngIf="selectedFile">
                                <span class="text-sm text-gray-600 truncate max-w-40">{{ selectedFile.name }}</span>
                                <button type="button" (click)="updateDataset()" [disabled]="isFileUploading"
                                    class="primary-blue-button">
                                    <ng-icon *ngIf="isFileUploading" name="lucideLoader" class="animate-spin">
                                    </ng-icon>
                                    <ng-icon *ngIf="!isFileUploading" name="lucideSave">
                                    </ng-icon>
                                    {{ t('save_dataset') }}
                                </button>
                                <button type="button" (click)="resetFileInput()" [disabled]="isFileUploading"
                                    class="secondary-blue-button">
                                    <ng-icon name="lucideX"></ng-icon>
                                    {{ t('cancel') }}
                                </button>
                            </ng-container>
                        </div>
                    </ng-container>

                    <ng-template #datasetLocked>
                        <div class="flex items-center gap-2 text-gray-500" matTooltip="{{ t('dataset_locked_desc') }}">
                            <ng-icon name="lucideLock"></ng-icon>
                            {{ t('dataset_locked') }}
                        </div>
                    </ng-template>
                </div>
            </div>

            <!-- Report Settings Section -->
            <div class="mt-4 space-y-4" *ngIf="project?.datasets?.length && project.datasets[0]?.analyses?.length">
                <div class="flex items-center justify-between">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ t('report_settings_title') }}</label>
                        <div class="flex items-center mt-2 space-x-4">
                            <!-- Separator Preview -->
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-600">{{ t('separator') }}:</span>
                                <span class="font-medium">{{ reportFormat === 1 ? t('comma') : t('dot') }}</span>
                            </div>

                            <!-- Precision Preview -->
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-600">{{ t('precision') }}:</span>
                                <span class="font-medium">{{ decimalPlaces }}</span>
                            </div>

                            <!-- Example Preview -->
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-600">{{ t('example') }}:</span>
                                <span class="font-medium">{{ getFormattedPreviewNumber() }}</span>
                            </div>
                        </div>
                    </div>

                    <button type="button" (click)="openReportSettingsModal()" class="secondary-blue-button">
                        <ng-icon name="lucideSettings"></ng-icon>
                        {{ t('edit') }}
                    </button>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="mt-4">
                <div class="overflow-hidden border rounded-3xl border-status-error-200">
                    <div
                        class="flex items-center gap-2 p-4 text-xl font-semibold border-b bg-status-error-100 border-b-status-error-200 text-status-error-500">
                        <ng-icon name="lucideCircleAlert" class="text-xl"></ng-icon>
                        <span>{{ 'shared.danger_zone' | transloco }}</span>
                    </div>
                    <div class="flex flex-col items-end justify-between gap-2 p-4">
                        <div class="w-full">
                            <div class="text-base font-medium">{{ t('delete_project') }}</div>
                            <div class="pr-12 mt-3 text-base text-neutral-600">
                                {{ t('delete_project_warning') }}
                            </div>
                        </div>
                        <button [disabled]="hasReports"
                            matTooltip="{{hasReports ? t('delete_project_reports_warning') : ''}}"
                            (click)="deleteProject()" class="secondary-status-error-button">
                            <ng-icon name="heroTrash" class="text-xl"></ng-icon>
                            <span class="text-sm font-medium">{{ t('delete_project') }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </ng-container>
    </div>
</div>

<!-- Report Settings Modal -->
<div *ngIf="showReportSettingsModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div class="w-full max-w-lg p-6 bg-white rounded-3xl" *transloco="let t;read 'project_detail.settings'">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold text-gray-900">{{ t('report_settings_title') }}</h3>
            <button (click)="closeReportSettingsModal()" class="text-gray-500 hover:text-gray-700">
                <ng-icon name="lucideX" class="w-5 h-5"></ng-icon>
            </button>
        </div>

        <div class="space-y-6">
            <!-- Decimal Separator -->
            <div>
                <label class="block mb-2 text-sm font-medium text-gray-700">{{ t('separator') }}</label>
                <div class="flex space-x-4">
                    <label class="flex items-center">
                        <input type="radio" [(ngModel)]="reportFormat" [value]="1"
                            class="w-4 h-4 border-gray-300 text-brand-green-600 focus:ring-brand-green-500">
                        <span class="ml-2 text-sm text-gray-700">{{ t('comma') }}</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" [(ngModel)]="reportFormat" [value]="2"
                            class="w-4 h-4 border-gray-300 text-brand-green-600 focus:ring-brand-green-500">
                        <span class="ml-2 text-sm text-gray-700">{{ t('dot') }}</span>
                    </label>
                </div>
            </div>

            <!-- Decimal Places -->
            <div>
                <label class="block mb-2 text-sm font-medium text-gray-700">{{ t('precision') }}</label>
                <select [(ngModel)]="decimalPlaces"
                    class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                    <option [value]="0">0</option>
                    <option [value]="1">1</option>
                    <option [value]="2">2</option>
                    <option [value]="3">3</option>
                    <option [value]="4">4</option>
                </select>
            </div>

            <!-- Preview -->
            <div class="p-4 border border-gray-200 rounded-3xl bg-gray-50">
                <div class="mb-2 text-sm font-medium text-gray-700">{{ t('example') }}</div>
                <div class="text-2xl font-bold text-gray-900">{{ getFormattedPreviewNumber() }}</div>
            </div>

            <!-- Buttons -->
            <div class="flex justify-end space-x-3">
                <button (click)="closeReportSettingsModal()" class="secondary-blue-button">
                    <ng-icon name="lucideX"></ng-icon>
                    {{ 'shared.confirm.changes.cancel' | transloco }}
                </button>
                <button (click)="saveReportSettings()" class="primary-blue-button">
                    <ng-icon name="lucideSave"></ng-icon>
                    {{ t('save_changes') }}
                </button>
            </div>
        </div>
    </div>
</div>
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { ProjectService } from '@app/data/services/project.service';
import { ProjectStateService } from '@app/modules/projects/services/project-state.service';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
import { DatasetService } from '@app/data/services/dataset.service';
import { ExcelService } from '@app/data/services/excel.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { Dialog } from '@angular/cdk/dialog';
import { AnalysisService } from '@app/data/services/analysis.service';
import { Project } from '@app/data/models/project.interface';

@Component({
  selector: 'app-project-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {
  project: Project;
  settingsForm: FormGroup;
  loading = false;
  isEditMode = false;
  @ViewChild('fileInput') fileInput: ElementRef;

  selectedFile: File = null;
  isFileUploading = false;
  fileName = '';
  actualFileName = '';
  hasReports = true;
  isPageLoading = true;

  // Report settings
  showReportSettingsModal = false;
  reportFormat = 1; // 1: ',' 2: '.'
  decimalPlaces = 2;
  previewNumber = 1234.5678;
  analysisConfiguration: any = null;

  constructor(
    private bs: BreadcrumbService,
    private projectState: ProjectStateService,
    private p: ProjectService,
    private fb: FormBuilder,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
    private datasetService: DatasetService,
    private excelService: ExcelService,
    private router: Router,
    private dialog: Dialog,
    private analysisService: AnalysisService,
    private route: ActivatedRoute,
  ) {
    this.settingsForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(64)]]
    });
  }

  ngOnInit(): void {
    this.projectState.currentProject$.subscribe(project => {
      if (project) {
        this.project = project;
        this.settingsForm.patchValue({
          name: project.name
        });
        // Check for reports if dataset exists
        if (project.datasets?.[0]?.analyses?.length > 0) {
          this.checkDatasetReports(project.datasets[0].analyses[0].id);
          // Load analysis configuration
          this.loadAnalysisConfiguration();
        }else{
          this.hasReports = false
        }
        this.isPageLoading = false;
      } else {
        this.route.params.subscribe((params: Params) => {

          const projectId = params['pid'];
          this.p.getProjectById(projectId).subscribe({
            next: (data) => {
              if (data) {
                this.project = data;
                // Load analysis configuration
                this.loadAnalysisConfiguration();
              } else {
                this.router.navigate(['/projects']);
              }
            },
            error: () => {
              this.router.navigate(['/projects']);
            }
          });

        });
      }
    });
  }

  /**
   * Loads the analysis configuration from the project
   */
  loadAnalysisConfiguration(): void {
    if (!this.project || !this.project.datasets || this.project.datasets.length === 0) {
      return;
    }

    // Get the first dataset with analyses
    const dataset = this.project.datasets[0];
    if (!dataset.analyses || dataset.analyses.length === 0) {
      return;
    }

    // Get the first analysis with configuration
    const analysis = dataset.analyses[0];
    if (!analysis.analysis_configuration) {
      return;
    }

    this.analysisConfiguration = analysis.analysis_configuration;

    // Initialize report settings from analysis configuration
    if (this.analysisConfiguration.separator) {
      this.reportFormat = this.analysisConfiguration.separator === ',' ? 1 : 2;
    }

    if (this.analysisConfiguration.precision !== undefined && this.analysisConfiguration.precision !== null) {
      this.decimalPlaces = this.analysisConfiguration.precision;
    }
  }



  toggleEditMode() {
    this.isEditMode = !this.isEditMode;
    if (!this.isEditMode) {
      // Reset form when canceling edit
      this.settingsForm.patchValue({
        name: this.project.name
      });
    }
  }

  /**
   * Opens the report settings modal
   */
  openReportSettingsModal(): void {
    // Load current settings
    this.loadAnalysisConfiguration();
    this.showReportSettingsModal = true;
  }

  /**
   * Closes the report settings modal
   */
  closeReportSettingsModal(): void {
    this.showReportSettingsModal = false;
  }

  /**
   * Gets the formatted preview number based on current settings
   */
  getFormattedPreviewNumber(): string {
    const separator = this.reportFormat === 1 ? ',' : '.';
    return this.previewNumber.toFixed(this.decimalPlaces).replace('.', separator);
  }

  /**
   * Saves the report settings
   */
  saveReportSettings(): void {
    if (!this.analysisConfiguration || !this.project.datasets || this.project.datasets.length === 0) {
      return;
    }

    const dataset = this.project.datasets[0];
    if (!dataset.analyses || dataset.analyses.length === 0) {
      return;
    }

    const analysis = dataset.analyses[0];
    if (!analysis.analysis_configuration) {
      return;
    }

    const configId = analysis.analysis_configuration.id;
    const separator = this.reportFormat === 1 ? ',' : '.';

    const payload = {
      separator: separator,
      precision: this.decimalPlaces
    };

    this.analysisService.setAnalysisConfigurations(payload, configId).subscribe({
      next: (response) => {
        // Update the analysis configuration
        this.analysisConfiguration = response;

        // Update the project state with the new analysis configuration
        const updatedProject = { ...this.project };

        // Find the analysis in the project and update its configuration
        if (updatedProject.datasets && updatedProject.datasets.length > 0) {
          const datasetIndex = updatedProject.datasets.findIndex(d => d.id === dataset.id);

          if (datasetIndex !== -1 && updatedProject.datasets[datasetIndex].analyses) {
            const analysisIndex = updatedProject.datasets[datasetIndex].analyses.findIndex(a => a.id === analysis.id);

            if (analysisIndex !== -1) {
              // Update the analysis configuration in the project state
              updatedProject.datasets[datasetIndex].analyses[analysisIndex].analysis_configuration = response;

              // Update the project state
              this.projectState.setProject(updatedProject);
            }
          }
        }

        // Show success message
        this.snotifyService.success(
          this.transloco.translate('notification.analysis.report_conf.success.title')
        );

        // Close the modal
        this.closeReportSettingsModal();
      },
      error: () => {
        // Show error message
        this.snotifyService.error(
          this.transloco.translate('notification.analysis.report_conf.error.title'),
          this.transloco.translate('notification.analysis.report_conf.error.message')
        );
      }
    });
  }

  updateProject() {
    if (this.settingsForm.valid && !this.loading) {
      this.loading = true;
      const updatedName = this.settingsForm.get('name').value;

      this.p.updateProjectName(String(this.project.id), updatedName).subscribe({
        next: (response) => {
          // Update entire project state with new data
          this.projectState.setProject(response);
          this.snotifyService.success(
            this.transloco.translate('project_detail.settings.update_success')
          );
          this.loading = false;
          this.isEditMode = false;
        },
        error: (error) => {
          this.snotifyService.error(
            this.transloco.translate('project_detail.settings.update_error')
          );
          this.loading = false;
        }
      });
    }
  }

  private checkDatasetReports(datasetId: number): void {
    this.analysisService.getAnalysisReportLines(String(datasetId)).subscribe({
      next: (response) => {
        this.hasReports = response && response.length > 0;
      },
      error: () => {
        this.hasReports = false;
      }
    });
  }

  canUpdateDataset(): boolean {
    // Eğer hiç dataset yoksa true döndür
    if (!this.project?.datasets?.length) return true;

    // Eğer diagnosed_s3_url veya rapor varsa güncellemeye izin verme
    return !this.project.datasets[0].diagnosed_s3_url && !this.hasReports;
  }

  getCurrentDatasetName(): string {
    return this.project?.datasets?.[0]?.name || '';
  }

  resetFileInput(): void {
    this.selectedFile = null;
    this.fileInput.nativeElement.value = '';
  }

  async handleFileInput(event: any): Promise<void> {
    const file = event.target.files[0];
    if (!file) return;

    try {
      // Dosya validasyonu
      const fileValidation = this.datasetService.validateFile(file);
      if (!fileValidation.isValid) {
        fileValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }

      // Excel içerik validasyonu
      const data = await this.datasetService.parseExcelDataView(file);
      const contentValidation = this.datasetService.validateDataContent(data);
      if (!contentValidation.isValid) {
        contentValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }

      this.selectedFile = file;
      this.actualFileName = await this.excelService.readDatasetName(file);
      this.fileName = this.updateFileName(file.name);

    } catch (error) {
      this.snotifyService.error(this.transloco.translate('project_detail.settings.file_processing_error'));
      this.resetFileInput();
    }
  }

  async updateDataset(): Promise<void> {
    if (!this.selectedFile || this.isFileUploading) return;

    this.isFileUploading = true;
    try {
      const processedData = await this.excelService.readExcelFile(this.selectedFile);
      const processedBlob = await this.excelService.writeExcelFile(processedData, this.selectedFile.name);

      const uploadData = await this.datasetService.getUploadPresignedUrl(
        this.fileName,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ).toPromise();

      const processedFile = new File([processedBlob], this.fileName, {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      await this.datasetService.uploadToS3(uploadData.url, processedFile).toPromise();

      const s3_url = `https://istabot-development.s3.eu-central-1.amazonaws.com/${this.fileName}`;

      if (this.project.datasets?.length) {
        // Update existing dataset
        this.datasetService.updateDatasetUrl(
          this.project.datasets[0].id.toString(),
          s3_url,
          this.actualFileName
        );

        // Fetch fresh project data to get updated timestamp
        const freshProjectData = await this.p.getProjectById(this.project.id).toPromise();

        // Update local project state with new dataset and updated project data
        const updatedProject = {
          ...freshProjectData,
          datasets: [{
            ...this.project.datasets[0],
            s3_url,
            name: this.actualFileName
          }]
        };
        this.projectState.setProject(updatedProject);
      } else {
        // Add new dataset
        const response = await this.datasetService.addDatasetToProject(
          s3_url,
          this.actualFileName,
          String(this.project.id)
        ).toPromise();

        // Fetch fresh project data to get updated timestamp
        const freshProjectData = await this.p.getProjectById(this.project.id).toPromise();

        // Update project with new dataset and updated project data
        const updatedProject = {
          ...freshProjectData,
          datasets: [{
            id: response.id,
            name: response.name,
            s3_url: response.s3_url,
            project_id: response.project_id,
            variables_json: response.variables_json,
            diagnosed_s3_url: response.diagnosed_s3_url,
            created_at: response.created_at,
            updated_at: response.updated_at,
            analyses: [],
            variables: []
          }]
        };
        this.projectState.setProject(updatedProject);
      }

      // Show success message
      this.snotifyService.success(
        this.transloco.translate(
          this.project.datasets?.length
            ? 'project_detail.settings.dataset_update_success'
            : 'project_detail.settings.dataset_add_success'
        )
      );
      this.resetFileInput();

    } catch (error) {
      this.snotifyService.error(
        this.transloco.translate(
          this.project.datasets?.length
            ? 'project_detail.settings.dataset_update_error'
            : 'project_detail.settings.dataset_add_error'
        )
      );
    } finally {
      this.isFileUploading = false;
    }
  }

  async deleteProject(): Promise<void> {
    const dialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: this.transloco.translate('shared.confirm.project_detail.settings.delete_project.title'),
        content: this.transloco.translate('shared.confirm.project_detail.settings.delete_project.content'),
        confirm: this.transloco.translate('shared.confirm.project_detail.settings.delete_project.confirm'),
        cancel: this.transloco.translate('shared.confirm.project_detail.settings.delete_project.cancel'),
      }
    });
    dialog.closed.subscribe((result) => {
      if (result) {
        this.p.deleteProject(this.project.id).subscribe({
          next: () => {
            this.snotifyService.success(
              this.transloco.translate('project_detail.settings.delete_success')
            );
            this.router.navigate(['/projects']);
          },
          error: () => {
            this.snotifyService.error
              (this.transloco.translate('project_detail.settings.delete_error'));
          }
        }
        );
      }
    }
    );
  }

  private updateFileName(name: string): string {
    const extension = name.substring(name.lastIndexOf('.'));
    const fileNameWithProjectId = name.replace(extension, '') + new Date().toISOString();
    return this.slugify(fileNameWithProjectId) + extension;
  }

  private slugify(text: string): string {
    const turkishMap = {
      'ş': 's', 'Ş': 'S', 'ç': 'c', 'Ç': 'C',
      'ğ': 'g', 'Ğ': 'G', 'ü': 'u', 'Ü': 'U',
      'ö': 'o', 'Ö': 'O', 'ı': 'i', 'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-');
  }
}

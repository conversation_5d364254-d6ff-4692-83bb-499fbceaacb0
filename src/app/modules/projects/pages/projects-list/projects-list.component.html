<div class="flex flex-col h-full " *transloco="let t; read 'project_list'">
    <!-- Header -->
    <div class="flex-none p-4 mb-6 bg-white border border-neutral-150 rounded-3xl ">
        <!-- Header -->
        <div class="flex items-center justify-between ">
            <div>
                <div class="flex items-center gap-2 p-4">
                    <h1 class="text-3xl font-bold text-neutral-950">
                        {{t('title')}}
                    </h1>
                    <!-- Loading state for total count -->
                    <ng-container *ngIf="isLoading">
                        <div class="w-20 bg-gray-200 rounded-full h-7 animate-pulse"></div>
                    </ng-container>
                    <!-- Actual total count -->
                    <span *ngIf="!isLoading"
                        class="px-2 py-1.5 text-sm font-medium text-brand-blue-500 bg-brand-blue-200 rounded-full">
                        {{projects.length}} {{t('total')}}
                    </span>
                </div>
            </div>
            <button (click)="showCreateProject()" class="inline-flex items-center gap-2 font-medium rounded-full"
                [ngClass]="'primary-blue-button'">
                <ng-icon name="lucidePlus" class="text-xl"></ng-icon>
                {{t('create_project')}}
            </button>
        </div>
        <p class="pl-4 mb-6 text-sm text-gray-500">
            {{t('description')}}
        </p>
        <!-- Search and Filter -->
        <div class="flex gap-4 ">
            <div class="relative flex-1">
                <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="filterProjects()"
                    [placeholder]="t('search_placeholder')"
                    class="w-full py-3 pl-10 pr-4 transition-colors border-2 border-gray-200 rounded-full focus:border-brand-blue-700 focus:ring-2 focus:ring-brand-blue-100" />
                <div class="absolute inset-y-0 flex items-center pointer-events-none left-4">
                    <ng-icon name="lucideSearch" class="text-xl text-gray-400"></ng-icon>
                </div>
                <button *ngIf="searchTerm" (click)="clearSearch()"
                    class="absolute inset-y-0 flex items-center justify-center h-full gap-1 text-gray-500 right-4 hover:text-gray-700 focus:outline-none">
                    <ng-icon name="lucideX" class="text-xl"></ng-icon>
                    <span class="text-sm">{{t('clear_search')}}</span>
                </button>
            </div>
            <!-- Removed the favorites filter button from here -->
            <div class="relative">
                <button (click)="toggleFilterDropdown()" class="secondary-blue-button">
                    <ng-icon name="lucideFilter" class="text-2xl text-brand-blue-500"></ng-icon>
                    <span class="text-brand-blue-500">
                        {{t('filters.title')}}
                    </span>
                    <span *ngIf="getActiveFilterCount() > 0"
                        class="absolute flex items-center justify-center w-5 h-5 text-xs font-bold text-white rounded-full bg-brand-blue-600 -top-1 -right-1">
                        {{getActiveFilterCount()}}
                    </span>
                </button>
                <!-- Filter Dropdown -->
                <div *ngIf="isFilterDropdownOpen" [@fadeIn]
                    class="absolute right-0 z-20 mt-2 bg-white border shadow-lg rounded-3xl" appClickOutside
                    (clickOutside)="toggleFilterDropdown()">
                    <div class="p-4 space-y-4">
                        <!-- Favorites Filter - Added Here -->
                        <div class="pb-4 border-b border-gray-200">
                            <button (click)="toggleFavoritesFilter()"
                                class="flex items-center justify-center w-full gap-2 px-4 py-2 transition-all border rounded-full"
                                [ngClass]="{'bg-yellow-100 border-yellow-300 text-yellow-800': showOnlyFavorites, 
                                   'bg-white border-gray-300 text-gray-700 hover:border-yellow-300': !showOnlyFavorites}">
                                <ng-icon [name]="'lucideStar'" [class.text-yellow-500]="showOnlyFavorites"
                                    [class.text-gray-400]="!showOnlyFavorites" class="text-xl"
                                    [ngClass]="{'fill-current': showOnlyFavorites}"></ng-icon>
                                {{showOnlyFavorites ? t('show_all') : t('show_favorites')}}
                            </button>
                        </div>

                        <!-- Sort options -->
                        <div>
                            <label
                                class="block mb-2 text-sm font-medium text-gray-700">{{t('filters.sorting.label')}}</label>
                            <select [(ngModel)]="filters.sortBy" (ngModelChange)="filterProjects()"
                                class="w-full px-3 py-2 text-sm border rounded-3xl focus:ring-2 focus:ring-blue-500">
                                <option value="position">{{t('filters.sorting.options.position')}}</option>
                                <option value="date_desc">{{t('filters.sorting.options.date_desc')}}</option>
                                <option value="date_asc">{{t('filters.sorting.options.date_asc')}}</option>
                                <option value="name_asc">{{t('filters.sorting.options.name_asc')}}</option>
                                <option value="name_desc">{{t('filters.sorting.options.name_desc')}}</option>
                            </select>
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label
                                class="block mb-2 text-sm font-medium text-gray-700">{{t('filters.status.label')}}</label>
                            <select [(ngModel)]="filters.status" (ngModelChange)="filterProjects()"
                                class="w-full px-3 py-2 text-sm border rounded-3xl focus:ring-2 focus:ring-blue-500">
                                <option value="all">{{t('filters.status.options.all')}}</option>
                                <option value="new">{{t('filters.status.options.new')}}</option>
                                <option value="ready">{{t('filters.status.options.ready')}}</option>
                                <option value="needs_review">{{t('filters.status.options.needs_review')}}
                                </option>
                                <option value="needs_dataset">{{t('filters.status.options.needs_dataset')}}
                                </option>
                            </select>
                        </div>

                        <!-- Dataset Filter -->
                        <div>
                            <label
                                class="block mb-2 text-sm font-medium text-gray-700">{{t('filters.dataset.label')}}</label>
                            <select [(ngModel)]="filters.hasDataset" (ngModelChange)="filterProjects()"
                                class="w-full px-3 py-2 text-sm border rounded-3xl focus:ring-2 focus:ring-blue-500">
                                <option value="all">{{t('filters.dataset.options.all')}}</option>
                                <option value="with">{{t('filters.dataset.options.with')}}</option>
                                <option value="without">{{t('filters.dataset.options.without')}}</option>
                            </select>
                        </div>

                        <!-- Date Range Filter -->
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                {{t('filters.date_range.label')}}
                            </label>
                            <mat-form-field appearance="outline" class="w-full">
                                <mat-date-range-input [rangePicker]="picker">
                                    <input matStartDate [(ngModel)]="filters.dateRange.start"
                                        (dateChange)="filterProjects()" [placeholder]="t('filters.date_range.start')">
                                    <input matEndDate [(ngModel)]="filters.dateRange.end"
                                        (dateChange)="filterProjects()" [placeholder]="t('filters.date_range.end')">
                                </mat-date-range-input>
                                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                <mat-date-range-picker #picker></mat-date-range-picker>
                            </mat-form-field>
                        </div>

                        <!-- Reset Button -->
                        <button (click)="resetFilters()"
                            class="flex items-center justify-center w-full gap-2 px-4 py-2 mt-4 text-sm border text-neutral-950 rounded-3xl hover:bg-gray-50">
                            <ng-icon name="lucideX" class="w-4 h-4"></ng-icon>
                            {{t('filters.reset')}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects List -->
    <div class="flex-1 overflow-y-auto">
        <div class="space-y-4">
            <!-- Skeleton Loader -->
            <div *ngIf="isLoading" class="space-y-4">
                <div *ngFor="let item of skeletonItems"
                    class="flex items-center justify-between w-full p-4 bg-white border rounded-3xl animate-pulse">
                    <div class="flex items-center justify-between w-full">
                        <div class="flex items-center gap-4">
                            <!-- Icon placeholder -->
                            <div class="w-12 h-12 bg-gray-200 rounded-xl"></div>
                            <div>
                                <!-- Title placeholder -->
                                <div class="w-48 h-5 mb-2 bg-gray-200 rounded-md"></div>
                                <!-- Info line placeholder -->
                                <div class="flex items-center gap-2 mt-2">
                                    <div class="w-20 h-3 bg-gray-200 rounded"></div>
                                    <div class="w-32 h-3 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex">
                        <!-- Action buttons placeholder -->
                        <div class="w-16 h-10 bg-gray-200 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>

            <!-- Actual Projects List -->
            <ng-container *ngIf="!isLoading">
                <!-- Drag and drop container -->
                <div cdkDropList (cdkDropListDropped)="onProjectListDrop($event)"
                    [cdkDropListDisabled]="showOnlyFavorites || filters.sortBy !== 'position'" class="space-y-4">
                    <div *ngFor="let project of filteredProjects; let i = index" cdkDrag [cdkDragData]="project"
                        [cdkDragDisabled]="showOnlyFavorites || filters.sortBy !== 'position'" class="relative group">

                        <!-- Main Card - Adjusted for drag -->
                        <button (click)="navigateToDetail(project)"
                            class="flex items-center w-full p-4 transition-all bg-white border border-neutral-150 rounded-3xl hover:border-brand-blue-500">
                            <!-- Main content - Adjusted padding when drag handle is present -->
                            <div class="flex items-center justify-between w-full cursor-pointer"
                                [ngClass]="{'': !showOnlyFavorites && filters.sortBy === 'position'}"
                                >
                                <div class="flex items-center gap-4">
                                    <div
                                        class="relative flex items-center justify-center w-12 h-12 rounded-xl bg-brand-blue-50 group">
                                        <!-- Folder icon: visible normally, hidden on hover -->
                                        <ng-icon name="lucideFolderOpen"
                                            class="text-2xl transition-opacity duration-200 opacity-100 text-brand-blue-500 group-hover:opacity-0"></ng-icon>

                                        <!-- Drag handle: hidden normally, shown on hover -->
                                        <div cdkDragHandle *ngIf="!showOnlyFavorites && filters.sortBy === 'position'"
                                            class="absolute flex items-center justify-center p-2 transition-opacity duration-200 opacity-0 cursor-move left-1 group-hover:opacity-100">
                                            <div class="flex items-center justify-center size-full">
                                                <ng-icon name="lucideMove"
                                                    class="text-2xl text-gray-500 group-hover:text-brand-blue-500"></ng-icon>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <div class="flex items-center gap-2">
                                            <h3 class="font-semibold text-gray-900 truncate max-w-144">
                                                {{project.name}}
                                            </h3>
                                            <span
                                                class="inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-sm"
                                                [ngClass]="getProjectStatus(project).class">
                                                <ng-icon *ngIf="getProjectStatus(project).text === 'status.new_project'"
                                                    name="lucideSparkles"></ng-icon>
                                                <ng-icon
                                                    *ngIf="getProjectStatus(project).text === 'status.needs_dataset'"
                                                    name="lucidePackagePlus"></ng-icon>
                                                <ng-icon
                                                    *ngIf="getProjectStatus(project).text === 'status.ready_for_analysis'"
                                                    name="lucideCircleCheckBig"></ng-icon>
                                                <ng-icon
                                                    *ngIf="getProjectStatus(project).text === 'status.needs_review'"
                                                    name="lucideCircleAlert"></ng-icon>
                                                {{t(getProjectStatus(project).text)}}
                                            </span>
                                            <span *ngIf="project.project_type && project.project_type !== 'normal'"
                                                class="inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-sm"
                                                [ngClass]="{'bg-blue-100 text-blue-800': project.project_type === 'demo', 
                                                'bg-purple-100 text-purple-800': project.project_type === 'demo_template'}">
                                                {{project.project_type === 'demo_template' ? 'Demo Şablonu' : 'Demo Proje'}}
                                            </span>

                                        </div>
                                        <!-- Projelerin bilgi satırına eklenecek kredi gösterimi -->
                                        <p class="flex items-center gap-1 text-sm text-neutral-600">
                                            <ng-icon name="lucideClock4" class=""></ng-icon>
                                            <span class="mr-1">
                                                {{formatDate(project.created_at)}}
                                            </span>
                                            <ng-container *ngIf="project.datasets?.length > 0" class="">
                                                <ng-icon name="lucideDatabase" class=""></ng-icon>
                                                <span class="truncate max-w-40">
                                                    {{project.datasets[0].name}}
                                                </span>
                                            </ng-container>
                                            <!-- Kredi kullanım bilgisi -->
                                            <ng-container *ngIf="getTotalCreditsUsed(project) > 0">
                                                <img src="assets/icons/istacoin.svg" alt="" class="ml-2 size-4">
                                                <span class="truncate">
                                                    {{getTotalCreditsUsed(project) | number:'1.0-0'}}
                                                    {{t('credit_used')}}
                                                </span>
                                            </ng-container>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <button
                                        class="flex-shrink-0 p-1.5 transition-colors rounded-full hover:bg-gray-100 flex items-center justify-center size-8"
                                        (click)="toggleFavorite($event, project.id)"
                                        [title]="project.favorite ? t('remove_favorite') : t('add_favorite')">
                                        <ng-icon [name]="project.favorite ? 'tablerStarFilled' : 'tablerStarOff'"
                                            class="text-xl" [class.text-yellow-500]="project.favorite"
                                            [class.text-gray-400]="!project.favorite"
                                            [ngClass]="{'fill-current': project.favorite}"></ng-icon>
                                    </button>
                                    <button
                                        class="flex items-center justify-center w-8 h-8 text-gray-400 transition-colors rounded-full group-hover:text-brand-blue-500 ">
                                        <ng-icon name="lucideChevronRight" class="text-2xl"></ng-icon>
                                    </button>
                                </div>
                            </div>
                        </button>

                        <!-- Preview when dragging -->
                        <div *cdkDragPreview
                            class="flex items-center justify-between p-4 bg-white border shadow-lg w-fit border-neutral-150 rounded-3xl">
                            <div class="flex items-center gap-4">
                                <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-brand-blue-50">
                                    <ng-icon name="lucideFolderOpen" class="text-2xl text-brand-blue-600"></ng-icon>
                                </div>
                                <span class="font-medium">{{ project.name }}</span>
                            </div>
                        </div>

                        <!-- Placeholder when dragging -->
                        <div *cdkDragPlaceholder
                            class="w-full p-4 border-2 border-blue-300 border-dashed bg-blue-50 rounded-3xl">
                            <div class="flex items-center gap-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-xl"></div>
                                <div class="w-1/3 h-6 bg-blue-100 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>
    </div>
</div>
ng-scrollbar {
  --scrollbar-size: 5px;
  --scrollbar-thumb-color: #e4800f;
  --scrollbar-hover-size: 8px;
  --scrollbar-track-color: #da8f203d;
  --scrollbar-border-radius: 12px;
  --scrollbar-thumb-transition: height ease-out 150ms, width ease-out 150ms;
  --scrollbar-track-transition: height ease-out 1500ms, width ease-out 150ms;

  height: 600px;
}
// projects-list-NEW.component.scss
ng-scrollbar {
  --scrollbar-size: 5px;
  --scrollbar-thumb-color: #e4800f;
  --scrollbar-hover-size: 8px;
  --scrollbar-track-color: #da8f203d;
  --scrollbar-border-radius: 12px;
  --scrollbar-thumb-transition: height ease-out 150ms, width ease-out 150ms;
  --scrollbar-track-transition: height ease-out 1500ms, width ease-out 150ms;

  height: 600px;
}

// Drag and drop styles
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 1rem;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .cdk-drag {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .cdk-drag-preview {
  opacity: 0.95;
}
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProjectsComponent } from './pages/projects/projects.component';
import { ProjectsListComponent } from './pages/projects-list/projects-list.component';
import { title } from 'process';
import { TitleResolver } from '@app/shared/pipes/pageTitle.resolver';
import { AnalysisComponent } from './pages/project-detail/analysis/analysis.component';
import { DatasetComponent } from './pages/project-detail/dataset/dataset.component';
import { OverviewComponent } from './pages/project-detail/overview/overview.component';
import { SettingsComponent } from './pages/project-detail/settings/settings.component';
import { ProjectDetailComponent } from './pages/project-detail/project-detail.component';
import { AnalysisHistoryComponent } from './pages/project-detail/analysis/analysis-history/analysis-history.component';
import { MembersComponent } from './pages/project-detail/members/members.component';

const routes: Routes = [
  {
    path: '',
    component: ProjectsListComponent,
    resolve: {
      title: TitleResolver
    }
  },
  {
    path: ':pid',
    component: ProjectDetailComponent,
    resolve: {
      title: TitleResolver
    },
    children: [
      {
        path: '',
        redirectTo: 'overview',
        pathMatch: 'full'
      },
      {
        path: 'overview',
        component: OverviewComponent,
        resolve: {
          title: TitleResolver
        }
      },
      {
        path: 'analysis',
        component: AnalysisComponent,
        resolve: {
          title: TitleResolver
        },
        children: [
          {
            path: '',
            component: AnalysisComponent
          },
          {
            path: 'history',
            component: AnalysisHistoryComponent
          }
        ]
      },
      {
        path: 'dataset',
        component: DatasetComponent,
        resolve: {
          title: TitleResolver
        }
      },
      {
        path: 'settings',
        component: SettingsComponent,
        resolve: {
          title: TitleResolver
        }
      },
      {
        path: 'members',
        component: MembersComponent,
        resolve: {
          title: TitleResolver
        }
      },
    ]
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [TitleResolver],
})
export class ProjectsRoutingModule { }
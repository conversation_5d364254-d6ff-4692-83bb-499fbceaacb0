import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { ProjectsComponent } from './pages/projects/projects.component';
import { ProjectsRoutingModule } from './projects-routing.module';
import { ProjectsListComponent } from './pages/projects-list/projects-list.component';
import { SharedModule } from '@app/shared/shared.module';
import { CreateProjectComponent } from './dialogs/create-project/create-project.component';
import { SelectProjectComponent } from './dialogs/select-project/select-project.component';
import { AnalysisComponent } from './pages/project-detail/analysis/analysis.component';
import { DatasetComponent } from './pages/project-detail/dataset/dataset.component';
import { ProjectDetailComponent } from './pages/project-detail/project-detail.component';
import { OverviewComponent } from './pages/project-detail/overview/overview.component';
import { SettingsComponent } from './pages/project-detail/settings/settings.component';
import { AnalysisHistoryComponent } from './pages/project-detail/analysis/analysis-history/analysis-history.component';
import { FormatValueLabelsPipe } from '@app/shared/pipes/formatValueLabels.pipe';
import { CompareReportsDialogComponent } from '../reports/components/compare-reports-dialog.component';
import { AnalysisDetailsComponent } from './pages/project-detail/analysis/dialogs/analysis-details/analysis-details.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { EditProjectComponent } from './dialogs/edit-project/edit-project.component';
import { MembersComponent } from './pages/project-detail/members/members.component';
import { AddMemberComponent } from './dialogs/add-member/add-member.component';
import { MatDialogModule } from '@angular/material/dialog';
import { DialogModule } from '@angular/cdk/dialog';

@NgModule({
  declarations: [
    ProjectsComponent,
    ProjectsListComponent,
    CreateProjectComponent,
    AnalysisComponent,
    DatasetComponent,
    OverviewComponent,
    SettingsComponent,
    ProjectDetailComponent,
    AnalysisHistoryComponent,
    FormatValueLabelsPipe,
    CompareReportsDialogComponent,
    AnalysisDetailsComponent,
    SelectProjectComponent,
    EditProjectComponent,
    MembersComponent,
    AddMemberComponent
  ],
  imports: [
    CommonModule,
    ProjectsRoutingModule,
    SharedModule,
    DragDropModule,
    ReactiveFormsModule,
    MatDialogModule,
    DialogModule
  ],
  providers: []
})
export class ProjectsModule { }
import { Injectable } from '@angular/core';
import { Project } from '@app/data/models/project.interface';
import { BehaviorSubject } from 'rxjs';
import { ProjectService } from '@app/data/services/project.service';

@Injectable({
  providedIn: 'root'
})
export class ProjectStateService {
  private projectSubject = new BehaviorSubject<Project | null>(null);
  currentProject$ = this.projectSubject.asObservable();

  constructor(private projectService: ProjectService) { }

  setProject(project: Project) {
    this.projectSubject.next(project);
  }

  clearProject() {
    this.projectSubject.next(null);
  }

  updateProject(updatedProject: Partial<Project>) {
    const currentProject = this.projectSubject.getValue();
    if (currentProject) {
      this.projectSubject.next({
        ...currentProject,
        ...updatedProject
      });
    }
  }

  getCurrentProject(): Project | null {
    return this.projectSubject.getValue();
  }

  /**
   * Mevcut projeyi backend'den yeniden yükler
   * Ownership transfer sonrası güncel bilgileri almak için kullanılır
   */
  refreshCurrentProject(): void {
    const currentProject = this.getCurrentProject();
    if (currentProject?.id) {
      this.projectService.getProjectById(currentProject.id).subscribe({
        next: (project) => {
          this.setProject(project);
        },
        error: (error) => {
          console.error('Error refreshing current project:', error);
        }
      });
    }
  }
}
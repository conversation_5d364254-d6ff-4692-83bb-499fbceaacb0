import { Component, Inject } from '@angular/core';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { ReportLine } from '@app/data/models/report_line.interface';
import { AnalysisService } from '@app/data/services/analysis.service';

interface DiffLine {
  type: 'added' | 'removed' | 'unchanged';
  lineNumber: number;
  content: string;
}

interface TableDiff {
  name: string;
  oldContent: {
    table: string[];
    interpretation?: string[];
  };
  newContent: {
    table: string[];
    interpretation?: string[];
  };
  matchingLines: {
    table: boolean[];
    interpretation?: boolean[];
  };
}

@Component({
  selector: 'app-compare-reports-dialog',
  styles: [`
    .diff-line { font-family: monospace; white-space: pre; padding: 4px 10px; }
    .diff-added { background-color: #e6ffec; }
    .diff-removed { background-color: #ffebe9; }
    .diff-unchanged { background-color: white; }
    .line-number { 
      color: #6e7781;
      padding: 0 8px;
      text-align: right;
      width: 1%;
      min-width: 50px;
      font-family: monospace;
    }
    .split-view {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1px;
      background: #e5e7eb;
    }
    .split-view > div {
      background: white;
    }
    .line-content {
      padding: 4px 10px;
      font-family: monospace;
      white-space: pre;
    }
    .line-removed { background-color: #ffd7d7; }
    .line-added { background-color: #d7ffd7; }
    .line-number {
      user-select: none;
      color: #6e7781;
      text-align: right;
      padding: 4px 8px;
      border-right: 1px solid #e5e7eb;
      min-width: 40px;
    }
    .reports-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      max-height: calc(100vh - 200px);
      overflow: auto;
    }

    .report-content {
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      overflow: hidden;
    }

    .report-header {
      padding: 1rem;
      background-color: #f9fafb;
      border-bottom: 1px solid #e5e7eb;
      font-weight: 500;
    }

    .report-body {
      padding: 1rem;
    }

    :host ::ng-deep table {
      width: 100%;
      border-collapse: collapse;
    }

    :host ::ng-deep th, :host ::ng-deep td {
      border: 1px solid #e5e7eb;
      padding: 0.5rem;
    }

    :host ::ng-deep tr:nth-child(even) {
      background-color: #f9fafb;
    }

    .interpretation {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #e5e7eb;
    }

    .cell-different {
      position: relative;
      background-color: #fff8dc !important;
    }

    .old-value {
      color: #b91c1c;
      text-decoration: line-through;
    }

    .new-value {
      color: #15803d;
      margin-left: 8px;
    }

    .change-arrow {
      color: #6b7280;
      margin: 0 4px;
    }
  `],
  template: `
    <div class="p-6 max-w-[90vw] bg-white" *transloco="let t">
      <div class="mb-6">
        <h2 class="text-2xl font-bold mb-2">{{t('comparing_reports')}}</h2>
        <div class="flex items-center gap-4 text-sm text-gray-600">
          <div>{{reports[0].title_with_code}}</div>
          <ng-icon name="lucideArrowRight"></ng-icon>
          <div>{{reports[1].title_with_code}}</div>
        </div>
      </div>

      <div class="reports-grid">
        <!-- Left Report -->
        <div *ngFor="let report of reportContents; let reportIndex = index" class="report-content">
          <div class="report-header">
            {{reports[reportIndex].title_with_code}}
          </div>
          <div class="report-body">
            <div *ngFor="let content of report.content; let contentIndex = index" class="mb-8">
              <div class="overflow-x-auto">
                <table class="w-full">
                  <tbody>
                    <!-- Header Row -->
                    <tr *ngIf="getTableHeaders(content.tr.table) as headers">
                      <th *ngFor="let header of headers" class="font-bold bg-gray-50 p-2 border">
                        {{header}}
                      </th>
                    </tr>
                    
                    <!-- Data Rows -->
                    <ng-container *ngFor="let row of getAllTableRows(contentIndex); let rowIndex = index">
                      <tr *ngIf="shouldShowRow(reportIndex, contentIndex, rowIndex)"
                          [class.bg-red-50]="isDeletedRow(reportIndex, contentIndex, rowIndex)"
                          [class.bg-green-50]="isAddedRow(reportIndex, contentIndex, rowIndex)">
                        <td *ngFor="let cell of row; let colIndex = index" 
                            [class.font-bold]="colIndex === 0"
                            class="border p-2">
                          {{cell}}
                          <span *ngIf="isDifferentCell(reportIndex, contentIndex, rowIndex, colIndex)" 
                                [class.text-red-600]="reportIndex === 0"
                                [class.text-green-600]="reportIndex === 1"
                                class="text-xs ml-2">
                            {{reportIndex === 0 ? '(Silindi)' : '(Eklendi)'}}
                          </span>
                        </td>
                      </tr>
                    </ng-container>
                  </tbody>
                </table>
              </div>
              <div *ngIf="content.tr.interpret" 
                   [class.cell-different]="isInterpretationDifferent(contentIndex)"
                   class="interpretation text-sm text-gray-600">
                {{content.tr.interpret}}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-end gap-2">
        <button (click)="close()" class="secondary-button">
          {{t('close')}}
        </button>
      </div>
    </div>
  `
})
export class CompareReportsDialogComponent {
  reports: ReportLine[];
  reportContents = [];
  tableDiffs: TableDiff[] = [];
  private tableComparisons: {[key: string]: boolean} = {};
  private interpretationComparisons: {[key: string]: boolean} = {};

  constructor(
    @Inject(DIALOG_DATA) data: any,
    private dialogRef: DialogRef,
    private analysisService: AnalysisService
  ) {
    this.reports = data.reports;

    this.loadReportContents();
  }

  async loadReportContents() {
    try {
      const contents = await Promise.all(
        this.reports.map(report => 
          this.analysisService.getReportContentById(
            String(report.credit_usage?.creditable_id)
          ).toPromise()
        )
      );
      
      this.reportContents = contents.map(content => content.reports[0]);
      
      // Process table HTML for better display
      this.reportContents.forEach(report => {
        report.content.forEach(content => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(content.tr.table, 'text/html');
          const table = doc.querySelector('table');
          if (table) {
            // Add classes for styling
            table.classList.add('w-full', 'border-collapse');
            Array.from(table.querySelectorAll('tr')).forEach((tr, index) => {
              if (index % 2 === 0) {
                tr.classList.add('bg-gray-50');
              }
            });
          }
          content.tr.table = table.outerHTML;
        });
      });

    } catch (error) {
      console.error('Error loading report contents:', error);
    }
  }

  private cleanTableHtml(html: string): string[] {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const table = doc.querySelector('table');
    const rows: string[] = [];
    
    if (table) {
      // Get headers
      const headers = Array.from(table.querySelectorAll('th'))
        .map(th => th.textContent?.trim() || '')
        .join(' | ');
      if (headers) {
        rows.push(headers);
        rows.push('-'.repeat(headers.length)); // Separator line
      }

      // Get data rows
      Array.from(table.querySelectorAll('tr')).forEach(tr => {
        const cells = Array.from(tr.querySelectorAll('td'))
          .map(td => td.textContent?.trim() || '')
          .filter(text => text.length > 0);
          
        if (cells.length > 0) {
          rows.push(cells.join(' | '));
        }
      });
    }
    
    return rows;
  }

  private compareReports() {
    const [oldReport, newReport] = this.reportContents;
    
    // Get all unique table indices
    const allTableIndices = new Set([
      ...oldReport.content.map((_, i) => i),
      ...newReport.content.map((_, i) => i)
    ]);

    // Compare each table and its interpretation
    Array.from(allTableIndices).sort().forEach((index) => {
      const oldContent = oldReport.content[index];
      const newContent = newReport.content[index];

      // Initialize empty content if table doesn't exist in one report
      const oldTableRows = oldContent ? this.cleanTableHtml(oldContent.tr.table) : [];
      const newTableRows = newContent ? this.cleanTableHtml(newContent.tr.table) : [];

      // Get interpretations
      const oldInterp = oldContent?.tr.interpret ? 
        oldContent.tr.interpret.split('\n').filter(line => line.trim()) : [];
      const newInterp = newContent?.tr.interpret ? 
        newContent.tr.interpret.split('\n').filter(line => line.trim()) : [];

      // Compare lines
      const tableMaxLines = Math.max(oldTableRows.length, newTableRows.length);
      const interpMaxLines = Math.max(oldInterp.length, newInterp.length);

      const tableMatching = Array(tableMaxLines).fill(false).map((_, i) => 
        oldTableRows[i] === newTableRows[i]
      );

      const interpMatching = Array(interpMaxLines).fill(false).map((_, i) => 
        oldInterp[i] === newInterp[i]
      );

      this.tableDiffs.push({
        name: `Tablo ${index + 1}`,
        oldContent: {
          table: oldTableRows,
          interpretation: oldInterp
        },
        newContent: {
          table: newTableRows,
          interpretation: newInterp
        },
        matchingLines: {
          table: tableMatching,
          interpretation: interpMatching
        }
      });
    });
  }

  private generateDiff(oldLines: string[], newLines: string[]): DiffLine[] {
    const diffLines: DiffLine[] = [];
    let lineNumber = 1;

    const maxLen = Math.max(oldLines.length, newLines.length);
    
    for (let i = 0; i < maxLen; i++) {
      const oldLine = oldLines[i];
      const newLine = newLines[i];

      if (oldLine === newLine) {
        diffLines.push({
          type: 'unchanged',
          lineNumber,
          content: oldLine
        });
      } else {
        if (oldLine !== undefined) {
          diffLines.push({
            type: 'removed',
            lineNumber,
            content: oldLine
          });
        }
        if (newLine !== undefined) {
          diffLines.push({
            type: 'added',
            lineNumber,
            content: newLine
          });
        }
      }
      lineNumber++;
    }

    return diffLines;
  }

  getTableRows(tableHtml: string): string[][] {
    const parser = new DOMParser();
    const doc = parser.parseFromString(tableHtml, 'text/html');
    const rows = Array.from(doc.querySelectorAll('tr'));
    
    return rows.map(row => 
      Array.from(row.querySelectorAll('td, th'))
        .map(cell => cell.textContent?.trim() || '')
    );
  }

  getTableHeaders(tableHtml: string): string[] {
    const parser = new DOMParser();
    const doc = parser.parseFromString(tableHtml, 'text/html');
    const headerRow = doc.querySelector('tr');
    return headerRow ? Array.from(headerRow.querySelectorAll('th')).map(th => th.textContent?.trim() || '') : [];
  }

  getAllTableRows(contentIndex: number): string[][] {
    const oldContent = this.reportContents[0]?.content[contentIndex];
    const newContent = this.reportContents[1]?.content[contentIndex];
    
    // Her iki rapordan da tüm satırları al
    const oldRows = oldContent ? this.getTableRows(oldContent.tr.table) : [];
    const newRows = newContent ? this.getTableRows(newContent.tr.table) : [];
    
    // Tüm benzersiz satırları sakla, hangi raporda olduğunu da tut
    const allRows = new Map<string, {
      row: string[],
      inOld: boolean,
      inNew: boolean
    }>();
    
    oldRows.forEach(row => {
      const key = row.join('|');
      allRows.set(key, {
        row,
        inOld: true,
        inNew: false
      });
    });
    
    newRows.forEach(row => {
      const key = row.join('|');
      const existing = allRows.get(key);
      if (existing) {
        existing.inNew = true;
      } else {
        allRows.set(key, {
          row,
          inOld: false,
          inNew: true
        });
      }
    });
    
    // Satırları orijinal sırayla döndür
    return [...oldRows, ...newRows.filter(row => 
      !oldRows.some(oldRow => oldRow.join('|') === row.join('|')))
    ];
  }

  shouldShowRow(reportIndex: number, contentIndex: number, rowIndex: number): boolean {
    const allRows = this.getAllTableRows(contentIndex);
    const row = allRows[rowIndex];
    if (!row) return false;

    const rowKey = row.join('|');
    const oldContent = this.reportContents[0]?.content[contentIndex];
    const newContent = this.reportContents[1]?.content[contentIndex];
    
    const oldRows = oldContent ? this.getTableRows(oldContent.tr.table) : [];
    const newRows = newContent ? this.getTableRows(newContent.tr.table) : [];

    const existsInOld = oldRows.some(r => r.join('|') === rowKey);
    const existsInNew = newRows.some(r => r.join('|') === rowKey);

    // Satır hangi raporda varsa o raporda göster
    if (reportIndex === 0) {
      return existsInOld || this.isDeletedRow(reportIndex, contentIndex, rowIndex);
    } else {
      return existsInNew || this.isAddedRow(reportIndex, contentIndex, rowIndex);
    }
  }

  isDeletedRow(reportIndex: number, contentIndex: number, rowIndex: number): boolean {
    const allRows = this.getAllTableRows(contentIndex);
    const row = allRows[rowIndex];
    if (!row) return false;

    const rowKey = row.join('|');
    const oldContent = this.reportContents[0]?.content[contentIndex];
    const newContent = this.reportContents[1]?.content[contentIndex];
    
    if (!oldContent || !newContent) return false;

    const oldRows = this.getTableRows(oldContent.tr.table);
    const newRows = this.getTableRows(newContent.tr.table);

    // Satır eski raporda var ama yeni raporda yoksa silinmiştir
    return oldRows.some(r => r.join('|') === rowKey) && 
           !newRows.some(r => r.join('|') === rowKey);
  }

  isAddedRow(reportIndex: number, contentIndex: number, rowIndex: number): boolean {
    const allRows = this.getAllTableRows(contentIndex);
    const row = allRows[rowIndex];
    if (!row) return false;

    const rowKey = row.join('|');
    const oldContent = this.reportContents[0]?.content[contentIndex];
    const newContent = this.reportContents[1]?.content[contentIndex];
    
    if (!oldContent || !newContent) return false;

    const oldRows = this.getTableRows(oldContent.tr.table);
    const newRows = this.getTableRows(newContent.tr.table);

    // Satır yeni raporda var ama eski raporda yoksa eklenmiştir
    return !oldRows.some(r => r.join('|') === rowKey) && 
           newRows.some(r => r.join('|') === rowKey);
  }

  isDifferentCell(reportIndex: number, contentIndex: number, rowIndex: number, colIndex: number): boolean {
    const oldContent = this.reportContents[0]?.content[contentIndex];
    const newContent = this.reportContents[1]?.content[contentIndex];
    
    if (!oldContent || !newContent) return false;

    const oldRows = this.getTableRows(oldContent.tr.table);
    const newRows = this.getTableRows(newContent.tr.table);

    // Satırın hem eski hem yeni rapordaki karşılıklarını bul
    const currentRow = this.getAllTableRows(contentIndex)[rowIndex];
    if (!currentRow) return false;

    const rowKey = currentRow.join('|');
    const matchingOldRow = oldRows.find(r => r.join('|') === rowKey);
    const matchingNewRow = newRows.find(r => r.join('|') === rowKey);

    // Eğer satır diğer raporda yoksa veya hücre değeri farklıysa
    if (reportIndex === 0) {
      return !matchingNewRow || (matchingOldRow && matchingNewRow && matchingOldRow[colIndex] !== matchingNewRow[colIndex]);
    } else {
      return !matchingOldRow || (matchingOldRow && matchingNewRow && matchingOldRow[colIndex] !== matchingNewRow[colIndex]);
    }
  }

  isInterpretationDifferent(contentIndex: number): boolean {
    const key = `interp_${contentIndex}`;
    if (this.interpretationComparisons[key] !== undefined) {
      return this.interpretationComparisons[key];
    }

    const oldInterp = this.reportContents[0]?.content[contentIndex]?.tr.interpret;
    const newInterp = this.reportContents[1]?.content[contentIndex]?.tr.interpret;

    const isDifferent = oldInterp !== newInterp;
    this.interpretationComparisons[key] = isDifferent;
    
    return isDifferent;
  }

  close() {
    this.dialogRef.close();
  }
}

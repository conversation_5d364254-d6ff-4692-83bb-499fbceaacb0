import { Component, OnInit } from '@angular/core';
import { DialogRef } from '@angular/cdk/dialog';
import { ProjectService } from '@app/data/services/project.service';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { AnalysisRequirementsService } from '@app/data/services/analysis-requirements.service';

// Interface for analysis types
export interface AnalysisTypes {
  type: string;
  icon: string;
  videoUrl: string;
  isNew?: boolean;
  isAvailable?: boolean;
  requirementDescription?: string;
}

@Component({
  selector: 'app-create-report-dialog',
  template: `
<div class="p-6 bg-white rounded-3xl w-[700px]" *transloco="let t; read: 'create_report'">
  <h2 class="mb-6 text-2xl font-bold text-gray-900">{{ t('title') }}</h2>

  <!-- Step 1: Initial Selection Options -->
  <div *ngIf="currentStep === 'initial'" class="grid grid-cols-2 gap-4">
    <button (click)="selectOption('new')"
      class="flex flex-col items-center justify-center p-6 text-center transition-all border-2 rounded-2xl hover:border-brand-green-500 hover:bg-brand-green-50"
      [class.border-brand-green-500]="selectedOption === 'new'" [class.bg-brand-green-50]="selectedOption === 'new'">
      <ng-icon name="lucideCirclePlus" class="mb-3 text-4xl text-brand-green-500"></ng-icon>
      <h3 class="mb-2 font-medium">{{ t('new_analysis') }}</h3>
      <p class="text-sm text-gray-500">{{ t('new_analysis_desc') }}</p>
    </button>

    <button (click)="selectOption('existing')"
      class="flex flex-col items-center justify-center p-6 text-center transition-all border-2 rounded-2xl hover:border-brand-green-500 hover:bg-brand-green-50"
      [class.border-brand-green-500]="selectedOption === 'existing'"
      [class.bg-brand-green-50]="selectedOption === 'existing'">
      <ng-icon name="lucideCopy" class="mb-3 text-4xl text-brand-green-500"></ng-icon>
      <h3 class="mb-2 font-medium">{{ t('from_existing') }}</h3>
      <p class="text-sm text-gray-500">{{ t('from_existing_desc') }}</p>
    </button>
  </div>

  <!-- Step 2: Project Selection (shown only when new analysis is selected) -->
  <div *ngIf="currentStep === 'project'" class="mt-6">
    <div class="mb-4">
      <button (click)="goBack()" class="flex items-center gap-1 text-sm text-gray-500">
        <ng-icon name="lucideArrowLeft"></ng-icon>
        {{ t('common.back') }}
      </button>
    </div>

    <label class="block mb-2 text-sm font-medium text-gray-700">
      {{ t('select_project') }}
    </label>

    <div *ngIf="!hasValidProjects()" class="p-4 mb-3 text-sm text-amber-800 bg-amber-50 rounded-xl">
      {{ t('no_valid_projects') }}
    </div>

    <select [(ngModel)]="selectedProject"
      class="w-full px-3 py-2 border rounded-3xl focus:ring-2 focus:ring-brand-green-500">
      <option [ngValue]="null" disabled>
        {{ projects.length === 0 ? t('no_projects_available') : t('select_project_placeholder') }}
      </option>
      <option *ngFor="let project of projects" [value]="project.id" [disabled]="!project.isValid">
        {{project.name}}{{ !project.isValid ? ' (' + t('invalid_dataset') + ')' : '' }}
      </option>
    </select>
  </div>

  <!-- Step 3: Analysis Type Selection -->
  <div *ngIf="currentStep === 'analysis'" class="mt-6">
    <div class="mb-4">
      <button (click)="goBack()" class="flex items-center gap-1 text-sm text-gray-500">
        <ng-icon name="lucideArrowLeft"></ng-icon>
        {{ t('common.back') }}
      </button>
    </div>

    <label class="block mb-2 text-sm font-medium text-gray-700">
      {{ t('select_analysis_type') }}
    </label>

    <div *ngIf="isAnalysisLoading" class="flex justify-center py-4">
      <div class="w-8 h-8 border-4 border-t-brand-green-500 rounded-full animate-spin"></div>
    </div>

    <div *ngIf="!isAnalysisLoading" class="grid gap-3 md:grid-cols-2">
      <div *ngFor="let analysis of analyses" (click)="selectAnalysis(analysis.type)"
        class="relative transition-all border-2 rounded-xl cursor-pointer p-3"
        [ngClass]="analysis.isAvailable ? 'border-brand-green-300 hover:bg-brand-green-50  hover:shadow-md cursor-pointer' : 'border-gray-300 opacity-60'"
        [class.bg-brand-green-50]="selectedAnalysis === analysis.type">

        <span *ngIf="analysis.isNew"
          class="absolute top-0 right-0 flex items-center gap-1 px-2 py-1 text-xs font-semibold text-white bg-brand-green-500 rounded-bl-xl rounded-tr-xl">
          {{ t('new') }}
          <ng-icon name="lucideSparkles" class="w-3 h-3"></ng-icon>
        </span>

        <div class="flex items-center gap-2">
          <div class="flex items-center justify-center w-6 h-6 rounded-full bg-brand-green-100">
            <img [src]="'assets/icons/' + analysis.icon + '.svg'" class="w-4 h-4">
          </div>
          <h3 class="text-sm font-medium text-gray-800">
            {{'analyses_type_list.' + analysis.type | transloco}}
          </h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="flex justify-end gap-3 mt-6">
    <button (click)="close()" class="px-4 py-2 text-sm font-medium text-gray-700 border rounded-full hover:bg-gray-50">
      {{ t('common.cancel') }}
    </button>
    <button (click)="continue()" [disabled]="!canContinue()" class="primary-green-button">
      {{ t('common.continue') }}
    </button>
  </div>
</div>
  `
})
export class CreateReportDialogComponent implements OnInit {
  selectedOption: 'new' | 'existing' = null;
  selectedProject: number = null;
  selectedAnalysis: string = null;
  currentStep: 'initial' | 'project' | 'analysis' = 'initial';
  projects = [];
  isAnalysisLoading = false;

  analyses: AnalysisTypes[] = [
    { type: 'comean', icon: 'comean', videoUrl: 'https://www.youtube.com/embed/1Z9v6Jjv9Z0?si=6TlZDztMXrhh18nz', isNew: true },
    { type: 'descriptive', icon: 'descriptive', videoUrl: 'https://www.youtube.com/embed/SrpBGvnDmqo?si=9Ms-b4tHWF49kGbz' },
    { type: 'single', icon: 'single', videoUrl: 'https://www.youtube.com/embed/19GiPTLOcUg?si=KzIRls2HKjVF7F_R' },
    { type: 'multi', icon: 'multi', videoUrl: 'https://www.youtube.com/embed/aANHllnl6vQ?si=GPyzxJ830cp1ZwZU' },
    { type: 'dependent', icon: 'dependent', videoUrl: 'https://www.youtube.com/embed/Zi-oUSgBxcI?si=6TlZDztMXrhh18nz' },
    { type: 'correlation', icon: 'correlation', videoUrl: 'https://www.youtube.com/embed/gn3lfdLzx-o?si=wszUguCBzdjRAs8n' },
    { type: 'chisq', icon: 'chisq', videoUrl: 'https://www.youtube.com/embed/NgdXEQnzxx4?si=r6tltKFWuR1ysrCV' },
    { type: 'logistic_cox', icon: 'logistic_cox', videoUrl: 'NOURL', isNew: true },
    { type: 'survival', icon: 'survival', videoUrl: 'NOURL', isNew: true },
    { type: 'roc', icon: 'roc', videoUrl: 'NOURL', isNew: true },
    { type: 'linear', icon: 'linear', videoUrl: 'NOURL', isNew: true },
  ];

  constructor(
    private dialogRef: DialogRef<any>,
    private projectService: ProjectService,
    private diagnoseHelper: DiagnoseHelperService,
    private analysisReqs: AnalysisRequirementsService
  ) { }

  ngOnInit() {
    this.loadProjects();
  }

  loadProjects() {
    this.projectService.getProjectAll().subscribe(projects => {
      // Add isValid property to each project instead of filtering
      this.projects = projects.map(project => ({
        ...project,
        isValid: this.isValidProject(project)
      }));
    });
  }

  // Helper method to check if project is valid
  isValidProject(project: any): boolean {
    return project.datasets?.length > 0 &&
      project.datasets[0]?.diagnosed_s3_url;
  }

  // Helper to check if there are any valid projects
  hasValidProjects(): boolean {
    return this.projects.some(project => project.isValid);
  }

  selectOption(option: 'new' | 'existing') {
    this.selectedOption = option;
    if (option === 'new') {
      this.currentStep = 'project';
    } else {
      this.continue();
    }
  }

  selectAnalysis(type: string) {
    const analysis = this.analyses.find(a => a.type === type);
    if (analysis?.isAvailable) {
      this.selectedAnalysis = type;
    }
  }

  loadAnalysisOptions(projectId: number) {
    const project = this.projects.find(p => p.id == projectId);
    if (!project || !project.datasets?.[0]) return;

    this.isAnalysisLoading = true;
    this.diagnoseHelper.diagnoseAnalysis(String(project.datasets[0].id)).subscribe({
      next: (availabilityMap) => {
        this.analyses = this.analyses.map(analysis => ({
          ...analysis,
          isAvailable: availabilityMap[analysis.type],
          requirementDescription: this.analysisReqs.getRequirementDescription(analysis.type)
        }));
        this.isAnalysisLoading = false;
      },
      error: (error) => {
        console.error('Analysis diagnosis failed:', error);
        this.isAnalysisLoading = false;
      }
    });
  }

  canContinue(): boolean {
    if (this.currentStep === 'initial') {
      return !!this.selectedOption;
    } else if (this.currentStep === 'project') {
      const selectedProject = this.projects.find(p => p.id == this.selectedProject);
      return !!selectedProject && selectedProject.isValid;
    } else if (this.currentStep === 'analysis') {
      return !!this.selectedAnalysis;
    }
    return false;
  }

  goBack() {
    if (this.currentStep === 'project') {
      this.currentStep = 'initial';
    } else if (this.currentStep === 'analysis') {
      this.currentStep = 'project';
    }
  }

  continue() {
    if (this.currentStep === 'initial') {
      // If 'existing' is selected, close the dialog with the selected option
      if (this.selectedOption === 'existing') {
        this.dialogRef.close({
          action: this.selectedOption
        });
      } else {
        this.currentStep = 'project';
      }
    } else if (this.currentStep === 'project') {
      // Move to the analysis selection step and load available analyses
      this.currentStep = 'analysis';
      this.loadAnalysisOptions(this.selectedProject);
    } else if (this.currentStep === 'analysis') {
      // Close the dialog with all selected data
      const project = this.projects.find(p => p.id == this.selectedProject);
      this.dialogRef.close({
        type: this.selectedAnalysis,
        project: project,
        analysisType: this.selectedAnalysis,
        action: this.selectedOption
      });
    }
  }

  close() {
    this.dialogRef.close();
  }
}

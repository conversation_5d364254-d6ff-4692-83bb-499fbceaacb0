<div class="flex justify-end w-full p-6" *transloco="let t; read: 'analyses.edit_report'">
    <div class="flex flex-col h-full gap-2 p-5 overflow-auto bg-white shadow-lg min-w-108 rounded-3xl">
        <div class="relative flex items-start justify-between flex-none w-full gap-3 p-2">
            <p class="flex items-center gap-2 text-2xl font-semibold text-center text-blue-950">
                <ng-icon name="lucidePencilLine" class="text-4xl text-green-600"></ng-icon>
                {{t('title')}}
            </p>
            <button class="z-30 scale-125 text-blue-950" (click)="checkChanges()">
                <ng-icon class="text-xl transition-all" name="matCloseRound"></ng-icon>
            </button>
        </div>
        <form [formGroup]="form" (submit)="updateReport()">
            <div class="pb-4 mb-2 border-b">
                <label for="reportLabel" class="block font-medium text-blue-950">{{t('label')}}</label>
                <input required type="text" id="reportLabel" name="reportLabel" formControlName="label"
                    class="block w-full mt-1 border-gray-300 shadow-sm rounded-3xl disabled:bg-zinc-100 focus:ring-green-500 focus:border-green-500 sm:text-sm">
                <div *ngIf="submitted" class="text-xs text-red-500">
                    <div *ngIf="form.get('label').hasError('required')">{{t('label_is_required')}}</div>
                </div>
            </div>
            <div class="pb-4 mb-2">
                <div class="flex items-center justify-between">
                    <label for="reportDescription" class="block font-medium text-blue-950">{{t('description')}} <span
                            class="text-xs text-gray-500">({{t('optional')}})</span></label>
                    <span class="text-xs text-gray-500"
                        [ngClass]="{'text-red-500': form.get('description').value?.length > 256}">
                        {{form.get('description').value?.length || 0}}/256
                    </span>
                </div>
                <textarea id="reportDescription" name="reportDescription" formControlName="description" rows="4"
                    maxlength="256"
                    class="block w-full mt-1 border-gray-300 shadow-sm rounded-3xl disabled:bg-zinc-100 focus:ring-green-500 focus:border-green-500 sm:text-sm"
                    placeholder="{{t('description_placeholder')}}"></textarea>
                <div *ngIf="form.get('description').errors?.maxlength" class="mt-1 text-xs text-red-500">
                    {{t('description_max_length')}}
                </div>
            </div>
            <div class="flex justify-end">
                <button type="submit" name="update" [disabled]="form.invalid || isLoading"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent shadow-sm rounded-3xl disabled:hover:bg-green-500 disabled:opacity-40 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <ng-icon *ngIf="isLoading" class="mr-2 animate-spin" name="heroArrowPath"></ng-icon>
                    {{t('update')}}
                </button>
            </div>
        </form>
    </div>
</div>
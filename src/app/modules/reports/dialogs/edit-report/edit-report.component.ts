import { animate, state, style, transition, trigger } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AnalysisService } from '@app/data/services/analysis.service';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';

@Component({
  selector: 'app-edit-report',
  templateUrl: './edit-report.component.html',
  styleUrls: ['./edit-report.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class EditReportComponent implements OnInit {
  animationState = 'in';
  isLoading = false;
  submitted = false;
  form: FormGroup;
  originalLabel: string;
  originalDescription: string;

  constructor(
    @Inject(DIALOG_DATA) public data: { report: any },
    public dialogRef: DialogRef<any>,
    private analysisService: AnalysisService,
    private snotifyService: SnotifyService,
    private transloco: TranslocoService,
    private dialog: Dialog
  ) { }

  ngOnInit(): void {
    this.originalLabel = this.data.report.label;
    this.originalDescription = this.data.report.description || '';

    this.form = new FormGroup({
      label: new FormControl(this.originalLabel, [Validators.required]),
      description: new FormControl(this.originalDescription, [Validators.maxLength(256)])
    });

    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => {
      this.checkChanges();
    });
  }

  checkChanges(): void {
    const labelChanged = this.form.value.label !== this.originalLabel;
    const descriptionChanged = this.form.value.description !== this.originalDescription;

    if (labelChanged || descriptionChanged) {
      const dialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('shared.confirm.create_project.title'),
          content: this.transloco.translate('shared.confirm.create_project.content'),
          confirm: this.transloco.translate('shared.confirm.create_project.confirm'),
          cancel: this.transloco.translate('shared.confirm.create_project.cancel')
        }
      });

      dialog.closed.subscribe((result) => {
        if (result) {
          this.closeModal();
        }
      });
    } else {
      this.closeModal();
    }
  }

  closeModal(result?: any): void {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(result), 300);
  }

  updateReport(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    if (this.isLoading) {
      return;
    }

    this.isLoading = true;

    // Track if we need to update title, description, or both
    const titleChanged = this.form.value.label !== this.originalLabel;
    const descriptionChanged = this.form.value.description !== this.originalDescription;

    // If nothing changed, just close the dialog
    if (!titleChanged && !descriptionChanged) {
      this.isLoading = false;
      this.closeModal({ saved: false });
      return;
    }

    // Counter to track completed operations
    let completedOperations = 0;
    const totalOperations = titleChanged && descriptionChanged ? 2 : 1;
    let hasError = false;

    // Function to check if all operations are complete
    const checkCompletion = () => {
      if (completedOperations === totalOperations && !hasError) {
        this.snotifyService.success(
          this.transloco.translate('notification.analysis.report_update.success.title'),
          {
            position: 'centerBottom',
            timeout: 1500,
            pauseOnHover: false,
            titleMaxLength: 50,
          }
        );

        this.closeModal({
          saved: true,
          label: this.form.value.label,
          description: this.form.value.description,
          id: this.data.report.id,
          line_id: this.data.report.line_id
        });
      }
    };

    // Update title if changed
    if (titleChanged) {
      const titlePayload = {
        title: this.form.value.label
      };

      this.analysisService.updateReportLine(this.data.report.line_id, titlePayload).subscribe({
        next: () => {
          completedOperations++;
          checkCompletion();
        },
        error: (error) => {
          console.error(error);
          hasError = true;
          this.snotifyService.error(
            this.transloco.translate('notification.analysis.report_update.error.title'),
            {
              position: 'centerBottom',
              timeout: 1500,
              pauseOnHover: false,
              titleMaxLength: 50,
            }
          );
          this.isLoading = false;
        }
      });
    }

    // Update description if changed
    if (descriptionChanged) {
      const descriptionPayload = {
        description: this.form.value.description || ''
      };

      this.analysisService.updateReportDescription(this.data.report.id, descriptionPayload).subscribe({
        next: () => {
          completedOperations++;
          checkCompletion();
        },
        error: (error) => {
          console.error(error);
          hasError = true;
          this.snotifyService.error(
            this.transloco.translate('notification.analysis.report_update.error.title'),
            {
              position: 'centerBottom',
              timeout: 1500,
              pauseOnHover: false,
              titleMaxLength: 50,
            }
          );
          this.isLoading = false;
        }
      });
    }
  }
}

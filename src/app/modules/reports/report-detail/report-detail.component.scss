:host {
  ::ng-deep {
    // Highlight the active row
    .highlight-row {
      background-color: rgba(34, 197, 94, 0.1) !important;
      transition: background-color 0.3s ease;
    }

    .highlight-section {
      background-color: rgba(34, 197, 94, 0.05) !important;
      border-radius: 0.5rem;
      transition: all 0.3s ease;
    }

    // Add data attributes to table rows
    table tbody tr {
      position: relative;
      scroll-margin-top: 100px;
    }

    .text-justify, .methods-section {
      scroll-margin-top: 10rem;
    }

    .methods-section {
      scroll-margin-top: 10rem;

    }

    table {
      tr {
        transition: background-color 0.3s ease;
      }
    }

    .nav-button {
      @apply flex items-center w-full gap-2 px-3 py-1.5 text-sm transition-colors rounded-lg;

      &:hover {
        @apply bg-green-50;
      }

      &.active {
        @apply bg-green-100 text-green-600;
      }
    }
  }
}

// Fullscreen mode styles
.report-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background-color: white;
  overflow-y: auto;
  padding: 1rem;
  transition: all 0.3s ease-in-out;

  // Add a subtle shadow to indicate it's in a modal-like state
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

  // Ensure the content is properly spaced
  .report-content {
    max-width: 1400px;
    margin: 0 auto;
  }
}

.highlight-row {
  background-color: rgba(34, 197, 94, 0.1);
  transition: background-color 0.3s ease;
}

.highlight-section {
  background-color: rgba(34, 197, 94, 0.1);
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

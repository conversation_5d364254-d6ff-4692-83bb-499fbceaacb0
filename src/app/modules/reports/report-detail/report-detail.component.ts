import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild, ElementRef, Input, Output, EventEmitter } from '@angular/core';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import { ReportLine } from '@app/data/models/report_line.interface';
import { ReportHelperService } from '@app/data/helper/report.helper.service';
import { Location } from '@angular/common';
import { AnalysisService } from '@app/data/services/analysis.service';
import { SnotifyService } from 'ng-alt-snotify';
import { TranslocoService } from '@ngneat/transloco';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { Dialog } from '@angular/cdk/dialog';
import { ProjectService } from '@app/data/services/project.service';
import { Breadcrumb } from '@app/data/models/breadcrumb.interface';
import { CreateAnalysisComponent } from '@app/shared/components/create-analysis/create-analysis.component';
import { EditReportComponent } from '../dialogs/edit-report/edit-report.component';
import { PaymentComponent } from '@app/modules/payment/dialogs/payment/payment.component';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Overlay } from '@angular/cdk/overlay';
import { Subject, filter, takeUntil } from 'rxjs';


// Highlight için interface'i güncelleyelim
export interface HighlightInfo {
  word: string;
  tableIndex: number;
  positions: Array<{ start: number, end: number }>;
}

@Component({
  selector: 'app-report-detail',
  templateUrl: './report-detail.component.html',
  styleUrls: ['./report-detail.component.scss'],
  animations: [
    trigger('fadeIn', [
      state('void', style({ opacity: '0', transform: 'translateY(10%)' })),
      state('*', style({ opacity: '*' })),
      transition('void <=> *', [animate('0.3s ease-in')]),
    ]),
    trigger('opacity', [
      state('void', style({ opacity: '0' })),
      state('*', style({ opacity: '*' })),
      transition('void <=> *', [animate('0.5s ease-in-out')]),
    ]),
  ]
})
export class ReportDetailComponent implements OnInit, OnDestroy {

  isMobile = false;
  selectedReportName = '';
  isEdited = false;
  private destroy$ = new Subject<void>();
  isFullscreen = false;

  report: ReportLine;
  projectName: string;
  projectId: string;
  reportContent: any;
  currentHighlight: string | null = null;
  isLoading: boolean = true;
  showCreditTooltip: boolean = false;


  selectedVariable: {
    text: string;
    tableIndex: number;
  } | null = null;

  selectedHighlight: HighlightInfo | null = null;

  constructor(
    private b: BreadcrumbService,
    private router: Router,
    private rh: ReportHelperService,
    private location: Location,
    private route: ActivatedRoute,
    private a: AnalysisService,
    private snotifyService: SnotifyService,
    private translocoService: TranslocoService,
    private dialog: Dialog,
    private p: ProjectService,
  ) {
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras.state as {
      report: ReportLine,
      projectName: string,
      projectId: string,
      fromHistory: boolean
    };

    if (state) {
      this.report = state.report;
      this.projectName = state.projectName;
      this.projectId = state.projectId;
    }
  }

  ngOnInit() {
    // Check for mobile view
    this.checkIfMobile();
    window.addEventListener('resize', this.checkIfMobile.bind(this));

    // Mevcut sayfa bilgisini güncelle
    localStorage.setItem('currentPage', 'report-detail');

    // Subscribe to language changes from TranslocoService
    this.translocoService.langChanges$
      .pipe(takeUntil(this.destroy$))
      .subscribe(lang => {
        if (this.reportContent) {
          this.reportContent.lang = lang;
        }
      });

    // Router olaylarını dinle - NavigationEnd olaylarını yakalayarak rapor içeriğini yenile
    // Sadece URL değiştiğinde ve route.params'ın yakalamadığı durumlarda çalışır
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe((_event: NavigationEnd) => {
      // Rapor detay sayfasında olduğumuzu doğrula
      if (this.router.url.includes('/reports/')) {
        // Sadece breadcrumb'ları güncelle, rapor içeriğini yeniden yükleme
        // Çünkü route.params zaten rapor içeriğini yükleyecek
        if (this.report) {
          this.updateBreadcrumbs();
        }
      }
    });

    // Subscribe to route parameter changes
    this.route.params
      .pipe(takeUntil(this.destroy$))
      .subscribe((params: Params) => {
        const reportId = params['id'];

        // Check if we already have the report data from navigation state
        if (this.report && this.report.id === Number(reportId)) {
          // We already have the report data, just load the content
          this.isLoading = true;
          this.loadReport();
        } else {
          // We don't have the report data, need to get it directly from the API
          // Instead of getting all report lines, we'll use the report ID directly
          this.isLoading = true;

          // Get report content directly using the ID from URL
          // This will load the report content which contains all the necessary report data
          this.a.getReportContentById(reportId).subscribe({
            next: (data) => {
              if (data && data.reports && data.reports[0]) {
                // Create a report object with the necessary data
                // Use the report data from the API response
                const reportData = data.reports[0];
                const reportLine = reportData.lines[0];

                // Create a complete report object to avoid type errors
                this.report = {
                  id: Number(reportId),
                  title_with_code: reportLine.title,
                  title: reportLine.title,
                  credit_usage: {
                    id: reportLine.credit_usage?.id || 0,
                    creditable_id: Number(reportId),
                    creditable_type: reportLine.credit_usage?.creditable_type || 'ReportContent',
                    used_credit: reportLine.credit_usage?.used_credit || 0,
                    user_id: reportLine.credit_usage?.user_id || 0,
                    project_id: reportLine.credit_usage?.project_id || 0,
                    project_name: reportLine.credit_usage?.project_name || '',
                    created_at: reportLine.credit_usage?.created_at || new Date().toISOString()
                  }
                } as ReportLine;

                this.projectName = this.report.credit_usage?.project_name || '';
                this.projectId = String(this.report.credit_usage?.project_id || '');

                // Now load the full report content
                this.loadReport();
              } else {
                console.error('ReportDetailComponent: Report not found, navigating to reports list');
                this.router.navigate(['/reports']);
              }
            },
            error: (error) => {
              console.error('ReportDetailComponent: Error fetching report content:', error);
              this.isLoading = false;
              this.router.navigate(['/reports']);
            }
          });
        }
      });
  }

  ngOnDestroy() {
    window.removeEventListener('resize', this.checkIfMobile.bind(this));
    this.destroy$.next();
    this.destroy$.complete();
  }

  private checkIfMobile() {
    this.isMobile = window.innerWidth < 768;
  }

  private updateBreadcrumbs() {


    if (!this.report) {
      return;
    }

    const breadcrumbs: Breadcrumb[] = [
      {
        label: 'navigation.projects',
        link: '/projects',
        icon: 'lucideLayers',
        shortLabel: 'navigation.projects_short'
      },
      {
        label: this.projectName,
        link: `/projects/${this.projectId}`,
        icon: 'lucideFolderOpen',
        shortLabel: this.projectName?.length > 15 ? this.projectName.substring(0, 12) + '...' : this.projectName,
        tooltip: this.projectName
      },
      {
        label: 'navigation.project.history',
        link: `/projects/${this.projectId}/analysis/history`,
        icon: 'lucideHistory',
        shortLabel: 'navigation.project.history_short'
      },
      {
        label: this.report.title_with_code,
        link: `/reports/${this.report.id}`,
        icon: 'lucideFileText',
        tooltip: this.report.title_with_code
      }
    ];

    // Mevcut sayfa bilgisini güncelle
    localStorage.setItem('currentPage', 'report-detail');

    // Breadcrumb'ları ayarla
    this.b.setBreadcrumbs(breadcrumbs);
  }

  loadReport() {
    this.isLoading = true;

    // Clear existing report content to avoid showing stale data
    this.reportContent = null;

    // Fetch the latest report content from the server
    this.rh.getReportContentById(String(this.report.credit_usage.creditable_id)).subscribe({
      next: (reportContent) => {
        this.processReportContent(reportContent);
      },
      error: (error) => {
        console.error('ReportDetailComponent: Error loading report content:', error);
        this.isLoading = false;

        // Show error notification to user
        this.snotifyService.error(
          this.translocoService.translate('notification.report.load.error.message'),
          this.translocoService.translate('notification.report.load.error.title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }

  // Rapor içeriğini işleyen yardımcı metod
private processReportContent(reportContent: any) {
  this.reportContent = reportContent.reportContent;

  // Get the current active language from TranslocoService
  const currentLang = localStorage.getItem('activeLang') || this.translocoService.getActiveLang();

  // Set the language in reportContent to match the app's current language
  this.reportContent.lang = currentLang;

  // Analiz tipini frontend'de kullanılan formata dönüştür
  this.mapAnalysisType();

  // Set default values for report content if not present
  if (!this.reportContent.showOptions) this.reportContent.showOptions = false;

  this.updateBreadcrumbs();
  this.isLoading = false;

  // Sayfa başlığını dinamik olarak ayarla
  const pageTitle = `${this.report.title_with_code} | istabot`;
  document.title = pageTitle;
}

/**
 * Backend'den gelen analiz tiplerini frontend için uygun formata dönüştürür
 * ve ikon yollarını ayarlar
 */
private mapAnalysisType() {
  if (!this.reportContent) return;

  // Orijinal kodu saklayın (debugging ve hata ayıklama için)
  this.reportContent.originalCode = this.reportContent.code;

  // Backend'den gelen kodu frontend'de kullanılan koda dönüştür
  // Bu dönüşüm çok önemli, çünkü çeviri anahtarlarının doğru şekilde eşleşmesi gerekir
  const codeMapping = {
    'binary': 'logistic_cox',
    'regression': 'linear',
    // Diğer eşleştirmeler buraya eklenebilir
  };

  // Eğer ilgili kod için bir eşleştirme varsa, dönüştür
  if (this.reportContent.code && codeMapping[this.reportContent.code]) {
    console.log(`Mapping analysis code from ${this.reportContent.code} to ${codeMapping[this.reportContent.code]}`);
    this.reportContent.code = codeMapping[this.reportContent.code];
  }

  // İkon yollarını ayarla
  const iconBasePath = 'assets/icons/';
  const iconMap = {
    'descriptive': 'descriptive.svg',
    'single': 'single.svg',
    'multi': 'multi.svg',
    'dependent': 'dependent.svg',
    'correlation': 'correlation.svg',
    'chisq': 'chisq.svg',
    'comean': 'comean.svg',
    'logistic_cox': 'logistic_cox.svg',
    'survival': 'survival.svg',
    'roc': 'roc.svg',
    'linear': 'linear.svg',
    // Diğer analiz tipleri için ikon eşleştirmeleri
  };
  
  // Analiz koduna göre ikon yolunu ayarla
  if (this.reportContent.code && iconMap[this.reportContent.code]) {
    this.reportContent.icon = `${iconBasePath}${iconMap[this.reportContent.code]}`;
  } else {
    // Kod eşleşmiyorsa varsayılan ikon
    this.reportContent.icon = `${iconBasePath}default.svg`;
  }
}

// İkon ve analiz tipi eşleştirme yardımcı metodu
private mapAnalysisTypeAndSetIcon() {
  if (!this.reportContent) return;

  // Analiz tiplerini backend'den frontend'e dönüştür
  if (this.reportContent.code === 'binary') {
    this.reportContent.code = 'logistic_cox';
  } else if (this.reportContent.code === 'regression') {
    this.reportContent.code = 'linear';
  }

  // İkon yollarını ayarla
  const iconBasePath = 'assets/icons/';
  
  // Analiz kodlarını ikon dosya adlarıyla eşleştir
  const iconMap = {
    'descriptive': 'descriptive.svg',
    'single': 'single.svg',
    'multi': 'multi.svg',
    'dependent': 'dependent.svg',
    'correlation': 'correlation.svg',
    'chisq': 'chisq.svg',
    'comean': 'comean.svg',
    'logistic_cox': 'logistic_cox.svg',
    'survival': 'survival.svg',
    'roc': 'roc.svg',
    'linear': 'linear.svg',
    // Diğer analiz tipleri için gerekirse burada ikon eşleştirmelerini ekleyin
  };
  
  // Analiz koduna göre ikon yolunu ayarla
  if (this.reportContent.code && iconMap[this.reportContent.code]) {
    this.reportContent.icon = `${iconBasePath}${iconMap[this.reportContent.code]}`;
  } else {
    // Kod eşleşmiyorsa varsayılan ikon
    this.reportContent.icon = `${iconBasePath}default.svg`;
  }
}

  goBack(): void {
    this.location.back();
  }

  clearHighlight() {
    document.querySelectorAll('.highlight-row, .highlight-section')
      .forEach(el => el.classList.remove('highlight-row', 'highlight-section'));
    this.currentHighlight = null;
    this.selectedVariable = null;
  }

  deleteReport() {
    const dialogRef = this.dialog.open(ConfirmComponent,
      {
        data: {
          title: this.translocoService.translate('shared.confirm.report_delete.title'),
          content: this.translocoService.translate('shared.confirm.report_delete.content'),
          confirm: this.translocoService.translate('shared.confirm.report_delete.confirm'),
          cancel: this.translocoService.translate('shared.confirm.report_delete.cancel')
        }
      }
    );
    dialogRef.closed.subscribe(result => {
      if (result) {
        if (this.isFullscreen) {
          this.toggleFullscreen();
        }
        this.a.deleteReportContent(String(this.reportContent.id)).subscribe(
          {
            next: (_result) => {
              this.location.back();
              this.snotifyService.success
                (this.translocoService.translate('notification.analysis.report_delete.success.message'),
                  this.translocoService.translate('notification.analysis.report_delete.success.title'), {
                  position: 'centerBottom',
                  timeout: 1500,
                  pauseOnHover: false,
                  titleMaxLength: 50,
                });
            },
            error: (_error) => {
              this.snotifyService.error
                (this.translocoService.translate('notification.analysis.report_delete.error.message'),
                  this.translocoService.translate('notification.analysis.report_delete.error.title'), {
                  position: 'centerBottom',
                  timeout: 1500,
                  pauseOnHover: false,
                  titleMaxLength: 50,
                });
            }
          })
      }
    }
    );
  }

  toggleFavorite(event: Event) {
    // Stop the click event from propagating to the parent button
    event.stopPropagation();

    if (!this.reportContent) return;

    // Toggle the favorite status
    const newFavoriteStatus = !this.reportContent.favorite;

    // Update the report in the UI immediately for better UX
    this.reportContent.favorite = newFavoriteStatus;

    // Call the API to update the favorite status
    this.a.toggleReportFavorite(this.reportContent.id).subscribe({
      next: () => {
        // Success notification
        this.snotifyService.success(
          this.translocoService.translate(newFavoriteStatus ? 'notification.analysis.report_favorite.added' : 'notification.analysis.report_favorite.removed'),
          this.translocoService.translate('notification.analysis.report_favorite.title'),
          {
            timeout: 2000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      },
      error: (error) => {
        console.error(error);
        // Revert the UI change if the API call fails
        this.reportContent.favorite = !newFavoriteStatus;

        this.snotifyService.error(
          this.translocoService.translate('notification.analysis.report_favorite.error'),
          this.translocoService.translate('notification.analysis.report_favorite.title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }

getReportClone() {
  if (this.isFullscreen) {
    this.toggleFullscreen();
  }

  const handleReportClone = (reportContent: any, project: any) => {
    this.a.getReportsAnalysisRequest(reportContent.reports[0].analysis.id).subscribe({
      next: (result) => {
        // Log analiz tipi
        console.log('Original analysis type:', result.analysis_group);
        
        // Analiz tipini frontend'de kullanılan terminolojiye göre düzelt
        // "binary" -> "logistic_cox", "regression" -> "linear"
        if (result.analysis_group === 'binary') {
          console.log('Converting binary to logistic_cox');
          result.analysis_group = 'logistic_cox';
        } else if (result.analysis_group === 'regression') {
          console.log('Converting regression to linear');
          result.analysis_group = 'linear';
        }
        
        console.log('Using analysis type:', result.analysis_group);
        
        // Params içindeki analiz grubunu da düzelt
        if (result.params && result.params.analysis_group) {
          if (result.params.analysis_group === 'binary') {
            result.params.analysis_group = 'logistic_cox';
          } else if (result.params.analysis_group === 'regression') {
            result.params.analysis_group = 'linear';
          }
        }
        
        const dialog = this.dialog.open(CreateAnalysisComponent, {
          data: {
            variables: result,
            selectedAnalyseType: result.analysis_group,
            aid: project.datasets[0].analyses[0].id,
            did: project.datasets[0].id,
            pid: project.id,
            raid: reportContent.reports[0].analysis.id,
          },
        });

        dialog.closed.subscribe(result => {
          if (result) {
            this.loadReport();
          }
        });
      },
      error: (error) => {
        console.error('Error retrieving analysis request:', error);
        this.snotifyService.error(
          this.translocoService.translate('notification.analysis.clone.error.message'),
          this.translocoService.translate('notification.analysis.clone.error.title'),
          {
            timeout: 3000,
            showProgressBar: true,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  };

  this.a.getReportContentById(String(this.report.credit_usage?.creditable_id)).subscribe({
    next: (reportContent) => {
      this.p.getProjectById(this.report.credit_usage?.project_id).subscribe({
        next: (project) => {
          handleReportClone(reportContent, project);
        },
        error: (error) => {
          console.error('Error retrieving project:', error);
          this.snotifyService.error(
            this.translocoService.translate('notification.project.load.error.message'),
            this.translocoService.translate('notification.project.load.error.title'),
            {
              timeout: 3000,
              showProgressBar: true,
              closeOnClick: true,
              pauseOnHover: true,
              position: 'centerBottom'
            }
          );
        }
      });
    },
    error: (error) => {
      console.error('Error retrieving report content:', error);
      this.snotifyService.error(
        this.translocoService.translate('notification.report.load.error.message'),
        this.translocoService.translate('notification.report.load.error.title'),
        {
          timeout: 3000,
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          position: 'centerBottom'
        }
      );
    }
  });
}

  toggleReportActions() {
    this.reportContent.showOptions = !this.reportContent.showOptions;
  }

  maximizeReport() {
    // Use the new toggleFullscreen method instead of opening a dialog
    this.toggleFullscreen();
  }

  toggleFullscreen() {
    this.isFullscreen = !this.isFullscreen;

    // When entering fullscreen, scroll to top
    if (this.isFullscreen) {
      window.scrollTo(0, 0);
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }
  }

  changeReportLanguage(lang: string) {
    this.reportContent.lang = lang;
  }

  downloadDocxById() {
    const lang = this.reportContent.lang === 'tr' ? 'tr' : 'en';
    this.selectedReportName = this.report.title_with_code;

    this.a.getReportContentDocxById(String(this.report.credit_usage?.creditable_id), lang).subscribe((data) => {
      const a = document.createElement('a')
      const objectUrl = URL.createObjectURL(data)
      a.href = objectUrl
      a.download = this.slugify(this.report.credit_usage.project_name) + '_' + this.slugify(this.report.title) + '.docx';
      a.click();
      URL.revokeObjectURL(objectUrl);
    });
  }

  slugify(text: string): string {
    const turkishMap = {
      'ş': 's',
      'Ş': 'S',
      'ç': 'c',
      'Ç': 'C',
      'ğ': 'g',
      'Ğ': 'G',
      'ü': 'u',
      'Ü': 'U',
      'ö': 'o',
      'Ö': 'O',
      'ı': 'i',
      'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '') // Remove all non-alphanumeric characters except spaces and dashes
      .replace(/\s+/g, '-') // Replace spaces with dashes
      .replace(/-+/g, '-'); // Replace multiple dashes with a single dash
  }

  openPaymentDialog() {
    const dialog = this.dialog.open(PaymentComponent,
      {
        width: '100%',
      },
    );
    dialog.closed.subscribe(result => {
      if (result) {
        this.loadReport();
      }
    });
  }

  openEditReportDialog() {
    if (!this.reportContent) return;

    const dialog = this.dialog.open(EditReportComponent, {
      data: { report: this.reportContent },
    });

    dialog.closed.subscribe((result: any) => {
      if (result && result.saved) {
        // Update the report label and description
        this.reportContent.label = result.label;
        this.report.title_with_code = result.label;
        this.reportContent.description = result.description;
        this.isEdited = true;

        // Update breadcrumbs with new title
        this.updateBreadcrumbs();
      }
    });
  }

}
<div class="flex flex-col h-full " *transloco="let t; read 'report_list'">
    <!-- <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> overlay -->
    <div class="relative">
        <!-- Seçim modu overlay -->
        <div *ngIf="isSelectingExistingReport" class="absolute inset-0 z-50 bg-white/50 backdrop-blur-[1px]">
        </div>

        <!-- Head<PERSON> i<PERSON>i -->
        <div class="flex-none p-4 mb-6 bg-white border border-neutral-150 rounded-3xl"
            [class.pointer-events-none]="isSelectingExistingReport">
            <!-- Header -->
            <div class="flex items-center justify-between ">
                <div class="flex items-center p-4">
                    <h1 class="text-3xl font-bold text-neutral-950">
                        {{t('title')}}
                    </h1>
                </div>
                <div class="flex items-center gap-2">
                    <div class="p-1 bg-[#F5FBF8] rounded-full flex items-center gap-1">
                        <button (click)="setGrouping(false)"
                            class="flex items-center gap-2 px-6 py-3 font-medium transition-all rounded-full"
                            [ngClass]="!isGroupedByProject ? 'text-white bg-[#19C480] shadow-[inset_0_1px_rgba(255,255,255,0.15)] hover:bg-[#16B073] active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.15)]' : 'text-[#19C480] hover:bg-[#EBF7F2]'">
                            <ng-icon name="lucideFiles" class="text-xl"></ng-icon>
                            {{t('all_reports')}}
                        </button>
                        <button (click)="setGrouping(true)"
                            class="flex items-center gap-2 px-6 py-3 font-medium transition-all rounded-full"
                            [ngClass]="isGroupedByProject ? 'text-white bg-[#19C480] shadow-[inset_0_1px_rgba(255,255,255,0.15)] hover:bg-[#16B073] active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.15)]' : 'text-[#19C480] hover:bg-[#EBF7F2]'">
                            <ng-icon name="lucideLayers" class="text-xl"></ng-icon>
                            {{t('by_projects')}}
                        </button>
                    </div>
                    <button (click)="showCreateReport()" class="inline-flex items-center gap-2 font-medium rounded-full"
                        [ngClass]="'primary-green-button'">
                        <ng-icon name="lucidePlus" class="text-xl"></ng-icon>
                        {{t('create_report')}}
                    </button>
                </div>
            </div>
            <div *ngIf="isLoading" class="pl-4 mb-6">
                <div class="w-64 h-5 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <p *ngIf="!isLoading" class="pl-4 mb-6 text-sm text-gray-500">
                {{totalProjectCount}} {{t('project_count')}} {{reports.length}} {{t('total_report')}}
            </p>
            <!-- Search and Filter -->
            <div class="flex gap-4 ">
                <div class="relative flex-1">
                    <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="filterReports()"
                        [placeholder]="t('search_placeholder')"
                        class="w-full py-3 pl-10 pr-4 transition-colors border-2 border-gray-200 rounded-full focus:border-brand-green-700 focus:ring-2 focus:ring-brand-green-100" />
                    <div class="absolute inset-y-0 flex items-center pointer-events-none left-4">
                        <ng-icon name="lucideSearch" class="text-xl text-gray-400"></ng-icon>
                    </div>
                    <!-- Clear butonu (sadece searchTerm varsa görünür) -->
                    <button *ngIf="searchTerm" (click)="clearSearch()"
                        class="absolute inset-y-0 flex items-center justify-center h-full gap-1 text-gray-500 right-4 hover:text-gray-700 focus:outline-none">
                        <ng-icon name="lucideX" class="text-xl"></ng-icon>
                        <span class="text-sm">{{t('clear_search')}}</span>
                    </button>
                </div>
                <div class="relative">
                    <button (click)="toggleFilterDropdown()" class="secondary-green-button">
                        <ng-icon name="lucideFilter" class="text-2xl text-brand-green-500"></ng-icon>
                        <span class="text-brand-green-500">
                            {{t('filters.title')}}
                        </span>
                        <span *ngIf="getActiveFilterCount() > 0"
                            class="absolute flex items-center justify-center w-5 h-5 text-xs font-bold text-white rounded-full bg-brand-green-500 -top-1 -right-1">
                            {{getActiveFilterCount()}}
                        </span>
                    </button>
                    <!-- Filter Dropdown -->
                    <div *ngIf="isFilterDropdownOpen" [@fadeIn]
                        class="absolute right-0 z-50 mt-2 bg-white border shadow-lg rounded-3xl" appClickOutside
                        (clickOutside)="toggleFilterDropdown()">
                        <div class="p-4 space-y-4">
                            <!-- Show favorites filter -->
                            <div class="pb-4 border-b border-gray-200">
                                <button (click)="toggleFavoritesFilter()"
                                    class="flex items-center justify-center w-full gap-2 px-4 py-2 transition-all border rounded-full"
                                    [ngClass]="{
                                                                'bg-yellow-100 border-yellow-300 text-yellow-800': showOnlyFavorites,
                                                                'bg-white border-gray-300 text-gray-700 hover:border-yellow-300': !showOnlyFavorites
                                                              }">
                                    <ng-icon [name]="'lucideStar'" [class.text-yellow-500]="showOnlyFavorites"
                                        [class.text-gray-400]="!showOnlyFavorites" class="text-xl"
                                        [ngClass]="{'fill-current': showOnlyFavorites}"></ng-icon>
                                    {{ showOnlyFavorites ? t('show_all') : t('show_favorites') }}
                                </button>
                            </div>
                            <!-- Sort options -->
                            <div>
                                <label
                                    class="block mb-2 text-sm font-medium text-gray-700">{{t('filters.sorting.label')}}</label>
                                <select [(ngModel)]="filters.sortBy" (ngModelChange)="filterReports()"
                                    class="w-full px-3 py-2 text-sm border rounded-3xl focus:ring-2 focus:ring-green-500">
                                    <option value="position">{{t('filters.sorting.options.position')}}</option>
                                    <option value="date_desc">{{t('filters.sorting.options.date_desc')}}</option>
                                    <option value="date_asc">{{t('filters.sorting.options.date_asc')}}</option>
                                    <option value="name_asc">{{t('filters.sorting.options.name_asc')}}</option>
                                    <option value="name_desc">{{t('filters.sorting.options.name_desc')}}</option>
                                </select>
                            </div>

                            <!-- Date Range Filter -->
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-700">
                                    {{t('filters.date_range.label')}}
                                </label>
                                <mat-form-field appearance="outline" class="w-full">
                                    <mat-date-range-input [rangePicker]="picker">
                                        <input matStartDate [(ngModel)]="filters.dateRange.start"
                                            (dateChange)="filterReports()"
                                            [placeholder]="t('filters.date_range.start')">
                                        <input matEndDate [(ngModel)]="filters.dateRange.end"
                                            (dateChange)="filterReports()" [placeholder]="t('filters.date_range.end')">
                                    </mat-date-range-input>
                                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                    <mat-date-range-picker #picker></mat-date-range-picker>
                                </mat-form-field>
                            </div>

                            <!-- Reset Button -->
                            <button (click)="resetFilters()"
                                class="flex items-center justify-center w-full gap-2 px-4 py-2 mt-4 text-sm border text-neutral-950 rounded-3xl hover:bg-gray-50">
                                <ng-icon name="lucideX" class="w-4 h-4"></ng-icon>
                                {{t('filters.reset')}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Seçim modu göstergesi -->
    <div *ngIf="isSelectingExistingReport"
        class="sticky inset-x-0 top-0 z-50 flex items-center justify-between px-6 py-4 mb-4 text-white rounded-3xl bg-brand-green-500">
        <div class="flex items-center gap-3">
            <ng-icon name="lucideCopy" class="text-2xl"></ng-icon>
            <div>
                <h3 class="text-lg font-medium">{{ t('select_report_prompt') }}</h3>
                <p class="text-sm text-green-100">{{ t('select_report_desc') }}</p>
            </div>
        </div>
        <button (click)="cancelReportSelection()"
            class="px-4 py-2 text-sm font-medium transition-colors border border-white rounded-full hover:bg-white hover:text-brand-green-500">
            {{ t('common.cancel') }}
        </button>
    </div>

    <!-- Rapor listesi için overlay efekti -->
    <div *ngIf="isSelectingExistingReport" class="fixed inset-0 z-10 pointer-events-none bg-black/5 backdrop-blur-sm">
    </div>

    <!-- Reports List -->
    <div class="z-40 flex-1 overflow-y-auto">
        <div class="space-y-4">
            <!-- Skeleton Loader -->
            <div *ngIf="isLoading" class="space-y-4">
                <div *ngFor="let item of skeletonItems"
                    class="flex items-center justify-between w-full p-4 bg-white border rounded-3xl animate-pulse">
                    <div class="flex items-center justify-between w-full">
                        <div class="flex items-center gap-4">
                            <!-- Icon placeholder -->
                            <div class="w-12 h-12 bg-gray-200 rounded-xl"></div>
                            <div>
                                <!-- Title placeholder -->
                                <div class="w-48 h-5 mb-2 bg-gray-200 rounded-md"></div>
                                <!-- Info line placeholder -->
                                <div class="flex items-center gap-2 mt-2">
                                    <div class="w-20 h-3 bg-gray-200 rounded"></div>
                                    <div class="w-32 h-3 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                        </div>
                        <!-- Arrow placeholder -->
                        <div class="flex items-center gap-8">
                            <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                            <div class="w-8 h-8 mx-2 bg-gray-200 rounded-full"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grouped Reports List -->
            <ng-container *ngIf="!isLoading && isGroupedByProject">
                <div *ngFor="let group of groupedReports | keyvalue"
                    class="overflow-hidden bg-white border-2 border-neutral-150 rounded-3xl">
                    <!-- Collapsible Group Header -->
                    <div class="flex items-center justify-between py-4 pl-6 pr-3 transition-colors border-b cursor-pointer bg-brand-green-100 hover:bg-brand-green-300"
                        (click)="toggleGroupCollapse(group.key)">
                        <div class="flex items-center gap-3">
                            <ng-icon name="lucideFolderOpen" class="text-2xl text-brand-green-500"></ng-icon>
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900">
                                    {{group.key}}
                                </h2>
                                <p class="text-sm text-gray-500">{{group.value.length}} reports</p>
                            </div>
                        </div>
                        <button
                            class="flex items-center justify-center p-2 text-gray-400 transition-colors rounded-full hover:bg-gray-100 size-12 min-w-12 ">
                            <ng-icon [name]="isGroupCollapsed(group.key) ? 'lucideChevronRight' : 'lucideChevronDown'"
                                class="text-2xl"></ng-icon>
                        </button>
                    </div>
                    <!-- Collapsible Content -->
                    <div class="border-t border-neutral-100" [class.hidden]="isGroupCollapsed(group.key)">
                        <div class="divide-y divide-neutral-100">
                            <div *ngFor="let report of group.value"
                                class="flex items-center justify-between w-full transition-colors hover:bg-gray-50 report-item-{{report.id}}"
                                (click)="isSelectingExistingReport ? selectExistingReport(report) : null">
                                <button class="flex items-center justify-between w-full p-4"
                                    (click)="$event.stopPropagation(); navigateToDetail(report)"
                                    [class.pointer-events-none]="isSelectingExistingReport">
                                    <div class="flex items-center gap-4">
                                        <div
                                            class="flex items-center justify-center w-12 h-12 rounded-xl bg-brand-green-50">
                                            <ng-icon name="lucideFileText"
                                                class="text-2xl text-brand-green-500"></ng-icon>
                                        </div>
                                        <div>
                                            <div class="flex items-center gap-2">
                                                <h3 class="font-semibold text-gray-900 truncate max-w-144 text-start"
                                                    [title]="report.title_with_code">{{report.title_with_code}}</h3>
                                            </div>
                                            <p class="flex items-center gap-1 text-sm text-neutral-600">
                                                <ng-icon name="lucideClock4" class=""></ng-icon>
                                                <span class="mr-1">
                                                    {{formatDate(report.created_at)}}
                                                </span>

                                                <span *ngIf="report.credit_usage?.used_credit" class="flex text-xs">
                                                    <img src="assets/icons/istacoin.svg" alt="" class="mr-1 size-4">
                                                    {{report.credit_usage?.used_credit}} {{t('credit_used')}}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <button
                                            class="flex-shrink-0 p-1.5 transition-colors rounded-full hover:bg-gray-100 flex items-center justify-center size-8"
                                            (click)="$event.stopPropagation(); toggleFavorite($event, report.id)"
                                            [title]="report.favorite ? t('remove_favorite') : t('add_favorite')">
                                            <ng-icon [name]="report.favorite ? 'tablerStarFilled' : 'tablerStarOff'"
                                                class="text-xl" [class.text-yellow-500]="report.favorite"
                                                [class.text-gray-400]="!report.favorite"
                                                [ngClass]="{'fill-current': report.favorite}"></ng-icon>
                                        </button>
                                        <button
                                            class="flex items-center justify-center w-8 h-8 text-gray-400 transition-colors rounded-full group-hover:text-brand-green-500"
                                            (click)="$event.stopPropagation();">
                                            <ng-icon name="lucideChevronRight" class="text-2xl"></ng-icon>
                                        </button>
                                    </div>
                                </button>
                                <button [matTooltip]="t('download_report')" matTooltipPosition="above"
                                    *ngIf="!isSelectingExistingReport"
                                    (click)="$event.stopPropagation(); downloadDocxById(report)"
                                    [class.pointer-events-none]="isSelectingExistingReport"
                                    class="flex items-center justify-center mx-2 text-gray-400 rounded-3xl size-12 min-w-12 hover:transition-colors hover:bg-gray-100 hover:text-brand-green-500">
                                    <ng-icon name="lucideDownload" class="text-xl"></ng-icon>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>

            <!-- Ungrouped Reports List (Without Drag and Drop) -->
            <ng-container *ngIf="!isLoading && !isGroupedByProject">
                <div class="space-y-4">
                    <div *ngFor="let report of filteredReports" class="relative group">
                        <div class="flex items-center w-full">
                            <!-- Main Card -->
                            <div class="flex items-center w-full transition-all bg-white border rounded-3xl border-neutral-150 hover:border-brand-green-500"
                                [class.cursor-pointer]="isSelectingExistingReport"
                                [class.report-item-{{report.id}}]="true"
                                [class.pointer-events-auto]="isSelectingExistingReport"
                                [class.border-2]="isSelectingExistingReport"
                                [class.border-brand-green-500]="isSelectingExistingReport"
                                [class.hover:bg-brand-green-50]="isSelectingExistingReport"
                                [class.hover:shadow-lg]="isSelectingExistingReport"
                                [class.scale-100]="isSelectingExistingReport"
                                [class.hover:scale-[1.01]]="isSelectingExistingReport"
                                [class.transform]="isSelectingExistingReport"
                                [class.transition-all]="isSelectingExistingReport"
                                (click)="isSelectingExistingReport ? selectExistingReport(report) : null">

                                <!-- Main content -->
                                <div class="flex items-center justify-between w-full p-4">
                                    <button class="flex items-center justify-between w-full"
                                        (click)="$event.stopPropagation(); navigateToDetail(report)"
                                        [class.pointer-events-none]="isSelectingExistingReport">
                                        <div class="flex items-center gap-4">
                                            <div
                                                class="flex items-center justify-center w-12 h-12 rounded-xl bg-brand-green-50">
                                                <ng-icon name="lucideFileText"
                                                    class="text-2xl text-brand-green-500"></ng-icon>
                                            </div>
                                            <div>
                                                <div class="flex items-center gap-2">
                                                    <h3 class="font-semibold text-gray-900 truncate max-w-144 text-start"
                                                        [title]="report.title_with_code">{{report.title_with_code}}</h3>
                                                </div>
                                                <p class="flex items-center gap-1 text-sm text-neutral-600">
                                                    <ng-icon name="lucideClock4" class=""></ng-icon>
                                                    <span class="mr-1">
                                                        {{formatDate(report.created_at)}}
                                                    </span>
                                                    <ng-container>
                                                        <ng-icon name="lucideFolderOpen" class=""></ng-icon>
                                                        <span class="truncate max-w-48">
                                                            {{report.credit_usage?.project_name}}
                                                        </span>
                                                    </ng-container>
                                                    <span *ngIf="report.credit_usage?.used_credit" class="flex text-xs">
                                                        <img src="assets/icons/istacoin.svg" alt=""
                                                            class="ml-2 mr-1 size-4">
                                                        {{report.credit_usage?.used_credit}} {{t('credit_used')}}
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </button>

                                    <div class="flex items-center gap-2">
                                        <button
                                            class="flex-shrink-0 p-1.5 transition-colors rounded-full hover:bg-gray-100 flex items-center justify-center size-8"
                                            (click)="$event.stopPropagation(); toggleFavorite($event, report.id)"
                                            [title]="report.favorite ? t('remove_favorite') : t('add_favorite')">
                                            <ng-icon [name]="report.favorite ? 'tablerStarFilled' : 'tablerStarOff'"
                                                class="text-xl" [class.text-yellow-500]="report.favorite"
                                                [class.text-gray-400]="!report.favorite"
                                                [ngClass]="{'fill-current': report.favorite}"></ng-icon>
                                        </button>
                                        <button [matTooltip]="t('download_report')" matTooltipPosition="above"
                                            *ngIf="!isSelectingExistingReport"
                                            (click)="$event.stopPropagation(); downloadDocxById(report)"
                                            [class.pointer-events-none]="isSelectingExistingReport"
                                            class="flex items-center justify-center text-gray-400 rounded-3xl size-12 min-w-12 hover:transition-colors hover:bg-gray-100 hover:text-brand-green-500">
                                            <ng-icon name="lucideDownload" class="text-xl"></ng-icon>
                                        </button>
                                        <button
                                            class="flex items-center justify-center w-8 h-8 text-gray-400 transition-colors rounded-full group-hover:text-brand-green-500"
                                            (click)="$event.stopPropagation();">
                                            <ng-icon name="lucideChevronRight" class="text-2xl"></ng-icon>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>
    </div>
</div>
import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { NavigationEnd, Router } from '@angular/router';
import { ReportHelperService } from '@app/data/helper/report.helper.service';
import { ReportLine } from '@app/data/models/report_line.interface';
import { AnalysisService } from '@app/data/services/analysis.service';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { AuthService } from '@app/modules/auth/auth.service';
import { TranslocoService } from '@ngneat/transloco';
import emailjs from '@emailjs/browser';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { CreateReportDialogComponent } from '../dialogs/create-report-dialog.component';
import { Dialog } from '@angular/cdk/dialog';
import { ProjectService } from '@app/data/services/project.service';
import { Breadcrumb } from '@app/data/models/breadcrumb.interface';
import { CreateAnalysisComponent } from '@app/shared/components/create-analysis/create-analysis.component';
import { SnotifyService } from 'ng-alt-snotify';
import { Subject, filter, takeUntil } from 'rxjs';

const publicKey = 'o9TqDVlONwom86VF2';

@Component({
  selector: 'app-report-list',
  templateUrl: './report-list.component.html',
  styleUrls: ['./report-list.component.scss'],
  animations: [
    trigger('fadeIn', [
      state('void', style({ opacity: '0' })),
      state('*', style({ opacity: '*' })),
      transition('void <=> *', [animate('0.2s ease-in')]),
    ]),
  ],
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'tr-TR' }
  ]
})
export class ReportListComponent implements OnInit, OnDestroy {
  searchTerm: string = '';
  skeletonItems = new Array(5);
  isGroupedByProject = false;
  groupedReports: { [key: string]: ReportLine[] } = {};
  collapsedGroups: { [key: string]: boolean } = {};

  // New properties for favorites and drag-and-drop
  showOnlyFavorites: boolean = false;
  isDragging: boolean = false;
  isSelectingExistingReport = false;

  totalProjectCount = 0;
  currentLang = 'tr-TR';
  reports: ReportLine[] = [];
  filteredReports: ReportLine[] = [];
  isLoading = true;
  isFilterDropdownOpen = false;
  private destroy$ = new Subject<void>();

  filters = {
    status: 'all',
    dateRange: {
      start: null,
      end: null
    },
    hasDataset: 'all',
    sortBy: 'position' // Change default to position
  };
  selectedReportProject: any;

  constructor(
    private rh: ReportHelperService,
    private b: BreadcrumbService,
    private a: AnalysisService,
    private router: Router,
    private auth: AuthService,
    private dialog: Dialog,
    private p: ProjectService,
    private translocoService: TranslocoService,
    private snotifyService: SnotifyService
  ) {
    // Dil değişikliklerini dinle
    this.translocoService.langChanges$.subscribe(() => {
      if (this.router.url === '/reports') {
        this.updatePageTitle();
      }
    });
  }

  loadReports() {
    this.isLoading = true;
    this.rh.getAllReportLines().subscribe((data) => {
      this.reports = data.report_lines;

      this.filteredReports = data.report_lines;
      this.totalProjectCount = data.total_projects;
      this.filterReports();
      this.isLoading = false;
    }, (error) => {
      console.error('Error loading reports:', error);
      this.auth.logout();
      this.router.navigate(['/login']);
      this.isLoading = false;
    });
  }

  ngOnInit(): void {
    // Breadcrumb'ları ayarla - bu artık BreadcrumbService tarafından otomatik olarak yapılıyor
    // Ancak yine de çağırıyoruz, çünkü sayfa yenilendiğinde BreadcrumbService'in NavigationStart olayı tetiklenmeyebilir
    this.updateBreadcrumbs();

    // Verileri yükle
    this.loadReports();

    // Sayfa başlığını dinamik olarak ayarla
    this.updatePageTitle();

    // Router olaylarını dinle
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      // Rota değiştiğinde sayfa başlığını güncelle
      if (this.router.url === '/reports') {
        this.updatePageTitle();
      }
    });

    // Dil değişikliklerini dinle
    this.translocoService.langChanges$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      if (this.router.url === '/reports') {
        this.updatePageTitle();
        this.updateBreadcrumbs(); // Dil değiştiğinde breadcrumb'ları güncelle
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Sayfa başlığını günceller
   */
  private updatePageTitle(): void {
    this.translocoService.selectTranslate('report_list.title').subscribe(title => {
      const pageTitle = `${title} | istabot`;
      document.title = pageTitle;
    });
  }

  updateBreadcrumbs() {

    // Raporlar sayfasında olduğumuzu doğrula
    if (this.router.url === '/reports') {


      const breadcrumbs: Breadcrumb[] = [
        {
          label: 'navigation.reports',
          link: '/reports',
          icon: 'lucideNotepadText'  // Sidebar ile uyumlu ikon
        }
      ];
      this.b.setBreadcrumbs(breadcrumbs);

      // Mevcut sayfa bilgisini güncelle
      localStorage.setItem('currentPage', 'reports');

      // Zorla breadcrumb'ları ayarla (setTimeout ile zamanlama sorunlarını önle)
      setTimeout(() => {

        this.b.setBreadcrumbs(breadcrumbs);
      }, 100);
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR');
  }

  toggleFavorite(event: Event, reportId: number) {
    event.stopPropagation();

    const report = this.reports.find(r => r.id === reportId);
    if (!report) return;

    const previousStatus = report.favorite;
    report.favorite = !report.favorite;

    const filteredReport = this.filteredReports.find(r => r.id === reportId);
    if (filteredReport) {
      filteredReport.favorite = report.favorite;
    }

    this.a.toggleReportFavorite(reportId.toString()).subscribe({
      next: () => {
        this.snotifyService.success(
          this.translocoService.translate(report.favorite ? 'notification.report.favorite.added' : 'notification.report.favorite.removed'),
          this.translocoService.translate('notification.report.favorite.title'),
          {
            timeout: 2000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );

        this.filterReports();
      },
      error: (error) => {
        console.error(error);
        report.favorite = previousStatus;
        if (filteredReport) {
          filteredReport.favorite = previousStatus;
        }

        this.snotifyService.error(
          this.translocoService.translate('notification.report.favorite.error'),
          this.translocoService.translate('notification.report.favorite.title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }

  toggleFavoritesFilter() {
    this.showOnlyFavorites = !this.showOnlyFavorites;
    this.filterReports();
  }
  toggleFilterDropdown(): void {
    this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
  }

  resetFilters(): void {
    this.filters = {
      status: 'all',
      dateRange: {
        start: null,
        end: null
      },
      hasDataset: 'all',
      sortBy: 'position' // Change default to position
    };
    this.searchTerm = '';
    this.showOnlyFavorites = false;
    this.filterReports();
  }

  getActiveFilterCount(): number {
    let count = 0;
    if (this.filters.dateRange.start || this.filters.dateRange.end) count++;
    if (this.filters.sortBy !== 'position') count++;
    if (this.showOnlyFavorites) count++;
    return count;
  }

  filterReports(): void {
    let filtered = [...this.reports];

    // Favorite filter
    if (this.showOnlyFavorites) {
      filtered = filtered.filter(report => report.favorite);
    }

    // Text search filter
    if (this.searchTerm.trim()) {
      const searchTermNormalized = this.searchTerm.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').trim();
      filtered = filtered.filter(report => {
        const nameMatch = report.title_with_code.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTermNormalized);
        const projectMatch = report.credit_usage?.project_name?.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTermNormalized);
        return nameMatch || projectMatch;
      });
    }

    // Date range filter
    if (this.filters.dateRange.start || this.filters.dateRange.end) {
      filtered = filtered.filter(report => {
        const reportDate = new Date(report.created_at);
        const start = this.filters.dateRange.start ? new Date(this.filters.dateRange.start) : null;
        const end = this.filters.dateRange.end ? new Date(this.filters.dateRange.end) : null;

        if (start && end) {
          end.setHours(23, 59, 59, 999);
          return reportDate >= start && reportDate <= end;
        } else if (start) {
          return reportDate >= start;
        } else if (end) {
          end.setHours(23, 59, 59, 999);
          return reportDate <= end;
        }
        return true;
      });
    }

    // Sort results
    filtered = this.sortReports(filtered);

    // Group reports if enabled
    if (this.isGroupedByProject) {
      this.groupedReports = filtered.reduce((groups, report) => {
        const projectName = report.credit_usage?.project_name || 'No Project';
        if (!groups[projectName]) {
          groups[projectName] = [];
        }
        groups[projectName].push(report);
        return groups;
      }, {});
    }

    this.filteredReports = filtered;
  }

  private sortReports(reports: ReportLine[]): ReportLine[] {
    return reports.sort((a, b) => {
      switch (this.filters.sortBy) {
        case 'date_desc':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'date_asc':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'name_asc':
          return a.title_with_code.localeCompare(b.title_with_code);
        case 'name_desc':
          return b.title_with_code.localeCompare(a.title_with_code);
        default:
          return 0;
      }
    });
  }

  // Add this helper method to close filter dropdown when clicking outside
  onClickOutside(_event: Event) {
    if (this.isFilterDropdownOpen) {
      this.isFilterDropdownOpen = false;
    }
  }

  // Helper method to format date string for input
  formatDateForInput(date: Date | null): string {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }

  getDateInputLocale(): string {
    return this.currentLang;
  }

  formatDateForDisplay(date: string | null): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString(this.getDateInputLocale());
  }
  downloadDocxById(item: any) {
    if (this.isSelectingExistingReport) return;
    const lang = localStorage.getItem('activeLang') || 'tr';

    this.a.getReportContentDocxById(item.credit_usage?.creditable_id, lang).subscribe((data) => {
      // emailjs
      emailjs.send("service_v1wfhxn", "template_48a59cm",
        {
          user_id: localStorage.getItem('user_id'),
          dataset_id: 'from_report_list',
          username: localStorage.getItem('username'),
          format: 'docx',
          report_id: item.id,
          time_stamp: new Date().toLocaleString()
        },
        {
          publicKey: publicKey
        }
      );
      const a = document.createElement('a')
      const objectUrl = URL.createObjectURL(data)
      a.href = objectUrl
      a.download = this.slugify(item.credit_usage.project_name) + '_' + this.slugify(item.title) + '.docx';
      a.click();
      URL.revokeObjectURL(objectUrl);
    });
  }
  slugify(text: string): string {
    const turkishMap = {
      'ş': 's',
      'Ş': 'S',
      'ç': 'c',
      'Ç': 'C',
      'ğ': 'g',
      'Ğ': 'G',
      'ü': 'u',
      'Ü': 'U',
      'ö': 'o',
      'Ö': 'O',
      'ı': 'i',
      'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '') // Remove all non-alphanumeric characters except spaces and dashes
      .replace(/\s+/g, '-') // Replace spaces with dashes
      .replace(/-+/g, '-'); // Replace multiple dashes with a single dash
  }

  setGrouping(grouped: boolean): void {
    if (this.isSelectingExistingReport) return;
    this.isGroupedByProject = grouped;
    this.collapsedGroups = {};
    this.filterReports();
  }

  toggleGroupCollapse(groupName: string): void {
    if (this.isSelectingExistingReport) return;
    this.collapsedGroups[groupName] = !this.collapsedGroups[groupName];
  }

  isGroupCollapsed(groupName: string): boolean {
    return this.collapsedGroups[groupName] || false;
  }

  showCreateReport() {
    const dialogRef = this.dialog.open(CreateReportDialogComponent);

    dialogRef.closed.subscribe((result: any) => {
      if (result) {

        if (result.action === 'new') {
          this.selectedReportProject = result.project;
          const dialog = this.dialog.open(CreateAnalysisComponent, {
            data: {
              selectedAnalyseType: result.type,
              pid: this.selectedReportProject.id,
              did: this.selectedReportProject.datasets[0].id,
              aid: this.selectedReportProject.datasets[0].analyses[0].id,
              cid: this.selectedReportProject.datasets[0].analyses[0].analysis_configuration.id
            }
          });
          dialog.closed.subscribe(result => {
            if (result) {
              this.loadReports();
            }
          });
        } else if (result.action === 'existing') {
          // Toggle the report selection mode
          this.isSelectingExistingReport = true;
        }
      }
    });
  }

  selectExistingReport(report: any) {
    if (!this.isSelectingExistingReport) return;

    // Animasyon için kısa gecikme
    const selectedReport = report;
    const element = document.querySelector(`.report-item-${report.id}`) as HTMLElement;
    if (element) {
      element.classList.add('scale-105', 'bg-brand-green-50');
    }

    // Show a confirmation toast
    this.snotifyService.info(
      this.translocoService.translate('notification.report.clone.processing'),
      this.translocoService.translate('notification.report.clone.title'),
      {
        timeout: 2000,
        showProgressBar: true,
        closeOnClick: false,
        pauseOnHover: true,
        position: 'centerBottom'
      }
    );

    setTimeout(() => {
      this.getReportClone(selectedReport);
      this.isSelectingExistingReport = false;
    }, 500);
  }

  cancelReportSelection() {
    this.isSelectingExistingReport = false;
  }

  getReportClone(item: any) {
    this.a.getReportContentById(item.credit_usage?.creditable_id).subscribe((reportContent) => {
      this.p.getProjectById(item.credit_usage?.project_id).subscribe((project) => {
        this.a.getReportsAnalysisRequest(reportContent.reports[0].analysis.id).subscribe(
          {
            next: (result) => {
              const dialog = this.dialog.open(CreateAnalysisComponent,
                {
                  data: {
                    variables: result,
                    aid: project.datasets[0].analyses[0].id,
                    did: project.datasets[0].id,
                    pid: project.id,
                    raid: reportContent.reports[0].analysis.id,
                    cid: project.datasets[0].analyses[0].analysis_configuration.id
                  },
                }
              );
              dialog.closed.subscribe(result => {
                if (result) {
                  this.loadReports();
                }
              });
            },
            error: (error) => {
              console.error('Error retrieving analysis request:', error);
            }
          }
        );
      });
    });
  }

  // Etkileşimleri engellemek için event handler'lar
  preventEventIfSelecting(event: Event) {
    if (this.isSelectingExistingReport) {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  navigateToDetail(report: ReportLine) {
  // report.id yerine report.credit_usage?.creditable_id kullan
  const reportContentId = report.credit_usage?.creditable_id || report.id;
  
  this.router.navigate(['/reports', reportContentId], {
    state: {
      report: report,
      projectName: report.credit_usage?.project_name,
      projectId: report.credit_usage?.project_id
    }
  });
}

  clearSearch() {
    this.searchTerm = '';
    this.filterReports()
  }
}

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TitleResolver } from '@app/shared/pipes/pageTitle.resolver';
import { ReportListComponent } from './report-list/report-list.component';
import { ReportDetailComponent } from './report-detail/report-detail.component';

const routes: Routes = [
  {
    path: '',
    component: ReportListComponent,
    resolve: {
      title: TitleResolver
    }
  },
  {
    path: ':id',
    component: ReportDetailComponent,
    resolve: {
      title: TitleResolver
    }
  }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [TitleResolver],
})
export class ReportsRoutingModule { }
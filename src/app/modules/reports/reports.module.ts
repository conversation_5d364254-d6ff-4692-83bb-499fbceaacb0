import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReportListComponent } from './report-list/report-list.component';
import { SharedModule } from '@app/shared/shared.module';
import { ReportsRoutingModule } from './reports-routing.module';
import { CreateReportDialogComponent } from './dialogs/create-report-dialog.component';
import { ReportDetailComponent } from './report-detail/report-detail.component';
import { EditReportComponent } from './dialogs/edit-report/edit-report.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
@NgModule({
  declarations: [ReportListComponent, CreateReportDialogComponent, ReportDetailComponent, EditReportComponent],
  imports: [CommonModule, ReportsRoutingModule, SharedModule, DragDropModule],
})
export class ReportsModule { }

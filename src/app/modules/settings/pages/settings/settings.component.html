<div role="dialog"
    class="relative flex flex-col h-[calc(100dvh-8rem)] w-[calc(100dvw-8rem)] overflow-hidden bg-white shadow-xl text-start rounded-2xl focus:outline-none"
    tabindex="-1">
    <!-- Header -->
    <div class="flex items-center flex-shrink-0 border-b border-black/10">
        <div class="flex items-center flex-none w-64 p-6">
            <h2 class="text-lg font-semibold leading-6 text-blue-950">{{ 'settings.settings' | transloco }}</h2>
        </div>
        <div class="flex items-center justify-between p-6 border-l grow">
            {{activeTitle | transloco}}
            <button (click)="closeDialog()"
                class="flex items-center justify-center transition-all rounded-full size-8 hover:bg-gray-100 active:scale-95">
                <ng-icon name="matCloseRound" class="text-xl font-medium text-gray-500"></ng-icon>
            </button>
        </div>
    </div>

    <!-- Content -->
    <div class="flex flex-grow overflow-hidden">
        <div dir="ltr" class="flex flex-col w-full md:flex-row">
            <!-- Left Menu -->
            <div role="tablist" class="flex flex-col flex-shrink-0 gap-2 p-4 min-w-64">
                <div class="flex flex-col flex-1 gap-2">
                    <button *ngFor="let item of menuItems" type="button" role="tab"
                        [attr.aria-selected]="activeTab === item.id" (click)="setActiveTab(item.id)"
                        [class.bg-brand-blue-200]="activeTab === item.id"
                        class="flex items-center justify-start w-full gap-2 px-3 py-2.5 transition-all border hover:border-blue-700 text-blue-950 active:scale-95 group rounded-xl">
                        <ng-icon [name]="item.icon" class="text-xl"></ng-icon>
                        <span class="text-sm font-medium truncate text-nowrap">{{item.label| transloco}}</span>
                    </button>
                </div>
            </div>

            <!-- Right Content -->
            <div class="flex-1 overflow-hidden border-l">
                <div class="h-full overflow-y-auto">
                    <!-- Hesap Content -->
                    <div *ngIf="activeTab === 'hesap'" @fadeSlide
                        class="flex flex-col gap-6 px-4 pb-6 text-sm text-blue-950 sm:px-6 md:px-8 md:pt-6">

                        <!-- Profile Information Container - Yan Yana -->
                        <div class="grid grid-cols-1 gap-6 pb-2 border-b lg:grid-cols-2">

                            <!-- Profile Information - Sol Taraf -->
                            <div class="pb-6 border-b border-slate-200 lg:border-r lg:border-b-0 lg:pr-6">
                                <div class="flex flex-col gap-4">
                                    <div class="text-base font-medium">{{ 'profile.personal_info' | transloco }}</div>
                                    <div class="grid grid-cols-1 gap-4">
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.name' | transloco }}</div>
                                            <div *ngIf="user; else skeleton" class="font-medium">
                                                {{(user?.name | titlecase)}} {{(user?.surname | titlecase)}}
                                            </div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.email' | transloco }}</div>
                                            <div *ngIf="user; else skeleton" class="font-medium">{{ user?.email }}</div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.phone' | transloco }}</div>
                                            <div *ngIf="user; else skeleton" class="font-medium">{{ user?.phone_number
                                                || '-' }}</div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.member_since' | transloco }}
                                            </div>
                                            <div *ngIf="user; else skeleton" class="font-medium">
                                                {{ user?.created_at | date:'mediumDate' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Academic Information - Sağ Taraf -->
                            <div class="pb-6">
                                <div class="relative flex flex-col gap-4">
                                    <!-- Loading Overlay -->
                                    <div *ngIf="isProfileLoading"
                                        class="absolute inset-0 z-10 flex items-center justify-center bg-white/80 rounded-2xl">
                                        <div class="flex items-center gap-2">
                                            <ng-icon name="heroArrowPath"
                                                class="text-2xl text-brand-blue-600 animate-spin"></ng-icon>
                                            <span class="text-brand-blue-600">{{ 'shared.saving' | transloco }}</span>
                                        </div>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div class="text-base font-medium">{{ 'profile.academic_info' | transloco }}
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button *ngIf="!isEditingProfile && !hasProfileChanges" type="button"
                                                (click)="toggleEditProfile()" class="secondary-blue-button">
                                                <ng-icon name="lucidePencilLine" class="text-xl"></ng-icon>
                                                <span class="text-sm font-medium">{{ 'shared.edit' | transloco }}</span>
                                            </button>
                                            <button *ngIf="isEditingProfile" type="button" (click)="cancelEditProfile()"
                                                class="secondary-status-error-button">
                                                <ng-icon name="lucideX" class="text-xl"></ng-icon>
                                                <span class="text-sm font-medium">{{ 'shared.cancel' | transloco
                                                    }}</span>
                                            </button>
                                            <button *ngIf="hasProfileChanges" type="button" (click)="saveProfile()"
                                                [disabled]="isProfileLoading || !profileForm.valid"
                                                class="secondary-blue-button">
                                                <ng-icon [name]="isProfileLoading ? 'heroArrowPath' : 'featherSave'"
                                                    [class.animate-spin]="isProfileLoading" class="text-xl"></ng-icon>
                                                <span class="text-sm font-medium">
                                                    {{ (isProfileLoading ? 'shared.saving' : 'shared.save') | transloco
                                                    }}
                                                </span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Display Mode -->
                                    <div *ngIf="!isEditingProfile" class="grid grid-cols-1 gap-4">
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.university' | transloco }}
                                            </div>
                                            <div *ngIf="user; else skeleton" class="font-medium">
                                                {{ user?.profile?.university || '-' }}
                                            </div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.faculty' | transloco }}</div>
                                            <div *ngIf="user; else skeleton" class="font-medium">
                                                {{ user?.profile?.faculty || '-' }}
                                            </div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.department' | transloco }}
                                            </div>
                                            <div *ngIf="user; else skeleton" class="font-medium">
                                                {{ user?.profile?.department || '-' }}
                                            </div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="text-sm text-gray-500">{{ 'profile.interests' | transloco }}
                                            </div>
                                            <div *ngIf="user; else skeleton" class="font-medium whitespace-pre-wrap">
                                                {{ user?.profile?.interests || '-' }}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Edit Mode -->
                                    <form *ngIf="isEditingProfile" [formGroup]="profileForm"
                                        class="grid grid-cols-1 gap-4">
                                        <!-- University -->
                                        <div class="space-y-1">
                                            <label class="text-sm text-gray-500">{{ 'profile.university' | transloco
                                                }}</label>
                                            <input type="text" formControlName="university"
                                                [placeholder]="'profile.university_placeholder' | transloco"
                                                class="w-full px-3 py-2 text-sm transition-colors border outline-none rounded-xl border-slate-200 focus:border-brand-blue-500 focus:ring-1 focus:ring-brand-blue-500">
                                        </div>

                                        <!-- Faculty -->
                                        <div class="space-y-1">
                                            <label class="text-sm text-gray-500">{{ 'profile.faculty' | transloco
                                                }}</label>
                                            <input type="text" formControlName="faculty"
                                                [placeholder]="'profile.faculty_placeholder' | transloco"
                                                class="w-full px-3 py-2 text-sm transition-colors border outline-none rounded-xl border-slate-200 focus:border-brand-blue-500 focus:ring-1 focus:ring-brand-blue-500">
                                        </div>

                                        <!-- Department -->
                                        <div class="space-y-1">
                                            <label class="text-sm text-gray-500">{{ 'profile.department' | transloco
                                                }}</label>
                                            <input type="text" formControlName="department"
                                                [placeholder]="'profile.department_placeholder' | transloco"
                                                class="w-full px-3 py-2 text-sm transition-colors border outline-none rounded-xl border-slate-200 focus:border-brand-blue-500 focus:ring-1 focus:ring-brand-blue-500">
                                        </div>

                                        <!-- Interests -->
                                        <div class="space-y-1">
                                            <label class="text-sm text-gray-500">{{ 'profile.interests' | transloco
                                                }}</label>
                                            <textarea formControlName="interests"
                                                [placeholder]="'profile.interests_placeholder' | transloco" rows="3"
                                                class="w-full px-3 py-2 text-sm transition-colors border outline-none resize-none rounded-xl border-slate-200 focus:border-brand-blue-500 focus:ring-1 focus:ring-brand-blue-500"></textarea>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Skeleton Template -->
                        <ng-template #skeleton>
                            <div class="w-24 h-5 bg-gray-200 rounded animate-pulse"></div>
                        </ng-template>

                        <!-- Password Reset -->
                        <div class="pb-6 border-b border-slate-200">
                            <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                                <div class="flex flex-col gap-1">
                                    <span class="text-base font-medium">{{ 'auth.change_password.reset_pass' | transloco
                                        }}</span>
                                    <span class="text-sm text-neutral-600">{{ 'auth.change_password.reset_pass_desc' |
                                        transloco }}</span>
                                </div>
                                <button (click)="forgotPassword()" [disabled]="isSendingMail"
                                    class="primary-blue-button">
                                    <ng-icon [name]="isSendingMail ? 'lucideLoader' : 'lucideKey'"
                                        [class.animate-spin]="isSendingMail" class="text-xl"></ng-icon>
                                    <span class="text-sm font-medium">
                                        {{ (isSendingMail ? 'auth.mail-input.sending_email' :
                                        'auth.change_password.reset_pass') | transloco }}
                                    </span>
                                </button>
                            </div>
                        </div>

                        <!-- Danger Zone: Delete Account -->
                        <div class="overflow-hidden border rounded-2xl border-status-error-200 bg-status-error-50/50">
                            <div
                                class="flex items-center gap-2 p-4 text-lg font-semibold border-b bg-status-error-100 border-b-status-error-200 text-status-error-600">
                                <ng-icon name="lucideCircleAlert" class="text-xl"></ng-icon>
                                <span>{{ 'shared.danger_zone' | transloco }}</span>
                            </div>
                            <div class="flex flex-col gap-4 p-6 lg:flex-row lg:items-center lg:justify-between">
                                <div class="flex-1">
                                    <div class="text-base font-medium text-gray-900">{{ 'profile.delete_account' |
                                        transloco }}</div>
                                    <div class="mt-2 text-sm text-gray-600">
                                        {{ 'shared.confirm.delete_account.content' | transloco }}
                                    </div>
                                </div>
                                <button (click)="deleteUser()" class="secondary-status-error-button">
                                    <ng-icon name="heroTrash" class="text-xl"></ng-icon>
                                    <span class="text-sm font-medium">{{ 'profile.delete_account' | transloco }}</span>
                                </button>
                            </div>
                        </div>

                        <!-- Policy Links -->
                        <div class="pt-5 border-t border-slate-200">
                            <div class="flex justify-center gap-6">
                                <button (click)="showContract('privacy_policy')"
                                    class="text-sm font-medium text-neutral-600 hover:text-brand-blue-600 hover:underline">
                                    Gizlilik Politikası
                                </button>
                                <button (click)="showContract('cookie')"
                                    class="text-sm font-medium text-neutral-600 hover:text-brand-blue-600 hover:underline">
                                    Kullanım Şartları
                                </button>
                                <button (click)="showContract('membership_agreement')"
                                    class="text-sm font-medium text-neutral-600 hover:text-brand-blue-600 hover:underline">
                                    Üyelik Sözleşmesi
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Satın alım -->
                    <div *ngIf="activeTab === 'satin-alim'" @fadeSlide class="flex flex-col gap-2 p-4 md:p-6">
                        <div class="w-full overflow-auto text-center grow max-h-128">
                            <table *ngIf="orders.length!=0" class="relative w-full table-auto">
                                <thead>
                                    <tr
                                        class="sticky top-0 z-10 items-center text-sm font-bold uppercase bg-slate-100 backdrop-blur-sm text-brand-blue-800">
                                        <th class="px-4 py-3 pl-6 text-start">
                                            <p>{{ 'settings.product' | transloco }}</p>
                                        </th>
                                        <th class="px-4 py-3 text-end">
                                            <p>{{ 'settings.status' | transloco }}</p>
                                        </th>
                                        <th class="px-4 py-3 text-end">
                                            <p>{{ 'settings.date' | transloco }}</p>
                                        </th>
                                        <th class="px-4 py-3 text-end">
                                            <p>{{ 'settings.price' | transloco }}</p>
                                        </th>
                                        <th class="px-4 py-3 text-center">
                                            <p>{{ 'settings.invoice' | transloco }}</p>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of orders"
                                        class="transition-all border-b border-dashed hover:bg-zinc-100">
                                        <td class="relative h-0 px-4 text-start max-h-20 hover:max-h-auto">
                                            <p class="overflow-hidden truncate peer">
                                                {{item.total_credit}} {{'settings.credit' | transloco}}
                                            </p>
                                        </td>
                                        <td class="flex items-center justify-end h-14">
                                            <p [ngClass]="{
                            'text-status-error-700 border-status-error-500 bg-status-error-500/50': item.status === 'canceled',
                            'text-green-700 border-brand-green-500 bg-brand-green-500/50': item.status === 'approved',
                            'text-status-warning-700 border-status-warning-500 bg-status-warning-500/50': item.status === 'pending'
                          }" class="px-2 py-1 text-sm border rounded-3xl">
                                                {{item.status}}
                                            </p>
                                        </td>
                                        <td class="truncate text-end">
                                            {{item.created_at | date: 'dd/MM/yyyy HH:mm'}}
                                        </td>
                                        <td class="truncate text-end">
                                            {{ math.trunc(item.total_price)| thousandSeparator }} ₺
                                        </td>
                                        <td class="w-20">
                                            <div class="flex items-center justify-end w-12 h-14">
                                                <button class="p-1 rounded-lg bg-brand-blue-700"
                                                    (click)="getOrderInvoice(item.id)">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                        viewBox="0 0 20 20" fill="none">
                                                        <path
                                                            d="M14.166 10.8333L9.99935 15M9.99935 15L5.83268 10.8333M9.99935 15V5"
                                                            stroke="white" stroke-width="1.66667" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <p *ngIf="orders.length==0" class="w-full text-center">
                                {{ 'settings.no_purchase' | transloco }}
                            </p>
                        </div>
                    </div>

                    <!-- Dil -->
                    <div *ngIf="activeTab === 'dil'" @fadeSlide
                        class="flex flex-col gap-3 px-4 pb-6 text-sm text-brand-blue-800 sm:px-6 md:px-8 md:pt-6">
                        <!-- Dil Seçenekleri -->
                        <div class="flex flex-col gap-3">
                            <button *ngFor="let lang of languages" (click)="changeLang(lang.code)"
                                class="flex items-center justify-between w-full gap-2 px-4 py-3 transition-all bg-white border shadow border-slate-200 hover:border-brand-blue-700 text-brand-blue-900 active:scale-95 group rounded-3xl"
                                [class.border-blue-700]="currentLang === lang.code"
                                [class.shadow-md]="currentLang === lang.code">

                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">{{ lang.flag }}</span>
                                    <span class="text-base font-medium">{{ lang.name }}</span>
                                </div>

                                <div *ngIf="currentLang === lang.code"
                                    class="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full text-brand-blue-700">
                                    <ng-icon name="heroCheck" class="w-4 h-4"></ng-icon>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Rapor -->
                    <div *ngIf="activeTab === 'rapor'" @fadeSlide
                        class="flex flex-col gap-3 px-4 pb-6 text-sm text-blue-950 sm:px-6 md:px-8 md:pt-6">
                        <!-- Form -->
                        <form [formGroup]="reportForm" class="relative flex flex-col gap-6">
                            <!-- Loading Overlay -->
                            <div *ngIf="isLoading"
                                class="absolute inset-0 z-10 flex items-center justify-center bg-white/80 rounded-2xl">
                                <div class="flex items-center gap-2">
                                    <ng-icon name="heroArrowPath"
                                        class="text-2xl text-brand-blue-600 animate-spin"></ng-icon>
                                    <span class="text-brand-blue-600">{{ 'shared.saving' | transloco }}</span>
                                </div>
                            </div>

                            <!-- Ayraç Seçimi -->
                            <div class="space-y-3">
                                <label class="text-base font-medium">{{ 'project_list.separator' | transloco }}</label>
                                <div class="flex flex-col gap-2">
                                    <div *ngFor="let option of separatorOptions" class="flex items-center">
                                        <button type="button"
                                            (click)="reportForm.get('separator').setValue(option.value)"
                                            class="flex items-center justify-between w-full gap-2 px-4 py-3 transition-all bg-white border shadow border-slate-200 hover:border-brand-blue-700 text-blue-950 active:scale-95 group rounded-3xl">
                                            <div class="flex items-center gap-3">
                                                <span class="font-medium">{{ option.label | transloco }}</span>
                                                <span class="text-gray-500">({{ 'project_list.example' | transloco }} {{
                                                    option.example }})</span>
                                            </div>
                                            <div *ngIf="reportForm.get('separator').value === option.value"
                                                class="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full text-brand-blue-700">
                                                <ng-icon name="heroCheck" class="w-4 h-4"></ng-icon>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Basamak Sayısı -->
                            <div class="space-y-3">
                                <label class="text-base font-medium">{{ 'project_list.precision' | transloco }}</label>
                                <div
                                    class="flex items-center justify-between p-4 bg-white border shadow rounded-3xl border-slate-200">
                                    <button (click)="decrementPrecision()"
                                        class="flex items-center justify-center w-8 h-8 transition-all bg-white border rounded-full border-slate-200 hover:border-brand-blue-700 active:scale-95">
                                        <ng-icon name="heroMinus" class="w-5 h-5 text-brand-blue-700"></ng-icon>
                                    </button>

                                    <span class="text-lg font-medium">{{ getZeroCount(precision) }}</span>

                                    <button (click)="incrementPrecision()"
                                        class="flex items-center justify-center w-8 h-8 transition-all bg-white border rounded-full border-slate-200 hover:border-brand-blue-700 active:scale-95">
                                        <ng-icon name="heroPlus" class="w-5 h-5 text-brand-blue-700"></ng-icon>
                                    </button>
                                </div>
                            </div>

                            <!-- Önizleme -->
                            <div class="p-4 bg-gray-50 rounded-3xl">
                                <div class="mb-2 text-base font-medium">{{ 'analyses.preview' | transloco }}</div>
                                <div class="text-gray-600">
                                    <span>{{ 'analyses.preview_number' | transloco }}</span>:
                                    <span class="font-medium text-blue-950">{{ getPreviewNumber() }}</span>
                                </div>
                            </div>

                            <!-- Kaydet Butonu -->
                            <div class="flex justify-end mt-4">
                                <button type="button" (click)="saveReportSettings()" class="secondary-blue-button">
                                    <ng-icon [name]="isLoading ? 'heroArrowPath' : 'featherSave'"
                                        [class.animate-spin]="isLoading" class="text-xl"></ng-icon>
                                    <span class="font-medium">
                                        {{ (isLoading ? 'shared.saving' : 'project_list.save') | transloco }}
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Gift -->
                    <div *ngIf="activeTab === 'gift'" @fadeSlide
                        class="flex flex-col gap-3 px-4 pb-6 text-sm text-blue-950 sm:px-6 md:px-8 md:pt-6">

                        <div class="pb-3 border-b border-slate-200">
                            <div class="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
                                <div class="flex-1">
                                    <div class="mt-2">
                                        <ng-container *ngIf="user?.gift_acceptance; else inactiveText">
                                            <p class="text-sm font-medium text-green-600">{{'profile.gift_pref_ok' |
                                                transloco }}</p>
                                        </ng-container>
                                        <ng-template #inactiveText>
                                            <p class="text-sm font-medium text-red-600">{{'profile.gift_pref_no' |
                                                transloco }}</p>
                                        </ng-template>
                                    </div>
                                    <div class="mt-3 text-xs text-gray-500">
                                        {{'profile.gift_credit_desc' | transloco }}
                                    </div>
                                </div>
                                <ng-container *ngIf="user?.gift_acceptance; else closedState">
                                    <button (click)="disableGiftAcceptance()"
                                        class="flex items-center justify-center gap-2 px-4 py-2.5 transition-all bg-white border shadow border-slate-200 hover:border-blue-700 text-blue-950 active:scale-95 group rounded-3xl">
                                        <span class="text-sm font-medium">{{'profile.change_pref_no' |
                                            transloco}}</span>
                                    </button>
                                </ng-container>
                                <ng-template #closedState>
                                    <button (click)="enableGiftAcceptance()"
                                        class="flex items-center justify-center gap-2 px-4 py-2.5 transition-all bg-white border shadow border-slate-200 hover:border-blue-700 text-blue-950 active:scale-95 group rounded-3xl">
                                        <span class="text-sm font-medium">{{'profile.change_pref_ok' |
                                            transloco}}</span>
                                    </button>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
import { trigger, transition, style, animate, state } from '@angular/animations';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PaymentService } from '@app/data/services/payment.service';
import { AgreementComponent } from '@app/shared/components/agreement/agreement.component';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
import { Subject, takeUntil } from 'rxjs';
import { AuthService } from '@app/modules/auth/auth.service';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { Router } from '@angular/router';
import { AdminService } from '@app/data/services/admin.service';
import { User } from '@app/data/models/user.interface';


@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
  animations: [
    trigger('fadeSlide', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),
    trigger('menuActive', [
      state('active', style({
        borderLeft: '4px solid #1d4ed8',
        backgroundColor: '#eff6ff',
        color: '#1d4ed8'
      })),
      state('inactive', style({
        borderLeft: '4px solid transparent',
        backgroundColor: 'transparent',
        color: '#374151'
      })),
      transition('inactive => active', animate('100ms ease-in')),
      transition('active => inactive', animate('100ms ease-out'))
    ])
  ]
})
export class SettingsComponent implements OnInit, OnDestroy {
  reportForm: FormGroup;
  profileForm: FormGroup;
  private destroy$ = new Subject<void>();
  separatorOptions = [
    { value: ',', label: 'project_list.comma', example: '1,234' },
    { value: '.', label: 'project_list.dot', example: '1.234' }
  ];
  user: User;
  isProfileLoading = false;
  hasProfileChanges = false;
  isEditingProfile = false;
  initialProfileValue: any;

  constructor(
    private fb: FormBuilder,
    @Inject(DIALOG_DATA) public data,
    public dialogRef: DialogRef<any>,
    private paymentService: PaymentService,
    private snotifyService: SnotifyService,
    private dialog: Dialog,
    private translocoService: TranslocoService,
    private authService: AuthService,
    private router: Router,
    private adminService: AdminService
  ) {
    this.reportForm = this.fb.group({
      separator: [{ value: ',', disabled: false }, Validators.required],
      precision: [{ value: 2, disabled: false }, [Validators.required, Validators.min(0), Validators.max(10)]]
    });

    this.profileForm = this.fb.group({
      university: [''],
      faculty: [''],
      department: [''],
      interests: ['']
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  activeTab: string = 'hesap';
  isMobileMenuOpen: boolean = false;
  hasChanges = false;
  isLoading = false;
  menuItems = [
    { id: 'hesap', icon: 'lucideUser', label: 'profile.account', title: 'profile.account_title' },
    { id: 'satin-alim', icon: 'lucideHistory', label: 'profile.purchase_history', title: 'profile.purchase_history_title' },
    { id: 'dil', icon: 'lucideGlobe', label: 'profile.language', title: 'profile.language_title' },
    { id: 'rapor', icon: 'lucideFileText', label: 'project_list.report_settings', title: 'project_list.report_settings_title' },
    { id: 'gift', icon: 'lucideGift', label: 'profile.gift_credit', title: 'profile.gift_credit_pref' }
  ];
  currentLang: string;

  languages = [
    { code: 'tr', name: 'Türkçe', flag: '🇹🇷' },
    { code: 'en', name: 'English', flag: '🇬🇧' }
  ];

  // Profile editing methods
  toggleEditProfile() {
    this.isEditingProfile = !this.isEditingProfile;
    if (this.isEditingProfile) {
      this.loadProfileData(); // Reload current data when starting to edit
    }
  }

  cancelEditProfile() {
    this.isEditingProfile = false;
    this.loadProfileData(); // Reset form to original values
    this.hasProfileChanges = false;
  }

  enableGiftAcceptance() {
    this.adminService.updateGiftState(this.user.id, true).subscribe({
      next: (response) => {
        this.snotifyService.success('Hediye alma tercihiniz açıldı.');
        this.user.gift_acceptance = true;
      },
      error: (error) => {
        console.error('Gift state update error:', error);
        this.snotifyService.error('Hediye alma tercihi açılamadı.');
      }
    });
  }

  disableGiftAcceptance() {
    this.adminService.updateGiftState(this.user.id, false).subscribe({
      next: (response) => {
        this.snotifyService.success('Hediye alma tercihiniz kapatıldı.');
        this.user.gift_acceptance = false;
      },
      error: (error) => {
        console.error('Gift state update error:', error);
        this.snotifyService.error('Hediye alma tercihi kapatılamadı.');
      }
    });
  }

  changeLang(langCode: string) {
    // Update the current language in the component
    this.currentLang = langCode;

    // Update the active language in the Transloco service
    this.translocoService.setActiveLang(langCode);

    // Save the language preference to localStorage
    localStorage.setItem('activeLang', langCode);

    // Update the language preference on the backend
    this.authService.updateLocale(langCode).subscribe({
      next: (response) => {
        // Show success notification
        this.snotifyService.success(
          this.translocoService.translate('profile.language_updated_success'),
          {
            position: 'centerBottom',
            timeout: 2000
          }
        );

        // Update the user object with the new locale
        if (this.user) {
          this.user.locale = langCode;
        }
      },
      error: (error) => {
        console.error('Error updating language preference:', error);
        this.snotifyService.error(
          this.translocoService.translate('profile.language_updated_error'),
          {
            position: 'centerBottom',
            timeout: 2000
          }
        );
      }
    });
  }

  activeTitle: string = 'profile.account_title';
  setActiveTab(menuId: string): void {
    this.activeTab = menuId;
    this.activeTitle = this.menuItems.find((item) => item.id === menuId).title;
    this.isMobileMenuOpen = false;
  }

  initialFormValue: any;
  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeDialog() {
    this.dialogRef.close();
  }

  private isEqual(obj1: any, obj2: any): boolean {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }

  ngOnInit() {
    this.getOrders();
    this.currentLang = this.translocoService.getActiveLang();
    this.loadReportSettings();
    this.watchFormChanges();
    this.loadUserGiftAcceptance();
    this.loadUser();
    this.watchProfileFormChanges();
  }

  private watchFormChanges() {
    this.reportForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.initialFormValue) {
          this.hasChanges = !this.isEqual(this.reportForm.value, this.initialFormValue);
        }
      });
  }

  private watchProfileFormChanges() {
    this.profileForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.initialProfileValue && this.isEditingProfile) {
          this.hasProfileChanges = !this.isEqual(this.profileForm.value, this.initialProfileValue);
        }
      });
  }

  loadUserGiftAcceptance(): void {
    this.adminService.getUser().subscribe(
      (response) => {
        this.user = response;
      },
      (error) => {
        console.error('Kullanıcı bilgileri alınamadı:', error);
        this.snotifyService.error('Kullanıcı bilgileri alınamadı.');
      });
  }

  orders = [];
  math: Math = Math;
  getOrders() {
    this.paymentService.getOrders().subscribe({
      next: (data) => {
        this.orders = data;
        this.orders = this.orders
          .filter((order) => order.status != "draft")
          .sort((a, b) => {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          })
          .map((order) => {
            return {
              ...order,
              total_credit: order.order_items.reduce((acc, item) => acc + item.credit * item.quantity, 0)
            }
          });
      },
      error: (error) => {
        console.log(error);
      }
    });
  }

  getOrderInvoice(order_id: string) {
    this.paymentService.getOrderInvoice(order_id).subscribe({
      next: (data) => {
        if (data.status == 'ready') {
          window.open(data.url, '_blank');
        } else {
          this.snotifyService.error(data.message, {
            position: 'centerBottom',
            timeout: 1000,
            pauseOnHover: false
          });
        }
      },
      error: (error) => {
        this.snotifyService.error(error.error.message, {
          position: 'centerBottom',
          timeout: 1000,
          pauseOnHover: false
        });
      }
    });
  }

  showContract(type: string) {
    const dialog = this.dialog.open(AgreementComponent, {
      data: { type: type }
    });
  }

  loadUser() {
    this.authService.getUser().subscribe({
      next: (user) => {
        this.user = user;
        this.loadProfileData();
      },
      error: (error) => {
        console.log(error);
      }
    });
  }

  loadProfileData() {
    if (this.user?.profile) {
      const profileData = {
        university: this.user.profile.university || '',
        faculty: this.user.profile.faculty || '',
        department: this.user.profile.department || '',
        interests: this.user.profile.interests || ''
      };
      this.profileForm.patchValue(profileData);
      this.initialProfileValue = { ...profileData }; // Create a copy
    } else {
      const emptyProfileData = {
        university: '',
        faculty: '',
        department: '',
        interests: ''
      };
      this.profileForm.patchValue(emptyProfileData);
      this.initialProfileValue = { ...emptyProfileData }; // Create a copy
    }
    this.hasProfileChanges = false;
  }

  // GÜNCELLENEN: saveProfile metodu - event tetiklemeli 🚀
  async saveProfile() {
    if (this.profileForm.valid && this.hasProfileChanges && !this.isProfileLoading) {
      this.isProfileLoading = true;

      try {
        const profileData = {
          profile: this.profileForm.value
        };

        console.log('💾 Saving profile data:', profileData);

        let response;
        if (this.user?.profile?.id) {
          // Update existing profile
          response = await this.authService.updateProfile(this.user.profile.id, profileData).toPromise();
          console.log('✅ Profile updated successfully');
        } else {
          // Create new profile
          response = await this.authService.createProfile(profileData).toPromise();
          console.log('✅ Profile created successfully');
        }

        // Update local user object
        if (this.user) {
          this.user.profile = response;
        }

        this.initialProfileValue = { ...this.profileForm.value }; // Create a copy
        this.hasProfileChanges = false;
        this.isEditingProfile = false; // Exit edit mode after saving

        // YENİ: Manual olarak AuthService'e bildir ki profile güncellendi
        // Bu zaten updateProfile/createProfile içinde otomatik tetikleniyor ama emin olmak için
        this.authService.notifyProfileUpdated();

        // YENİ: Eğer interests değiştiyse, bunu da özel olarak bildir
        const newInterests = this.profileForm.get('interests')?.value || '';
        if (newInterests.trim().length > 0) {
          this.authService.notifyUserInterestsChanged(newInterests);
          console.log('🎯 User interests manually notified:', newInterests);
        }

        this.snotifyService.success('Profil bilgileri başarıyla kaydedildi. Explore sayfası yeni ilgi alanlarınıza göre güncellenecek! 🎯', {
          position: 'centerBottom',
          timeout: 3000
        });

      } catch (error) {
        console.error('❌ Profile save error:', error);
        this.snotifyService.error('Profil bilgileri kaydedilirken bir hata oluştu', {
          position: 'centerBottom',
          timeout: 2000
        });
      } finally {
        this.isProfileLoading = false;
      }
    }
  }

  loadReportSettings() {
    this.setFormDisabled(true);
    this.authService.getReportSettings().subscribe({
      next: (settings) => {
        this.reportForm.patchValue(settings);
        this.precision = settings.precision;
        this.initialFormValue = settings;
        this.setFormDisabled(false);
        this.isLoading = false;
      },
      error: (error) => {
        console.log(error);
        const savedSettings = localStorage.getItem('reportSettings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          this.reportForm.patchValue(settings);
          this.initialFormValue = this.reportForm.value;
        } else {
          this.initialFormValue = this.reportForm.value;
        }
        this.setFormDisabled(false);
        this.isLoading = false;
      }
    });
  }

  async saveReportSettings() {
    if (this.reportForm.valid && this.hasChanges && !this.isLoading) {
      this.setFormDisabled(true);
      this.isLoading = true;
      try {
        await this.authService.updateReportSettings(this.reportForm.value).toPromise();
        localStorage.setItem('reportSettings', JSON.stringify(this.reportForm.value));
        this.initialFormValue = this.reportForm.value;
        this.hasChanges = false;

        this.snotifyService.success('Ayarlar başarıyla kaydedildi', {
          position: 'centerBottom',
          timeout: 2000
        });
      } catch (error) {
        this.snotifyService.error('Ayarlar kaydedilirken bir hata oluştu', {
          position: 'centerBottom',
          timeout: 2000
        });
      } finally {
        this.setFormDisabled(false);
        this.isLoading = false;
      }
    }
  }

  private setFormDisabled(disabled: boolean) {
    if (disabled) {
      this.reportForm.get('separator')?.disable();
      this.reportForm.get('precision')?.disable();
    } else {
      this.reportForm.get('separator')?.enable();
      this.reportForm.get('precision')?.enable();
    }
  }

  getPreviewNumber(): string {
    const separator = this.reportForm.get('separator')?.value;
    const precision = this.reportForm.get('precision')?.value;
    const number = 1234.5678;
    return number.toFixed(precision).replace('.', separator);
  }

  deleteUser() {
    const dialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: this.translocoService.translate('shared.confirm.delete_account.title'),
        content: this.translocoService.translate('shared.confirm.delete_account.content'),
        confirm: this.translocoService.translate('shared.confirm.delete_account.confirm'),
        cancel: this.translocoService.translate('shared.confirm.delete_account.cancel'),
      }
    });
    dialog.closed.subscribe((result) => {
      if (result) {
        this.authService.deleteUser().subscribe({
          next: (data) => {
            this.snotifyService.success(
              this.translocoService.translate('notification.settings.delete_account.success.message'),
              this.translocoService.translate('notification.settings.delete_account.success.title'),
              {
                position: 'centerBottom',
                timeout: 1000,
                pauseOnHover: false,
              }).on('beforeHide', (toast) => {
                this.router.navigateByUrl('/login');
                return true;
              });
          },
          error: (e) => {
            this.snotifyService.error(
              this.translocoService.translate('notification.settings.delete_account.error.message'),
              this.translocoService.translate('notification.settings.delete_account.error.title'),
              {
                position: 'centerBottom',
                timeout: 1000,
                pauseOnHover: false,
              })
          }
        });
      }
    }
    );
  }

  isSendingMail = false;
  forgotPassword() {
    const body = {
      user: {
        email: localStorage.getItem('email'),
      }
    };
    if (body) {
      this.isSendingMail = true;
      this.authService.sendPasswordResetEmail(body).subscribe(
        (response) => {
          this.isSendingMail = false;
          this.snotifyService.success(
            this.translocoService.translate('auth.mail-input.' + response.body.message),
            this.translocoService.translate('auth.mail-input.email_sent'),
            {
              position: 'centerBottom',
              timeout: 2000,
            }
          );
        },
        (error) => {
          this.isSendingMail = false;
          this.snotifyService.error(
            this.translocoService.translate('auth.login.error'),
            this.translocoService.translate('auth.mail-input.' + error.error.error),
            {
              position: 'centerBottom',
              timeout: 2000,
            }
          );
        }
      );
    } else {
      this.snotifyService.error(
        this.translocoService.translate('auth.mail-input.no_email'),
        {
          position: 'centerBottom',
          timeout: 2000,
        }
      );
    }
  }

  getZeroCount(count: number): string {
    const separator = this.reportForm.get('separator')?.value;
    let result = separator;
    for (let i = 1; i <= count; i++) {
      result += i;
    }
    return result;
  }

  precision: number = 2;

  decrementPrecision() {
    if (this.reportForm.get('precision').value > 0) {
      const newValue = this.reportForm.get('precision').value - 1;
      this.reportForm.get('precision').setValue(newValue);
      this.precision = newValue;
      this.hasChanges = !this.isEqual(this.reportForm.value, this.initialFormValue);
    }
  }

  incrementPrecision() {
    if (this.reportForm.get('precision').value < 10) {
      const newValue = this.reportForm.get('precision').value + 1;
      this.reportForm.get('precision').setValue(newValue);
      this.precision = newValue;
      this.hasChanges = !this.isEqual(this.reportForm.value, this.initialFormValue);
    }
  }
}
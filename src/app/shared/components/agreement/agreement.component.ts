import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ContractsService } from '@app/data/services/contracts.service';

@Component({
  selector: 'app-agreement',
  templateUrl: './agreement.component.html',
  styleUrls: ['./agreement.component.scss']
})
export class AgreementComponent implements OnInit {
  selected = 'cookie';
  constructor(
    @Inject(DIALOG_DATA) public data,
    public dialogRef: DialogRef<any>,
    private c : ContractsService,
    private sanitizer : DomSanitizer
  ) { }
  contract : any = {
    cookie: 'https://drive.google.com/file/d/1iBK4-1Qgy2X-mnuFsslo0uYzala_bATq/preview',
    membership_agreement: 'https://drive.google.com/file/d/1RI5THueFF8lhzHW8Tm7L7aR5nweOMBAG/preview',
    privacy_policy: 'https://drive.google.com/file/d/13f5BzqCQ0vV61mL6wyQYhbsymbqlf3Pu/preview',
  };
  ngOnInit(): void {
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => {
      this.close();
    }
    );
    if (this.data.payment_method) {
      this.getContractAgreement();
    }else{
      this.selected = this.data.type
    }
  }
  safePdfUrl!: SafeResourceUrl;
  getContractAgreement(): void {
    var payload = {
      contract_type: 'distance_sale_agreement',
      buyer_name: localStorage.getItem('username'),
      buyer_email: localStorage.getItem('email'),
      order_date: new Date().toISOString(),
      payment_method: this.data.payment_method,
    }
    this.c.getDistanceSalesContracts(payload).subscribe((res:any) => {
      this.agreement = res;
      this.agreement= this.sanitizer.bypassSecurityTrustHtml(res.contract) as string;
    });
  }
  agreement = '';
  canAccept = false;

  isAccepted = false;
  acceptAgreement(): void {
    this.isAccepted = true;
    this.dialogRef.close(this.isAccepted);
  }
  close(): void {
    this.isAccepted = false;
    this.dialogRef.close(this.isAccepted);
  }
  @HostListener('window:keyup.esc') onKeyUp() {
    this.close();
  }

  @HostListener("window:beforeunload", ["$event"]) unloadHandler(event: Event) {
    this.close();
  }
}
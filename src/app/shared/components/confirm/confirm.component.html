<div [@bouncyScale]="animationState"  class="flex flex-col items-center justify-center text-center bg-white rounded-3xl max-w-144">
    <div class="flex flex-col items-center justify-center p-5 pb-2 text-center ">
        <div class="flex items-center justify-center p-2 rounded-full bg-status-warning-100">
            <ng-icon name="lucideTriangleAlert" class="text-xl text-status-warning-500"></ng-icon>
        </div>
        <p class="flex flex-col w-full gap-2 mb-2">
            <span class="text-lg font-semibold">
                {{data.title}}
            </span>
            <span *ngIf="data.content" [ngClass]="{'text-blue-600 bg-blue-200': isDiagnose ,'hidden': data.content == ' '}"
                class="w-full p-2 text-sm font-medium text-red-600 bg-red-200 rounded-3xl ">
                {{data.content}}
            </span>
        </p>
    </div>
    <div class="flex justify-end w-full gap-4 p-3 bg-neutral-100 rounded-b-3xl">
        <button [ngClass]="data.confirm ? '' :'w-full' " *ngIf="data.cancel" class="secondary-blue-button" (click)="cancel()">{{data.cancel}}
        </button>
        <button *ngIf="data.confirm" class="px-2 py-1 text-white rounded-3xl " [ngClass]="isDiagnose ? 'primary-blue-button': ' secondary-status-error-button' "
            (click)="confirm()">
            {{data.confirm}}
        </button>
    </div>
</div>
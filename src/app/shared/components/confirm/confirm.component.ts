import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';
@Component({
  selector: 'app-confirm',
  templateUrl: './confirm.component.html',
  styleUrls: ['./confirm.component.scss'],
  animations: [
    trigger('bouncyScale', [
      state('in', style({ transform: 'scale(1)' })),
      state('out', style({ transform: 'scale(0)' })),
      transition('void => in', [
        style({ transform: 'scale(0)' }),
        animate('300ms cubic-bezier(0.68, -0.55, 0.27, 1.55)')
      ]),
      transition('in => out', [
        animate('300ms cubic-bezier(0.68, -0.55, 0.27, 1.55)', style({ transform: 'scale(0)' }))
      ])
    ])
  ]
})
export class ConfirmComponent implements OnInit {
  constructor(
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<boolean>,

  ) {
    this.setupDialogBehavior();
   }
  private setupDialogBehavior(): void {
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => this.cancel());
  }
  animationState = 'in';
  isDiagnose: boolean = false;
  ngOnInit(): void {
    if (this.data?.diagnose) {
      this.isDiagnose = true
    }
  }
  confirm() {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(true), 300);
  }
  cancel() {
    this.animationState = 'out';
    setTimeout(() => this.dialogRef.close(false), 300);
  }
}

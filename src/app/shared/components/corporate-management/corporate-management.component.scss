:host {
  display: block;
}

.table-container {
  overflow-x: auto;
  max-height: 400px;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

// Make sure the first column has a higher z-index if you want it to appear above other columns when scrolling horizontally
.sticky-header th:first-child {
  z-index: 11;
}

// Fix for tab content scrolling
.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.tab-content-body {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

// Ensure table containers properly scroll
.table-scroll-container {
  overflow-y: auto;
  overflow-x: auto;
  flex: 1;
}

// Fix for users section scrolling
[class*="flex-col flex-1 overflow-hidden"] {
  display: flex;
  flex-direction: column;
  height: 100%;

  > [class*="overflow-x-auto overflow-y-auto"] {
    flex: 1;
    overflow-y: auto;
    overflow-x: auto;
  }
}

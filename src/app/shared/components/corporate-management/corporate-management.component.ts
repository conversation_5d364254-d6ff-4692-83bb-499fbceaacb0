import { Dialog, DialogRef } from '@angular/cdk/dialog';
import { Component, OnInit } from '@angular/core';
import { SnotifyService } from 'ng-alt-snotify';
import { ConfirmComponent } from '../confirm/confirm.component';
import { TranslocoService } from '@ngneat/transloco';
import { CorporateService, UnitActivity, UnitUser, UserCreditSummary, UserCreditSummaryResponse, UnitProject } from '@app/data/services/corporate.service';
import { finalize, tap } from 'rxjs/operators';
import * as XLSX from 'xlsx';
import { forkJoin, from, Observable, of } from 'rxjs';

// Interface for simplified user objects in unit lists
interface UnitMember {
  id: number;
  name: string;
  email: string;
  projects?: UnitProject[]; // Add projects to member
}
// Icons mapping for each unit type with corresponding Lucide icon names
const UNIT_TYPE_ICONS = {
  consultancy: 'briefcase',
  academic: 'graduation-cap',
  goverment: 'landmark',
  journal: 'book-open',
  survey: 'clipboard-list',
  healthcare: 'heart-pulse',
  other: 'building'
} as const;

type UnitType = 'consultancy' | 'academic' | 'goverment'|  'journal' | 'survey' | 'healthcare' | 'other'; // Added for department type

// Interface for the unit
interface Unit {
  id: number;
  utype?: UnitType;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  available_credits: number;
  managers: UnitMember[];
  members: UnitMember[];
  projects?: UnitProject[]; // Add projects to unit
}

interface UserRole {
  id: number;
  name: string;
  resource_type: string | null;
  resource_id: number | null;
  created_at: string;
  updated_at: string;
}

interface CorporateUser {
  id: number;          // User ID from database
  user_id: number;     // User ID for API operations
  username: string;    // Display name (combination of name + surname)
  email: string;       // Email address
  phone_number?: string; // Optional phone number
  status: 'user_registered' | 'user_not_registered' | 'user_not_confirmed'; // Account status
  statusText: string;  // Human readable status for display
  role: 'admin' | 'user'; // Role in the corporate unit
  banned: boolean;     // Whether user is banned
  created_at: string;  // When the user was created
  updated_at: string;  // When the user was last updated
  roles: UserRole[];   // Full roles array for additional checks
  reason: string | null; // Reason for adding user
  credits: {
    total: number;     // Total credits assigned
    used: number;      // Credits used
    remaining: number; // Credits remaining
  };
}

// New interface for parsed Excel user data
interface ParsedExcelUser {
  email: string;
  reason?: string;
  credit?: number;
  valid: boolean;
  error?: string;
}

@Component({
  selector: 'app-corporate-management',
  templateUrl: './corporate-management.component.html',
  styleUrls: ['./corporate-management.component.scss']
})
export class CorporateManagementComponent implements OnInit {
  // Make Math available to the template
  Math = Math;
  // Make UNIT_TYPE_ICONS available to the template
  UNIT_TYPE_ICONS = UNIT_TYPE_ICONS;

  departmentName = '';
  departmentType = ''; // Added for department type
  departmentDescription = ''; // Added for department description
  credits = {
    total: 0,
    transferred: 0,
    remaining: 0,
    used: 0,
    unused: 0
  };

  // Unit selection related properties
  units: Unit[] = [];
  selectedUnitId: number | null = null;
  showUnitSelection = false;
  loading = {
    units: false,
    users: false,
    credits: false,
    operation: false,
    activities: false // Add loading state for activities
  };

  // Credit amount and reason for current operation
  creditAmount: number = 0;
  creditReason: string = '';
  selectedUser: CorporateUser | null = null;
  showCreditInput: boolean = false;
  creditOperation: 'give' | 'remove' | null = null;

  // User addition
  showUserAddForm: boolean = false;
  newUserEmail: string = '';
  newUserReason: string = '';
  newUserInitialCredit: number = 0;

  users: CorporateUser[] = [];

  // Search and filter properties
  searchTerm: string = '';
  statusFilter: string = 'all'; // Changed from union type to string type

  // Project-related properties
  projects: UnitProject[] = [];
  projectSearchTerm: string = '';
  projectStatusFilter: string = 'all';
  activeTab: 'users' | 'projects' | 'activities' = 'users'; // Add 'activities' as a tab option

  // Activities-related properties
  activities: UnitActivity[] = [];
  activitySearchTerm: string = '';
  activityTypeFilter: string = 'all';
  activityStatusFilter: string = 'all';

  // Excel import related properties
  showExcelImportForm: boolean = false;
  excelFile: File | null = null;
  parsedUsers: ParsedExcelUser[] = [];
  importProgress: number = 0;
  importResults = {
    total: 0,
    success: 0,
    failed: 0
  };

  // Get filtered activities based on search term and filters
  get filteredActivities(): UnitActivity[] {
    return this.activities.filter(activity => {
      // Apply type filter
      if (this.activityTypeFilter !== 'all' && activity.atype !== this.activityTypeFilter) {
        return false;
      }

      // Apply status filter
      if (this.activityStatusFilter !== 'all' && activity.status !== this.activityStatusFilter) {
        return false;
      }

      // Apply search term filter (case insensitive) to user email or manager email
      if (this.activitySearchTerm) {
        const searchLower = this.activitySearchTerm.toLowerCase();
        const userEmail = activity.user?.email?.toLowerCase() || '';
        const managerEmail = activity.unit_manager?.email?.toLowerCase() || '';
        const activityEmail = activity.email?.toLowerCase() || '';

        if (!userEmail.includes(searchLower) &&
            !managerEmail.includes(searchLower) &&
            !activityEmail.includes(searchLower)) {
          return false;
        }
      }

      return true;
    });
  }

  // Get filtered users based on search term and status filter
  get filteredUsers(): CorporateUser[] {
    return this.users.filter(user => {
      // Apply status filter
      if (this.statusFilter != 'all' && user.status != this.statusFilter) {
        return false;
      }

      // Apply search term filter (case insensitive)
      if (this.searchTerm && !user.email.toLowerCase().includes(this.searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });
  }

  // Get filtered projects based on search term and status filter
  get filteredProjects(): UnitProject[] {
    return this.projects.filter(project => {
      // Apply status filter
      if (this.projectStatusFilter !== 'all' && project.status !== this.projectStatusFilter) {
        return false;
      }

      // Apply search term filter (case insensitive)
      if (this.projectSearchTerm && !project.name.toLowerCase().includes(this.projectSearchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });
  }

  // Status filter options for display
  get statusFilterOptions() {
    return [
      { value: 'all', label: this.transloco.translate('corporate-management.status.all') },
      { value: 'user_registered', label: this.transloco.translate('corporate-management.status.user_registered') },
      { value: 'user_not_registered', label: this.transloco.translate('corporate-management.status.user_not_registered') },
      { value: 'user_not_confirmed', label: this.transloco.translate('corporate-management.status.user_not_confirmed') }
    ];
  }

  // Activity type filter options
  get activityTypeFilterOptions() {
    return [
      { value: 'all', label: this.transloco.translate('corporate-management.activities.type.all') },
      { value: 'credit_send', label: this.transloco.translate('corporate-management.activities.type.credit_send') },
      { value: 'credit_refund', label: this.transloco.translate('corporate-management.activities.type.credit_refund') },
      { value: 'user_add', label: this.transloco.translate('corporate-management.activities.type.user_add') },
      { value: 'user_remove', label: this.transloco.translate('corporate-management.activities.type.user_remove') }
    ];
  }

  // Activity status filter options
  get activityStatusFilterOptions() {
    return [
      { value: 'all', label: 'activities.status.all' },
      { value: 'done', label: 'activities.status.done' },
      { value: 'cancelled', label: 'activities.status.cancelled' },
      { value: 'pending', label: 'activities.status.pending' }
    ];
  }

  constructor(
    public dialogRef: DialogRef<any>,
    private snotifyService: SnotifyService,
    private dialog: Dialog,
    private transloco: TranslocoService,
    private corporateService: CorporateService
  ) {
    dialogRef.disableClose = true;
  }

  ngOnInit(): void {
    this.loadUnits();
  }

  // Load available units for the user
  loadUnits() {
    this.loading.units = true;
    this.corporateService.getUnits().pipe(
      finalize(() => this.loading.units = false)
    ).subscribe({
      next: (units: Unit[]) => {
        this.units = units;

        if (this.units.length === 0) {
          // No units available
          this.showError('units.no_units_available', null);
          this.dialogRef.close();
        } else if (this.units.length === 1) {
          // Only one unit, select it automatically
          this.selectUnit(this.units[0].id);
        } else {
          // Multiple units, show selection dialog
          this.showUnitSelection = true;
        }
      },
      error: (err) => {
        this.showError('units.load_error', err);
        this.dialogRef.close();
      }
    });
  }

  // Select a unit and load its data
  selectUnit(unitId: number) {
    // Reset data first to avoid showing previous unit's data
    this.users = [];
    this.activities = []; // Reset activities
    this.credits = {
      total: 0,
      transferred: 0,
      remaining: 0,
      used: 0,
      unused: 0
    };

    // Set new unit ID
    this.selectedUnitId = unitId;
    // Reset to users tab when switching units
    this.activeTab = 'users';

    // Important: Set the unit ID in the service
    this.corporateService.setUnitId(unitId.toString());
    this.showUnitSelection = false;
    // Load unit details for name and description
    this.corporateService.getUnit(unitId).subscribe({
      next: (unit: Unit) => {
        this.departmentName = unit.name;
        this.departmentType = unit.utype ? unit.utype : 'other'; // Set department type if available
        this.departmentDescription = unit.description;
        // Force a delay to ensure unitId is properly set before loading data
        setTimeout(() => {
          this.loadUnitData();
        }, 100);
      },
      error: (err) => {
        this.showError('units.details_error', err);
      }
    });
  }
isAnyProjectHasStatus(){
  return this.projects.some(project => project.status != '');
}
  loadUnitData() {
    this.loading.users = true;
    this.loading.credits = true;
    this.loading.activities = true; // Add loading state for activities

    // Load users with credit summary - now contains all necessary data
    this.corporateService.getUserCreditSummary().pipe(
      finalize(() => this.loading.users = false)
    ).subscribe({
      next: (data: UserCreditSummaryResponse) => {
        // Map users with the complete data from the response
        this.users = data.users.map(user => this.mapToCorporateUserFromSummary(user));

        // Extract projects from users if available
        this.extractProjectsFromUsers(data.users);
      },
      error: (err) => {
        this.loading.users = false;
        this.showError('users.load_error', err);
      }
    });

    // Load credit summary
    this.corporateService.getUnitCreditSummary().pipe(
      finalize(() => this.loading.credits = false)
    ).subscribe({
      next: (data) => {
        // Update to use the new credit summary format
        this.credits = {
          total: data.total || 0,
          transferred: data.transferred || 0,
          remaining: data.remaining || 0,
          used: data.used || 0,
          unused: data.unused || 0
        };
      },
      error: (err) => {
        this.showError('credits.load_error', err);
      }
    });

    // Load unit activities
    this.corporateService.getUnitActivities().pipe(
      finalize(() => this.loading.activities = false)
    ).subscribe({
      next: (data: UnitActivity[]) => {
        this.activities = data;
      },
      error: (err) => {
        this.showError('activities.load_error', err);
      }
    });

    // Load unit details for additional information
    this.corporateService.getUnit(this.selectedUnitId as number).subscribe({
      next: (unit: Unit) => {
        if (unit.members?.length > 0) {
          // Extract projects from unit members if available
          this.extractProjectsFromMembers(unit.members);
        }

        // If no projects were loaded, use dummy data for testing
        if (this.projects.length === 0) {
        }
      },
      error: (err) => {
        this.showError('units.details_error', err);
        // Load dummy projects even on error for testing purposes
      }
    });
  }

  // New method to extract projects from users
  private extractProjectsFromUsers(users: UserCreditSummary[]): void {
    const allProjects: UnitProject[] = [];

    // Collect projects from all users
    users.forEach(user => {
      if (user.projects && user.projects.length > 0) {
        // Add unique projects to the collection with owner information
        user.projects.forEach(project => {
          if (!allProjects.some(p => p.id === project.id)) {
            // Clone project and add owner information if not already present
            const projectWithOwner = {
              ...project,
              owner: project.owner || {
                id: user.id || 0,
                email: user.email,
              }
            };
            allProjects.push(projectWithOwner);
          }
        });
      }
    });

    if (allProjects.length > 0) {
      this.projects = allProjects
    }
  }

  // New method to extract projects from unit members
  private extractProjectsFromMembers(members: UnitMember[]): void {
    const allProjects: UnitProject[] = [];

    // Collect projects from all members
    members.forEach(member => {
      if (member.projects && member.projects.length > 0) {
        // Add unique projects to the collection with owner information
        member.projects.forEach(project => {
          if (!allProjects.some(p => p.id === project.id)) {
            // Clone project and add owner information if not already present
            const projectWithOwner = {
              ...project,
              owner: project.owner || {
                id: member.id,
                email: member.email,
                name: member.name
              }
            };
            allProjects.push(projectWithOwner);
          }
        });
      }
    });

    if (allProjects.length > 0) {
      this.projects = allProjects;
    }
  }

  // New method to map from the enhanced user credit summary format to CorporateUser
  mapToCorporateUserFromSummary(user: UserCreditSummary): CorporateUser {
    // Extract role information - now role is an array of role objects
    let isAdmin = false;
    const rolesList: UserRole[] = [];

    if (Array.isArray(user.role)) {
      // Convert API roles to the format expected by the component
      rolesList.push(...user.role.map(r => ({
        id: r.id,
        name: r.name,
        resource_type: r.resource_type,
        resource_id: r.resource_id,
        created_at: r.created_at,
        updated_at: r.created_at // Using created_at as updated_at since it's not provided
      })));

      // Check for admin roles specifically for the current unit
      isAdmin = user.role.some(r =>
        (r.name === 'unit_manager' || r.name === 'admin') &&
        r.resource_type === 'Unit' &&
        r.resource_id.toString() === this.selectedUnitId?.toString()
      );
    }

    // Determine user status based on the status text

    // Create the corporate user object with the new format
    return {
      id: user.id || 0,
      user_id: user.id || 0,
      username: '', // Name still not provided in the format
      email: user.email,
      status: user.status,
      statusText: user.status,
      role: isAdmin ? 'admin' : 'user',
      banned: false,
      created_at: '', // Not provided in format
      updated_at: '', // Not provided in format
      roles: rolesList,
      reason: user.reason, // Include the reason field
      credits: {
        total: user.credit?.total || 0,
        used: user.credit?.used || 0,
        remaining: user.credit?.unused || 0 // Unused is now the equivalent of remaining
      }
    };
  }

  // Map from API User to component User model
  mapToCorporateUser(user: any): CorporateUser {
    // Determine if user is a unit manager based on roles
    const isManager = Array.isArray(user.roles) && user.roles.some(role =>
      role.name === 'unit_manager' &&
      role.resource_type === 'Unit' &&
      (role.resource_id === parseInt(this.corporateService.getUnitId()) || role.resource_id?.toString() === this.corporateService.getUnitId())
    );

    // Create the corporate user object
    return {
      id: user.id,
      user_id: user.id, // Use the same ID for both fields if no separate user_id exists
      username: `${user.name} ${user.surname}`.trim(),
      email: user.email,
      phone_number: user.phone_number,
      status: user.status,
      reason: user.reason || null,
      statusText: this.getStatusText(status),
      role: isManager ? 'admin' : 'user',
      banned: user.banned || false,
      created_at: user.created_at,
      updated_at: user.updated_at,
      roles: user.roles || [],
      credits: {
        // If credits info is available in the response, use it
        // Otherwise initialize with zeros
        total: user.credits?.total || 0,
        used: user.credits?.used || 0,
        remaining: user.credits?.remaining || 0
      }
    };
  }

  getStatusText(status: string): string {
    return this.transloco.translate(`corporate-management.status.${status}`);
  }

  closeModal() {
    this.dialogRef.close();
  }

  // Show user add form
  showAddUserForm() {
    this.newUserEmail = '';
    this.showUserAddForm = true;
  }

  // Cancel user add operation
  cancelAddUser() {
    this.showUserAddForm = false;
    this.newUserEmail = '';
    this.newUserReason = '';
    this.newUserInitialCredit = 0;
  }

  // Validate email format
  validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }

  // Process user addition with credit assignment
  processAddUser() {
    if (!this.newUserEmail || !this.validateEmail(this.newUserEmail)) {
      this.snotifyService.error(this.transloco.translate('corporate-management.notifications.invalid_email'), 'Hata', {
        position: 'centerBottom',
        timeout: 3000
      });
      return;
    }

    // Check if email already exists
    const emailExists = this.users.some(user =>
      user.email && user.email.toLowerCase() === this.newUserEmail.toLowerCase()
    );

    if (emailExists) {
      this.snotifyService.error(this.transloco.translate('corporate-management.notifications.email_exists'), 'Hata', {
        position: 'centerBottom',
        timeout: 3000
      });
      return;
    }

    // Add user with email, reason, and initial credit
    this.addUser(this.newUserEmail, this.newUserReason, this.newUserInitialCredit);
  }

  // Updated method to add user with optional credit
  addUser(email: string, reason?: string, initialCredit?: number) {
    this.loading.operation = true;

    this.corporateService.addUserToUnit(email, reason).pipe(
      finalize(() => this.loading.operation = false)
    ).subscribe({
      next: (response) => {
        // Show success message
        this.snotifyService.success(
          this.transloco.translate('corporate-management.notifications.user_added', { email }),
          'Başarılı',
          { position: 'centerBottom', timeout: 3000 }
        );

        // If initial credit is specified and greater than 0, assign credits
        if (initialCredit && initialCredit > 0) {
          // Check if the user is active before assigning credits
          this.corporateService.getUserCreditSummary().subscribe({
            next: (data) => {
              const addedUser = data.users.find(u => u.email.toLowerCase() === email.toLowerCase());

              if (addedUser && addedUser.status === 'user_registered') {
                // User is active, assign credits
                this.assignInitialCredit(addedUser.id || 0, email, initialCredit, reason);
              } else {
                // User is not active, show notification
                const statusKey = addedUser ? addedUser.status : 'user_not_registered';
                this.snotifyService.warning(
                  this.transloco.translate('corporate-management.notifications.credit_not_assigned_inactive', {
                    email,
                    status: this.transloco.translate(`corporate-management.status.${statusKey}`)
                  }),
                  'Uyarı',
                  { position: 'centerBottom', timeout: 4000 }
                );
              }
            },
            error: (err) => {
              console.error('Error checking user status:', err);
            }
          });
        }

        // Hide form and refresh user list
        this.showUserAddForm = false;
        this.newUserEmail = '';
        this.newUserReason = '';
        this.newUserInitialCredit = 0;
        this.loadUnitData();
      },
      error: (err) => {
        if (err.status === 422) {
          this.snotifyService.error(
            this.transloco.translate('corporate-management.notifications.user_already_in_unit', { email }),
            'Hata',
            { position: 'centerBottom', timeout: 3000 }
          );
        } else {
          this.showError('users.add_error', err);
        }
      }
    });
  }

  // New method to assign initial credit to a user
  assignInitialCredit(userId: number, email: string, amount: number, reason?: string) {
    if (amount <= 0 || this.credits.remaining < amount) {
      this.snotifyService.error(
        this.transloco.translate('corporate-management.notifications.insufficient_unit_credits', {
          required: amount,
          available: this.credits.remaining
        }),
        'Hata',
        { position: 'centerBottom', timeout: 3000 }
      );
      return;
    }

    const creditReason = reason || this.transloco.translate('corporate-management.credit_dialog.initial_credit_reason');

    this.corporateService.sendCreditToUser(userId, amount, creditReason).subscribe({
      next: () => {
        this.snotifyService.success(
          this.transloco.translate('corporate-management.notifications.initial_credit_assigned', {
            email,
            amount
          }),
          'Başarılı',
          { position: 'centerBottom', timeout: 3000 }
        );

        // Refresh data to update credit information
        this.loadUnitData();
      },
      error: (err) => {
        this.showError('credits.assign_initial_error', err);
      }
    });
  }

  // Toggle user role between admin and user
  toggleUserRole(user: CorporateUser) {
    // Check if user account is active
    if (!this.isUserActive(user)) {
      this.snotifyService.warning(
        this.transloco.translate('corporate-management.notifications.inactive_user_role', { email: user.email }),
        'Uyarı',
        { position: 'centerBottom', timeout: 3000 }
      );
      return;
    }

    const newRole = user.role === 'admin' ? 'user' : 'admin';
    const confirmKey = newRole === 'admin' ? 'change_role_admin' : 'change_role_user';
    const contentKey = newRole === 'admin' ? 'change_role_admin_content' : '';

    const confirmDialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: this.transloco.translate(`corporate-management.confirmations.${confirmKey}`, { email: user.email }),
        content: contentKey ? this.transloco.translate(`corporate-management.confirmations.${contentKey}`) : '',
        confirm: this.transloco.translate('corporate-management.confirmations.yes'),
        cancel: this.transloco.translate('corporate-management.confirmations.no')
      }
    });

    confirmDialog.closed.subscribe(result => {
      if (result) {
        this.changeUserRole(user, newRole);
      }
    });
  }

  // Change user role
  changeUserRole(user: CorporateUser, newRole: 'admin' | 'user') {
    this.loading.operation = true;

    const operation = newRole === 'admin' ?
      this.corporateService.addManagerToUnit(user.user_id) :
      this.corporateService.removeManagerFromUnit(user.user_id);

    operation.pipe(
      finalize(() => this.loading.operation = false)
    ).subscribe({
      next: () => {
        user.role = newRole;

        const roleText = this.transloco.translate(`corporate-management.role.${newRole}`);
        this.snotifyService.success(
          this.transloco.translate('corporate-management.notifications.role_changed', {
            email: user.email,
            role: roleText
          }),
          'Başarılı',
          { position: 'centerBottom', timeout: 2000 }
        );
      },
      error: (err) => {
        this.showError('users.role_change_error', err);
      }
    });
  }

  // Get role display text
  getRoleDisplayText(role: string): string {
    return this.transloco.translate(`corporate-management.role.${role}`);
  }

  // Check if a user account is active (registered)
  isUserActive(user: CorporateUser): boolean {
    return user.status === 'user_registered';
  }

  // Show credit input dialog for giving credits
  showGiveCreditInput(user: CorporateUser) {
    if (!this.isUserActive(user)) {
      this.snotifyService.warning(
        this.transloco.translate('corporate-management.notifications.inactive_user', { email: user.email }),
        'Uyarı',
        { position: 'centerBottom', timeout: 3000 }
      );
      return;
    }

    this.selectedUser = user;
    this.creditOperation = 'give';
    this.creditAmount = 0;
    this.creditReason = '';
    this.showCreditInput = true;
  }

  // Show credit input dialog for removing credits
  showRemoveCreditInput(user: CorporateUser) {
    if (!this.isUserActive(user)) {
      this.snotifyService.warning(
        this.transloco.translate('corporate-management.notifications.inactive_user', { email: user.email }),
        'Uyarı',
        { position: 'centerBottom', timeout: 3000 }
      );
      return;
    }

    this.selectedUser = user;
    this.creditOperation = 'remove';
    this.creditAmount = 0;
    this.creditReason = '';
    this.showCreditInput = true;
  }

  // Cancel credit operation
  cancelCreditOperation() {
    this.showCreditInput = false;
    this.selectedUser = null;
    this.creditOperation = null;
    this.creditAmount = 0;
    this.creditReason = '';
  }

  // Process credit operation after amount has been entered
  processCreditOperation() {
    if (!this.selectedUser || !this.creditOperation || this.creditAmount <= 0) {
      return;
    }

    // Double check the user is still active
    if (!this.isUserActive(this.selectedUser)) {
      this.snotifyService.error(
        this.transloco.translate('corporate-management.notifications.inactive_user', { email: this.selectedUser.email }),
        'Hata',
        { position: 'centerBottom', timeout: 3000 }
      );
      this.cancelCreditOperation();
      return;
    }

    const operation = this.creditOperation === 'give' ? 'give_credit' : 'take_credit';
    const user = this.selectedUser;
    const amount = this.creditAmount;
    const reason = this.creditReason;

    // Open confirmation dialog
    const confirmDialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: this.transloco.translate(`corporate-management.confirmations.${operation}`, {
          amount,
          email: user.email
        }),
        content: '',
        confirm: this.transloco.translate('corporate-management.confirmations.yes'),
        cancel: this.transloco.translate('corporate-management.confirmations.no')
      }
    });

    confirmDialog.closed.subscribe(result => {
      if (result) {
        if (this.creditOperation === 'give') {
          this.giveCredit(user, amount, reason);
        } else {
          this.removeCredit(user, amount, reason);
        }
      }
      this.cancelCreditOperation();
    });
  }

  // Give credits to user with specified amount and optional reason
  giveCredit(user: CorporateUser, amount: number, reason?: string) {
    this.loading.operation = true;

    this.corporateService.sendCreditToUser(user.user_id, amount, reason).pipe(
      finalize(() => this.loading.operation = false)
    ).subscribe({
      next: () => {
        // Refresh data after credit operation
        this.loadUnitData();

        this.snotifyService.success(
          this.transloco.translate('corporate-management.notifications.credit_given', {
            email: user.email,
            amount: amount
          }),
          'Başarılı',
          { position: 'centerBottom', timeout: 2000 }
        );
      },
      error: (err) => {
        this.showError('credits.send_error', err);
      }
    });
  }

  // Remove credits from user with specified amount and optional reason
  removeCredit(user: CorporateUser, amount: number, reason?: string) {
    // Validate that user has enough credits
    if (user.credits.remaining < amount) {
      this.snotifyService.error(
        this.transloco.translate('corporate-management.notifications.insufficient_credits', {
          amount: user.credits.remaining
        }),
        'Hata',
        { position: 'centerBottom', timeout: 3000 }
      );
      return;
    }

    this.loading.operation = true;

    this.corporateService.refundCreditFromUser(user.user_id, amount, reason).pipe(
      finalize(() => this.loading.operation = false)
    ).subscribe({
      next: () => {
        // Refresh data after credit operation
        this.loadUnitData();

        this.snotifyService.success(
          this.transloco.translate('corporate-management.notifications.credit_taken', {
            email: user.email,
            amount: amount
          }),
          'Başarılı',
          { position: 'centerBottom', timeout: 2000 }
        );
      },
      error: (err) => {
        this.showError('credits.refund_error', err);
      }
    });
  }

  // Check if user can be deleted (has no credits)
  canDeleteUser(user: CorporateUser): boolean {
    return user.credits.total === 0;
  }

  // Delete user with confirmation
  confirmDeleteUser(user: CorporateUser) {
    // Check if user is an admin - prevent direct deletion of admins
    if (user.role === 'admin') {
      this.snotifyService.warning(
        this.transloco.translate('corporate-management.notifications.cannot_delete_admin', {
          email: user.email
        }),
        'Uyarı',
        { position: 'centerBottom', timeout: 4000 }
      );
      return;
    }

    // Check if user has credits
    if (!this.canDeleteUser(user)) {
      this.snotifyService.error(
        this.transloco.translate('corporate-management.notifications.cannot_delete_with_credits', {
          email: user.email,
          credits: user.credits.total
        }),
        'Hata',
        { position: 'centerBottom', timeout: 3000 }
      );
      return;
    }

    const confirmDialog = this.dialog.open(ConfirmComponent, {
      data: {
        title: this.transloco.translate('corporate-management.confirmations.delete_user', { email: user.email }),
        content: this.transloco.translate('corporate-management.confirmations.delete_user_content'),
        confirm: this.transloco.translate('corporate-management.confirmations.delete'),
        cancel: this.transloco.translate('corporate-management.confirmations.cancel')
      }
    });

    confirmDialog.closed.subscribe(result => {
      if (result) {
        this.deleteUser(user);
      }
    });
  }

  // Delete user after confirmation
  deleteUser(user: CorporateUser) {
    this.loading.operation = true;

    this.corporateService.removeUserFromUnit(user.email).pipe(
      finalize(() => this.loading.operation = false)
    ).subscribe({
      next: () => {
        // Remove user from list
        this.users = this.users.filter(u => u.email != user.email);

        this.snotifyService.success(
          this.transloco.translate('corporate-management.notifications.user_deleted', { email: user.email }),
          'Başarılı',
          { position: 'centerBottom', timeout: 2000 }
        );
      },
      error: (err) => {
        if (err.status === 422) {
          this.snotifyService.error(
            this.transloco.translate('corporate-management.notifications.cannot_delete_user_with_usage', { email: user.email }),
            'Hata',
            { position: 'centerBottom', timeout: 3000 }
          );
        } else {
          this.showError('users.delete_error', err);
        }
      }
    });
  }

  // Validate credit amount input
  validateCreditAmount() {
    if (this.creditOperation === 'remove' && this.selectedUser) {
      // Ensure we can't remove more credits than the user has
      return this.creditAmount <= this.selectedUser.credits.remaining;
    }
    // Basic validation for give operation
    return this.creditAmount > 0;
  }

  // Clear search term
  clearSearch() {
    this.searchTerm = '';
  }

  // Predefined credit amounts
  predefinedCreditAmounts: number[] = [10, 20, 30, 40, 50];

  // Set credit amount to a predefined value
  setPresetCreditAmount(amount: number): void {
    this.creditAmount = amount;
  }

  // Set credit amount to all available credits (for remove operation)
  setAllAvailableCredits(): void {
    if (this.creditOperation === 'remove' && this.selectedUser) {
      this.creditAmount = this.selectedUser.credits.remaining;
    }
  }

  // Helper method to show error messages
  private showError(key: string, error: any): void {

    // Try to get specific error message if available
    let errorMessage = this.transloco.translate(`corporate-management.notifications.${key}`);

    // If there's a specific error message from the API, use that instead
    if (error.error && error.error.message) {
      errorMessage = error.error.message;
    }

    this.snotifyService.error(errorMessage, 'Hata', {
      position: 'centerBottom',
      timeout: 3000
    });
  }

  // Add the clearAllFilters method
  clearAllFilters(): void {
    this.searchTerm = '';
    this.statusFilter = 'all';

    // Force the select element to update
    const selectElement = document.getElementById('statusFilter') as HTMLSelectElement;
    if (selectElement) {
      selectElement.value = 'all';
    }
  }

  // Show the unit selection screen
  showUnitSelectionScreen(): void {
    this.selectedUnitId = null;
    this.showUnitSelection = true;

    // Reset data to avoid showing previous unit's data
    this.departmentName = '';
    this.departmentType = '';
    this.departmentDescription = '';
    this.users = [];
    this.activities = []; // Reset activities when changing units
    this.credits = {
      total: 0,
      transferred: 0,
      remaining: 0,
      used: 0,
      unused: 0
    };

    // Reload units in case there are any changes
    this.loadUnits();
  }

  // Method to update a project's status
  updateProjectStatus(project: UnitProject, newStatus: 'paid' | 'unpaid') {
    this.loading.operation = true;

    this.corporateService.updateProjectStatus(project.id, newStatus).pipe(
      finalize(() => this.loading.operation = false)
    ).subscribe({
      next: () => {
        project.status = newStatus;
        this.snotifyService.success(
          this.transloco.translate('corporate-management.notifications.project_updated', { name: project.name }),
          'Başarılı',
          { position: 'centerBottom', timeout: 2000 }
        );
      },
      error: (err) => {
        this.showError('projects.update_error', err);
      }
    });
  }

  // Method to switch between users and projects tabs
  switchTab(tab: 'users' | 'projects' | 'activities') {
    this.activeTab = tab;

    // Load activities if switching to activities tab and they haven't been loaded yet
    if (tab === 'activities' && this.activities.length === 0) {
      this.loading.activities = true;
      this.corporateService.getUnitActivities().pipe(
        finalize(() => this.loading.activities = false)
      ).subscribe({
        next: (data: UnitActivity[]) => {
          this.activities = data;
        },
        error: (err) => {
          this.showError('activities.load_error', err);
        }
      });
    }
  }

  // Clear activity filters
  clearActivityFilters(): void {
    this.activitySearchTerm = '';
    this.activityTypeFilter = 'all';
    this.activityStatusFilter = 'all';
  }

  // Method to format activity date for display
  formatActivityDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // Method to get activity type display text
  getActivityTypeText(type: string): string {
    return this.transloco.translate(`corporate-management.activities.type.${type}`);
  }

  // Method to get activity status display text
  getActivityStatusText(status: string): string {
    return this.transloco.translate(`corporate-management.activities.status.${status}`);
  }

  // Get display name for a user
  getUserDisplayName(user: any): string {
    if (!user) return '';
    if (user.name && user.surname) return `${user.name} ${user.surname}`;
    if (user.name) return user.name;
    return user.email || '';
  }

  // Get display text for activity detail
  getActivityDetailText(activity: UnitActivity): string {
    if (!activity.detail) return '';
    return this.transloco.translate(`corporate-management.activities.detail.${activity.detail}`) || activity.detail;
  }

  // Show Excel import form
  showImportExcelForm() {
    this.showExcelImportForm = true;
    this.resetExcelImport();
  }

  // Cancel Excel import
  cancelImportExcel() {
    this.showExcelImportForm = false;
    this.resetExcelImport();
  }

  // Reset Excel import state
  resetExcelImport() {
    this.excelFile = null;
    this.parsedUsers = [];
    this.importProgress = 0;
    this.importResults = {
      total: 0,
      success: 0,
      failed: 0
    };
  }

  // Trigger file input click
  triggerFileInput() {
    document.getElementById('excelFileInput')?.click();
  }

  // Handle file selection
  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      if (!['xlsx', 'xls', 'csv'].includes(fileExt || '')) {
        this.snotifyService.error(
          this.transloco.translate('corporate-management.import_excel.invalid_file_type'),
          'Hata',
          { position: 'centerBottom', timeout: 3000 }
        );
        return;
      }

      this.excelFile = file;
      this.parseExcelFile(file);
    }
  }

  // Remove selected file
  removeFile() {
    this.excelFile = null;
    this.parsedUsers = [];
    (document.getElementById('excelFileInput') as HTMLInputElement).value = '';
  }

  // Format file size for display
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Parse Excel file
  parseExcelFile(file: File) {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });

      // Get first sheet
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { raw: false });

      // Parse and validate data
      this.parsedUsers = this.validateExcelData(jsonData);
    };
    reader.readAsArrayBuffer(file);
  }

  // Validate Excel data
  validateExcelData(data: any[]): ParsedExcelUser[] {
    return data.map(row => {
      const email = row['Email'] || row['email'] || '';
      const reason = row['Reason'] || row['reason'] || '';
      let credit = 0;

      // Parse credit value
      const creditValue = row['Credit'] || row['credit'];
      if (creditValue !== undefined && creditValue !== '') {
        const parsedCredit = Number(creditValue);
        credit = isNaN(parsedCredit) ? 0 : Math.max(0, parsedCredit);
      }

      // Validate email
      if (!email) {
        return {
          email,
          reason,
          credit,
          valid: false,
          error: this.transloco.translate('corporate-management.import_excel.missing_email')
        };
      }

      if (!this.validateEmail(email)) {
        return {
          email,
          reason,
          credit,
          valid: false,
          error: this.transloco.translate('corporate-management.import_excel.invalid_email')
        };
      }

      // Check if user already exists in the current list
      const emailExists = this.users.some(user =>
        user.email.toLowerCase() === email.toLowerCase()
      );

      if (emailExists) {
        return {
          email,
          reason,
          credit,
          valid: false,
          error: this.transloco.translate('corporate-management.import_excel.email_exists')
        };
      }

      // Check for duplicates in the imported list
      const isDuplicate = data.filter(r =>
        (r['Email'] || r['email'] || '').toLowerCase() === email.toLowerCase()
      ).length > 1;

      if (isDuplicate) {
        return {
          email,
          reason,
          credit,
          valid: false,
          error: this.transloco.translate('corporate-management.import_excel.duplicate_email')
        };
      }

      // Validate credit if specified
      if (credit > 0 && credit > this.credits.remaining) {
        return {
          email,
          reason,
          credit,
          valid: false,
          error: this.transloco.translate('corporate-management.import_excel.insufficient_credits', {
            required: credit,
            available: this.credits.remaining
          })
        };
      }

      return {
        email,
        reason,
        credit,
        valid: true
      };
    });
  }

  // Download updated template for Excel import with credit column
  downloadTemplate() {
    const worksheet = XLSX.utils.json_to_sheet([
      { 'Email': '<EMAIL>', 'Credit': 10, 'Reason': 'Sample reason for user 1' },
      { 'Email': '<EMAIL>', 'Credit': 5, 'Reason': 'Sample reason for user 2' }
    ]);

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Users');

    // Save the file
    XLSX.writeFile(workbook, 'user_import_template.xlsx');
  }

  // Process Excel import (batch add users)
  processExcelImport() {
    // Filter only valid users
    const validUsers = this.parsedUsers.filter(user => user.valid);

    if (validUsers.length === 0) {
      this.snotifyService.warning(
        this.transloco.translate('corporate-management.import_excel.no_valid_users'),
        'Uyarı',
        { position: 'centerBottom', timeout: 3000 }
      );
      return;
    }

    this.importResults = {
      total: validUsers.length,
      success: 0,
      failed: 0
    };

    // Process users in batches
    this.importProgress = 1; // Start progress

    // Process each user one by one to handle credit assignment
    let completed = 0;

    from(validUsers).subscribe({
      next: (user) => {
        // Add user first
        this.corporateService.addUserToUnit(user.email, user.reason).subscribe({
          next: (response) => {
            completed++;
            this.importResults.success++;
            this.importProgress = Math.floor((completed / validUsers.length) * 100);

            // If user has initial credit, assign it
            if (user.credit && user.credit > 0) {
              // Check if user is active before assigning credits
              setTimeout(() => {
                this.corporateService.getUserCreditSummary().subscribe({
                  next: (data) => {
                    const addedUser = data.users.find(u => u.email.toLowerCase() === user.email.toLowerCase());

                    if (addedUser && addedUser.status === 'user_registered' && addedUser.id) {
                      // User is active, assign credits
                      this.assignInitialCredit(
                        addedUser.id,
                        user.email,
                        user.credit || 0,
                        user.reason || this.transloco.translate('corporate-management.credit_dialog.import_credit_reason')
                      );
                    }
                  },
                  error: (err) => {
                    console.error('Error checking user status for credit assignment:', err);
                  }
                });
              }, 500); // Small delay to ensure user is processed
            }
          },
          error: (err) => {
            completed++;
            this.importResults.failed++;
            this.importProgress = Math.floor((completed / validUsers.length) * 100);
            console.error(`Error adding user ${user.email}:`, err);
          }
        });
      },
      complete: () => {
        // When all processes are done
        setTimeout(() => {
          this.importProgress = 100;
          this.snotifyService.success(
            this.transloco.translate('corporate-management.import_excel.import_complete', {
              total: this.importResults.total,
              success: this.importResults.success,
              failed: this.importResults.failed
            }),
            'Başarılı',
            { position: 'centerBottom', timeout: 3000 }
          );

          // Reload user data if at least one user was successfully added
          if (this.importResults.success > 0) {
            this.loadUnitData();
          }
        }, 1000);
      }
    });
  }
  // Handler for status filter changes
  onStatusFilterChange(): void {
    // Force change detection if needed
    setTimeout(() => {}, 100);
  }

  // Method to clear project filters
  clearProjectFilters() {
    this.projectSearchTerm = '';
    this.projectStatusFilter = 'all';
  }
  getValidUsersCount(): number {
    return this.parsedUsers.filter((user) => user.valid).length;
  }
  getInvalidUsersCount(): number {
    return this.parsedUsers.filter((user) => !user.valid).length;
  }
  hasInvalidUsers(): boolean {
    return this.parsedUsers.filter(u => !u.valid).length > 0;
  }

}

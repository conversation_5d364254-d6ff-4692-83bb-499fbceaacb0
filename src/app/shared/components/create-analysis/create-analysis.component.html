<div class="flex w-[calc(100vw)]  h-[calc(100vh)] overflow-auto" *transloco="let t; read: 'create-analysis'">
  <!-- Error message if analysis_info is missing -->
  <div *ngIf="!analysis_info || !analysis_info.did"
    class="absolute inset-0 z-50 flex flex-col items-center justify-center bg-white">
    <div class="p-8 text-center bg-white shadow-lg rounded-3xl">
      <ng-icon name="lucideTriangleAlert" class="w-16 h-16 mx-auto mb-4 text-yellow-500"></ng-icon>
      <h2 class="mb-2 text-xl font-bold text-gray-800">{{ t('error.missing_info') }}</h2>
      <p class="mb-6 text-gray-600">{{ t('error.missing_info_desc') }}</p>
      <button (click)="closeDialog()"
        class="px-4 py-2 font-medium text-white transition-colors rounded-full bg-brand-green-500 hover:bg-brand-green-600">
        {{ t.buttons.close }}
      </button>
    </div>
  </div>

  <!-- Close button -->
  <button (click)="closeDialog()"
    class="absolute z-50 p-2 transition-all rounded-full top-3 right-3 text-brand-green-700 hover:bg-brand-green-50">
    <ng-icon name="lucideX" class="w-6 h-6"></ng-icon>
  </button>
  <!-- Loading overlay -->
  <div *ngIf="isLoading" class="absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80">
    <div class="flex flex-col items-center">
      <div
        class="w-16 h-16 border-4 rounded-full border-t-brand-green-500 border-r-brand-green-300 border-b-brand-green-200 border-l-brand-green-100 animate-spin">
      </div>
      <p class="mt-4 font-medium text-brand-green-700">{{ t('loading') }}</p>
    </div>
  </div>

  <!-- Clone Options Modal -->
  <div *ngIf="showCloneOptionsModal"
    class="absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div class="w-full max-w-xl overflow-hidden bg-white shadow-xl rounded-3xl">
      <div class="p-5 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">{{ 'analyses.clone_report_question' | transloco }}</h3>
      </div>
      <div class="p-5">
        <div class="flex gap-3">
          <button (click)="handleCloneOption(true)"
            class="w-full px-4 py-3 text-sm font-medium text-white transition-colors rounded-3xl bg-brand-green-500 hover:bg-brand-green-600">
            {{ 'analyses.create_new_report' | transloco }}
          </button>
          <button (click)="handleCloneOption(false)"
            class="w-full px-4 py-3 text-sm font-medium text-gray-700 transition-colors bg-gray-100 border border-gray-300 rounded-3xl hover:bg-gray-200">
            {{ 'analyses.update_existing_report' | transloco }}
          </button>
        </div>
      </div>
      <div class="flex justify-end p-4 bg-gray-50">
        <button (click)="cancelCloneOptions()"
          class="px-4 py-2 text-sm font-medium text-gray-700 transition-colors bg-white border border-gray-300 rounded-3xl hover:bg-gray-50">
          {{ t('cancel') | transloco }}
        </button>
      </div>
    </div>
  </div>

  <!-- Left Column: Analysis Name and Steps -->
  <div class="flex flex-col w-1/4 h-full bg-white border-r border-gray-200">
    <!-- Analysis Title -->
    <div class="p-3 border-b border-gray-200">
      <div class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-brand-green-100">
          <img [src]="'assets/icons/' + currentAnalysisType + '.svg'" class="w-6 h-6" alt="Analysis Icon">
        </div>
        <h2 class="text-xl font-semibold text-gray-900">
          {{ 'analyses_type_list.' + currentAnalysisType | transloco }}
        </h2>
      </div>
      <p class="mt-2 text-sm text-gray-600">
        {{ 'analyses_type_list.' + currentAnalysisType + '_info' | transloco }}
      </p>
    </div>
    <!-- Steps Navigation -->
    <div class="flex-1 p-4 pb-0 overflow-y-auto">

      <h3 class="mb-4 text-sm font-medium text-gray-500">{{ t('steps.title') }}</h3>

      <div class="space-y-2">
        <ng-container *ngFor="let step of [].constructor(reviewStep); let i = index">
          <div (click)="goToStep(i+1)"
            class="relative flex items-center p-3 transition-all cursor-pointer rounded-3xl group"
            [class.bg-brand-green-50]="currentStep === i+1" [class.border-brand-green-500]="currentStep === i+1"
            [class.border]="currentStep === i+1" [class.text-brand-green-700]="currentStep === i+1"
            [class.text-gray-700]="currentStep !== i+1" [class.hover:bg-gray-50]="currentStep !== i+1"
            [class.opacity-50]="i+1 > currentStep && !canProceedToNextStep() && !isStepComplete(i+1)"
            [class.cursor-not-allowed]="i+1 > currentStep && !canProceedToNextStep() && !isStepComplete(i+1)">

            <!-- Step Number -->
            <div class="flex items-center justify-center w-8 h-8 mr-3 text-sm font-medium rounded-full"
              [class.bg-brand-green-500]="isStepComplete(i+1) && currentStep !== i+1"
              [class.text-white]="isStepComplete(i+1) && currentStep !== i+1"
              [class.bg-brand-green-100]="!isStepComplete(i+1) || currentStep === i+1"
              [class.text-brand-green-700]="!isStepComplete(i+1) || currentStep === i+1">
              <ng-icon *ngIf="isStepComplete(i+1) && currentStep !== i+1" name="lucideCheck" class="w-5 h-5"></ng-icon>
              <span *ngIf="!isStepComplete(i+1) || currentStep === i+1">{{ i+1 }}</span>
            </div>

            <!-- Step Name and Selected Variables Count -->
            <div class="flex-1">
              <div class="flex items-center justify-between">
                <p class="font-medium">{{ getStepName(i+1) }}</p>

                <!-- Selected Variables Count Badge -->
                <div *ngIf="selectedVariables[i+1] && selectedVariables[i+1].length > 0"
                  class="px-2 py-0.5 text-xs flex items-center font-medium rounded-full bg-brand-green-100 text-brand-green-800 cursor-help"
                  (mouseenter)="showTooltip(i+1, $event)">
                  {{ selectedVariables[i+1].length }}
                </div>
              </div>
            </div>

            <!-- Tooltip for Selected Variables -->
            <div *ngIf="selectedVariables[i+1] && selectedVariables[i+1].length > 0 && activeTooltipStep === i+1"
              (mouseleave)="hideTooltip($event)"
              class="fixed z-50 p-3 bg-white border border-gray-200 shadow-lg rounded-3xl"
              [ngStyle]="{'left': '225px', 'max-height': '300px', 'overflow-y': 'auto', 'top': getTooltipPosition(i).top}">
              <div class="flex items-center justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900">{{ t('variables.selected') }}</h4>
                <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-brand-green-100 text-brand-green-800">
                  {{ t('variables.count', { count: selectedVariables[i+1].length }) }}
                </span>
              </div>
              <ul class="space-y-1">
                <li *ngFor="let variable of selectedVariables[i+1]" class="p-1.5 text-xs hover:bg-gray-50 rounded-3xl">
                  <div class="flex items-center justify-between gap-1">
                    <div class="flex items-center gap-1 max-w-[70%]">
                      <span class="flex-shrink-0 w-2 h-2 rounded-full"
                        [class.bg-blue-500]="variable.measure === 'Scale'"
                        [class.bg-purple-500]="variable.measure === 'Ordinal'"
                        [class.bg-orange-500]="variable.measure === 'Nominal'"></span>
                      <span class="truncate" [title]="variable.name">{{ variable.name }}</span>
                    </div>

                    <div class="flex items-center flex-shrink-0 gap-1">
                      <!-- Değer etiketleri ikonu -->
                      <div *ngIf="hasValueLabels(variable)"
                        class="flex items-center px-1 py-0.5 text-xs font-medium rounded-full cursor-help relative"
                        [class.bg-data-orange-100]="variable.measure === 'Nominal'"
                        [class.text-data-orange-500]="variable.measure === 'Nominal'"
                        [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                        [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                        (mouseenter)="showStepValueLabelsTooltip(variable.id, $event)"
                        (mouseleave)="hideStepValueLabelsTooltip($event)">
                        <ng-icon name="lucideTag" class="w-2 h-2"></ng-icon>
                        <span class="text-[10px]">{{ getValueLabels(variable)?.length }}</span>

                        <!-- Değer etiketleri tooltip -->
                        <div *ngIf="activeStepValueLabelsVariable === variable.id"
                          class="fixed z-50 p-2 bg-white border border-gray-200 shadow-lg rounded-xl"
                          [ngStyle]="{'left': stepValueLabelsTooltipPosition.left, 'top': stepValueLabelsTooltipPosition.top, 'width': '200px', 'max-height': '150px', 'overflow-y': 'auto'}"
                          (click)="$event.stopPropagation()">
                          <div class="flex items-center justify-between mb-1">
                            <h4 class="text-xs font-medium text-gray-900">{{ t('variables.value_labels') }}</h4>
                            <span class="px-1 py-0.5 text-[10px] rounded-full"
                              [class.bg-data-orange-100]="variable.measure === 'Nominal'"
                              [class.text-data-orange-500]="variable.measure === 'Nominal'"
                              [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                              [class.text-data-purple-500]="variable.measure === 'Ordinal'">
                              {{ t('variables.count', { count: getValueLabels(variable)?.length }) }}
                            </span>
                          </div>
                          <div class="space-y-0.5">
                            <div *ngFor="let label of getValueLabels(variable)"
                              class="flex justify-between text-[10px] py-0.5 border-b border-gray-100 last:border-0">
                              <span class="text-gray-600">{{ label.value }}:</span>
                              <span class="font-medium text-gray-800">{{ label.label }}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Measure Type Badge -->
                      <span class="px-1 py-0.5 text-[10px] rounded-full"
                        [class.text-data-blue-500]="variable.measure === 'Scale'"
                        [class.bg-data-blue-100]="variable.measure === 'Scale'"
                        [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                        [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                        [class.text-data-orange-500]="variable.measure === 'Nominal'"
                        [class.bg-data-orange-100]="variable.measure === 'Nominal'">
                        {{ variable.measure }}
                      </span>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </ng-container>
      </div>
      <div *ngIf="showStepWarning"
        class="p-3 mb-3 text-xs text-red-800 border border-red-200 rounded-3xl bg-red-50 animate-pulse">
        <div class="flex items-start">
          <ng-icon name="lucideAlertTriangle" class="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5 text-red-500"></ng-icon>
          <p *ngIf="warningMessage">{{ warningMessage }}</p>
          <p *ngIf="!warningMessage">{{ t('warnings.default') }}</p>
        </div>
      </div>
    </div>
    <!-- View Dataset Button -->
    <div class="px-6 py-4">
      <button (click)="viewDataset()" class="secondary-green-button">
        <ng-icon name="lucideDatabase" class="w-5 h-5"></ng-icon>
        {{ t('view_dataset') }}
      </button>
    </div>
  </div>

  <!-- Middle Column: Operations -->
  <div class="flex flex-col w-2/4 h-full bg-gray-50">
    <!-- Step Header - Fixed at top -->
    <div class="sticky top-0 z-20 p-3 bg-white border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-900">{{ getStepName(currentStep) }}</h2>
      <p class="mt-1 text-sm text-gray-600">
        {{ currentStep === reviewStep ? t('review.description') : t('step_prefix') + '
        ' + currentStep }}
      </p>
    </div>

    <!-- Step Content - Scrollable area -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <!-- Variable Selection Step -->
      <ng-container
        *ngIf="currentStep !== reviewStep && filteredVariables[currentStep] && currentAnalysisType !== 'dependent'">
        <!-- Fixed Search and Filter Controls -->
        <div class="sticky top-0 z-10 p-3 border-b border-gray-200 bg-gray-50">
          <!-- Search and Filter Controls -->
          <div class="flex flex-wrap items-center gap-2 p-3 bg-white border border-gray-200 shadow-sm rounded-3xl">
            <!-- Search -->
            <div class="relative flex-grow min-w-[200px]">
              <input type="text" [(ngModel)]="searchTerm" placeholder="{{ t('filters.search') }}"
                class="w-full py-1.5 pl-8 pr-3 text-sm border border-gray-300 rounded-3xl focus:ring-1 focus:ring-brand-green-500 focus:border-brand-green-500">
              <ng-icon name="lucideSearch" class="absolute left-2.5 top-2 text-gray-400 w-3.5 h-3.5"></ng-icon>
            </div>

            <!-- Display Mode -->
            <div class="flex items-center gap-1 px-2 py-1 border border-gray-200 rounded-3xl bg-gray-50">
              <span class="text-xs font-medium text-gray-500">{{ t('filters.display_mode') }}</span>
              <div class="flex gap-1">
                <button (click)="variableDisplayMode = 'name'"
                  class="flex items-center justify-center px-2 py-1 text-xs font-medium transition-colors rounded-3xl"
                  [class.bg-brand-green-500]="variableDisplayMode === 'name'"
                  [class.text-white]="variableDisplayMode === 'name'"
                  [class.bg-gray-200]="variableDisplayMode !== 'name'"
                  [class.text-gray-700]="variableDisplayMode !== 'name'">
                  <ng-icon name="lucideTag" class="w-3 h-3 mr-1"></ng-icon>
                  {{ t('filters.name') }}
                </button>
                <button (click)="variableDisplayMode = 'header'"
                  class="flex items-center justify-center px-2 py-1 text-xs font-medium transition-colors rounded-3xl"
                  [class.bg-brand-green-500]="variableDisplayMode === 'header'"
                  [class.text-white]="variableDisplayMode === 'header'"
                  [class.bg-gray-200]="variableDisplayMode !== 'header'"
                  [class.text-gray-700]="variableDisplayMode !== 'header'">
                  <ng-icon name="lucideHeading" class="w-3 h-3 mr-1"></ng-icon>
                  {{ t('filters.header') }}
                </button>
                <button (click)="variableDisplayMode = 'both'"
                  class="flex items-center justify-center px-2 py-1 text-xs font-medium transition-colors rounded-3xl"
                  [class.bg-brand-green-500]="variableDisplayMode === 'both'"
                  [class.text-white]="variableDisplayMode === 'both'"
                  [class.bg-gray-200]="variableDisplayMode !== 'both'"
                  [class.text-gray-700]="variableDisplayMode !== 'both'">
                  <ng-icon name="lucideLayoutList" class="w-3 h-3 mr-1"></ng-icon>
                  {{ t('filters.both') }}
                </button>
              </div>
            </div>
            <div class="flex items-center justify-between w-full gap-2">


              <!-- Select All Button - Only show for steps that allow multiple selections and not for split step -->
              <div *ngIf="canSelectMultipleVariables(currentStep) && !isStepSplitList(currentStep)"
                class="flex items-center">
                <button (click)="toggleSelectAll(currentStep)"
                  class="flex items-center gap-1 px-2 py-2 text-xs font-medium transition-colors border border-gray-200 rounded-3xl bg-gray-50 hover:bg-gray-100">
                  <ng-icon [name]="isAllSelected(currentStep) ? 'lucideCheck' : 'lucideCircle'"
                    class="text-xs"></ng-icon>
                  {{ isAllSelected(currentStep) ? t('filters.deselect_all') : t('filters.select_all') }}
                </button>
              </div>
              <!-- Measure Filters -->
              <div class="flex items-center gap-1 px-2 py-1 border border-gray-200 rounded-3xl bg-gray-50">
                <span class="text-xs font-medium text-gray-500">{{ t('filters.type') }}</span>
                <div class="flex gap-1">
                  <!-- Scale Button -->
                  <ng-container *ngIf="hasMeasureTypeVariables('Scale', currentStep)">
                    <button (click)="toggleMeasureFilter('Scale')"
                      class="flex items-center justify-center px-3 py-1 text-xs font-medium transition-colors rounded-3xl"
                      [class.bg-brand-green-500]="measureFilters['Scale']" [class.text-white]="measureFilters['Scale']"
                      [class.bg-gray-200]="!measureFilters['Scale']" [class.text-gray-700]="!measureFilters['Scale']">
                      <ng-icon name="lucideChartBarIncreasing" class="w-3 h-3 mr-1"></ng-icon>
                      {{ t('filters.scale') }}
                    </button>
                  </ng-container>

                  <!-- Ordinal Button -->
                  <ng-container *ngIf="hasMeasureTypeVariables('Ordinal', currentStep)">
                    <button (click)="toggleMeasureFilter('Ordinal')"
                      class="flex items-center justify-center px-3 py-1 text-xs font-medium transition-colors rounded-3xl"
                      [class.bg-brand-green-500]="measureFilters['Ordinal']"
                      [class.text-white]="measureFilters['Ordinal']" [class.bg-gray-200]="!measureFilters['Ordinal']"
                      [class.text-gray-700]="!measureFilters['Ordinal']">
                      <ng-icon name="lucideListOrdered" class="w-3 h-3 mr-1"></ng-icon>
                      {{ t('filters.ordinal') }}
                    </button>
                  </ng-container>

                  <!-- Nominal Button -->
                  <ng-container *ngIf="hasMeasureTypeVariables('Nominal', currentStep)">
                    <button (click)="toggleMeasureFilter('Nominal')"
                      class="flex items-center justify-center px-3 py-1 text-xs font-medium transition-colors rounded-3xl"
                      [class.bg-brand-green-500]="measureFilters['Nominal']"
                      [class.text-white]="measureFilters['Nominal']" [class.bg-gray-200]="!measureFilters['Nominal']"
                      [class.text-gray-700]="!measureFilters['Nominal']">
                      <ng-icon name="lucideList" class="w-3 h-3 mr-1"></ng-icon>
                      {{ t('filters.nominal') }}
                    </button>
                  </ng-container>

                  <!-- No Measure Types Available Message -->
                  <span
                    *ngIf="!hasMeasureTypeVariables('Scale', currentStep) && !hasMeasureTypeVariables('Ordinal', currentStep) && !hasMeasureTypeVariables('Nominal', currentStep)"
                    class="text-xs text-gray-500">
                    {{ t('filters.no_type') }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Scrollable Variables List -->
        <div class="flex-1 p-3 overflow-y-auto">
          <div class="space-y-2">
            <!-- Empty state message -->
            <div *ngIf="filterVariables(filteredVariables[currentStep].variable).length === 0"
              class="p-3 text-center bg-white border border-gray-200 rounded-3xl">
              <ng-icon name="lucideCircleAlert" class="w-12 h-12 mx-auto mb-4 text-gray-400"></ng-icon>
              <h3 class="mb-2 text-lg font-medium text-gray-900">{{ t('empty_state.title') }}</h3>
              <p class="text-gray-600">
                {{ t('empty_state.description') }}
              </p>
            </div>

            <ng-container *ngFor="let variable of filterVariables(filteredVariables[currentStep].variable)">
              <div (click)="!variable.disabled && toggleVariableSelection(variable, currentStep)"
                class="relative flex items-center p-4 transition-all bg-white border rounded-3xl"
                [class.cursor-pointer]="!variable.disabled" [class.cursor-not-allowed]="variable.disabled"
                [class.opacity-60]="variable.disabled" [class.border-brand-green-500]="variable.selected"
                [class.border-gray-200]="!variable.selected" [class.bg-brand-green-50]="variable.selected">

                <!-- Nominal değişkenler için değer etiketleri tooltip -->
                <div *ngIf="hasValueLabels(variable) && activeValueLabelsVariable === variable.id"
                  class="absolute z-50 p-3 bg-white border border-gray-200 shadow-lg rounded-3xl"
                  style="right: 0; top: 0px; width: 250px; max-height: 200px; overflow-y: auto;"
                  (click)="$event.stopPropagation()" (mouseenter)="showValueLabelsTooltip(variable.id, $event)"
                  (mouseleave)="hideValueLabelsTooltip($event)">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="text-sm font-medium text-gray-900">{{ t('variables.value_labels') }}</h4>
                    <span class="px-1.5 py-0.5 text-xs rounded-full"
                      [class.bg-data-orange-100]="variable.measure === 'Nominal'"
                      [class.text-data-orange-500]="variable.measure === 'Nominal'"
                      [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                      [class.text-data-purple-500]="variable.measure === 'Ordinal'">
                      {{ t('variables.count', { count: getValueLabels(variable)?.length }) }}
                    </span>
                  </div>
                  <div class="space-y-1">
                    <div *ngFor="let label of getValueLabels(variable)"
                      class="flex justify-between text-xs py-0.5 border-b border-gray-100 last:border-0">
                      <span class="text-gray-600">{{ label.value }}:</span>
                      <span class="font-medium text-gray-800">{{ label.label }}</span>
                    </div>
                  </div>
                </div>

                <!-- Checkbox -->
                <div class="mr-4">
                  <div class="flex items-center justify-center w-5 h-5 transition-colors border rounded-3xl"
                    [class.bg-brand-green-500]="variable.selected" [class.border-brand-green-500]="variable.selected"
                    [class.border-gray-300]="!variable.selected">
                    <ng-icon *ngIf="variable.selected" name="lucideCheck" class="w-4 h-4 text-white"></ng-icon>
                  </div>
                </div>

                <!-- Variable Info -->
                <div class="flex-1">
                  <div class="flex flex-wrap items-center justify-between gap-2">
                    <!-- Variable Name/Header -->
                    <div class="flex items-center max-w-md gap-2 truncate">
                      <p class="text-sm font-medium text-gray-900 truncate "
                        [title]="variableDisplayMode === 'name' ? variable.name : (variableDisplayMode === 'header' ? (variable.header || variable.name) : (variable.name + ' - ' + (variable.header || variable.name)))">
                        <ng-container *ngIf="variableDisplayMode === 'name' || variableDisplayMode === 'both'">
                          {{ variable.name }}
                        </ng-container>
                        <ng-container *ngIf="variableDisplayMode === 'both'">
                          -
                        </ng-container>
                        <ng-container *ngIf="variableDisplayMode === 'header' || variableDisplayMode === 'both'">
                          {{ variable.header || variable.name }}
                        </ng-container>
                      </p>

                      <!-- Disabled indicator -->
                      <div *ngIf="variable.disabled"
                        class="flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-600 flex-shrink-0">

                        <!-- Devre dışı nedenleri -->
                        <ng-container *ngIf="variable.disabled">
                          <!-- Yetersiz etiket sayısı -->
                          <div *ngIf="variable.disabledReason === 'not_enough_labels'"
                            class="flex items-center mt-1 text-xs text-red-600">
                            <ng-icon name="lucideTriangleAlert" class="w-3 h-3 mr-1"></ng-icon>
                            <span>{{ t('variables.not_enough_labels') }}</span>
                          </div>

                          <!-- Kullanımda -->
                          <div *ngIf="variable.disabledReason === 'in_use'"
                            class="flex items-center mt-1 text-xs text-gray-500">
                            <ng-icon name="lucideLock" class="w-3 h-3 mr-1"></ng-icon>
                            <span>{{ t('variables.in_use') }}</span>
                          </div>

                          <!-- Ordinal değişken izin verilmiyor -->
                          <div *ngIf="variable.disabledReason === 'ordinal_not_allowed'"
                            class="flex items-center mt-1 text-xs text-gray-500">
                            <ng-icon name="lucideSlash" class="w-3 h-3 mr-1"></ng-icon>
                            <span>{{ t('variables.ordinal_not_allowed') }}</span>
                          </div>

                          <!-- Diğer nedenler veya neden belirtilmemiş -->
                          <div *ngIf="!variable.disabledReason" class="flex items-center mt-1 text-xs text-gray-500">
                            <ng-icon name="lucideInfo" class="w-3 h-3 mr-1"></ng-icon>
                            <span>{{ t('variables.disabled') }}</span>
                          </div>
                        </ng-container>
                      </div>

                    </div>


                    <!-- Right side badges -->
                    <div class="flex items-center flex-shrink-0 gap-1 ml-auto">

                      <!-- Reference Value Input (for single analysis) - Between name and measure type -->
                      <div *ngIf="filteredVariables[currentStep].type === 'reference' && variable.selected"
                        class="flex items-center flex-shrink-0" (click)="$event.stopPropagation()">
                        <div class="flex items-center">
                          <span
                            *ngIf="currentAnalysisType === 'single' && (!variable.referenceValue && variable.referenceValue !== 0)"
                            class="ml-1 text-xs font-medium text-red-600">*</span>
                          <label class="mr-2 text-xs font-medium text-gray-700 whitespace-nowrap"
                            (click)="$event.stopPropagation()">{{ t('variables.reference') }}</label>
                          <input type="text" [(ngModel)]="variable.referenceValue"
                            (input)="updateReferenceValue(variable, variable.referenceValue)"
                            (keydown)="handleReferenceValueKeypress($event, variable)"
                            (click)="$event.stopPropagation()" (focus)="$event.stopPropagation()"
                            class="w-16 px-2 py-1 text-xs border rounded-full focus:ring-1 focus:ring-brand-green-500 focus:border-brand-green-500"
                            [class.border-red-300]="currentAnalysisType === 'single' && (!variable.referenceValue && variable.referenceValue !== 0)"
                            [class.border-gray-300]="!(currentAnalysisType === 'single' && (!variable.referenceValue && variable.referenceValue !== 0))"
                            [attr.placeholder]="translocoService.getActiveLang() === 'tr' ? '0,0' : '0.0'"
                            [attr.id]="'reference-input-' + variable.id">
                        </div>
                      </div>
                      <!-- Değişkenler için değer etiketleri ikonu -->
                      <div *ngIf="hasValueLabels(variable)"
                        class="flex items-center px-2 py-0.5 text-xs font-medium rounded-full cursor-help"
                        [class.bg-data-orange-100]="variable.measure === 'Nominal'"
                        [class.text-data-orange-500]="variable.measure === 'Nominal'"
                        [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                        [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                        (mouseenter)="showValueLabelsTooltip(variable.id, $event)">
                        <ng-icon name="lucideTag" class="w-3 h-3 mr-1"></ng-icon>
                        <span>{{ getValueLabels(variable)?.length }}</span>
                      </div>

                      <!-- Measure Type Badge -->
                      <span class="px-2 py-0.5 text-xs rounded-full"
                        [class.text-data-blue-500]="variable.measure === 'Scale'"
                        [class.bg-data-blue-100]="variable.measure === 'Scale'"
                        [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                        [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                        [class.text-data-orange-500]="variable.measure === 'Nominal'"
                        [class.bg-data-orange-100]="variable.measure === 'Nominal'">
                        {{ variable.measure }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </ng-container>

      <!-- Dependent Analizi için Özel Tanımlama Adımı -->
      <ng-container *ngIf="currentStep !== reviewStep && currentAnalysisType === 'dependent'">
        <div class="flex-1 p-3 overflow-y-auto">
          <!-- Tanımlama Adımı (Adım 1) -->
          <ng-container *ngIf="currentStep === 1">
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-3xl">
              <h3 class="mb-2 text-lg font-medium text-gray-900">{{ t('dependent.define_title') }}</h3>
              <p class="mb-4 text-sm text-gray-600">
                {{ t('dependent.define_description') }}
              </p>

              <!-- Değişken Grubu Tanımlama -->
              <div class="p-4 mb-4 border border-gray-200 rounded-3xl bg-gray-50">
                <h4 class="mb-3 text-base font-medium text-gray-800">{{ t('dependent.group.title') }}</h4>

                <div class="mb-5">
                  <!-- Group Names (Turkish and English on the same row) -->
                  <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <!-- Turkish Group Name -->
                    <div>
                      <label class="block mb-1 text-xs font-medium text-gray-600">{{ t('dependent.group.name_tr')
                        }}</label>
                      <input type="text" [ngModel]="multiDefine?.defineTr"
                        (ngModelChange)="multiDefine && (multiDefine.defineTr = $event)"
                        placeholder="{{ t('dependent.group.placeholder_tr') }}"
                        class="w-full px-3 py-2 text-sm border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                    </div>

                    <!-- English Group Name -->
                    <div>
                      <label class="block mb-1 text-xs font-medium text-gray-600">{{ t('dependent.group.name_en')
                        }}</label>
                      <input type="text" [ngModel]="multiDefine?.defineEn"
                        (ngModelChange)="multiDefine && (multiDefine.defineEn = $event)"
                        placeholder="{{ t('dependent.group.placeholder_en') }}"
                        class="w-full px-3 py-2 text-sm border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                    </div>
                  </div>
                </div>

                <!-- Zaman ve Değişken Tanımlama -->
                <div class="mb-3">
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="text-base font-medium text-gray-800">{{ t('dependent.times.title') }}</h4>
                    <div class="flex items-center gap-2">
                      <button *ngIf="defineTimes && defineTimes.length > 0" (click)="showCopyTimesModal = true"
                        class="flex items-center gap-1 px-3 py-1 text-xs font-medium text-gray-700 transition-colors bg-gray-100 border border-gray-300 rounded-3xl hover:bg-gray-200">
                        <ng-icon name="lucideCopy" class="w-3 h-3"></ng-icon>
                        {{ t('dependent.times.copy') }}
                      </button>
                      <button (click)="addTimeToTimeList()" tabindex="-1"
                        class="flex items-center gap-1 px-3 py-1 text-xs font-medium text-white transition-colors rounded-3xl bg-brand-green-500 hover:bg-brand-green-600">
                        <ng-icon name="lucidePlus" class="w-3 h-3"></ng-icon>
                        {{ t('dependent.times.add') }}
                      </button>
                    </div>
                  </div>

                  <!-- Minimum 2 zaman uyarısı -->
                  <div *ngIf="multiDefineTimes?.sub?.length < 2"
                    class="p-2 mb-3 text-xs border text-amber-800 border-amber-200 rounded-3xl bg-amber-50">
                    <div class="flex items-start">
                      <ng-icon name="lucideAlertTriangle"
                        class="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5 text-amber-500"></ng-icon>
                      <p>{{ t('dependent.times.min_warning') }}</p>
                    </div>
                  </div>

                  <div cdkDropList [cdkDropListData]="multiDefineTimes?.sub || []"
                    (cdkDropListDropped)="dropTime($event)" class="space-y-4">
                    <div *ngFor="let time of multiDefineTimes?.sub || []; let i = index" cdkDrag [cdkDragData]="time"
                      class="p-4 bg-white border border-gray-200 rounded-3xl">

                      <!-- Header with drag handle and remove button -->
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-2">
                          <!-- Drag handle -->
                          <div
                            class="flex items-center justify-center text-gray-400 transition-colors rounded-full cursor-move size-6 hover:bg-gray-100"
                            cdkDragHandle>
                            <ng-icon name="lucideGripVertical" class="w-5 h-5"></ng-icon>
                          </div>
                          <h5 class="text-sm font-medium text-gray-800">{{ t('dependent.time') }} {{ i+1 }}</h5>
                        </div>

                        <!-- Remove button -->
                        <button *ngIf="multiDefineTimes?.sub?.length > 2" (click)="removeTimeFromTimeList(time.id)"
                          class="flex items-center justify-center text-red-500 transition-colors rounded-full size-6 hover:bg-red-50">
                          <ng-icon name="lucideTrash2" class="w-4 h-4"></ng-icon>
                        </button>
                      </div>

                      <!-- Preview when dragging -->
                      <div *cdkDragPreview class="p-3 bg-white border shadow-lg border-brand-green-300 rounded-3xl">
                        <div class="flex items-center gap-2">
                          <span class="font-medium text-gray-900">{{ time.timeTr || t('dependent.time') + ' ' + (i+1)
                            }}</span>
                        </div>
                      </div>

                      <!-- Placeholder when dragging -->
                      <div *cdkDragPlaceholder
                        class="p-3 py-5 border-2 border-dashed border-brand-green-300 rounded-3xl bg-brand-green-50 opacity-70">
                      </div>

                      <div class="space-y-4">
                        <!-- Time names (Turkish and English on the same row) -->
                        <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
                          <!-- Turkish name -->
                          <div>
                            <label class="block mb-1 text-xs font-medium text-gray-600">{{ t('dependent.time_name_tr')
                              }}</label>
                            <input type="text" [(ngModel)]="time.timeTr" placeholder="Örn: Öncesi, Sonrası, 1. Hafta..."
                              class="w-full px-3 py-2 text-sm border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                          </div>

                          <!-- English name -->
                          <div>
                            <label class="block mb-1 text-xs font-medium text-gray-600">{{ t('dependent.time_name_en')
                              }}</label>
                            <input type="text" [(ngModel)]="time.timeEn" placeholder="Örn: Before, After, Week 1..."
                              class="w-full px-3 py-2 text-sm border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                          </div>
                        </div>

                        <!-- Variable selection below time names -->
                        <div>
                          <label class="block mb-1 text-xs font-medium text-gray-600">{{
                            t('dependent.variable_for_time') }}</label>

                          <!-- Custom dropdown -->
                          <div class="relative">
                            <div class="relative">
                              <div [id]="'dropdown-button-' + time.id" (click)="toggleDropdown(time)"
                                class="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 cursor-pointer rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                                <span *ngIf="!time.variable" class="text-gray-500">{{ t('dependent.select_variable')
                                  }}</span>
                                <div *ngIf="time.variable" class="flex items-center justify-between w-full">
                                  <span class="truncate max-w-[150px]" [title]="time.variable.name">{{
                                    time.variable.name }}</span>
                                  <span class="px-2 py-0.5 text-xs rounded-full ml-2 flex-shrink-0"
                                    [class.text-data-blue-500]="time.variable.measure === 'Scale'"
                                    [class.bg-data-blue-100]="time.variable.measure === 'Scale'"
                                    [class.text-data-purple-500]="time.variable.measure === 'Ordinal'"
                                    [class.bg-data-purple-100]="time.variable.measure === 'Ordinal'"
                                    [class.text-data-orange-500]="time.variable.measure === 'Nominal'"
                                    [class.bg-data-orange-100]="time.variable.measure === 'Nominal'">
                                    {{ time.variable.measure }}
                                  </span>
                                </div>
                                <ng-icon name="lucideChevronDown" class="w-4 h-4 ml-2 text-gray-400"
                                  [class.transform]="time.showDropdown"
                                  [class.rotate-180]="time.showDropdown"></ng-icon>
                              </div>

                              <!-- Dropdown menu -->
                              <div *ngIf="time.showDropdown" [class.dropdown-top]="time.dropdownPosition === 'top'"
                                [class.dropdown-bottom]="time.dropdownPosition === 'bottom'"
                                class="absolute z-10 w-full overflow-y-auto bg-white border border-gray-200 shadow-lg rounded-xl max-h-60"
                                appClickOutside (clickOutside)="time.showDropdown = false">

                                <!-- Search input inside dropdown -->
                                <div class="sticky top-0 z-10 p-2 bg-white border-b border-gray-200">
                                  <div class="relative">
                                    <input type="text" [(ngModel)]="time.searchTerm"
                                      (input)="filterVariablesForTime(time)"
                                      placeholder="{{ t('dependent.search_variable') }}"
                                      [id]="'search-input-' + time.id"
                                      class="w-full px-8 py-2 text-sm border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                                    <ng-icon name="lucideSearch"
                                      class="absolute left-3 top-2.5 text-gray-400 w-4 h-4"></ng-icon>
                                    <button *ngIf="time.searchTerm"
                                      (click)="time.searchTerm = ''; filterVariablesForTime(time)"
                                      class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
                                      <ng-icon name="lucideX" class="w-4 h-4"></ng-icon>
                                    </button>
                                  </div>
                                </div>

                                <!-- No results message -->
                                <div *ngIf="time.filteredVariables && time.filteredVariables.length === 0"
                                  class="p-3 text-sm text-center text-gray-500">
                                  {{ t('dependent.no_variable_found') }}
                                </div>

                                <!-- Variable list -->
                                <div *ngFor="let variable of time.filteredVariables"
                                  (click)="!variable.disabled && selectVariable(variable, time); !variable.disabled && (time.showDropdown = false)"
                                  class="flex items-center justify-between p-2 text-sm hover:bg-gray-50"
                                  [class.cursor-pointer]="!variable.disabled"
                                  [class.cursor-not-allowed]="variable.disabled" [class.opacity-50]="variable.disabled">
                                  <div class="flex items-center">
                                    <span class="truncate max-w-[150px]" [title]="variable.name">{{ variable.name
                                      }}</span>
                                    <span *ngIf="variable.disabled" class="ml-2 text-xs text-gray-500">({{
                                      t('dependent.already_selected') }})</span>
                                  </div>
                                  <span class="px-2 py-0.5 text-xs rounded-full ml-2 flex-shrink-0"
                                    [class.text-data-blue-500]="variable.measure === 'Scale'"
                                    [class.bg-data-blue-100]="variable.measure === 'Scale'"
                                    [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                                    [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                                    [class.text-data-orange-500]="variable.measure === 'Nominal'"
                                    [class.bg-data-orange-100]="variable.measure === 'Nominal'">
                                    {{ variable.measure }}
                                  </span>
                                </div>
                              </div>
                            </div>

                            <!-- Clear button -->
                            <button *ngIf="time.variable" (click)="clearVariable(time)"
                              class="absolute top-0 right-0 flex items-center justify-center h-full px-3 text-gray-400 hover:text-gray-600">
                              <ng-icon name="lucideX" class="w-4 h-4"></ng-icon>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Copy Times Modal -->
              <div *ngIf="showCopyTimesModal"
                class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                <div class="p-4 bg-white shadow-lg w-96 rounded-3xl">
                  <h3 class="mb-3 text-lg font-medium text-gray-900">{{ t('dependent.copy_times.title') }}</h3>
                  <p class="mb-4 text-sm text-gray-600">{{ t('dependent.copy_times.description') }}</p>

                  <div class="mb-4 space-y-2 overflow-y-auto max-h-60">
                    <div *ngFor="let timeGroup of defineTimes"
                      class="p-3 border border-gray-200 cursor-pointer rounded-3xl hover:border-brand-green-300"
                      (click)="copyTimes(timeGroup)">
                      <div class="mb-2 font-medium text-gray-800">{{ t('dependent.copy_times.group', { index:
                        timeGroup.ind }) }}</div>
                      <div class="space-y-1">
                        <div *ngFor="let time of timeGroup.sub" class="text-sm text-gray-600">
                          {{ time.tr }} / {{ time.en }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="flex justify-end">
                    <button (click)="showCopyTimesModal = false"
                      class="px-4 py-2 text-sm font-medium text-gray-700 transition-colors bg-gray-100 border border-gray-300 rounded-3xl hover:bg-gray-200">
                      {{ t('buttons.close') }}
                    </button>
                  </div>
                </div>
              </div>

              <!-- Tanımlama Butonları -->
              <div class="flex justify-between">
                <button (click)="clearDefine()"
                  class="px-4 py-2 text-sm font-medium text-gray-700 transition-colors bg-gray-100 border border-gray-300 rounded-3xl hover:bg-gray-200">
                  {{ t('buttons.clear') }}
                </button>

                <button (click)="addDefineToList()" [disabled]="!isValidMultiDefine()"
                  class="px-4 py-2 text-sm font-medium text-white transition-colors rounded-3xl bg-brand-green-500 hover:bg-brand-green-600 disabled:opacity-50 disabled:cursor-not-allowed">
                  {{ t('buttons.add') }}
                </button>
              </div>
            </div>

            <!-- Tanımlanan Gruplar Listesi -->
            <div *ngIf="multiDefineList?.length > 0" class="p-4 bg-white border border-gray-200 rounded-3xl">
              <h3 class="mb-3 text-lg font-medium text-gray-900">{{ t('dependent.defined_groups.title') }}</h3>

              <div class="space-y-3">
                <div *ngFor="let define of multiDefineList" class="p-4 border border-gray-200 rounded-3xl bg-gray-50">
                  <!-- Group Header -->
                  <div class="flex items-center justify-between mb-3">
                    <div>
                      <h4 class="text-base font-medium text-gray-800">{{ define.defineTr }}</h4>
                      <p class="text-xs text-gray-500">{{ define.defineEn }}</p>
                    </div>
                    <button (click)="removeDefine(define)" class="p-1 text-gray-500 rounded-full hover:bg-gray-200">
                      <ng-icon name="lucideTrash2" class="w-4 h-4"></ng-icon>
                    </button>
                  </div>

                  <!-- Times and Variables Table -->
                  <div class="overflow-hidden border border-gray-200 rounded-xl">
                    <!-- Table Header -->
                    <div class="grid grid-cols-3 p-2 text-xs font-medium text-gray-700 bg-gray-100">
                      <div>{{ t('dependent.defined_groups.time') }}</div>
                      <div>{{ t('dependent.defined_groups.variable') }}</div>
                      <div>{{ t('dependent.defined_groups.variable_type') }}</div>
                    </div>

                    <!-- Table Rows -->
                    <div class="divide-y divide-gray-200">
                      <div *ngFor="let time of define.timeTable?.sub || []"
                        class="grid items-center grid-cols-3 p-2 text-sm">
                        <!-- Time Names -->
                        <div class="flex flex-col">
                          <span class="font-medium text-gray-800">{{ time.timeTr }}</span>
                          <span class="text-xs text-gray-500">{{ time.timeEn }}</span>
                        </div>

                        <!-- Variable Name -->
                        <div class="font-medium text-gray-800">{{ time.variable?.name }}</div>

                        <!-- Variable Type -->
                        <div>
                          <span class="px-2 py-0.5 text-xs rounded-full"
                            [class.text-data-blue-500]="time.variable?.measure === 'Scale'"
                            [class.bg-data-blue-100]="time.variable?.measure === 'Scale'"
                            [class.text-data-purple-500]="time.variable?.measure === 'Ordinal'"
                            [class.bg-data-purple-100]="time.variable?.measure === 'Ordinal'"
                            [class.text-data-orange-500]="time.variable?.measure === 'Nominal'"
                            [class.bg-data-orange-100]="time.variable?.measure === 'Nominal'">
                            {{ time.variable?.measure }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>

          <!-- Ayırma Değişkeni Adımı (Adım 2) -->
          <ng-container *ngIf="currentStep === 2">
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-3xl">
              <h3 class="mb-2 text-lg font-medium text-gray-900">{{ t('split.title') }}</h3>
              <p class="mb-4 text-sm text-gray-600">
                {{ t('split.description') }}
              </p>

              <!-- Arama ve Filtreleme -->
              <div class="p-3 mb-4 bg-white border border-gray-200 rounded-3xl">
                <div class="relative">
                  <input type="text" [(ngModel)]="searchTerm" placeholder="{{ t('variables.search') }}"
                    class="w-full py-2 pl-8 pr-3 text-sm border border-gray-300 rounded-3xl focus:ring-1 focus:ring-brand-green-500 focus:border-brand-green-500">
                  <ng-icon name="lucideSearch" class="absolute left-3 top-2.5 text-gray-400 w-4 h-4"></ng-icon>
                </div>
              </div>

              <!-- Değişken Listesi -->
              <div class="space-y-2">
                <ng-container *ngFor="let variable of filterVariables(filteredVariables[currentStep].variable)">
                  <div (click)="!variable.disabled && toggleVariableSelection(variable, currentStep)"
                    class="relative flex items-center p-3 transition-all bg-white border rounded-3xl"
                    [class.cursor-pointer]="!variable.disabled" [class.cursor-not-allowed]="variable.disabled"
                    [class.opacity-60]="variable.disabled" [class.border-brand-green-500]="variable.selected"
                    [class.border-gray-200]="!variable.selected" [class.bg-brand-green-50]="variable.selected">

                    <!-- Checkbox -->
                    <div class="mr-4">
                      <div class="flex items-center justify-center w-5 h-5 transition-colors border rounded-3xl"
                        [class.bg-brand-green-500]="variable.selected"
                        [class.border-brand-green-500]="variable.selected" [class.border-gray-300]="!variable.selected">
                        <ng-icon *ngIf="variable.selected" name="lucideCheck" class="w-4 h-4 text-white"></ng-icon>
                      </div>
                    </div>

                    <!-- Değişken Bilgisi -->
                    <div class="flex-1">
                      <div class="flex flex-wrap items-center justify-between gap-2">
                        <div class="flex flex-col">
                          <p class="text-sm font-medium text-gray-900">{{ variable.name }}</p>

                          <!-- Devre dışı nedenleri -->
                          <ng-container *ngIf="variable.disabled">
                            <!-- Yetersiz etiket sayısı -->
                            <div *ngIf="variable.disabledReason === 'not_enough_labels'"
                              class="flex items-center mt-1 text-xs text-red-600">
                              <ng-icon name="lucideAlertTriangle" class="w-3 h-3 mr-1"></ng-icon>
                              <span>{{ t('variables.not_enough_labels') }}</span>
                            </div>

                            <!-- Kullanımda -->
                            <div *ngIf="variable.disabledReason === 'in_use'"
                              class="flex items-center mt-1 text-xs text-gray-500">
                              <ng-icon name="lucideLock" class="w-3 h-3 mr-1"></ng-icon>
                              <span>{{ t('variables.in_use') }}</span>
                            </div>

                            <!-- Ordinal değişken izin verilmiyor -->
                            <div *ngIf="variable.disabledReason === 'ordinal_not_allowed'"
                              class="flex items-center mt-1 text-xs text-gray-500">
                              <ng-icon name="lucideSlash" class="w-3 h-3 mr-1"></ng-icon>
                              <span>{{ t('variables.ordinal_not_allowed') }}</span>
                            </div>

                            <!-- Diğer nedenler veya neden belirtilmemiş -->
                            <div *ngIf="!variable.disabledReason" class="flex items-center mt-1 text-xs text-gray-500">
                              <ng-icon name="lucideInfo" class="w-3 h-3 mr-1"></ng-icon>
                              <span>{{ t('variables.disabled') }}</span>
                            </div>
                          </ng-container>
                        </div>

                        <!-- Ölçüm Tipi Rozeti -->
                        <span class="px-2 py-0.5 text-xs rounded-full"
                          [class.text-data-blue-500]="variable.measure === 'Scale'"
                          [class.bg-data-blue-100]="variable.measure === 'Scale'"
                          [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                          [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                          [class.text-data-orange-500]="variable.measure === 'Nominal'"
                          [class.bg-data-orange-100]="variable.measure === 'Nominal'">
                          {{ variable.measure }}
                        </span>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </div>
      </ng-container>

      <!-- Review Step -->
      <ng-container *ngIf="currentStep === reviewStep">
        <div class="flex-1 p-3 overflow-y-auto">
          <div class="space-y-6">
            <!-- Credit and Report Settings at the top of Review Step -->
            <div class="p-4 bg-white border border-brand-green-400 rounded-3xl">
              <h3 class="mb-4 text-lg font-medium text-gray-900">{{ t('review.analysis_info') }}</h3>



              <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <!-- Credit Information -->
                <div class="p-4 border rounded-3xl" [class.border-gray-100]="canPerformAnalysis"
                  [class.border-red-200]="!canPerformAnalysis" [class.bg-gray-50]="canPerformAnalysis"
                  [class.bg-red-50]="!canPerformAnalysis">
                  <h4 class="mb-2 text-sm font-medium text-gray-700">{{ t('review.credit.title') }}</h4>
                  <div class="flex items-center">
                    <ng-icon name="lucideCreditCard" class="w-5 h-5 mr-2"
                      [class.text-brand-green-500]="canPerformAnalysis"
                      [class.text-red-500]="!canPerformAnalysis"></ng-icon>
                    <div *ngIf="calculatingCredits" class="flex items-center text-gray-600">
                      <ng-icon name="lucideLoader" class="w-4 h-4 mr-2 animate-spin"></ng-icon>
                      {{ t('review.credit.calculating') }}
                    </div>
                    <div *ngIf="!calculatingCredits" class="text-gray-700">
                      <span class="font-medium">{{ requiredCredits }}</span> {{ t('review.credit.amount') }}
                      <span *ngIf="!canPerformAnalysis" class="ml-2 text-xs font-medium text-red-600">
                        ({{ t('review.credit.insufficient') }})
                      </span>
                    </div>
                  </div>
                  <div *ngIf="!canPerformAnalysis" class="mt-2 text-xs text-red-600">
                    {{ t('review.credit.insufficient_message') }}
                  </div>
                </div>

                <!-- Report Settings -->
                <div class="p-4 border border-gray-100 rounded-3xl bg-gray-50">
                  <h4 class="mb-2 text-sm font-medium text-gray-700">{{ t('review.report.title') }}</h4>

                  <!-- Decimal Separator -->
                  <div class="mb-3">
                    <label class="block mb-1 text-xs font-medium text-gray-600">{{ t('review.report.decimal_separator')
                      }}</label>
                    <div class="flex space-x-4">
                      <label class="flex items-center">
                        <input type="radio" [(ngModel)]="reportFormat" [value]="1"
                          class="w-4 h-4 border-gray-300 text-brand-green-600 focus:ring-brand-green-500">
                        <span class="ml-2 text-sm text-gray-700">{{ t('review.report.comma') }}</span>
                      </label>
                      <label class="flex items-center">
                        <input type="radio" [(ngModel)]="reportFormat" [value]="2"
                          class="w-4 h-4 border-gray-300 text-brand-green-600 focus:ring-brand-green-500">
                        <span class="ml-2 text-sm text-gray-700">{{ t('review.report.dot') }}</span>
                      </label>
                    </div>
                  </div>

                  <!-- Decimal Places -->
                  <div>
                    <label class="block mb-1 text-xs font-medium text-gray-600">{{ t('review.report.decimal_places')
                      }}</label>
                    <select [(ngModel)]="decimalPlaces"
                      class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-brand-green-500 focus:border-brand-green-500">
                      <option [value]="0">0</option>
                      <option [value]="1">1</option>
                      <option [value]="2">2</option>
                      <option [value]="3">3</option>
                      <option [value]="4">4</option>
                    </select>
                  </div>

                  <!-- Preview Section -->
                  <div class="p-3 mt-3 border border-gray-100 bg-gray-50 rounded-3xl">
                    <div class="mb-1 text-xs font-medium text-gray-600">{{ t('analyses.preview') }}</div>
                    <div class="flex items-center">
                      <span class="text-sm text-gray-600">{{ t('analyses.preview_number') }}:</span>
                      <span class="ml-2 font-medium text-gray-900">{{ getFormattedPreviewNumber() }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Ki-kare analizi için rapor türü seçimi -->
              <div *ngIf="currentAnalysisType === 'chisq'"
                class="p-4 mt-4 border border-gray-100 rounded-3xl bg-gray-50">
                <h4 class="mb-2 text-sm font-medium text-gray-700">{{ t('review.report_type.title') }}</h4>
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2">
                    <ng-icon *ngIf="selectedReportType === 'row'" name="lucideRows3"
                      class="w-5 h-5 text-brand-green-500"></ng-icon>
                    <ng-icon *ngIf="selectedReportType === 'column'" name="lucideColumns3"
                      class="w-5 h-5 text-brand-green-500"></ng-icon>
                    <span class="text-gray-700">
                      {{ selectedReportType === 'row' ? t('review.report_type.by_row') :
                      t('review.report_type.by_column') }}
                    </span>
                  </div>
                  <button (click)="selectedReportType = selectedReportType === 'row' ? 'column' : 'row'"
                    class="px-3 py-1 text-sm font-medium transition-colors border rounded-full text-brand-green-700 border-brand-green-300 hover:bg-brand-green-50">
                    {{ t('buttons.change') }}
                  </button>
                </div>
              </div>

              <!-- Ortalama karşılaştırma analizi için zaman seçimi -->
              <div *ngIf="currentAnalysisType === 'comean' && shouldShowTimeOption()"
                class="p-4 mt-4 border border-green-300 rounded-3xl bg-gray-50">
                <h4 class="mb-2 text-sm font-medium text-gray-700">{{ t('review.time_option.title') }}</h4>
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2">
                    <ng-icon *ngIf="isInvolveTime" name="lucideClock" class="w-5 h-5 text-brand-green-500"></ng-icon>
                    <ng-icon *ngIf="!isInvolveTime" name="lucideCircleOff" class="w-5 h-5 text-gray-500"></ng-icon>
                    <span class="text-gray-700">
                      {{ isInvolveTime ? t('review.time_option.has_time') : t('review.time_option.no_time') }}
                    </span>
                  </div>
                  <button (click)="isInvolveTime = !isInvolveTime"
                    class="px-3 py-1 text-sm font-medium transition-colors border rounded-full text-brand-green-700 border-brand-green-300 hover:bg-brand-green-50">
                    {{ t('buttons.change') }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Selected Variables by Step -->
            <ng-container *ngFor="let step of [].constructor(maxSteps); let i = index">
              <div class="p-3 bg-white border border-gray-200 rounded-3xl">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-medium text-gray-900">{{ getStepName(i+1) }}</h3>
                  <div *ngIf="selectedVariables[i+1] && selectedVariables[i+1].length > 1"
                    class="flex items-center text-xs text-gray-500">
                    <ng-icon name="lucideGripVertical" class="w-3 h-3 mr-1 text-lg"></ng-icon>
                    <span>{{ t('review.drag_to_order') }}</span>
                  </div>
                </div>

                <div class="space-y-2">
                  <!-- Değişken seçilmediğinde gösterilecek mesaj -->
                  <div *ngIf="!selectedVariables[i+1] || selectedVariables[i+1].length === 0"
                    class="flex items-center justify-center p-3 text-gray-500 rounded-3xl bg-gray-50">
                    <ng-icon name="lucideInfo" class="w-4 h-4 mr-2 text-gray-400"></ng-icon>
                    <span *ngIf="getStepName(i+1) === t('steps.define')">{{ t('review.no_definition') }}</span>
                    <span *ngIf="getStepName(i+1) !== t('steps.define')">{{ t('review.no_variable_selected') }}</span>
                  </div>

                  <!-- Seçilen değişkenler (Drag & Drop ile sıralama) -->
                  <div cdkDropList [cdkDropListData]="selectedVariables[i+1] || []"
                    [cdkDropListDisabled]="!selectedVariables[i+1] || selectedVariables[i+1].length <= 1"
                    (cdkDropListDropped)="dropVariable($event, i+1)" class="space-y-2">
                    <!-- Only enable cdkDrag when there are multiple variables -->
                    <div *ngFor="let variable of selectedVariables[i+1] || []"
                      [cdkDrag]="selectedVariables[i+1] && selectedVariables[i+1].length > 1"
                      [cdkDragDisabled]="!selectedVariables[i+1] || selectedVariables[i+1].length <= 1"
                      [cdkDragData]="variable"
                      class="relative p-3 transition-all duration-200 border border-gray-200 rounded-3xl bg-gray-50 hover:shadow-md hover:border-brand-green-300 hover:bg-gray-100"
                      [class.cursor-default]="!selectedVariables[i+1] || selectedVariables[i+1].length <= 1">
                      <!-- Preview when dragging - only show when multiple variables -->
                      <div *cdkDragPreview
                        [class.hidden]="!selectedVariables[i+1] || selectedVariables[i+1].length <= 1"
                        class="p-3 bg-white border shadow-lg border-brand-green-300 rounded-3xl">
                        <div class="flex items-center gap-2">
                          <span class="font-medium text-gray-900">{{ variable.name }}</span>
                          <span class="px-2 py-0.5 text-xs rounded-full"
                            [class.text-data-blue-500]="variable.measure === 'Scale'"
                            [class.bg-data-blue-100]="variable.measure === 'Scale'"
                            [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                            [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                            [class.text-data-orange-500]="variable.measure === 'Nominal'"
                            [class.bg-data-orange-100]="variable.measure === 'Nominal'">
                            {{ variable.measure }}
                          </span>
                        </div>
                      </div>
                      <!-- Placeholder when dragging - only show when multiple variables -->
                      <div *cdkDragPlaceholder
                        [class.hidden]="!selectedVariables[i+1] || selectedVariables[i+1].length <= 1"
                        class="p-3 py-5 border-2 border-dashed border-brand-green-300 rounded-3xl bg-brand-green-50 opacity-70">
                      </div>
                      <!-- Drag handle -->

                      <div class="flex flex-wrap items-center justify-between gap-2">
                        <!-- Variable Name/Header - Left Side -->
                        <div class="flex flex-1 items-center gap-2 max-w-[60%]">
                          <!-- Always show the space for the icon, but only show the drag handle when multiple variables -->
                          <div
                            class="flex items-center justify-center text-gray-400 transition-colors rounded-full size-6">
                            <!-- Only show the icon and make it draggable when multiple variables -->
                            <ng-icon *ngIf="selectedVariables[i+1] && selectedVariables[i+1].length > 1"
                              name="lucideGripVertical" class="w-5 h-5 cursor-move hover:bg-gray-100"
                              cdkDragHandle></ng-icon>
                          </div>
                          <p class="w-full text-sm font-medium text-gray-900 truncate"
                            [title]="variable.name + ' - ' + (variable.header || variable.name)">
                            {{ variable.name }} - {{ variable.header || variable.name }}
                          </p>
                        </div>

                        <!-- Reference Value (if applicable) - Between name and measure type -->
                        <div *ngIf="variable.referenceValue" class="flex items-center flex-shrink-0 gap-1">
                          <span class="text-xs font-medium text-gray-700 whitespace-nowrap">{{ t('variables.reference')
                            }}</span>
                          <div
                            class="flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-600">
                            <span>{{ formatReferenceValue(variable.referenceValue) }}</span>
                          </div>
                        </div>

                        <!-- Right side badges -->
                        <div class="flex items-center flex-shrink-0 gap-1 ml-auto">
                          <!-- Tanımlar için zamanlar ikonu -->
                          <div
                            *ngIf="variable.isDefine && variable.timeTable && variable.timeTable.sub && variable.timeTable.sub.length > 0"
                            class="flex items-center px-2 py-0.5 text-xs font-medium rounded-full cursor-help bg-brand-green-100 text-brand-green-700"
                            (mouseenter)="showReviewTimesForDefine(variable.id, $event)">
                            <ng-icon name="lucideClock" class="w-3 h-3 mr-1"></ng-icon>
                            <span>{{ variable.timeTable.sub.length }}</span>
                          </div>

                          <!-- Değişkenler için değer etiketleri ikonu -->
                          <div *ngIf="hasValueLabels(variable)"
                            class="flex items-center px-2 py-0.5 text-xs font-medium rounded-full cursor-help"
                            [class.bg-data-orange-100]="variable.measure === 'Nominal'"
                            [class.text-data-orange-500]="variable.measure === 'Nominal'"
                            [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                            [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                            (mouseenter)="showReviewValueLabelsTooltip(variable.id, $event)">
                            <ng-icon name="lucideTag" class="w-3 h-3 mr-1"></ng-icon>
                            <span>{{ getValueLabels(variable)?.length }}</span>
                          </div>

                          <!-- Measure Type Badge -->
                          <span class="px-2 py-0.5 text-xs rounded-full"
                            [class.text-data-blue-500]="variable.measure === 'Scale'"
                            [class.bg-data-blue-100]="variable.measure === 'Scale'"
                            [class.text-data-purple-500]="variable.measure === 'Ordinal'"
                            [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                            [class.text-data-orange-500]="variable.measure === 'Nominal'"
                            [class.bg-data-orange-100]="variable.measure === 'Nominal'">
                            {{ variable.measure }}
                          </span>
                        </div>
                      </div>

                      <!-- Değer etiketleri tooltip -->
                      <div *ngIf="activeReviewValueLabelsVariable === variable.id"
                        class="absolute z-50 p-3 bg-white border border-gray-200 shadow-lg rounded-3xl"
                        style="right: 0; top: 0px; width: 250px; max-height: 200px; overflow-y: auto;"
                        (click)="$event.stopPropagation()"
                        (mouseenter)="showReviewValueLabelsTooltip(variable.id, $event)"
                        (mouseleave)="hideReviewValueLabelsTooltip($event)">
                        <div class="flex items-center justify-between mb-2">
                          <h4 class="text-sm font-medium text-gray-900">{{ t('variables.value_labels') }}</h4>
                          <span class="px-1.5 py-0.5 text-xs rounded-full"
                            [class.bg-data-orange-100]="variable.measure === 'Nominal'"
                            [class.text-data-orange-500]="variable.measure === 'Nominal'"
                            [class.bg-data-purple-100]="variable.measure === 'Ordinal'"
                            [class.text-data-purple-500]="variable.measure === 'Ordinal'">
                            {{ t('variables.count', { count: getValueLabels(variable)?.length }) }}
                          </span>
                        </div>
                        <div class="space-y-1">
                          <div *ngFor="let label of getValueLabels(variable)"
                            class="flex justify-between text-xs py-0.5 border-b border-gray-100 last:border-0">
                            <span class="text-gray-600">{{ label.value }}:</span>
                            <span class="font-medium text-gray-800">{{ label.label }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Tanım zamanları tooltip -->
                      <div *ngIf="activeReviewTimesForDefine === variable.id && variable.isDefine && variable.timeTable"
                        class="absolute z-50 p-3 bg-white border border-gray-200 shadow-lg rounded-3xl"
                        style="right: 0; top: 0px; width: 300px; max-height: 250px; overflow-y: auto;"
                        (click)="$event.stopPropagation()" (mouseenter)="showReviewTimesForDefine(variable.id, $event)"
                        (mouseleave)="hideReviewTimesForDefine($event)">
                        <div class="flex items-center justify-between mb-2">
                          <h4 class="text-sm font-medium text-gray-900">{{ t('review.times.title') }}</h4>
                          <span class="px-1.5 py-0.5 text-xs rounded-full bg-brand-green-100 text-brand-green-700">
                            {{ t('review.times.count', { count: variable.timeTable.sub.length }) }}
                          </span>
                        </div>
                        <div class="space-y-2">
                          <div *ngFor="let time of variable.timeTable.sub; let i = index"
                            class="p-2 text-xs border border-gray-100 rounded-lg">
                            <div class="flex items-center justify-between mb-1">
                              <span class="font-medium text-gray-800">{{ t('review.times.time', { index: i+1 })
                                }}</span>
                            </div>
                            <div class="grid grid-cols-2 gap-2">
                              <div>
                                <div class="text-gray-500">{{ t('review.times.tr') }}</div>
                                <div class="font-medium text-gray-800">{{ time.timeTr || '-' }}</div>
                              </div>
                              <div>
                                <div class="text-gray-500">{{ t('review.times.en') }}</div>
                                <div class="font-medium text-gray-800">{{ time.timeEn || '-' }}</div>
                              </div>
                            </div>
                            <div class="mt-1">
                              <div class="text-gray-500">{{ t('review.times.variable') }}</div>
                              <div class="flex items-center justify-between">
                                <span class="font-medium text-gray-800 truncate max-w-[150px]"
                                  [title]="time.variable?.name">{{ time.variable?.name || '-' }}</span>
                                <span *ngIf="time.variable?.measure" class="px-1.5 py-0.5 text-xs rounded-full ml-1"
                                  [class.text-data-blue-500]="time.variable.measure === 'Scale'"
                                  [class.bg-data-blue-100]="time.variable.measure === 'Scale'"
                                  [class.text-data-purple-500]="time.variable.measure === 'Ordinal'"
                                  [class.bg-data-purple-100]="time.variable.measure === 'Ordinal'"
                                  [class.text-data-orange-500]="time.variable.measure === 'Nominal'"
                                  [class.bg-data-orange-100]="time.variable.measure === 'Nominal'">
                                  {{ time.variable.measure }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </ng-container>
    </div>

    <!-- Navigation Buttons - Fixed at bottom -->
    <div class="sticky bottom-0 z-20 flex justify-between p-3 bg-white border-t border-gray-200">
      <button *ngIf="currentStep > 1" (click)="previousStep()"
        class="flex items-center gap-2 px-6 py-2 font-medium text-gray-700 transition-colors border border-gray-300 rounded-full hover:bg-gray-50">
        <ng-icon name="lucideArrowLeft" class="w-4 h-4"></ng-icon>
        {{ t('buttons.previous') }}
      </button>

      <div class="flex-1"></div>

      <button *ngIf="currentStep < reviewStep" [disabled]="!canProceedToNextStep()" (click)="nextStep()"
        class="flex items-center gap-2 px-6 py-2 font-medium text-white transition-colors rounded-full bg-brand-green-500 hover:bg-brand-green-600 disabled:opacity-50 disabled:cursor-not-allowed">
        {{ t('buttons.next') }}
        <ng-icon name="lucideArrowRight" class="w-4 h-4"></ng-icon>
      </button>

      <button *ngIf="currentStep === reviewStep" (click)="submitAnalysis()"
        class="flex items-center gap-2 px-6 py-2 font-medium text-white transition-colors rounded-full bg-brand-green-500 hover:bg-brand-green-600">
        {{ t('buttons.submit') }}
        <ng-icon name="lucideCheck" class="w-4 h-4"></ng-icon>
      </button>
    </div>
  </div>

  <!-- Right Column: Analysis Information -->
  <div class="flex flex-col w-1/4 h-full bg-white border-l border-gray-200">
    <!-- Info Header -->
    <div class="p-3 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-900">{{ t('review.title') }}</h2>
    </div>

    <!-- Info Content -->
    <div class="flex-1 p-3 overflow-y-auto">
      <div class="space-y-4">

        <!-- Variable Selection Info -->
        <div *ngIf="currentStep !== reviewStep"
          class="p-4 text-sm text-blue-800 border border-blue-200 rounded-3xl bg-blue-50">
          <div class="flex items-start">
            <ng-icon name="lucideInfo" class="w-5 h-5 mr-2 flex-shrink-0 mt-0.5 text-blue-500"></ng-icon>
            <p>
              <strong>{{ t('info.title') }}:</strong> {{ t('info.variables_in_use') }}
              <ng-container *ngIf="currentStep && currentAnalysisType && isStepSplitList(currentStep)">
                <br><br>
                <strong>{{ t('info.split_variable') }}:</strong> {{ t('info.split_description') }}
              </ng-container>
              <ng-container
                *ngIf="currentStep && currentAnalysisType === 'comean' && getStepName(currentStep) === t('steps.covariate_list')">
                <br><br>
                <strong>{{ t('info.covariate') }}:</strong> {{ t('info.covariate_description') }}
              </ng-container>
              <ng-container
                *ngIf="currentStep && currentAnalysisType === 'single' && getStepName(currentStep) === t('steps.variable_list')">
                <br><br>
                <strong>{{ t('info.single_group') }}:</strong> {{ t('info.single_group_description') }}
              </ng-container>
            </p>
          </div>
        </div>


        <!-- Help Text -->
        <div>
          <h3 class="mb-2 text-sm font-medium text-gray-500">{{ t('help.title') }}</h3>
          <p class="text-gray-700">{{ 'analyses.' + currentAnalysisType + '.help_text' | transloco }}</p>
        </div>

        <!-- Examples -->
        <div>
          <h3 class="mb-2 text-sm font-medium text-gray-500">{{ t('examples.title') }}</h3>
          <ul class="space-y-2 text-gray-700">
            <li class="flex items-start">
              <ng-icon name="lucideCircleCheck"
                class="w-5 h-5 text-brand-green-500 mr-2 flex-shrink-0 mt-0.5"></ng-icon>
              <span>{{ 'analyses.' + currentAnalysisType + '.examples.1' | transloco }}</span>
            </li>
            <li class="flex items-start">
              <ng-icon name="lucideCircleCheck"
                class="w-5 h-5 text-brand-green-500 mr-2 flex-shrink-0 mt-0.5"></ng-icon>
              <span>{{ 'analyses.' + currentAnalysisType + '.examples.2' | transloco }}</span>
            </li>
            <li class="flex items-start">
              <ng-icon name="lucideCircleCheck"
                class="w-5 h-5 text-brand-green-500 mr-2 flex-shrink-0 mt-0.5"></ng-icon>
              <span>{{ 'analyses.' + currentAnalysisType + '.examples.3' | transloco }}</span>
            </li>
          </ul>
        </div>

      </div>
    </div>
  </div>
</div>
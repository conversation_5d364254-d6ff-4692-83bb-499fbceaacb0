.button {
    @apply bg-white text-green-700 rounded-full transition-all p-3 font-medium text-sm sm:text-base border border-green-300 border-opacity-20 shadow-lg hover:shadow-xl hover:-translate-y-0.5 transform duration-300 flex gap-2 items-center justify-center;
}

ng-scrollbar {
    --scrollbar-size: 8px;
    --scrollbar-padding: 8px;
    --scrollbar-thumb-color: #16A34A;
    --scrollbar-hover-size: 12px;
    --scrollbar-track-color: #3f69483d;
    --scrollbar-border-radius: 12px;
  }

  .small-scrollbar {
    --scrollbar-size: 4px;
    --scrollbar-padding: 4px;
    --scrollbar-thumb-color: #16A34A;
    --scrollbar-hover-size: 6px;
    --scrollbar-track-color: #3f69483d;
    --scrollbar-border-radius: 6px;
  }

  // Custom styles for ng-select
  ::ng-deep .ng-select-custom {
    .ng-select-container {
      border-radius: 3rem !important;
      overflow: hidden;
      border-color: #D1D5DB !important;
      min-height: 42px !important;
      font-size: 0.875rem !important;
    }

    .ng-select-container:hover {
      border-color: #16A34A !important;
    }

    .ng-select-container .ng-value-container .ng-placeholder {
      color: #6B7280 !important;
    }

    .ng-select-container .ng-value-container .ng-value {
      max-width: 90% !important;
      overflow: hidden !important;
    }

    &.ng-select-focused:not(.ng-select-opened) > .ng-select-container {
      border-color: #16A34A !important;
      box-shadow: 0 0 0 2px rgba(22, 163, 74, 0.25) !important;
    }

    .ng-dropdown-panel {
      border-radius: 3rem !important;
      overflow: hidden;
      border: 1px solid #E5E7EB !important;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
      width: auto !important;
      min-width: 100% !important;
    }

    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
      padding: 8px 12px !important;
      font-size: 0.875rem !important;
      white-space: normal !important;
    }

    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
      background-color: rgba(22, 163, 74, 0.1) !important;
      color: #16A34A !important;
    }

    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
      background-color: rgba(22, 163, 74, 0.05) !important;
      color: #16A34A !important;
    }

    // Truncate styles
    .truncate {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
  }


.fade-in {
  z-index: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #F4F4F5;
  top: 0;
  right: 0;
  animation: heartRateIn 4s linear infinite;
}

.fade-out {
  position: absolute;
  width: 120%;
  height: 100%;
  top: 0;
  left: -120%;
  z-index: 0;
  animation: heartRateOut 4s linear infinite;
  background: rgba(244, 244, 244, 1);
  background: -moz-linear-gradient(left, rgba(244, 244, 244, 1) 0%, rgba(244, 244, 244, 1) 50%, rgba(255, 255, 255, 0) 100%);
  background: -webkit-linear-gradient(left, rgba(244, 244, 244, 1) 0%, rgba(244, 244, 244, 1) 50%, rgba(255, 255, 255, 0) 100%);
  background: -o-linear-gradient(left, rgba(244, 244, 244, 1) 0%, rgba(244, 244, 244, 1) 50%, rgba(255, 255, 255, 0) 100%);
  background: -ms-linear-gradient(left, rgba(244, 244, 244, 1) 0%, rgba(244, 244, 244, 1) 50%, rgba(255, 255, 255, 0) 100%);
  background: linear-gradient(to right, rgba(244, 244, 244, 1) 0%, rgba(244, 244, 244, 1) 80%, rgba(255, 255, 255, 0) 100%);
}

@keyframes heartRateIn {
  0% {
    width: 100%;
  }
  50% {
    width: 0;
  }
  100% {
    width: 0;
  }
}

@keyframes heartRateOut {
  0% {
    left: -120%;
  }
  30% {
    left: -120%;
  }
  100% {
    left: 0;
  }
}

// Dropdown position styles
.dropdown-top {
  bottom: 100%;
  margin-bottom: 0.5rem;
  top: auto;
}

.dropdown-bottom {
  top: 100%;
  margin-top: 0.5rem;
  bottom: auto;
}

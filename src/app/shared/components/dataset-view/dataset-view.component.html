<div class="flex flex-col h-[90vh] w-[95vw] bg-white rounded-3xl shadow-lg overflow-hidden"
    *transloco="let t; read: 'dataset_view'">
    <!-- Header with dataset name and actions -->
    <div class="flex items-center justify-between p-4 bg-white border-b border-neutral-200">
        <div class="truncate">
            <h1 class="text-2xl font-bold truncate text-[#0F2C6E]">{{datasetInfo.name}}</h1>
            <div class="flex items-center text-sm text-neutral-600">
                <span class="mx-2">•</span>
                <span>{{datasetInfo.rowCount || 0}} {{ t('rows') }}</span>
                <span class="mx-2">•</span>
                <span>{{datasetInfo.variableCount || 0}} {{ t('variables') }}</span>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="flex gap-3">
            <button
                class="px-4 py-2 rounded-full text-[#0F2C6E] border-2 border-[#CAD6F2] bg-[#F5F9FF] hover:bg-[#EBF2FF] flex items-center gap-2"
                (click)="exportDataset()">
                <ng-icon class="text-xl" name="lucideDownload"></ng-icon>
                <span class="text-nowrap">{{ t('export') }}</span>
            </button>
            <button class="p-2 text-gray-500 transition-all rounded-full hover:text-red-500 hover:bg-gray-100"
                (click)="hideModal()" aria-label="Close">
                <ng-icon class="text-xl" name="lucideX"></ng-icon>
            </button>
        </div>
    </div>

    <!-- Error state -->
    <div *ngIf="hasError" class="flex items-center justify-center flex-grow p-8">
        <div class="text-center">
            <div class="mb-4 text-red-500">
                <ng-icon name="lucideCircleAlert" class="text-4xl"></ng-icon>
            </div>
            <h3 class="mb-2 text-lg font-medium text-gray-900">{{ t('error_title') }}</h3>
            <p class="mb-4 text-gray-500">{{ t('error_description') }}</p>
            <button class="px-4 py-2 text-white bg-blue-500 rounded-3xl hover:bg-blue-600" (click)="loadDataView()">
                {{ t('try_again') }}
            </button>
        </div>
    </div>

    <!-- Content area - only show when no error -->
    <div *ngIf="!hasError" class="flex flex-col flex-1 p-4 overflow-hidden">
        <!-- Controls bar -->
        <div class="flex flex-wrap items-center justify-between gap-3 mb-4">
            <div class="flex flex-wrap items-center gap-2" *ngIf="activeView != 'data'">
                <!-- Search input -->
                <div class="relative">
                    <input type="text" [(ngModel)]="searchTerm"
                        class="w-64 px-4 py-2.5 rounded-3xl border-2 border-neutral-200 focus:border-[#1E3A8A] focus:ring-2 focus:ring-[#EEF2FF] placeholder-gray-400 transition-colors duration-200"
                        [placeholder]="activeView === 'data' ? t('search_placeholder') : t('search_variable_placeholder')" />
                    <div class="absolute inset-y-0 flex items-center pointer-events-none right-4">
                        <ng-icon class="text-xl text-gray-400" name="lucideSearch"></ng-icon>
                    </div>
                </div>

                <!-- Filter dropdown -->
                <div class="relative inline-block">
                    <button
                        class="inline-flex items-center gap-2 px-4 py-2 rounded-3xl border-2 border-neutral-200 bg-white hover:border-[#1E3A8A] transition-colors duration-200"
                        (click)="showFilterOptions = !showFilterOptions">
                        <ng-icon class="text-xl text-[#1E3A8A]" name="lucideFilter"></ng-icon>
                        <span class="text-gray-700">
                            <ng-container *ngIf="activeView === 'data'">{{ t('filter') }}</ng-container>
                            <ng-container *ngIf="activeView === 'variables'">
                                <ng-container [ngSwitch]="typeFilter">
                                    <ng-container *ngSwitchCase="'all'">{{ t('filter_options.all_variables')
                                        }}</ng-container>
                                    <ng-container *ngSwitchCase="'scale'">{{ t('filter_options.scale') }}</ng-container>
                                    <ng-container *ngSwitchCase="'nominal'">{{ t('filter_options.nominal')
                                        }}</ng-container>
                                    <ng-container *ngSwitchCase="'ordinal'">{{ t('filter_options.ordinal')
                                        }}</ng-container>
                                </ng-container>
                            </ng-container>
                        </span>
                    </button>

                    <!-- Filter dropdown menu -->
                    <div *ngIf="showFilterOptions && activeView === 'variables'"
                        class="absolute z-10 w-56 mt-2 overflow-hidden bg-white border-2 shadow-lg rounded-3xl border-neutral-200">
                        <div class="p-2">
                            <button class="w-full px-3 py-2 text-left rounded-3xl"
                                [ngClass]="typeFilter === 'all' ? 'bg-[#EEF2FF] text-[#1E3A8A]' : 'hover:bg-neutral-50'"
                                (click)="typeFilter = 'all'; showFilterOptions = false">
                                {{ t('filter_options.all_variables') }}
                            </button>
                            <button class="w-full px-3 py-2 text-left rounded-3xl"
                                [ngClass]="typeFilter === 'scale' ? 'bg-[#EEF2FF] text-[#1E3A8A]' : 'hover:bg-neutral-50'"
                                (click)="typeFilter = 'scale'; showFilterOptions = false">
                                {{ t('filter_options.scale') }}
                            </button>
                            <button class="w-full px-3 py-2 text-left rounded-3xl"
                                [ngClass]="typeFilter === 'nominal' ? 'bg-[#EEF2FF] text-[#1E3A8A]' : 'hover:bg-neutral-50'"
                                (click)="typeFilter = 'nominal'; showFilterOptions = false">
                                {{ t('filter_options.nominal') }}
                            </button>
                            <button class="w-full px-3 py-2 text-left rounded-3xl"
                                [ngClass]="typeFilter === 'ordinal' ? 'bg-[#EEF2FF] text-[#1E3A8A]' : 'hover:bg-neutral-50'"
                                (click)="typeFilter = 'ordinal'; showFilterOptions = false">
                                {{ t('filter_options.ordinal') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="activeView == 'data'">

            </div>
            <!-- Data/Variable view toggle -->
            <div class="flex overflow-hidden border-2 border-neutral-200 rounded-3xl">
                <button class="px-4 py-2"
                    [ngClass]="activeView === 'data' ? 'bg-[#003CBD] text-white' : 'bg-white text-gray-700 hover:bg-neutral-100'"
                    (click)="toggleView('data')">
                    {{ t('data_view') }}
                </button>
                <button class="px-4 py-2"
                    [ngClass]="activeView === 'variables' ? 'bg-[#003CBD] text-white' : 'bg-white text-gray-700 hover:bg-neutral-100'"
                    (click)="toggleView('variables')">
                    {{ t('variable_view') }}
                </button>
            </div>
        </div>

        <!-- Data View -->
        <ng-container *ngIf="activeView === 'data' && dataRows.length > 0">
            <!-- Filter indicators -->
            <div *ngIf="searchTerm || dataFilterColumn" class="flex gap-2 mb-3">
                <div *ngIf="searchTerm"
                    class="flex items-center px-3 py-1 text-sm text-blue-700 border border-blue-200 rounded-full bg-blue-50">
                    <span>{{ t('search') }} "{{searchTerm}}"</span>
                    <button class="ml-2 text-blue-400 hover:text-blue-600" (click)="searchTerm = ''">
                        <ng-icon name="lucideX" class=""></ng-icon>
                    </button>
                </div>
                <div *ngIf="dataFilterColumn"
                    class="flex items-center px-3 py-1 text-sm text-blue-700 border border-blue-200 rounded-full bg-blue-50">
                    <span>{{ t('filter_column') }} {{getColumnName(dataFilterColumn)}} = "{{dataFilterValue}}"</span>
                    <button class="ml-2 text-blue-400 hover:text-blue-600" (click)="clearColumnFilter()">
                        <ng-icon name="lucideX" class=""></ng-icon>
                    </button>
                </div>
                <button *ngIf="searchTerm || dataFilterColumn"
                    class="px-3 py-1 text-sm text-blue-700 border border-blue-200 rounded-full hover:bg-blue-100"
                    (click)="clearFilters()">
                    {{ t('clear_filters') }}
                </button>
            </div>

            <!-- Data table -->
            <div class="flex-1 overflow-auto bg-white border border-neutral-200 rounded-3xl">
                <table class="w-full">
                    <thead class="bg-[#EEF2FF] sticky top-0">
                        <tr>
                            <!-- Row number header -->
                            <th
                                class="px-6 py-3 text-left text-sm font-semibold text-[#0F2C6E] border-r border-neutral-200 w-16">
                                #</th>

                            <!-- Column headers -->
                            <th *ngFor="let column of columns"
                                class="px-6 py-3 text-left text-sm font-semibold text-[#0F2C6E] border-r">
                                <div class="flex items-center gap-2">
                                    <span>{{column.name}}</span>
                                    <!-- Variable type indicator -->
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                        [ngClass]="column.typeColor">
                                        {{column.typeLabel | titlecase}}
                                    </span>


                                    <!-- Sort indicator -->
                                    <button
                                        class="flex items-center justify-center w-6 h-6 transition-all rounded-full hover:bg-blue-100"
                                        (click)="sortData(column.id)" aria-label="Sort by {{column.name}}">
                                        <ng-icon class="w-4 h-4" [name]="getSortIconName(column.id)"
                                            [ngClass]="{'text-[#003CBD]': isSortedBy(column.id), 'text-gray-400': !isSortedBy(column.id)}"
                                            [style.transform]="getSortIconRotation(column.id)">
                                        </ng-icon>
                                    </button>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-neutral-200">

                        <!-- Data rows - use filteredDataRows instead of dataRows -->
                        <tr *ngFor="let row of filteredDataRows"
                            class="transition-colors duration-200 hover:bg-neutral-100">
                            <td class="px-6 py-4 text-sm text-gray-700 border-r border-neutral-200">{{row.id}}</td>
                            <td *ngFor="let column of columns" class="px-6 py-4 text-sm text-gray-700 border-r"
                                [ngClass]="{'text-right': column.type === 'scale'}">
                                <ng-container *ngIf="column.type === 'scale'">
                                    {{row[column.id]}}
                                </ng-container>
                                <ng-container *ngIf="column.type != 'scale'">
                                    {{row[column.id]}}
                                </ng-container>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Results count indicator -->
            <div *ngIf="searchTerm || dataFilterColumn" class="py-2 text-sm text-gray-600">
                {{ t('search_results.showing', { filtered: filteredDataRows.length, total: dataRows.length }) }}
            </div>
        </ng-container>

        <!-- Variable View -->
        <ng-container *ngIf="activeView === 'variables'">
            <!-- Display info when no variables match filter -->
            <div *ngIf="filteredVariables.length === 0"
                class="flex flex-col items-center justify-center h-64 text-center">
                <div class="w-16 h-16 rounded-full bg-[#EEF2FF] flex items-center justify-center mb-4">
                    <ng-icon class="w-8 h-8 text-[#1E3A8A]" name="lucideCircleAlert"></ng-icon>
                </div>
                <h3 class="text-lg font-semibold text-[#0F2C6E] mb-2">{{ t('search_results.not_found_title') }}</h3>
                <p class="max-w-md text-gray-500">
                    {{ t('search_results.not_found_description') }}
                </p>
            </div>

            <!-- Variables list -->
            <div class="pb-4 space-y-3 overflow-auto">
                <div *ngFor="let variable of filteredVariables"
                    class="overflow-hidden transition-all duration-200 bg-white border rounded-3xl border-neutral-200">
                    <!-- Variable header - always visible -->
                    <div class="flex justify-between items-center px-4 py-3 cursor-pointer hover:bg-[#FAFBFC]"
                        (click)="toggleVariableExpansion(variable.id)">
                        <div class="flex items-center gap-3">
                            <!-- Expand/collapse icon -->
                            <ng-icon *ngIf="expandedVariables[variable.id]" class="text-lg text-gray-400"
                                name="lucideChevronUp"></ng-icon>
                            <ng-icon *ngIf="!expandedVariables[variable.id]" class="text-lg text-gray-400"
                                name="lucideChevronDown"></ng-icon>

                            <!-- Variable name and type -->
                            <div class="flex items-center gap-2">
                                <span class="text-base font-medium text-[#0F2C6E]">{{variable.name}}</span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                    [ngClass]="variable.typeColor">
                                    {{variable.typeLabel | titlecase}}
                                </span>
                            </div>

                            <!-- Basic info -->
                            <div class="items-center hidden gap-3 text-sm text-gray-500 md:flex">
                                <span>TR: {{variable.labelTR}}</span>
                                <span>•</span>
                                <span>EN: {{variable.labelEN}}</span>
                                <ng-container *ngIf="variable.missingValues > 0">
                                    <span>•</span>
                                    <span class="text-[#FF9500]">{{variable.missingValues}} {{ t('variable.missing')
                                        }}</span>
                                </ng-container>
                            </div>
                        </div>

                        <div class="flex items-center gap-2">
                            <button class="p-1 rounded-full hover:bg-[#EEF2FF] text-[#003CBD]">
                                <ng-icon class="text-lg" name="lucideEye"></ng-icon>
                            </button>
                        </div>
                    </div>

                    <!-- Expandable details -->
                    <div *ngIf="expandedVariables[variable.id]" class="p-4 bg-white border-t border-neutral-100">
                        <!-- Variable labels for all variable types -->
                        <div class="grid gap-4 mb-4 md:grid-cols-2">
                            <div>
                                <div class="mb-1 text-xs font-medium text-gray-500">{{ t('variable.label_tr') }}</div>
                                <div class="text-sm font-medium">{{variable.labelTR}}</div>
                            </div>
                            <div>
                                <div class="mb-1 text-xs font-medium text-gray-500">{{ t('variable.label_en') }}</div>
                                <div class="text-sm font-medium">{{variable.labelEN}}</div>
                            </div>
                        </div>

                        <!-- Value labels only for nominal and ordinal variables -->
                        <ng-container *ngIf="variable.type === 'nominal' || variable.type === 'ordinal'">
                            <div class="mt-3">
                                <div class="mb-2 text-xs font-medium text-gray-500">{{ t('variable.value_labels') }}
                                </div>
                                <div class="overflow-auto border rounded-3xl max-h-60 border-neutral-100">
                                    <table class="w-full border-collapse">
                                        <thead class="sticky top-0 bg-neutral-50">
                                            <tr>
                                                <th class="px-3 py-2 text-xs font-medium text-left text-gray-500">{{
                                                    t('variable.value') }}</th>
                                                <th class="px-3 py-2 text-xs font-medium text-left text-gray-500">{{
                                                    t('variable.label_tr') }}</th>
                                                <th class="px-3 py-2 text-xs font-medium text-left text-gray-500">{{
                                                    t('variable.label_en') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-neutral-100">
                                            <tr *ngFor="let label of variable.valueLabels" class="hover:bg-neutral-50">
                                                <td class="px-3 py-2 text-sm">{{label.value}}</td>
                                                <td class="px-3 py-2 text-sm">{{label.labelTR}}</td>
                                                <td class="px-3 py-2 text-sm">{{label.labelEN}}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
            </div>
        </ng-container>
    </div>
</div>
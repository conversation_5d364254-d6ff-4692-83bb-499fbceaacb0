:host {
  display: block;
  border-radius: 24px !important;  
  overflow: hidden;
}

// Custom scrollbar styling
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

// Table styling enhancements
table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  th, td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  th {
    position: sticky;
    top: 0;
    background-color: #f9fafb;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    z-index: 10;
  }

  thead tr {
    height: 48px;
  }

  tbody tr {
    height: 40px;
    
    &:nth-child(odd) {
      background-color: #fafafa;
    }
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
}

// Animation for skeleton loading
@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

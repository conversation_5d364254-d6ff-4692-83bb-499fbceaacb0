import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { DatasetService } from '@app/data/services/dataset.service';
import { DatasetInfo, DataColumn, DataVariable, DataRow, ValueLabel } from '@app/data/models/dataset.interface';
import { ExcelService } from '@app/data/services/excel.service';

@Component({
  selector: 'app-dataset-view',
  templateUrl: './dataset-view.component.html',
  styleUrls: ['./dataset-view.component.scss']
})
export class DatasetViewComponent implements OnInit {
  // View state
  activeView: 'data' | 'variables' = 'data';
  searchTerm: string = '';
  typeFilter: 'all' | 'scale' | 'nominal' | 'ordinal' = 'all';
  showFilterOptions: boolean = false;
  expandedVariables: { [key: string]: boolean } = {};

  // Dataset information
  datasetInfo: DatasetInfo = {
    name: "Loading...",
    rowCount: 0,
    variableCount: 0
  };

  // Data for tables
  columns: DataColumn[] = [];
  dataRows: DataRow[] = [];
  variables: DataVariable[] = [];

  // Raw dataset data
  datasetData: any = null;

  // Sorting state
  sortColumn: string | null = null;
  sortDirection: 'asc' | 'desc' = 'asc';

  // Add error tracking
  hasError: boolean = false;
  errorMessage: string = '';

  // Add data filtering properties
  dataFilterColumn: string | null = null;
  dataFilterValue: string = '';

  constructor(
    private datasetService: DatasetService,
    private excelService: ExcelService, // Add ExcelService to the constructor
    @Inject(DIALOG_DATA) public data: { title: string, s3_url: string, type?: string, dataset_id?: string },
    public dialogRef: DialogRef<DatasetViewComponent>,
  ) {}

  async ngOnInit(): Promise<void> {
    try {
      this.datasetInfo.name = this.data.title;
      this.datasetInfo.s3_url = this.data.s3_url;
      
      // Fetch initial data
      await this.loadDataView();
    } catch (error) {
      console.error('Error initializing component:', error);
      this.hasError = true;
      this.errorMessage = 'Veri seti yüklenirken bir hata oluştu.';

    }
  }

  async loadDataView(): Promise<void> {
    try {
      this.activeView = 'data';
      this.hasError = false;
      
      try {
        const urlParts = this.data.s3_url.split('/');
        const fileName = urlParts[urlParts.length - 1];
        
        // Get the file blob from the S3 URL
        const fileBlob = await this.datasetService.getDataset(fileName);
        
        // Use ExcelService to read the file
        const excelData = await this.excelService.readExcelFile(new File([fileBlob], fileName));
        
        // Extract the variables_json part which contains all the data we need
        this.datasetData = excelData.variables_json;
        
        // Set basic information
        this.datasetInfo.name = this.data.title;
        this.datasetInfo.rowCount = this.datasetData.data?.length || 0;
        this.datasetInfo.variableCount = this.datasetData.variable_list?.length || 0;
        
        // Map headers directly to columns with proper type conversion
        this.columns = this.datasetData.headers.map((header: string, index: number) => {
          // Find the corresponding variable definition in variable_list
          const variable = this.datasetData.variable_list.find((v: any) => v.header === header);
          
          if (!variable) {
            console.warn(`No variable definition found for column: ${header}`);
          }
          
          // Convert the measure string to our type format
          let type: 'scale' | 'nominal' | 'ordinal';
          switch (variable?.measure) {
            case 'Scale':
              type = 'scale';
              break;
            case 'Nominal':
              type = 'nominal';
              break;
            case 'Ordinal':
              type = 'ordinal';
              break;
            default:
              // Default to scale if unknown
              type = 'scale';
          }
          
          return {
            id: `col_${index}`,
            name: header,
            type: type,
            typeLabel: type,
            typeColor: this.getTypeColor(type)
          };
        });
        
        // Map data rows directly from the 2D array
        this.dataRows = this.datasetData.data.map((row: any[], rowIndex: number) => {
          const dataRow: DataRow = { id: rowIndex + 1 };
          this.columns.forEach((column, colIndex) => {
            dataRow[column.id] = row[colIndex];
          });
          return dataRow;
        });
        
        // Build variable information for the variable view
        this.variables = this.datasetData.variable_list.map((variable: any, index: number) => {
          // Convert measure string to our type format
          let type: 'scale' | 'nominal' | 'ordinal';
          switch (variable.measure) {
            case 'Scale':
              type = 'scale';
              break;
            case 'Nominal':
              type = 'nominal';
              break;
            case 'Ordinal':
              type = 'ordinal';
              break;
            default:
              type = 'scale';
          }
          
          const dataVariable: DataVariable = {
            id: variable.id,
            name: variable.header,
            labelTR: variable.label?.tr || variable.header,
            labelEN: variable.label?.en || variable.header,
            type: type,
            typeLabel: type,
            typeColor: this.getTypeColor(type),
            description: '',
            missingValues: variable.missingCount || 0,
            uniqueValues: 0  // We'll calculate this later
          };
          
          // For categorical variables (nominal/ordinal), add value labels
          if (type === 'nominal' || type === 'ordinal') {
            if (variable.value_labels) {
              const valueLabels: ValueLabel[] = [];
              
              // Process the value_labels object
              Object.entries(variable.value_labels.tr).forEach(([value, label]) => {
                valueLabels.push({
                  value: Number(value),
                  labelTR: label as string,
                  labelEN: variable.value_labels.en[value] as string,
                  count: 0  // We don't have counts yet
                });
              });
              
              dataVariable.valueLabels = valueLabels;
            }
          }
          
          return dataVariable;
        });

        

      } catch (error) {
        console.error('Error loading dataset:', error);
        this.hasError = true;
        this.errorMessage = 'Veri seti yüklenirken bir hata oluştu. Lütfen tekrar deneyin.';
      }
    } catch (finalError) {
      console.error('Critical error loading dataset view:', finalError);
      this.hasError = true;
      this.errorMessage = 'Veri seti yüklenirken kritik bir hata oluştu.';
    }
  }

  // Add this new method for exporting the dataset
  async exportDataset(): Promise<void> {
    try {
      if (!this.datasetData) {
        console.error('No dataset data available to export');
        return;
      }

      // Create a blob from the dataset data
      const exportData = {
        variables_json: this.datasetData
      };

      // Use the Excel service to create an Excel file blob
      const excelBlob = await this.excelService.writeExcelFile(exportData, this.datasetInfo.name);
      
      // Create a download link and trigger the download
      const url = window.URL.createObjectURL(excelBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${this.datasetInfo.name || 'dataset'}.xlsx`;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('Error exporting dataset:', error);
    }
  }

  // Toggle between data and variable view
  toggleView(view: 'data' | 'variables'): void {
    if (this.activeView !== view) {
      this.activeView = view;
    }
  }
  
  // Toggle variable expansion
  toggleVariableExpansion(variableId: string): void {
    this.expandedVariables = {
      ...this.expandedVariables,
      [variableId]: !this.expandedVariables[variableId]
    };
  }
  
  // Get filtered variables based on search term and type filter
  get filteredVariables(): DataVariable[] {
    return this.variables.filter(variable => {
      const matchesSearch = this.searchTerm === '' || 
        variable.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        variable.labelTR?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        variable.labelEN?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        variable.description?.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesType = this.typeFilter === 'all' || variable.type === this.typeFilter;
      
      return matchesSearch && matchesType;
    });
  }

  // Get filtered data rows based on search term and column filter
  get filteredDataRows(): DataRow[] {
    if (!this.dataRows || this.dataRows.length === 0) return [];
    
    return this.dataRows.filter(row => {
      // Search across all columns
      if (this.searchTerm !== '') {
        const searchTermLower = this.searchTerm.toLowerCase();
        // Check if any column value includes the search term
        const rowMatches = Object.values(row).some(value => 
          value !== null && 
          value !== undefined && 
          String(value).toLowerCase().includes(searchTermLower)
        );
        
        if (!rowMatches) return false;
      }
      
      // Apply column-specific filter if set
      if (this.dataFilterColumn && this.dataFilterValue) {
        const cellValue = row[this.dataFilterColumn];
        if (cellValue === undefined || cellValue === null) return false;
        
        return String(cellValue).toLowerCase().includes(this.dataFilterValue.toLowerCase());
      }
      
      return true;
    });
  }
  
  // Set data filter column
  setDataFilterColumn(columnId: string | null): void {
    if (this.dataFilterColumn === columnId) {
      // If clicking the same column, clear the filter
      this.dataFilterColumn = null;
      this.dataFilterValue = '';
    } else {
      this.dataFilterColumn = columnId;
      this.dataFilterValue = '';
    }
    this.showFilterOptions = false;
  }
  
  // Clear all filters
  clearFilters(): void {
    this.searchTerm = '';
    this.dataFilterColumn = null;
    this.dataFilterValue = '';
    this.typeFilter = 'all';
  }
  
  // Add missing methods
  getColumnName(columnId: string): string {
    const column = this.columns.find(col => col.id === columnId);
    return column ? column.name : columnId;
  }

  clearColumnFilter(): void {
    this.dataFilterColumn = null;
    this.dataFilterValue = '';
  }
  
  getTypeColor(type: string): string {
    switch(type) {
      case 'scale': return 'bg-[#DBEAFE] text-[#3B82F6]';
      case 'nominal': return 'bg-[#FCF3CC] text-[#FE7D20]';
      case 'ordinal': return 'bg-[#F3E8FF] text-[#A855F7]';
      default: return 'bg-gray-100 text-gray-600';
    }
  }
  

  
  // Calculate percentage
  calculatePercentage(count: number, total: number): number {
    if (!total) return 0;
    return Math.round((count / total) * 100);
  }

  // Close the modal
  hideModal(): void {
    this.dialogRef.close();
  }

  // Sort data by column
  sortData(columnId: string): void {
    if (this.sortColumn === columnId) {
      // Toggle sort direction if clicking the same column
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // Set new sort column and default to ascending
      this.sortColumn = columnId;
      this.sortDirection = 'asc';
    }

    // Sort dataRows based on the column and direction
    this.dataRows = [...this.dataRows].sort((a, b) => {
      const aValue = a[columnId];
      const bValue = b[columnId];
      
      // Handle numeric sorting
      if (!isNaN(Number(aValue)) && !isNaN(Number(bValue))) {
        return this.sortDirection === 'asc' 
          ? Number(aValue) - Number(bValue) 
          : Number(bValue) - Number(aValue);
      }
      
      // Handle string sorting
      const aString = String(aValue || '').toLowerCase();
      const bString = String(bValue || '').toLowerCase();
      
      if (this.sortDirection === 'asc') {
        return aString.localeCompare(bString, 'tr');
      } else {
        return bString.localeCompare(aString, 'tr');
      }
    });
  }

  // Check if column is sorted
  isSortedBy(columnId: string): boolean {
    return this.sortColumn === columnId;
  }

  // Get sort icon name based on sort state
  getSortIconName(columnId: string): string {
    return 'lucideChevronUp';
  }

  // Get rotation for sort icon
  getSortIconRotation(columnId: string): string {
    if (this.sortColumn !== columnId) {
      return 'rotate(0deg)';
    }
    return this.sortDirection === 'asc' ? 'rotate(0deg)' : 'rotate(180deg)';
  }
}

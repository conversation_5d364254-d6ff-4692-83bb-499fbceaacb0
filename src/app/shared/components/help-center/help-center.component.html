<div class=" p-6 bg-white rounded-2xl h-[calc(100dvh-12rem)] w-[calc(100dvw-12rem)] flex flex-col">
    <!-- Header -->
    <div class="flex flex-col flex-none gap-4 mb-6">
        <div class="flex items-center justify-between">
            <h2 class="text-2xl font-semibold text-gray-900">Yardım Merkezi</h2>
            <button (click)="close()" class="flex items-center justify-center p-2 text-gray-500 rounded-full hover:bg-gray-100">
                <ng-icon name="lucideX" class="text-xl font-medium text-gray-500"></ng-icon>
            </button>
        </div>
        
        <!-- Search Input -->
        <div class="relative">
            <input type="text"
                   [(ngModel)]="searchQuery"
                   (input)="onSearch($event)"
                   placeholder="Yardım içeriğinde ara..."
                   class="w-full px-4 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            
            <!-- Search Results Dropdown -->
            <div *ngIf="searchResults.length > 0" 
                 class="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg">
                <div *ngFor="let result of searchResults" 
                     (click)="selectSearchResult(result)"
                     class="p-3 cursor-pointer hover:bg-gray-50">
                    <div class="text-sm font-medium text-gray-900">{{ result.section.title }}</div>
                    <div class="text-sm text-gray-500">{{ result.subsection.title }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Section (show only when no section is selected) -->
    <div *ngIf="!selectedSection" class="overflow-auto">
        <div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
            <!-- Veri Yükleme -->
            <div (click)="selectOverviewItem('Veri Yönetimi')" 
                 class="p-4 transition-colors bg-white border border-gray-200 cursor-pointer rounded-xl hover:border-brand-blue-500">
                <div class="flex flex-col items-center gap-3 text-center">
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                        📊
                    </div>
                    <div>
                        <h3 class="font-medium text-brand-blue-700">Veri Yönetimi</h3>
                        <p class="mt-1 text-sm text-gray-600">Veri yükleme ve düzenleme</p>
                    </div>
                </div>
            </div>

            <!-- Analizler -->
            <div (click)="selectOverviewItem('Analiz Türleri')"
                 class="p-4 transition-colors bg-white border border-gray-200 cursor-pointer rounded-xl hover:border-brand-blue-500">
                <div class="flex flex-col items-center gap-3 text-center">
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                        📈
                    </div>
                    <div>
                        <h3 class="font-medium text-brand-blue-700">Analiz Türleri</h3>
                        <p class="mt-1 text-sm text-gray-600">İstatistiksel analizler</p>
                    </div>
                </div>
            </div>


            <!-- Ödemeler -->
            <div (click)="selectOverviewItem('Kredi Satın Alma')"
                 class="p-4 transition-colors bg-white border border-gray-200 cursor-pointer rounded-xl hover:border-brand-blue-500">
                <div class="flex flex-col items-center gap-3 text-center">
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                        💳
                    </div>
                    <div>
                        <h3 class="font-medium text-brand-blue-700">Kredi Satın Alma</h3>
                        <p class="mt-1 text-sm text-gray-600">Kredi ve ödeme işlemleri</p>
                    </div>
                </div>
            </div>

            <!-- SSS -->
            <div (click)="selectOverviewItem('Sık Sorulan Sorular')"
                 class="p-4 transition-colors bg-white border border-gray-200 cursor-pointer rounded-xl hover:border-brand-blue-500">
                <div class="flex flex-col items-center gap-3 text-center">
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                        ❓
                    </div>
                    <div>
                        <h3 class="font-medium text-brand-blue-700">SSS</h3>
                        <p class="mt-1 text-sm text-gray-600">Sık sorulan sorular</p>
                    </div>
                </div>
            </div>

            <!-- İletişim - Updated description -->
            <div (click)="selectOverviewItem('İletişim')"
                 class="p-4 transition-colors bg-white border border-gray-200 cursor-pointer rounded-xl hover:border-brand-blue-500">
                <div class="flex flex-col items-center gap-3 text-center">
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-brand-blue-100">
                        ✉️
                    </div>
                    <div>
                        <h3 class="font-medium text-brand-blue-700">İletişim</h3>
                        <p class="mt-1 text-sm text-gray-600">Destek ve iletişim bilgileri</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Area (show when section is selected) -->
    <div *ngIf="selectedSection" class="flex gap-6 overflow-auto grow">
        <!-- Navigation Tree -->
        <div class="pr-2 overflow-y-auto border-r w-72">
            <div class="space-y-2">
                <div *ngFor="let section of sections" class="relative">
                    <!-- Main Section -->
                    <button (click)="toggleSection(section)" 
                            class="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-600 rounded-lg text-start hover:bg-gray-50"
                            [class.bg-blue-50]="selectedSection?.id === section.id">
                        <span class="flex-1">{{ section.title }}</span>
                        <ng-icon [name]="section.isExpanded ? 'heroChevronDown' : 'heroChevronRight'" 
                                class="w-4 h-4"></ng-icon>
                    </button>
                    
                    <!-- Subsections -->
                    <div *ngIf="section.isExpanded" class="mt-1 ml-4 space-y-1">
                        <button *ngFor="let subsection of section.subsections"
                                (click)="selectSubsection(section, subsection)"
                                class="flex items-center w-full px-4 py-2 text-sm text-gray-500 rounded-lg hover:bg-gray-50"
                                [class.bg-blue-50]="selectedSubsection?.id === subsection.id"
                                [class.text-blue-600]="selectedSubsection?.id === subsection.id">
                            {{ subsection.title }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="flex-1 overflow-y-auto">
            <div *ngIf="selectedSubsection" class="prose max-w-none">
                <div [innerHTML]="selectedSubsection.content"></div>
            </div>
        </div>
    </div>
</div>
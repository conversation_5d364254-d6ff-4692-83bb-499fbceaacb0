import { Component, OnInit, ElementRef } from '@angular/core';
import { DialogRef } from '@angular/cdk/dialog';
import { HelpCenterService, HelpSection, HelpSubsection } from '@app/shared/services/help-center.service';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

interface NavigationTarget {
  type: 'section' | 'subsection' | 'anchor';
  sectionId?: number;
  subsectionId?: string;
  anchorId?: string;
}

@Component({
  selector: 'app-help-center',
  templateUrl: './help-center.component.html',
})
export class HelpCenterComponent implements OnInit {
  sections: (HelpSection & { isExpanded?: boolean })[] = [];
  selectedSection: HelpSection | null = null;
  selectedSubsection: HelpSubsection | null = null;
  
  searchQuery = '';
  searchResults: { section: HelpSection, subsection: HelpSubsection }[] = [];
  private searchSubject = new Subject<string>();
  
  readonly navigationMap: Record<string, NavigationTarget> = {
    '<PERSON>eri <PERSON>net<PERSON>': { type: 'section', sectionId: 2 },
    '<PERSON><PERSON><PERSON>': { type: 'section', sectionId: 3 },
    'Kredi Satın Alma': { type: 'subsection', sectionId: 1, subsectionId: '1.3' },
    'Sık Sorulan Sorular': { type: 'section', sectionId: 4 },
    'İletişim': { type: 'anchor', sectionId: 4, subsectionId: '4.1', anchorId: 'contact-info' }
  };

  constructor(
    private dialogRef: DialogRef<any>,
    private helpCenterService: HelpCenterService,
    private elementRef: ElementRef
  ) {
    // Setup search debounce
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(query => {
      this.performSearch(query);
    });
  }

  ngOnInit() {
    this.sections = this.helpCenterService.getHelpSections().map(section => ({
      ...section,
      isExpanded: false
    }));
    
    if (this.sections.length > 0) {
    //   this.toggleSection(this.sections[0]);
    }
  }

  toggleSection(section: HelpSection & { isExpanded?: boolean }) {
    // Close other sections
    this.sections.forEach(s => {
      if (s.id !== section.id) s.isExpanded = false;
    });
    
    // Toggle clicked section
    section.isExpanded = !section.isExpanded;
    
    // Select section and its first subsection
    this.selectedSection = section;
    if (section.subsections?.length && section.isExpanded) {
      this.selectSubsection(section, section.subsections[0]);
    }
  }

  selectSubsection(section: HelpSection, subsection: HelpSubsection) {
    this.selectedSection = section;
    this.selectedSubsection = subsection;
  }

  close() {
    this.dialogRef.close();
  }

  onSearch(event: Event): void {
    const query = (event.target as HTMLInputElement).value;
    this.searchQuery = query;
    this.searchSubject.next(query);
  }

  private performSearch(query: string): void {
    if (!query.trim()) {
      this.searchResults = [];
      return;
    }
    this.searchResults = this.helpCenterService.searchContent(query);
  }

  selectSearchResult(result: { section: HelpSection, subsection: HelpSubsection }): void {
    this.sections.forEach(s => s.isExpanded = s.id === result.section.id);
    this.selectSubsection(result.section, result.subsection);
    this.searchQuery = '';
    this.searchResults = [];
  }

  selectOverviewItem(itemTitle: string) {
    const target = this.navigationMap[itemTitle];
    if (!target) return;

    const section = this.sections.find(s => s.id === target.sectionId);
    if (!section) return;

    this.toggleSection(section);

    switch (target.type) {
      case 'section':
        if (section.subsections?.length) {
          this.selectSubsection(section, section.subsections[0]);
        }
        break;

      case 'subsection':
        const subsection = section.subsections?.find(sub => sub.id === target.subsectionId);
        if (subsection) {
          this.selectSubsection(section, subsection);
        }
        break;

      case 'anchor':
        const targetSubsection = section.subsections?.find(sub => sub.id === target.subsectionId);
        if (targetSubsection) {
          this.selectSubsection(section, targetSubsection);
          
          // Wait for next tick and DOM updates
          setTimeout(() => {
            // Find elements within the dialog context
            const contentArea = this.elementRef.nativeElement.querySelector('.max-w-4xl');
            if (contentArea) {
              contentArea.scrollTop = 0;
            }
            
            // Then find and scroll to anchor with a delay
            setTimeout(() => {
              const element = this.elementRef.nativeElement.querySelector(`#${target.anchorId}`);
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
              }
            }, 1000);
          });
        }
        break;
    }
  }
}

// Animasyon keyframes tanımı
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

// <PERSON>ma ikonları için animasyon stilleri
// Her ikon için farklı animasyon gecikmesi ve süresi ayarlanıyor
:host ::ng-deep {
  .icon-animated {
    position: absolute;
    animation: float 3s ease-in-out infinite;
    opacity: 0.6;
  }
  
  // Her ikon için farklı animasyon gecikmeleri
  .icon-delay-1 {
    animation-delay: 0s;
    animation-duration: 3s;
  }
  
  .icon-delay-2 {
    animation-delay: 0.5s;
    animation-duration: 3.5s;
  }
  
  .icon-delay-3 {
    animation-delay: 1s;
    animation-duration: 4s;
  }
  
  .icon-delay-4 {
    animation-delay: 1.5s;
    animation-duration: 3.2s;
  }
  
  .icon-delay-5 {
    animation-delay: 2s;
    animation-duration: 3.8s;
  }
  
  .icon-delay-6 {
    animation-delay: 0.7s;
    animation-duration: 4.2s;
  }
  
  .icon-delay-7 {
    animation-delay: 1.2s;
    animation-duration: 3.6s;
  }
  
  .icon-delay-8 {
    animation-delay: 1.8s;
    animation-duration: 4.5s;
  }
  
  .icon-delay-9 {
    animation-delay: 0.3s;
    animation-duration: 3.4s;
  }
  
  .icon-delay-10 {
    animation-delay: 0.9s;
    animation-duration: 3.9s;
  }
}
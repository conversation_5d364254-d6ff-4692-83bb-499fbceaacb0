import { Component } from '@angular/core';
interface IconDef {
  left: string;
  top: string;
  size: string;
  color: string;
}
@Component({
  selector: 'app-pagenotfound',
  templateUrl: './pagenotfound.component.html',
  styleUrls: ['./pagenotfound.component.scss'],
})
export class PagenotfoundComponent {
  icons: IconDef[] = [
    { left: '5%',  top: '15%', size: '6xl', color: 'brand-green-500' },
    { left: '48%', top: '12%', size: '6xl', color: 'brand-blue-500' },
    { left: '80%', top: '5%',  size: '5xl', color: 'brand-green-500' },
    { left: '18%', top: '35%', size: '5xl', color: 'brand-blue-500' },
    { left: '89%', top: '37%', size: '7xl', color: 'brand-blue-500' },
    { left: '60%', top: '70%', size: '5xl', color: 'brand-blue-500' },
    { left: '10%', top: '78%', size: '8xl', color: 'brand-blue-500' },
    { left: '41%', top: '93%', size: '4xl', color: 'brand-green-500' },
    { left: '69%', top: '45%', size: '5xl', color: 'brand-green-500' },
    { left: '82%', top: '82%', size: '5xl', color: 'brand-green-500' },
  ];
  goBack() {
    history.back();
  }
}
<div *transloco="let t ; read: 'settings'" class="flex flex-col gap-2 p-5 bg-white rounded-3xl">
    <div class="flex justify-between ">
        <p class="flex items-center gap-2 text-3xl font-semibold text-center text-blue-950 ">
            <ng-icon name="matHistoryRound" class="text-4xl text-blue-600 "></ng-icon>
            {{t('purchase_history')}}
        </p>
        <button (click)="close()"
            class=" flex items-center justify-center px-2 p-1 text-sm font-normal transition-all bg-white border shadow gap-0.5 hover:border-red-600 text-green-950 active:scale-95 group rounded-3xl hover:border-3 ">
            <ng-icon class="text-xl transition-all " name="matCloseRound"></ng-icon>
        </button>
    </div>
    <div class="w-full overflow-auto text-center grow max-h-108">
        <table *ngIf="orders.length!=0" class="relative w-full table-auto">
            <thead class="">
                <tr
                    class="sticky top-0 z-10 items-center text-sm font-bold uppercase bg-slate-100 backdrop-blur-sm text-prussian-blue-900">
                    <th class="px-4 py-3 text-start ">
                        <p>
                            {{t('product')}}
                        </p>
                    </th>
                    <th class="px-4 py-3 text-end">
                        <p>
                            {{t('status')}}
                        </p>
                    </th>
                    <th class="px-4 py-3 text-end">
                        <p>
                            {{t('date')}}
                        </p>
                    </th>
                    <th class="px-4 py-3 text-end">
                        <p>
                            {{t('price')}}
                        </p>
                    </th>
                    <th class="px-4 py-3 text-center">
                        <p>
                            {{t('invoice')}}
                        </p>
                    </th>
                    <!--TODO waiting for bill-->
                    <!-- <th class="max-w-32">
                    <p class="truncate">
                        Dışa Aktar
                    </p>
                </th> -->
                </tr>
            </thead>
            <tbody class="">
                <tr *ngFor="let item of orders" class="transition-all border-b border-dashed hover:bg-zinc-100">
                    <td class="relative h-0 pr-4 text-start max-h-20 hover:max-h-auto ">
                        <p class="overflow-hidden truncate peer ">
                            {{item.total_credit}} {{t('credit')}}
                        </p>
                    </td>
                    <td class="flex items-center justify-end h-14">
                        <p [ngClass]="{'text-red-700 border-red-500 bg-red-500/50': item.status === 'canceled', 'text-green-700 border-green-500 bg-green-500/50': item.status === 'approved', 'text-yellow-700 border-yellow-500 bg-yellow-500/50': item.status === 'pending'}"
                            class="px-2 py-1 text-sm border rounded-3xl">
                            {{t(item.status)}}
                        </p>
                    </td>
                    <td class="truncate text-end">
                        {{item.created_at | date: 'dd/MM/yyyy HH:mm'}}
                    </td>
                    <td class="truncate text-end">{{ math.trunc(item.total_price)| thousandSeparator }}
                        ₺ </td>
                    <td class="w-20">
                        <div class="flex items-center justify-end w-12 h-14 ">
                            <button class="p-1 rounded-lg bg-sky-700 " (click)="getOrderInvoice(item.id)">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                                    fill="none">
                                    <path d="M14.166 10.8333L9.99935 15M9.99935 15L5.83268 10.8333M9.99935 15V5"
                                        stroke="white" stroke-width="1.66667" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        <p *ngIf="orders.length==0" class="w-full text-center">
            {{t('no_purchase')}}
        </p>
    </div>
    <div class="flex justify-end flex-1 gap-1 text-xs text-end text-sky-600">
        <button (click)="showContract('cookie')" class="underline">
            {{t('cookie')}},
        </button>
        <button (click)="showContract('membership_agreement')" class="underline ">
            {{t('membership_agreement')}},
        </button>
        <button (click)="showContract('privacy_policy')" class="underline">
            {{t('privacy_policy')}}
        </button>
        {{t('agreement_text')}}
    </div>

</div>
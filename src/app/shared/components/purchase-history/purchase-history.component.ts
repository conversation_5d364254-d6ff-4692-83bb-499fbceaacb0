import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { PaymentService } from '@app/data/services/payment.service';
import { SnotifyService } from 'ng-alt-snotify';
import { AgreementComponent } from '../agreement/agreement.component';
@Component({
  selector: 'app-purchase-history',
  templateUrl: './purchase-history.component.html',
  styleUrls: ['./purchase-history.component.scss']
})
export class PurchaseHistoryComponent implements OnInit {
  constructor(
    @Inject(DIALOG_DATA) public data,
    public dialogRef: DialogRef<any>,
    private dialog: Dialog,
    private paymentService: PaymentService,
    private snotifyService: SnotifyService,
  ) {
  }
  ngOnInit() {
    this.getOrders();
  }
  close() {
    this.dialogRef.close();
  }
  orders = [];
  math: Math = Math;
  getOrders() {
    this.paymentService.getOrders(
    ).subscribe(
      {
        next: (data) => {
          this.orders = data;
          this.orders = this.orders
            .filter((order) => order.status != "draft")
            .sort((a, b) => {
              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            })
            .map((order) => {
              return {
                ...order,
                total_credit: order.order_items.reduce((acc, item) => acc + item.credit * item.quantity, 0)
              }
            });
        },
        error: (error) => {
          console.log(error);
        },
        complete: () => {
        }
      });
  }
  getOrderInvoice(order_id: string) {
    this.paymentService.getOrderInvoice(order_id).subscribe({
      next: (data) => {
        if (data.status == 'ready') {
          window.open(data.url, '_blank');
        }
        else {
          this.snotifyService.error(data.message, {
            position: 'centerBottom',
            timeout: 1000,
            pauseOnHover: false
          });
        }
      },
      error: (error) => {
        this.snotifyService.error(error.error.message, {
          position: 'centerBottom',
          timeout: 1000,
          pauseOnHover: false
        });
      },
      complete: () => {
      }
    }
    );
  }
  showContract(type:string){
    this.dialog.open(AgreementComponent, {
      data: { type: type }
    });
  }
}

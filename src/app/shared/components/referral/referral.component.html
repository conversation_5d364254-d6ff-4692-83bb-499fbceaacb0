<div class="referral-wrapper" *transloco="let t; read: 'shared.referral'">
    <!-- Header -->
    <div class="referral-header">
        <button href="javascript:void(0)" (click)="openBonusTermsDialog()" class="help-button">
            <ng-icon name="lucideCircleHelp"></ng-icon>
        </button>
        <div class="header-title">
            <ng-icon name="lucideUserRoundPlus" class="gift-icon"></ng-icon>
            <h2>{{t('title')}}</h2>
        </div>
        <button (click)="closeDialog()" class="close-button">
            <ng-icon name="lucideX"></ng-icon>
        </button>
    </div>

    <!-- Content -->
    <div class="referral-content">
        <div class="referral-grid">

            <div class="referral-left-section">
                <!-- Your Referral Code Section -->
                <div class="flex justify-between referral-card" id="referralCodeCard">
                    <div>
                        <h3 class="section-title">{{t('your_referral_code')}}</h3>

                        <!-- Skeleton loader for referral code -->
                        <div *ngIf="!referralCode; else referralCodeContent" class="referral-skeleton-container">
                            <div class="referral-skeleton-code"></div>
                            <div class="referral-skeleton-button"></div>
                        </div>

                        <ng-template #referralCodeContent>
                            <div class="code-wrapper">
                                <!-- Referral Code - Main area -->
                                <div class="code-main-area">
                                    <!-- Copy button text -->
                                    <div class="referral-code-display" id="codeDisplay">
                                        <span class="referral-code-text" id="codeText">{{referralCode}}</span>

                                        <button (click)="copyReferralCode()" class="blue-button copy-button"
                                            id="copyButton">
                                            <ng-icon name="lucideCopy"></ng-icon>
                                            <span>{{t(copyButtonText)}}</span>
                                        </button>
                                    </div>

                                    <div class="code-hint">
                                        <span>Kodunuzu paylaşmak için QR kodunuza tıklayın</span>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </div>

                    <!-- QR Code Image -->
                    <div class="qr-code-wrapper" id="qrCodeWrapper">
                        <img src="assets/img/qrdemo.png" class="qr-code-image" id="qrCodeImage"
                            (click)="openImageModal()">
                    </div>
                </div>

                <!-- Enter Someone's Referral Code -->
                <div class="referral-card enter-code-card" id="enterCodeCard">
                    <!-- Skeleton loader for referral code status -->
                    <div *ngIf="isReferralCodeUsed === null; else referralCodeStatusContent" class="skeleton-status">
                        <div class="skeleton-title"></div>
                        <div class="skeleton-input"></div>
                        <div class="skeleton-message"></div>
                    </div>
                    <ng-template #referralCodeStatusContent>
                        <h3 *ngIf="!receivedReferral" class="section-title">
                            {{t('use_referral_code')}}
                        </h3>
                        <h3 *ngIf="receivedReferral" class="section-title">
                            {{t('referral_code_used')}}
                        </h3>

                        <!-- Show whose referral code you used -->
                        <div *ngIf="receivedReferral" class="referral-status-card" [ngClass]="{'completed-status': receivedReferral.status === 'completed', 
                                        'pending-status': receivedReferral.status === 'pending'}">

                            <div class="referral-status-content">
                                <div class="referral-status-header">
                                    <div class="referral-user-info">
                                        <span [ngClass]="{'text-success': receivedReferral.status === 'completed', 
                                                        'text-pending': receivedReferral.status === 'pending'}">
                                            {{receivedReferral.referrer_name}}
                                        </span>
                                        <span [ngClass]="{'text-success-light': receivedReferral.status === 'completed', 
                                                        'text-pending-light': receivedReferral.status === 'pending'}">
                                            {{t('user_used_referral_code')}}
                                        </span>
                                    </div>
                                    <span [ngClass]="{'text-success-light': receivedReferral.status === 'completed', 
                                                    'text-pending-light': receivedReferral.status === 'pending'}">
                                        {{receivedReferral.created_at | date:'d MMMM yyyy'}}
                                    </span>
                                </div>

                                <!-- Updated info message for pending status -->
                                <div *ngIf="receivedReferral && receivedReferral.status === 'pending' && !showChangeCodeForm"
                                    class="info-message-box">
                                    <div class="info-message-content">
                                        <span class="info-icon-wrapper">
                                            <ng-icon name="lucideInfo" class="info-icon"></ng-icon>
                                        </span>
                                        <span class="info-text">
                                            {{t('bonus_info')}} <a href="javascript:void(0)"
                                                (click)="openBonusTermsDialog()">{{t('bonus_terms_info')}}</a>
                                        </span>
                                    </div>
                                    <!-- Buttons for changing or removing the referral code when in pending status -->
                                    <div *ngIf="canChangeReferralCode && !showChangeCodeForm" class="action-buttons">
                                        <button (click)="resetReferralCode()" class="outline-blue-button">
                                            <ng-icon name="lucideRefreshCw"></ng-icon>
                                            <span>{{t('use_different_code')}}</span>
                                        </button>
                                        <button (click)="removeReferralCode()" class="outline-red-button">
                                            <ng-icon name="lucideTrash2"></ng-icon>
                                            <span>{{t('remove_code')}}</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Code entry form - Only show if no referral code or showChangeCodeForm is true and first purchase not made -->
                        <div *ngIf="(!receivedReferral && !isReferralCodeUsed) || (receivedReferral && showChangeCodeForm)"
                            class="code-entry-form">
                            <div class="code-input-wrapper">
                                <input type="text" [(ngModel)]="inputReferralCode"
                                    placeholder="{{t('enter_referral_code')}}" class="code-input"
                                    [ngClass]="{'error-input': selfReferralError || invalidReferralCodeError}"
                                    (input)="onReferralCodeInput()">
                                <!-- Error messages -->
                                <div *ngIf="selfReferralError" class="error-message">
                                    {{t('self_referral_error')}}
                                </div>
                                <!-- Add new error message for invalid referral code -->
                                <div *ngIf="invalidReferralCodeError" class="error-message">
                                    {{t('invalid_referral_code_error')}}
                                </div>
                            </div>
                            <div class="form-buttons">
                                <button (click)="applyReferralCode()" [disabled]="!inputReferralCode"
                                    class="blue-button apply-button">
                                    {{ showChangeCodeForm ? t('change_code') : t('apply_code') }}
                                </button>

                                <!-- Cancel code change button -->
                                <button *ngIf="showChangeCodeForm" (click)="cancelChangeCode()"
                                    class="outline-blue-button">
                                    {{t('cancel')}}
                                </button>
                            </div>
                        </div>

                        <!-- Info Message -->
                        <div *ngIf="!receivedReferral" class="info-message-box info-message-default">
                            <ng-icon name="lucideInfo" class="info-icon"></ng-icon>
                            <span *ngIf="!isReferralCodeUsed" class="info-text">
                                {{t('referral_code_usage_info')}} <a href="javascript:void(0)"
                                    (click)="openBonusTermsDialog()">{{t('bonus_terms_info')}}</a>
                            </span>
                            <span *ngIf="isReferralCodeUsed && !receivedReferral" class="info-text">
                                {{t('previous_purchase_info')}}
                            </span>
                        </div>
                    </ng-template>
                </div>
            </div>

            <!-- Referral History -->
            <div class="referral-card history-card" id="historyCard">
                <div class="flex items-center history-header">
                    <h3 class="section-title">{{t('referral_history')}}</h3>

                    <!-- Stats Card -->
                    <div *ngIf="referrals.length > 0;" class="stats-card">
                        <div class="flex items-center gap-2">
                            <div class="stat-value">{{totalReferrals}}</div>
                            <div class="stat-label">{{t('total_referrals')}}</div>
                        </div>
                        <div class="stat-divider"></div>
                        <div class="flex items-center gap-2">
                            <div class="stat-value">{{totalRewards}}</div>
                            <div class="stat-label">{{t('earned_credit')}}</div>
                        </div>
                    </div>
                </div>

                <!-- Skeleton loader for referral history table -->
                <div *ngIf="!referrals || referrals.length === 0 && totalReferrals !== 0; else referralHistoryContent"
                    class="history-table-container skeleton-table">
                    <div class="history-table-wrapper">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>{{t('user')}}</th>
                                    <th class="text-center">{{t('date')}}</th>
                                    <th class="text-center">{{t('earned_credits')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Skeleton rows -->
                                <tr *ngFor="let i of [1,2,3]">
                                    <td>
                                        <div class="user-cell">
                                            <div class="skeleton-avatar"></div>
                                            <div class="skeleton-name"></div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="skeleton-date"></div>
                                    </td>
                                    <td class="text-center">
                                        <div class="skeleton-credits"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <ng-template #referralHistoryContent>
                    <div *ngIf="referrals.length > 0; else emptyState" class="history-table-container">
                        <div class="history-table-wrapper">
                            <table class="history-table">
                                <thead>
                                    <tr>
                                        <th>{{t('user')}}</th>
                                        <th class="text-center">{{t('date')}}</th>
                                        <th class="text-center">{{t('earned_credits')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let referral of referrals; let last = last" [class.last-row]="last"
                                        class="history-row">
                                        <td>
                                            <div class="user-cell">
                                                <div class="user-avatar">
                                                    <ng-icon name="lucideUser"></ng-icon>
                                                </div>
                                                <span>{{referral.full_name || referral.name}}</span>
                                            </div>
                                        </td>
                                        <td class="text-center">{{referral.date | date:'d MMMM yyyy'}}</td>
                                        <td class="text-center">
                                            <span class="credit-amount">
                                                <img src="assets/icons/istacoin.svg" alt="istacoin Logo"
                                                    class="coin-icon">
                                                <span>{{referral.reward}}</span>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </ng-template>

                <!-- Empty State -->
                <ng-template #emptyState>
                    <div class="empty-state" id="emptyState">
                        <ng-icon name="lucideUsers" class="empty-icon"></ng-icon>
                        <p class="empty-title">{{t('no_referrals_yet')}}</p>
                        <p class="empty-description">{{t('share_to_earn')}}</p>
                        <!-- Share on Social Media -->
                        <button *ngIf="!showShareButtons2" (click)="revealShareButtons2()"
                            class="blue-button share-button">
                            {{t('share_referral_code')}}
                        </button>

                        <!-- Social media buttons - initially hidden -->
                        <div *ngIf="showShareButtons2" class="social-buttons" id="socialButtons">
                            <button (click)="shareOnSocialMedia('whatsapp')" class="whatsapp-button">
                                <div class="button-content">
                                    <ng-icon name="lucideMessageCircle"></ng-icon>
                                    <span>{{t('share_via_whatsapp')}}</span>
                                </div>
                            </button>
                            <button (click)="shareOnSocialMedia('email')" class="email-button">
                                <div class="button-content">
                                    <ng-icon name="lucideMail"></ng-icon>
                                    <span>{{t('share_via_email')}}</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </ng-template>
            </div>
        </div>
    </div>
</div>

<!-- Bonus Terms Dialog -->
<div *ngIf="isBonusTermsDialogOpen" class="fixed inset-0 z-50 flex items-center justify-center"
    style="background-color: rgba(0,0,0,0.5);" (click)="closeBonusTermsDialog()">
    <div class="relative w-full max-w-4xl p-6 mx-4 bg-white rounded-3xl" (click)="$event.stopPropagation()"
        *transloco="let t; read: 'shared.referral'">
        <!-- Dialog Header -->
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center gap-2 text-brand-blue-600">
                <ng-icon name="lucideInfo" class="text-xl"></ng-icon>
                <h3 class="text-xl font-semibold">{{t('bonus_terms_title')}}</h3>
            </div>
            <button (click)="closeBonusTermsDialog()" class="p-2 text-gray-500 hover:text-gray-700 focus:outline-none">
                <ng-icon name="lucideX" class="text-xl"></ng-icon>
            </button>
        </div>

        <!-- Dialog Content -->
        <div class="px-2 py-4 overflow-y-auto max-h-[60vh]">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">{{t('referral_program')}}</h4>
                <p class="text-gray-700">
                    {{t('referral_bonus_info')}}
                </p>

                <!-- Kredi Kazanma Tablosu -->
                <div class="p-4 border rounded-xl bg-brand-blue-50">
                    <h5 class="mb-3 font-medium text-brand-blue-700">{{t('credit_earning_table')}}</h5>
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse">
                            <thead>
                                <tr class="bg-brand-blue-100">
                                    <th
                                        class="p-2 text-sm font-semibold text-left border text-brand-blue-800 border-brand-blue-200">
                                        {{t('purchased_credit')}}
                                    </th>
                                    <th
                                        class="p-2 text-sm font-semibold text-center border text-brand-blue-800 border-brand-blue-200">
                                        {{t('referrer_earnings')}}
                                    </th>
                                    <th
                                        class="p-2 text-sm font-semibold text-center border text-brand-blue-800 border-brand-blue-200">
                                        {{t('referred_earnings')}}
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="hover:bg-brand-blue-100/50">
                                    <td class="p-2 text-sm border border-brand-blue-200">100 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        12 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        5 kredi</td>
                                </tr>
                                <tr class="hover:bg-brand-blue-100/50">
                                    <td class="p-2 text-sm border border-brand-blue-200">150 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        16 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        5 kredi</td>
                                </tr>
                                <tr class="hover:bg-brand-blue-100/50">
                                    <td class="p-2 text-sm border border-brand-blue-200">300+ kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        28 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        7 kredi</td>
                                </tr>
                                <tr class="hover:bg-brand-blue-100/50">
                                    <td class="p-2 text-sm border border-brand-blue-200">1000 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        40 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        10 kredi</td>
                                </tr>
                                <tr class="hover:bg-brand-blue-100/50">
                                    <td class="p-2 text-sm border border-brand-blue-200">2000 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        72 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        18 kredi</td>
                                </tr>
                                <tr class="hover:bg-brand-blue-100/50">
                                    <td class="p-2 text-sm border border-brand-blue-200">6000+ kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        200 kredi</td>
                                    <td
                                        class="p-2 text-sm font-semibold text-center border border-brand-blue-200 text-brand-blue-600">
                                        50 kredi</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="mt-2 text-xs italic text-brand-blue-600">
                        {{t('table_note')}}
                    </p>
                </div>

                <div class="p-4 border rounded-xl bg-neutral-50">
                    <h5 class="mb-2 font-medium text-gray-900">{{t('referrer_title')}}</h5>
                    <ul class="pl-5 space-y-2 text-gray-700 list-disc">
                        <li>{{t('referrer_info_1')}}</li>
                        <li>{{t('referrer_info_2')}}</li>
                        <li>{{t('referrer_info_3')}}</li>
                        <li>{{t('referrer_info_4')}}</li>
                    </ul>
                </div>

                <div class="p-4 border rounded-xl bg-neutral-50">
                    <h5 class="mb-2 font-medium text-gray-900">{{t('referred_title')}}</h5>
                    <ul class="pl-5 space-y-2 text-gray-700 list-disc">
                        <li>{{t('referred_info_1')}}</li>
                        <li>{{t('referred_info_2')}}</li>
                        <li>{{t('referred_info_3')}}</li>
                        <!-- <li>Kayıt olduktan sonra 30 gün içinde satın alım yapılması gerekir.</li> -->
                    </ul>
                </div>

                <div class="p-4 border rounded-xl bg-blue-50">
                    <h5 class="mb-2 font-medium text-brand-blue-700">{{t('additional_info')}}</h5>
                    <ul class="pl-5 space-y-2 list-disc text-brand-blue-600">
                        <!-- <li>Kazanılan krediler 6 ay süreyle geçerlidir.</li> -->
                        <li>{{t('additional_info_1')}}</li>
                        <li>{{t('additional_info_2')}}</li>
                        <li>{{t('additional_info_3')}}</li>
                        <!-- <li>Kazanılan kredileri istediğin zaman kullanabilirsin.</li> -->
                    </ul>
                </div>
            </div>
        </div>

        <!-- Dialog Footer -->
        <div class="flex justify-end mt-4">
            <button (click)="closeBonusTermsDialog()"
                class="px-4 py-2 text-white rounded-full bg-brand-blue-600 hover:bg-brand-blue-700 focus:outline-none focus:ring-2 focus:ring-brand-blue-500 focus:ring-offset-2">
                {{t('understood')}}
            </button>
        </div>
    </div>
</div>

<!-- QR Code Modal -->
<div *ngIf="isImageModalOpen" class="modal-backdrop" id="modalBackdrop" (click)="closeImageModal()">
    <div class="modal-content" (click)="$event.stopPropagation()" *transloco="let t; read: 'shared.referral'">
        <div class="modal-body">
            <div class="flex justify-between">
                <div class="w-3 h-3"></div>
                <div class="modal-code">{{referralCode}}</div>
                <button (click)="closeImageModal()">
                    <ng-icon name="lucideX" class="text-2xl"></ng-icon>
                </button>
            </div>
            <div class="modal-qr-container rounded-3xl">
                <img [src]="modalImageSrc" alt="QR Code" class="modal-qr-image">
            </div>
            <p class="modal-description">
                Kodu taratarak istabot ailesine katılın, analizlerinizi ışık hızında yaparken hem siz kazanın hem
                arkadaşlarınız kazansın!
            </p>

            <!-- Share buttons in modal -->
            <div class="modal-share-buttons">
                <button (click)="shareOnSocialMedia('whatsapp')" class="whatsapp-button">
                    <div class="button-content">
                        <ng-icon name="lucideMessageCircle"></ng-icon>
                        <span>{{t('share_via_whatsapp')}}</span>
                    </div>
                </button>
                <button (click)="shareOnSocialMedia('email')" class="email-button">
                    <div class="button-content">
                        <ng-icon name="lucideMail"></ng-icon>
                        <span>{{t('share_via_email')}}</span>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>
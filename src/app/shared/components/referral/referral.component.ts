import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>iew<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild } from '@angular/core';
import { ReferralService } from '@app/shared/services/referral.service';
import { DialogRef } from '@angular/cdk/dialog';
import { gsap } from 'gsap';

interface Referral {
  id: number;
  name: string;
  full_name: string;
  date: string;
  reward: number;
  user_id?: number; // Added to help with matching rewards
}

interface Credit {
  id: number;
  credit_amount: number;
  remaining_credit: number;
  created_at: string;
  expires_at: string;
  order: {
    id: number;
    total_price: string;
  };
  credit_source: {
    earned_from: string;
    user: {
      id: number;
      email: string;
      name: string;
    };
  };
}

interface ReceivedReferral {
  id: number;
  referrer_id: number;
  referrer_name: string;
  created_at: string;
  status: string;
  code: string;
  reward?: number;
}

@Component({
  selector: 'app-referral',
  templateUrl: './referral.component.html',
  styleUrls: ['./referral.component.scss']
})
export class ReferralComponent implements OnI<PERSON>t, <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {
  referralCode: string = '';
  totalReferrals: number | null = null;
  totalRewards: number | null = null;
  referrals: Referral[] = [];
  credits: Credit[] = [];
  inputReferralCode: string = '';
  isReferralCodeUsed: boolean | null = null;
  receivedReferral: ReceivedReferral | null = null;
  canChangeReferralCode: boolean = false;
  showChangeCodeForm: boolean = false;
  // Skeleton loader için dummy array
  skeletonArray = [1, 2, 3];
  // Bonus terms dialog state
  isBonusTermsDialogOpen: boolean = false;
  selfReferralError: boolean = false;
  // Add invalid referral code error flag
  invalidReferralCodeError: boolean = false;
  private currentUserId: number = 0;
  copyButtonText = 'copy';
  isImageModalOpen: boolean = false;
  modalImageSrc: string = '';
  
  // GSAP animations
  private timeline: gsap.core.Timeline | null = null;
  private modalTimeline: gsap.core.Timeline | null = null;

  constructor(
    private referralService: ReferralService,
    private dialogRef: DialogRef<any>,
    private elementRef: ElementRef
  ) { }

  ngOnInit(): void {
    // User ID'yi localStorage'dan al
    const userId = localStorage.getItem('user_id');
    if (userId) {
      this.currentUserId = parseInt(userId, 10);
      this.loadReferralData();
    } else {
      console.error('User ID not found in localStorage');
    }
  }

  ngAfterViewInit(): void {
    // Initialize GSAP animations after view is initialized
    this.initializeAnimations();
  }

  ngOnDestroy(): void {
    // Clean up GSAP timelines when component is destroyed
    if (this.timeline) {
      this.timeline.kill();
      this.timeline = null;
    }
    
    if (this.modalTimeline) {
      this.modalTimeline.kill();
      this.modalTimeline = null;
    }
  }

  private initializeAnimations(): void {
    // Create main timeline
    this.timeline = gsap.timeline();
    
    // Select elements to animate
    const header = this.elementRef.nativeElement.querySelector('.referral-header');
    const referralCodeCard = this.elementRef.nativeElement.querySelector('#referralCodeCard');
    const enterCodeCard = this.elementRef.nativeElement.querySelector('#enterCodeCard');
    const historyCard = this.elementRef.nativeElement.querySelector('#historyCard');
    const qrCodeWrapper = this.elementRef.nativeElement.querySelector('#qrCodeWrapper');
    const codeDisplay = this.elementRef.nativeElement.querySelector('#codeDisplay');
    const codeText = this.elementRef.nativeElement.querySelector('#codeText');
    const copyButton = this.elementRef.nativeElement.querySelector('#copyButton');
    const emptyState = this.elementRef.nativeElement.querySelector('#emptyState');
    
    // Stagger animation for cards
    if (header && referralCodeCard && enterCodeCard && historyCard) {
      this.timeline
        .from(header, { 
          y: -20, 
          opacity: 0, 
          duration: 0.5, 
          ease: 'power3.out' 
        })
        .from(referralCodeCard, { 
          y: 30, 
          opacity: 0, 
          duration: 0.5, 
          ease: 'power3.out' 
        }, '-=0.3')
        .from(enterCodeCard, { 
          y: 30, 
          opacity: 0, 
          duration: 0.5, 
          ease: 'power3.out' 
        }, '-=0.3')
        .from(historyCard, { 
          y: 30, 
          opacity: 0, 
          duration: 0.5, 
          ease: 'power3.out' 
        }, '-=0.3');
    }
    
    // Animate QR code and code display
    if (qrCodeWrapper && codeDisplay && codeText && copyButton) {
      this.timeline
        .from(qrCodeWrapper, { 
          scale: 0.8, 
          opacity: 0, 
          duration: 0.6, 
          ease: 'back.out(1.7)' 
        }, '-=0.2')
        .from(codeDisplay, { 
          x: -20, 
          opacity: 0, 
          duration: 0.5, 
          ease: 'power3.out' 
        }, '-=0.4')
        .from(codeText, { 
          scale: 0.9, 
          opacity: 0, 
          duration: 0.4, 
          ease: 'back.out(1.7)' 
        }, '-=0.2')
        .from(copyButton, { 
          scale: 0.9, 
          opacity: 0, 
          duration: 0.4, 
          ease: 'back.out(1.7)' 
        }, '-=0.2');
    }
    
    // Add subtle pulse animation to QR code
    if (qrCodeWrapper) {
      gsap.to(qrCodeWrapper, {
        boxShadow: '0 8px 30px rgba(30, 58, 138, 0.3)',
        scale: 1.02,
        duration: 1.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut'
      });
    }
    
    // Animate empty state if present
    if (emptyState) {
      const emptyIcon = emptyState.querySelector('.empty-icon');
      const emptyTitle = emptyState.querySelector('.empty-title');
      const emptyDesc = emptyState.querySelector('.empty-description');
      const shareButton = emptyState.querySelector('.share-button');
      
      if (emptyIcon && emptyTitle && emptyDesc && shareButton) {
        this.timeline
          .from(emptyIcon, { 
            scale: 0.5, 
            opacity: 0, 
            duration: 0.5, 
            ease: 'back.out(1.7)' 
          }, '-=0.1')
          .from(emptyTitle, { 
            y: 20, 
            opacity: 0, 
            duration: 0.4, 
            ease: 'power3.out' 
          }, '-=0.3')
          .from(emptyDesc, { 
            y: 20, 
            opacity: 0, 
            duration: 0.4, 
            ease: 'power3.out' 
          }, '-=0.3')
          .from(shareButton, { 
            y: 20, 
            opacity: 0, 
            duration: 0.4, 
            ease: 'power3.out' 
          }, '-=0.2');
      }
    }
  }

  openImageModal(): void {
    this.modalImageSrc = 'assets/img/qrdemo.png';
    this.isImageModalOpen = true;
    document.body.classList.add('overflow-hidden');
    
    // Create modal animation timeline
    setTimeout(() => {
      const modalBackdrop = document.querySelector('#modalBackdrop');
      const modalContent = modalBackdrop?.querySelector('.modal-content');
      const modalCode = modalContent?.querySelector('.modal-code');
      const modalQr = modalContent?.querySelector('.modal-qr-container');
      const modalDesc = modalContent?.querySelector('.modal-description');
      const modalButtons = modalContent?.querySelector('.modal-share-buttons');
      
      if (modalBackdrop && modalContent && modalCode && modalQr && modalDesc && modalButtons) {
        this.modalTimeline = gsap.timeline();
        this.modalTimeline
          .from(modalContent, { 
            scale: 0.8, 
            opacity: 0, 
            duration: 0.5, 
            ease: 'back.out(1.7)' 
          })
          .from(modalCode, { 
            y: -20, 
            opacity: 0, 
            duration: 0.4, 
            ease: 'power3.out' 
          }, '-=0.2')
          .from(modalQr, { 
            scale: 0.9, 
            opacity: 0, 
            duration: 0.5, 
            ease: 'back.out(1.7)' 
          }, '-=0.2')
          .from(modalDesc, { 
            y: 20, 
            opacity: 0, 
            duration: 0.4, 
            ease: 'power3.out' 
          }, '-=0.2')
          .from(modalButtons, { 
            y: 20, 
            opacity: 0, 
            duration: 0.4, 
            ease: 'power3.out' 
          }, '-=0.2');
        
        // Add pulse effect to QR code
        gsap.to(modalQr, {
          boxShadow: '0 10px 30px rgba(30, 58, 138, 0.3)',
          duration: 2,
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut'
        });
      }
    }, 100);
  }

  closeImageModal(): void {
    // Animate modal closing
    const modalBackdrop = document.querySelector('#modalBackdrop');
    const modalContent = modalBackdrop?.querySelector('.modal-content');
    
    if (modalBackdrop && modalContent && this.modalTimeline) {
      // Reverse the animation and then close the modal
      gsap.to(modalContent, {
        scale: 0.9,
        opacity: 0,
        duration: 0.3,
        ease: 'power3.inOut',
        onComplete: () => {
          this.isImageModalOpen = false;
          document.body.classList.remove('overflow-hidden');
          
          // Clean up timeline
          if (this.modalTimeline) {
            this.modalTimeline.kill();
            this.modalTimeline = null;
          }
        }
      });
      
      gsap.to(modalBackdrop, {
        opacity: 0,
        duration: 0.3,
        ease: 'power3.inOut'
      });
    } else {
      // Fallback if animation doesn't work
      this.isImageModalOpen = false;
      document.body.classList.remove('overflow-hidden');
    }
  }

  copyReferralCode(): void {
    navigator.clipboard.writeText(this.referralCode).then(() => {
      console.log('Referral code copied to clipboard');

      // Animate copy button
      const copyButton = this.elementRef.nativeElement.querySelector('#copyButton');
      if (copyButton) {
        // Save original text
        const originalText = copyButton.querySelector('span').textContent;
        
        // Animate button
        gsap.to(copyButton, {
          backgroundColor: '#152c6e',
          scale: 1.05,
          duration: 0.3,
          ease: 'power2.out',
          onComplete: () => {
            // Change button text
            this.copyButtonText = 'copied';
            
            // Animate text change
            gsap.from(copyButton.querySelector('span'), {
              y: -10,
              opacity: 0,
              duration: 0.2
            });
            
            // Reset button after 3 seconds
            setTimeout(() => {
              // Animate text change back
              gsap.to(copyButton.querySelector('span'), {
                opacity: 0,
                y: 10,
                duration: 0.2,
                onComplete: () => {
                  this.copyButtonText = 'copy';
                  gsap.to(copyButton.querySelector('span'), {
                    opacity: 1,
                    y: 0,
                    duration: 0.2
                  });
                }
              });
              
              // Reset button style
              gsap.to(copyButton, {
                backgroundColor: '#1e3a8a',
                scale: 1,
                duration: 0.3,
                ease: 'power2.out'
              });
            }, 3000);
          }
        });
      }
    }).catch(err => {
      console.error('Failed to copy referral code:', err);
    });
  }

  shareOnSocialMedia(platform: string): void {
    const message = `istabot'da yeni üye olmak için benim referans kodumu kullan: ${this.referralCode}`;
    let url = '';

    switch (platform) {
      case 'whatsapp':
        url = `https://wa.me/?text=${encodeURIComponent(message)}`;
        break;
      case 'email':
        url = `mailto:?subject=istabot Referans Kodu&body=${encodeURIComponent(message)}`;
        break;
    }

    if (url) {
      // Animate button click effect
      const button = this.elementRef.nativeElement.querySelector(platform === 'whatsapp' ? '.whatsapp-button' : '.email-button');
      if (button) {
        gsap.to(button, {
          scale: 1.1,
          duration: 0.2,
          ease: 'back.out(1.7)',
          onComplete: () => {
            gsap.to(button, {
              scale: 1,
              duration: 0.2,
              ease: 'power2.out'
            });
            window.open(url, '_blank');
          }
        });
      } else {
        window.open(url, '_blank');
      }
    }
  }

  revealShareButtons(): void {
    this.showShareButtons = true;
    
    // Animate buttons appearing
    setTimeout(() => {
      const socialButtons = this.elementRef.nativeElement.querySelector('.social-buttons');
      if (socialButtons) {
        gsap.from(socialButtons, {
          y: 20,
          opacity: 0,
          duration: 0.5,
          ease: 'back.out(1.7)'
        });
        
        // Stagger animation for individual buttons
        const buttons = socialButtons.querySelectorAll('button');
        gsap.from(buttons, {
          scale: 0.8,
          opacity: 0,
          duration: 0.4,
          stagger: 0.1,
          ease: 'back.out(1.7)'
        });
      }
    }, 50);
  }

  revealShareButtons2(): void {
    this.showShareButtons2 = true;
    
    // Animate buttons appearing
    setTimeout(() => {
      const socialButtons = this.elementRef.nativeElement.querySelector('#socialButtons');
      if (socialButtons) {
        gsap.from(socialButtons, {
          y: 20,
          opacity: 0,
          duration: 0.5,
          ease: 'back.out(1.7)'
        });
        
        // Stagger animation for individual buttons
        const buttons = socialButtons.querySelectorAll('button');
        gsap.from(buttons, {
          scale: 0.8,
          opacity: 0,
          duration: 0.4,
          stagger: 0.1,
          ease: 'back.out(1.7)'
        });
      }
    }, 50);
  }
  
  // Animation for form errors
  highlightErrorInput(): void {
    const errorInput = this.elementRef.nativeElement.querySelector('.error-input');
    const errorMessage = this.elementRef.nativeElement.querySelector('.error-message');
    
    if (errorInput) {
      gsap.fromTo(errorInput, 
        { x: -5 },
        { x: 5, duration: 0.1, repeat: 5, yoyo: true, ease: 'power1.inOut', clearProps: 'x' }
      );
    }
    
    if (errorMessage) {
      gsap.from(errorMessage, {
        opacity: 0,
        y: -10,
        duration: 0.3,
        ease: 'power2.out'
      });
    }
  }

  loadReferralData(): void {
    // Get user's active referral code using the new endpoint
    this.referralService.getUserReferralCode(this.currentUserId).subscribe({
      next: (data) => {
        console.log('User referral code response:', data);
        // Active olan referral kodunu al
        if (data.referral_code && data.referral_code.active) {
          this.referralCode = data.referral_code.code;
          
          // Animate code appearance when it loads
          setTimeout(() => {
            const codeText = this.elementRef.nativeElement.querySelector('#codeText');
            if (codeText) {
              gsap.from(codeText, {
                opacity: 0,
                scale: 0.9,
                duration: 0.5,
                ease: 'back.out(1.7)'
              });
            }
          }, 100);
        } else {
          this.referralCode = ''; // Eğer yoksa boş string
        }
      },
      error: (error) => {
        console.error('Error fetching user referral code:', error);
      }
    });

    // Get total rewards
    this.referralService.getTotalRewards(this.currentUserId).subscribe({
      next: (data) => {
        console.log('Total rewards response:', data);
        this.totalRewards = data.total_credit_amount || 0;
        
        // Animate stats when they load
        setTimeout(() => {
          const statValues = this.elementRef.nativeElement.querySelectorAll('.stat-value');
          if (statValues.length) {
            gsap.from(statValues, {
              textContent: 0,
              duration: 1.5,
              ease: 'power1.inOut',
              snap: { textContent: 1 },
              stagger: 0.2
            });
          }
        }, 300);
      },
      error: (error) => {
        console.error('Error fetching total rewards:', error);
      }
    });

    // Fetch rewards data to get detailed credit information
    this.referralService.getRewards(this.currentUserId).subscribe({
      next: (data) => {
        console.log('Rewards response:', data);
        if (data && Array.isArray(data)) {
          // Filter only rewards earned from referred users
          this.credits = data.filter(credit =>
            credit.credit_source &&
            credit.credit_source.earned_from === 'referred_user'
          );
          console.log('Filtered credits:', this.credits);
        }
      },
      error: (error) => {
        console.error('Error fetching rewards:', error);
      },
      complete: () => {
        // Get referrals after rewards are loaded
        this.fetchReferrals();
      }
    });
  }

  fetchReferrals(): void {
    // Get referrals
    this.referralService.getReferrals(this.currentUserId).subscribe({
      next: (data) => {
        console.log('Referrals response:', data);
        // Sent referrals'ı al ve formatla - Sadece completed olanları göster
        if (data.referrals && data.referrals.sent_referrals) {
          const completedReferrals = data.referrals.sent_referrals
            .filter((ref: any) => ref.status === 'completed') // Sadece completed olanları filtrele
            .map((ref: any) => ({
              id: ref.id,
              name: ref.referred.full_name,
              full_name: ref.referred.full_name,
              date: ref.created_at,
              user_id: ref.referred.id, // Keep the user ID to match with rewards
              reward: 0 // Initialize with 0, will be updated from credits
            }));

          // Match referrals with credits to get the correct reward amount
          completedReferrals.forEach(referral => {
            const matchingCredit = this.credits.find(credit =>
              credit.credit_source &&
              credit.credit_source.user &&
              credit.credit_source.user.id === referral.user_id
            );

            if (matchingCredit) {
              referral.reward = matchingCredit.credit_amount;
            }
          });

          this.referrals = completedReferrals;

          // Animate referral rows when they load
          setTimeout(() => {
            const historyRows = this.elementRef.nativeElement.querySelectorAll('.history-row');
            if (historyRows.length) {
              gsap.from(historyRows, {
                y: 20,
                opacity: 0,
                duration: 0.4,
                stagger: 0.1,
                ease: 'power3.out'
              });
            }
          }, 200);

          // Toplam referans sayısını completed olanlarla güncelle
          this.totalReferrals = this.referrals.length;

          // Eğer pending referanslar varsa, kullanıcıya bilgi verebiliriz
          const pendingReferrals = data.referrals.sent_referrals.filter((ref: any) => ref.status === 'pending');
          if (pendingReferrals.length > 0) {
            console.log(`${pendingReferrals.length} referansınız harcama tamamlanmayı bekliyor.`);
          }
        }

        // Received referrals'ı kontrol et (kullanıcının kullandığı referans kodu)
        if (data.referrals && data.referrals.received_referrals && data.referrals.received_referrals.length > 0) {
          // Aktif olan received referral'ı bul
          const activeReceivedReferral = data.referrals.received_referrals.find((ref: any) =>
            ref.status === 'completed' || ref.status === 'pending'
          );

          if (activeReceivedReferral) {
            // Find the reward for this received referral if it exists
            let receivedReward = 0;
            if (activeReceivedReferral.status === 'completed') {
              // For received referrals, the reward might be stored differently
              // We need to check if there are credits received from referring users
              const receivedCredit = this.credits.find(credit =>
                credit.credit_source &&
                credit.credit_source.earned_from === 'referrer_user'
              );

              if (receivedCredit) {
                receivedReward = receivedCredit.credit_amount;
              }
            }

            this.receivedReferral = {
              id: activeReceivedReferral.id,
              referrer_id: activeReceivedReferral.referrer.id,
              referrer_name: activeReceivedReferral.referrer.full_name,
              created_at: activeReceivedReferral.created_at,
              status: activeReceivedReferral.status,
              code: activeReceivedReferral.code,
              reward: receivedReward
            };

            // Received referral varsa referral code kullanılmış demektir
            this.isReferralCodeUsed = true;

            // Eğer pending status'te ise kodu değiştirebilir
            this.canChangeReferralCode = activeReceivedReferral.status === 'pending';
            
            // Animate received referral card
            setTimeout(() => {
              const referralStatusCard = this.elementRef.nativeElement.querySelector('.referral-status-card');
              if (referralStatusCard) {
                gsap.from(referralStatusCard, {
                  opacity: 0,
                  y: 20,
                  duration: 0.5,
                  ease: 'power3.out'
                });
              }
            }, 200);
          }
        }
      },
      error: (error) => {
        console.error('Error fetching referrals:', error);
      }
    });

    // Check referral code status
    this.referralService.getReferralCodeStatus(this.currentUserId).subscribe({
      next: (data) => {
        console.log('Referral code status response:', data);
        // Kullanıcı kodu uygulayabilir mi? (Daha önce başka bir kod uygulanmış ve tamamlanmış mı?)
        this.isReferralCodeUsed = !data.can_update_referral || data.has_active_referral;
      },
      error: (error) => {
        console.error('Error fetching referral code status:', error);
      }
    });
  }

  closeDialog(): void {
    // Animate dialog closing
    const wrapper = this.elementRef.nativeElement.querySelector('.referral-wrapper');
    if (wrapper) {
      gsap.to(wrapper, {
        opacity: 0,
        y: 20,
        duration: 0.3,
        ease: 'power3.inOut',
        onComplete: () => {
          // Modalı/dialogu kapatmak için gerekli işlemler
          this.dialogRef.close();
        }
      });
    } else {
      this.dialogRef.close();
    }
  }

  // Bonus terms dialog methods
  openBonusTermsDialog(): void {
    this.isBonusTermsDialogOpen = true;
    // Prevent scrolling on the parent modal when dialog is open
    document.body.classList.add('overflow-hidden');
    
    // Add animation for bonus terms dialog if you implement it
  }

  closeBonusTermsDialog(): void {
    this.isBonusTermsDialogOpen = false;
    document.body.classList.remove('overflow-hidden');
  }

  applyReferralCode(): void {
    if (!this.inputReferralCode) {
      return;
    }

    // Reset error states before applying new code
    this.selfReferralError = false;
    this.invalidReferralCodeError = false;

    // Kullanıcının kendi referans kodunu girip girmediğini kontrol et
    if (this.inputReferralCode === this.referralCode) {
      // Kendi referans kodunu girmişse hata mesajı göster
      console.error('Kendi referans kodunuzu giremezsiniz');
      // Hata mesajını kullanıcıya göstermek için bir değişkene ata
      this.selfReferralError = true;
      
      // Animate error
      this.highlightErrorInput();
      return;
    }

    // Değiştirme işlemi mi yoksa ilk kez uygulama mı?
    const isChangingCode = this.showChangeCodeForm;

    const referralCodeData = {
      referral_code: {
        code: this.inputReferralCode
      }
    };

    console.log(`${isChangingCode ? 'Changing' : 'Applying'} referral code with data:`, referralCodeData);

    // Animate apply button
    const applyButton = this.elementRef.nativeElement.querySelector('.apply-button');
    if (applyButton) {
      gsap.to(applyButton, {
        scale: 0.95,
        backgroundColor: '#008b7a',
        duration: 0.2,
        onComplete: () => {
          gsap.to(applyButton, {
            scale: 1,
            duration: 0.2
          });
        }
      });
    }

    // Kod değiştirme ise önce mevcut kodu iptal et, sonra yeni kodu uygula
    if (isChangingCode) {
      // Önce referral kodu iptal et
      this.referralService.cancelReferral(this.currentUserId).subscribe({
        next: (response) => {
          console.log('Referral code canceled successfully:', response);

          // Sonra yeni kodu uygula
          this.updateReferralCode(referralCodeData);
        },
        error: (error) => {
          console.error('Error canceling referral code:', error);
          // Animate error
          this.invalidReferralCodeError = true;
          this.highlightErrorInput();
        }
      });
    } else {
      // Doğrudan kodu uygula
      this.updateReferralCode(referralCodeData);
    }
  }

  onReferralCodeInput(): void {
    // Reset error states on input change
    this.selfReferralError = false;
    this.invalidReferralCodeError = false;

    // Check if the input matches the user's own referral code
    if (this.inputReferralCode === this.referralCode && this.inputReferralCode !== '') {
      this.selfReferralError = true;
      
      // Animate error
      this.highlightErrorInput();
    }
  }

  // Referans kodunu güncelleyen yardımcı metot
  private updateReferralCode(referralCodeData: { referral_code: { code: string } }): void {
    this.referralService.updateReferralCode(this.currentUserId, referralCodeData).subscribe({
      next: (response) => {
        console.log('Referral code applied successfully:', response);
        
        // Animate success state
        const formContainer = this.elementRef.nativeElement.querySelector('.code-entry-form');
        if (formContainer) {
          gsap.to(formContainer, {
            scale: 1.03,
            boxShadow: '0 10px 25px rgba(0, 171, 149, 0.2)',
            duration: 0.3,
            ease: 'power2.out',
            onComplete: () => {
              gsap.to(formContainer, {
                scale: 1,
                boxShadow: 'none',
                duration: 0.3,
                ease: 'power2.in',
                onComplete: () => {
                  this.isReferralCodeUsed = true;
                  this.showChangeCodeForm = false;
                  this.loadReferralData(); // Reload data to reflect changes
                }
              });
            }
          });
        } else {
          this.isReferralCodeUsed = true;
          this.showChangeCodeForm = false;
          this.loadReferralData(); // Reload data to reflect changes
        }
      },
      error: (error) => {
        console.error('Error applying referral code:', error);
        // Set invalid referral code error flag
        this.invalidReferralCodeError = true;
        
        // Animate error
        this.highlightErrorInput();
      }
    });
  }

  resetReferralCode(): void {
    // Eğer kullanıcı henüz harcama yapmadıysa ve bir pending referral kodu varsa
    if (this.canChangeReferralCode) {
      // Animate button click
      const resetButton = this.elementRef.nativeElement.querySelector('.outline-teal-button');
      if (resetButton) {
        gsap.to(resetButton, {
          scale: 0.95,
          duration: 0.2,
          onComplete: () => {
            gsap.to(resetButton, {
              scale: 1,
              duration: 0.2,
              onComplete: () => {
                // Form gösterme durumunu değiştir
                this.showChangeCodeForm = true;
                // Reset any previous error state
                this.selfReferralError = false;
                this.invalidReferralCodeError = false;
                // Clear the input field when showing the form
                this.inputReferralCode = '';
                
                // Animate form appearance
                setTimeout(() => {
                  const formContainer = this.elementRef.nativeElement.querySelector('.code-entry-form');
                  if (formContainer) {
                    gsap.from(formContainer, {
                      opacity: 0,
                      y: 20,
                      duration: 0.4,
                      ease: 'power3.out'
                    });
                  }
                }, 50);
              }
            });
          }
        });
      } else {
        // Form gösterme durumunu değiştir
        this.showChangeCodeForm = true;
        // Reset any previous error state
        this.selfReferralError = false;
        this.invalidReferralCodeError = false;
        // Clear the input field when showing the form
        this.inputReferralCode = '';
      }
    }
  }

  // Kod değiştirme formunu kapat
  cancelChangeCode(): void {
    // Animate cancel button
    const cancelButton = this.elementRef.nativeElement.querySelector('.outline-teal-button');
    if (cancelButton) {
      gsap.to(cancelButton, {
        scale: 0.95,
        duration: 0.2,
        onComplete: () => {
          gsap.to(cancelButton, {
            scale: 1,
            duration: 0.2,
            onComplete: () => {
              // Animate form disappearance
              const formContainer = this.elementRef.nativeElement.querySelector('.code-entry-form');
              if (formContainer) {
                gsap.to(formContainer, {
                  opacity: 0,
                  y: 10,
                  duration: 0.3,
                  ease: 'power3.in',
                  onComplete: () => {
                    // Önceki kodu gizleme
                    const referralStatusCard = this.elementRef.nativeElement.querySelector('.referral-status-card');
                    if (referralStatusCard) {
                        gsap.to(formContainer, {
                            opacity: 0,
                            y: 10,
                            duration: 0.3,
                            ease: 'power3.in',
                            onComplete: () => {
                                this.showChangeCodeForm = false;
                                this.inputReferralCode = '';
                                this.selfReferralError = false; 
                                this.invalidReferralCodeError = false;
                            }
                        });
                    } else {
                        this.showChangeCodeForm = false;
                        this.inputReferralCode = '';
                        this.selfReferralError = false; 
                        this.invalidReferralCodeError = false;
                    }
                  }
                });
              } else {
                this.showChangeCodeForm = false;
                this.inputReferralCode = '';
                this.selfReferralError = false; 
                this.invalidReferralCodeError = false;
              }
            }
          });
        }
      });
    } else {
      this.showChangeCodeForm = false;
      this.inputReferralCode = '';
      this.selfReferralError = false; 
      this.invalidReferralCodeError = false;
    }
  }

  showShareButtons = false;
  showShareButtons2 = false;

  removeReferralCode(): void {
    // If the user can change their referral code (it's in pending status)
    if (this.canChangeReferralCode) {
      // Animate button click
      const removeButton = this.elementRef.nativeElement.querySelector('.outline-red-button');
      if (removeButton) {
        gsap.to(removeButton, {
          scale: 0.95,
          duration: 0.2,
          onComplete: () => {
            gsap.to(removeButton, {
              scale: 1,
              duration: 0.2
            });
          }
        });
      }
      
      // Call the service to cancel the referral using the new endpoint
      this.referralService.cancelReferral(this.currentUserId).subscribe({
        next: (response) => {
          console.log('Referral code removed successfully:', response);
          
          // Animate card disappearance
          const statusCard = this.elementRef.nativeElement.querySelector('.referral-status-card');
          if (statusCard) {
            gsap.to(statusCard, {
              opacity: 0,
              y: -20,
              duration: 0.4,
              ease: 'power3.in',
              onComplete: () => {
                this.receivedReferral = null;
                this.isReferralCodeUsed = false;
                this.showChangeCodeForm = false;
                this.inputReferralCode = '';
                this.loadReferralData(); // Reload data to reflect changes
              }
            });
          } else {
            this.receivedReferral = null;
            this.isReferralCodeUsed = false;
            this.showChangeCodeForm = false;
            this.inputReferralCode = '';
            this.loadReferralData(); // Reload data to reflect changes
          }
        },
        error: (error) => {
          console.error('Error removing referral code:', error);
          // You might want to show an error message to the user
        }
      });
    }
  }
}
import {
    Directive,
    ElementRef,
    EventEmitter,
    HostListener,
    Output,
} from '@angular/core';
import { fromEvent } from 'rxjs';
import { first, take } from 'rxjs/operators';

@Directive({
    selector: '[appClickOutside]',
})
export class ClickOutsideDirective {
    @Output() clickOutside = new EventEmitter();

    captured = false;

    constructor(private elRef: ElementRef) { }

    @HostListener('document:click', ['$event.target'])
    onClick(target) {
        if (!this.captured) {
            return;
        }

        // Check if click is on the element itself or its children
        if (this.elRef.nativeElement.contains(target)) {
            return;
        }

        // Check if click is on a mat-datepicker element
        const isDatepickerClick = target.closest('.mat-datepicker-content') || 
                                target.closest('.mat-datepicker-toggle');
        
        if (isDatepickerClick) {
            return;
        }

       this.clickOutside.emit();
    }

    ngOnInit() {
        fromEvent(document, 'click', { capture: true })
            .pipe(take(1))
            .subscribe(() => (this.captured = true));
    }
}

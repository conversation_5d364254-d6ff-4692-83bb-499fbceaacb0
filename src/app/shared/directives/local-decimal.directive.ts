import { Directive, HostListener, ElementRef, LOCALE_ID, Inject } from '@angular/core';
import { NgControl } from '@angular/forms';
import { TranslocoService } from '@ngneat/transloco';

@Directive({
    selector: '[appAutoCorrectDecimal]'
})
export class AutoCorrectDecimalDirective {
    private decimalSeparator: string;
    private regex: RegExp;

    constructor(private el: ElementRef, private model: NgControl, private translocoService: TranslocoService) {
        this.decimalSeparator = this.translocoService.getActiveLang() === 'en' ? '.' : ',';
        // Sadece sayılar ve yerel ayara bağlı olarak ondalık ayırıcı için bir regex oluştur
        this.regex = new RegExp(`^[0-9]*\\${this.decimalSeparator}?[0-9]*$`);
    }

    @HostListener('input', ['$event.target.value'])
    onInput(value: string): void {
        let transformedValue = value;

        // Yalnızca sayısal değerler ve ondalık ayırıcıya izin ver
        if (!this.regex.test(transformedValue)) {
            transformedValue = transformedValue.substring(0, transformedValue.length - 1);
            this.model.control.setValue(transformedValue, { emitEvent: false });
            this.el.nativeElement.value = transformedValue;
            return;
        }

        // Kullanıcının girdiğini yerel ayarın ondalık ayırıcısına göre düzelt
        if (this.decimalSeparator === ',') {
            transformedValue = transformedValue.replace(/\./g, ',');
        } else {
            transformedValue = transformedValue.replace(/,/g, '.');
        }

        // Düzeltme sonrası değeri model ve görünüme uygula
        this.model.control.setValue(transformedValue, { emitEvent: false });
        this.el.nativeElement.value = transformedValue;
    }

}

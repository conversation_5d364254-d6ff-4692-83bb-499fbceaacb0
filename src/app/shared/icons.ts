import { matCorporateFareRound, matKeyboardArrowRightRound, matSummarizeRound, } from '@ng-icons/material-icons/round';
import { matArrowDropUpOutline, matArrowUpwardOutline, matDangerousOutline, matMinusOutline, matMonitorHeartOutline, matPersonOutlineOutline, matAnalyticsOutline, matPersonOutline, matAdminPanelSettingsOutline, matBusinessOutline } from '@ng-icons/material-icons/outline';
import { matCalculateRound, matCheckRound, matCloseRound, matFavoriteRound, matHelpOutlineRound, matHistoryRound, matMultilineChartRound, matRestartAltRound } from '@ng-icons/material-icons/round';
import { matCreateNewFolderOutline, matEditOutline, matInfoOutline, matKeyboardArrowUpOutline } from '@ng-icons/material-icons/outline';
import { featherChevronRight, featherChevronUp, featherCreditCard, featherMenu, featherNavigation, featherSave, featherUser } from '@ng-icons/feather-icons';
import { ionPlay, ionAnalytics, ionSettingsOutline, ionWalletOutline, ionCartOutline, ionSwapHorizontal, ionGitCompareOutline } from '@ng-icons/ionicons';
import { phosphorCreditCard, phosphorDoorOpen, phosphorMicrosoftExcelLogo, phosphorWarningCircle, phosphorWarningDiamond, } from '@ng-icons/phosphor-icons/regular';
import { phosphorPercent } from '@ng-icons/phosphor-icons/regular';
import { phosphorFolderOpenDuotone } from '@ng-icons/phosphor-icons/duotone';
import { hugeArrowDataTransferVertical, hugeArrowExpand, hugeArrowMoveUpLeft, hugeBookmarkCheck02, hugeCheckList, hugeCircleArrowUpDown, hugeCoins02, hugeColumnInsert, hugeDashboardSquare02, hugeEdit02, hugeFilterAdd, hugeFilterReset, hugeFunction, hugeInvoice01, hugeInvoice04, hugeLoading03, hugeMoneyNotFound01, hugeNoteEdit, hugePropertyAdd, hugeRocket01, hugeRowInsert, hugeSettings02, hugeShoppingCart02, hugeUser, hugeUserAdd02, hugeUserMinus02 } from '@ng-icons/huge-icons';
import { aspectsCopy, aspectsCycle } from '@ng-icons/ux-aspects';
import { tdesignUndertakeTransaction } from '@ng-icons/tdesign-icons';
import { tablerBriefcase, tablerClock, tablerMail, tablerReport, tablerSend, tablerStarFilled, tablerStarOff } from '@ng-icons/tabler-icons';
import { jamDownload, jamFilter } from '@ng-icons/jam-icons';
import { tdesignChartAnalytics } from '@ng-icons/tdesign-icons';
import { lucideUpload, lucideFileSpreadsheet, lucideBarcode, lucideFileCheck, lucideZap, lucideUsers, lucideFileText, lucideLock, lucidePencil, lucideChevronRight, lucideLayers, lucideNotepadText, lucideGift, lucideUserRoundPlus, lucideSettings, lucideCircleHelp, lucideShoppingCart, lucideUser, lucideLogOut, lucideChevronLeft, lucideFolderOpen, lucideSearch, lucideFilter, lucideClock4, lucideSparkles, lucidePackagePlus, lucideCircleCheckBig, lucideCircleAlert, lucideDatabase, lucideFolderPlus, lucideCloudUpload, lucideDownload, lucidePlay, lucideTrash2, lucidePlus, lucideX, lucideFiles, lucideChevronDown, lucideCirclePlus, lucideCopy, lucideTriangleAlert, lucideArrowLeft, lucideEllipsisVertical, lucideSquarePen, lucideExpand, lucideRefreshCcw, lucideKey, lucideLoader, lucideGlobe, lucideHistory, lucidePackage, lucideEye, lucideArrowRight, lucideSave, lucideLayoutGrid, lucideCheck, lucideEyeOff, lucideMail, lucideCaptions, lucideTicketX, lucideBuilding2, lucideCircle, lucideMinus, lucideCalculator, lucideBoxes, lucidePercent, lucideCreditCard, lucideInfo, lucideChevronUp, lucideRefreshCw, lucideArrowDownUp, lucideArrowRightLeft, lucideArrowUp, lucideArrowDown, lucideSquareActivity, lucideRepeat, lucideUndo2, lucideSmile, lucideMonitor, lucideMessageCircle, lucidePhone, lucideShieldCheck, lucideClock, lucideCheckCheck, lucideHeartPulse, lucideGraduationCap, lucideBriefcase, lucideBrain, lucideWrench, lucideMousePointer, lucideBanknote, lucideChartBar, lucideStar, lucideCalendar, lucideMessageSquare, lucideBookOpen, lucideStarHalf, lucideQuote, lucideShield, lucideTwitter, lucideLinkedin, lucideInstagram, lucideYoutube, lucideMapPin, lucideSend, lucideStethoscope, lucideActivity, lucideSendHorizontal, lucidePencilLine, lucideTrendingUp, lucideArrowUpRight, lucideGrid2x2Plus, lucideMenu, lucideCircleCheck, lucideCircleX, lucideBuilding, lucideTag, lucideHeading, lucideLayoutList, lucideChartBarIncreasing, lucideListOrdered, lucideList, lucideTarget, lucideGripVertical, lucideGitCompare, lucideRows3, lucideColumns3, lucideMove, lucideVariable, lucideCircleOff, lucideCoins, lucideBadgeHelp, lucideClipboardList, lucideTrash, lucideUserMinus, lucideUserPlus, lucideFolder, lucideLayoutDashboard, lucideMinimize, lucideMoveHorizontal, lucideSlash, lucideGitCompareArrows, lucideLanguages, lucideBookCopy, lucideFolderPen, lucideCrown, lucideFlaskConical, lucideCompass, lucideMicroscope, lucideBookmark, lucideHeart, lucideShare2, lucideCalendarPlus, lucideBell, lucideChartNoAxesCombined } from '@ng-icons/lucide';
import { tablerBrandWhatsapp, tablerCreditCard, tablerCurrencyLira, tablerDatabaseEdit, tablerDatabaseExport, tablerDotsVertical, tablerEye, tablerFilter, tablerFolderPlus, tablerMapPin, tablerPencil, tablerPhone, tablerSearch, tablerTicket } from '@ng-icons/tabler-icons';
import { bootstrapArrowCounterclockwise, bootstrapArrowRightShort, bootstrapDatabase, bootstrapFiletypeXlsx, bootstrapGlobe, bootstrapLightningCharge } from '@ng-icons/bootstrap-icons';
import { heroArrowLeft, heroVideoCamera, } from '@ng-icons/heroicons/outline';
import { heroArrowRightOnRectangle, heroChatBubbleLeftRight, heroCog6Tooth, heroDocument, heroGlobeAlt, heroHome, heroLockOpen, heroMinus, heroQuestionMarkCircle, heroShare, heroTrash, heroUserMinus, heroUserPlus, } from '@ng-icons/heroicons/outline';
import { heroArrowDown, heroArrowDownTray, heroArrowLongRight, heroArrowPath, heroArrowsRightLeft, heroArrowsUpDown, heroArrowUp, heroArrowUpTray, heroArrowUturnLeft, heroCalculator, heroCheck, heroCheckCircle, heroChevronDown, heroChevronRight, heroChevronUp, heroChevronUpDown, heroClipboard, heroDocumentText, heroEllipsisVertical, heroExclamationCircle, heroExclamationTriangle, heroEye, heroEyeSlash, heroInformationCircle, heroListBullet, heroMagnifyingGlass, heroMinusCircle, heroPencil, heroPlus, heroPlusCircle, heroSquares2x2, heroTag, heroVariable, heroViewColumns, heroWrenchScrewdriver, heroXCircle, heroXMark } from '@ng-icons/heroicons/outline';
export const icons = {
  tablerDatabaseEdit,
  heroCheck,
  heroVideoCamera,
  heroArrowUturnLeft,
  heroArrowLeft,
  tablerMail,
  tablerSend,
  tablerClock,
  tablerMapPin,
  tablerFolderPlus,
  bootstrapLightningCharge,
  heroArrowPath,
  bootstrapGlobe,
  heroDocument,
  tablerCreditCard,
  tablerSearch,
  tablerDotsVertical,
  tablerEye,
  heroTrash,
  heroLockOpen,
  heroGlobeAlt,
  aspectsCopy,
  hugePropertyAdd, heroDocumentText,
  featherSave,
  heroExclamationCircle,
  heroCalculator,
  hugeEdit02,
  hugeRocket01,
  heroChevronDown,
  heroChevronUp,
  hugeFunction,
  heroVariable,
  heroSquares2x2,
  heroListBullet,
  heroEllipsisVertical,
  heroTag,
  heroArrowLongRight,
  matCloseRound,
  heroChevronRight,
  tablerDatabaseExport,
  heroXMark,
  heroXCircle,
  hugeArrowMoveUpLeft,
  phosphorFolderOpenDuotone,
  hugeCoins02,
  hugeArrowExpand,
  heroClipboard,
  heroPencil,
  matMonitorHeartOutline,
  heroCheckCircle,
  heroChevronUpDown,
  hugeSettings02,
  hugeNoteEdit,
  tablerBrandWhatsapp,
  hugeFilterReset,
  hugeFilterAdd,
  jamFilter,
  matArrowDropUpOutline,
  bootstrapArrowRightShort,
  bootstrapArrowCounterclockwise,
  matFavoriteRound,
  featherUser,
  tablerPhone,
  phosphorWarningDiamond,
  phosphorDoorOpen,
  hugeDashboardSquare02,
  featherChevronUp,
  hugeCheckList,
  matSummarizeRound,
  featherChevronRight,
  ionPlay,
  featherNavigation,
  phosphorMicrosoftExcelLogo,
  aspectsCycle,
  heroPlusCircle,
  heroMinusCircle,
  heroArrowsUpDown,
  heroArrowDown,
  heroArrowUp,
  heroMagnifyingGlass,
  heroArrowsRightLeft,
  heroExclamationTriangle,
  heroInformationCircle,
  heroEye,
  heroEyeSlash,
  heroPlus,
  matHistoryRound,
  ionAnalytics,
  tablerReport,
  hugeArrowDataTransferVertical,
  matCheckRound,
  heroArrowDownTray,
  heroArrowUpTray,
  matRestartAltRound,
  hugeCircleArrowUpDown,
  ionSettingsOutline,
  ionWalletOutline,
  ionCartOutline,
  heroMinus,
  lucideBuilding,
  featherMenu,
  matMinusOutline,
  hugeShoppingCart02,
  jamDownload,
  matCreateNewFolderOutline,
  matArrowUpwardOutline,
  bootstrapFiletypeXlsx,
  matEditOutline,
  matInfoOutline,
  lucidePencil,
  tdesignChartAnalytics,
  featherCreditCard,
  ionSwapHorizontal,
  matKeyboardArrowUpOutline,
  matKeyboardArrowRightRound,
  hugeInvoice01,
  hugeBookmarkCheck02,
  matPersonOutlineOutline,
  matCorporateFareRound,
  tablerPencil,
  phosphorCreditCard,
  tdesignUndertakeTransaction,
  tablerTicket,
  hugeInvoice04,
  hugeMoneyNotFound01,
  hugeLoading03,
  matCalculateRound,
  matMultilineChartRound,
  matHelpOutlineRound,
  bootstrapDatabase,
  hugeRowInsert,
  hugeColumnInsert,
  hugeUser,
  matDangerousOutline,
  phosphorWarningCircle,
  heroWrenchScrewdriver,
  lucideUpload,
  lucideFileSpreadsheet,
  lucideBarcode,
  lucideFileCheck,
  lucideZap,
  lucideUsers,
  lucideFileText,
  lucideLock,
  tablerCurrencyLira,
  matAnalyticsOutline,
  matPersonOutline,
  matAdminPanelSettingsOutline,
  tablerFilter,
  heroUserPlus,
  heroUserMinus,
  hugeUserMinus02,
  hugeUserAdd02,
  lucideChevronRight,
  lucideLayers,
  lucideNotepadText,
  lucideGift,
  lucideUserRoundPlus,
  lucideSettings,
  lucideCircleHelp,
  lucideShoppingCart,
  lucideUser,
  lucideLogOut,
  lucideChevronLeft,
  lucideFolderOpen,
  lucideSearch,
  lucideDownload,
  lucideFilter,
  lucideClock4,
  lucideSparkles,
  lucidePackagePlus,
  lucideCircleCheckBig,
  lucideCircleAlert,
  lucideDatabase,
  lucideFolderPlus,
  lucideCloudUpload,
  lucidePlay,
  lucideTrash2,
  lucidePlus,
  lucideX,
  lucideFiles,
  lucideChevronDown,
  lucideCirclePlus,
  lucideCopy,
  lucideTriangleAlert,
  lucideArrowLeft,
  lucideEyeOff,
  lucideEllipsisVertical,
  lucideSquarePen,
  lucideExpand,
  lucideRefreshCcw,
  lucideKey,
  lucideLoader,
  lucideGlobe,
  lucideHistory,
  lucidePackage,
  lucideEye,
  lucideArrowRight,
  lucideSave,
  lucideLayoutGrid,
  lucideCheck,
  lucideMail,
  lucideCaptions,
  lucideTicketX,
  lucideBuilding2,
  lucideCircle,
  lucideMinus,
  lucideCalculator,
  lucideBoxes,
  lucidePercent,
  lucideCreditCard,
  lucideInfo,
  lucideChevronUp,
  lucideRefreshCw,
  lucideArrowDownUp,
  lucideArrowRightLeft,
  lucideArrowUp,
  lucideArrowDown,
  lucideSquareActivity,
  lucideRepeat,
  lucideUndo2,
  lucideSmile,
  lucideMonitor,
  lucideMessageCircle,
  lucidePhone,
  lucideShieldCheck,
  lucideClock,
  lucideCheckCheck,
  lucideHeartPulse,
  lucideGraduationCap,
  lucideBriefcase,
  lucideBrain,
  lucideWrench,
  lucideMousePointer,
  lucideBanknote,
  lucideChartBar,
  lucideStar,
  lucideCalendar,
  lucideMessageSquare,
  lucideBookOpen,
  lucideStarHalf,
  lucideQuote,
  lucideShield,
  lucideTwitter,
  lucideLinkedin,
  lucideInstagram,
  lucideYoutube,
  lucideMapPin,
  lucideSend,
  lucideStethoscope,
  lucideActivity,
  ionGitCompareOutline,
  lucideSendHorizontal,
  matBusinessOutline,
  tablerBriefcase,
  lucidePencilLine,
  lucideTrendingUp,
  lucideArrowUpRight,
  lucideGrid2x2Plus,
  lucideMenu,
  lucideCircleCheck,
  lucideCircleX,
  lucideTag,
  lucideHeading,
  lucideLayoutList,
  lucideChartBarIncreasing,
  lucideListOrdered,
  lucideList,
  lucideTarget,
  lucideGripVertical,
  lucideGitCompare,
  lucideRows3,
  lucideColumns3,
  lucideCircleOff,
  lucideVariable,
  lucideMove,
  lucideCoins,
  lucideBadgeHelp,
  lucideClipboardList,
  lucideTrash,
  lucideUserMinus,
  lucideUserPlus,
  tablerStarOff,
  tablerStarFilled,
  lucideFolder,
  lucideLayoutDashboard,
  lucideMinimize,
  lucideMoveHorizontal,
  lucideSlash,
  lucideGitCompareArrows,
  lucideLanguages,
  lucideBookCopy,
  lucideFolderPen,
  lucideCrown,
  lucideFlaskConical,
  lucideCompass,
  lucideMicroscope,
  lucideBookmark,
  lucideHeart,
  lucideShare2,
  lucideCalendarPlus,
  lucideBell,
  lucideChartNoAxesCombined
}
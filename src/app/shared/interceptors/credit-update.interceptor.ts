// credit-update.interceptor.ts
import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpResponse
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CreditUpdateService } from '@app/data/services/credit-update.service';

@Injectable()
export class CreditUpdateInterceptor implements HttpInterceptor {

  // API endpoint patterns that affect credits
  private creditRelatedEndpoints = [
    // Ödeme ve sipariş işlemleri
    '/api/payments',
    '/api/orders',
    '/api/checkout',

    // Kredi kullanımı gerektiren işlemler
    '/api/projects',
    '/api/reports',
    '/api/analysis',

    // Kredi bakiyesi ile ilgili endpoint'ler
    '/api/user/credits',
    '/api/credits',

    // Kullanıcı kredi işlemleri
    '/api/user/packages',
    '/api/coupons'
  ];

  // HTTP metotları - hangi HTTP metotları kredi durumunu değiştirebilir
  private methodsToWatch = ['POST', 'PUT', 'DELETE', 'PATCH'];

  constructor(private creditUpdateService: CreditUpdateService) { }

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      tap((event) => {
        if (event instanceof HttpResponse) {
          // Sadece belirli HTTP metotlarını kontrol et
          if (this.methodsToWatch.includes(request.method)) {

            // Endpoint kontrolü
            const isEndpointMatched = this.creditRelatedEndpoints.some(endpoint =>
              request.url.includes(endpoint)
            );

            // Yanıt içinde kredi bilgisi var mı kontrol et
            const hasCreditData = event.body && (
              event.body.credits !== undefined ||
              event.body.credit_used !== undefined ||
              event.body.remaining_credit !== undefined ||
              event.body.total_remaining_credit !== undefined ||
              (event.body.data && Array.isArray(event.body.data) &&
                event.body.data.some((item: any) =>
                  item.credit !== undefined ||
                  item.remaining_credit !== undefined
                ))
            );

            // Başarılı işlem kontrolü - sadece başarılı yanıtlar için güncelle
            const isSuccessfulResponse = event.status >= 200 && event.status < 300;

            // Herhangi bir koşul sağlanıyorsa kredi güncelleme bildirimi yap
            if (isSuccessfulResponse && (isEndpointMatched || hasCreditData)) {
              console.log('Credit related action detected, updating sidebar balance');
              this.creditUpdateService.notifyCreditUpdated();
            }
          }
        }
      })
    );
  }
}

import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'appFilter' })
export class FilterPipe implements PipeTransform {
    /**
     * <PERSON><PERSON> filters the list of elements based on the search text provided
     *
     * @param items list of elements to search in
     * @param searchText search string
     * @returns list of elements filtered by search text of char or []
     */
    transform(items: any[], searchText: string): any[] {
        if (!items) {
            return [];
        }
        if (!searchText) {
            return items;
        }
        searchText = searchText.toLocaleLowerCase();
        let filteredItems = items.filter(it => {
            if (it.label) {
                if (it.label) {
                    if (!it.label.toLocaleLowerCase().includes(searchText)) {
                        return false;
                    }
                }
            }
            else if (it.name) {
                if (!it.name.toLocaleLowerCase().includes(searchText)) {
                    return false;
                }
            }
            else if (it.order.user) {
                if (!it.order.user.full_name.toLocaleLowerCase().includes(searchText)) {
                    return false;
                }
            }
            return true;
        });

        return filteredItems;
    }
}
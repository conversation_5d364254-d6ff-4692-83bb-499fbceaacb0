import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatValueLabels'
})
export class FormatValueLabelsPipe implements PipeTransform {
  transform(value: { [key: string]: any }, maxLength: number = 10): string {
    if (!value || typeof value !== 'object') return '-';

    const truncate = (str: string, len: number): string => {
      return str.length > len ? str.slice(0, len) + '...' : str;
    };

    return Object.entries(value)
      .map(([key, val]) => `${truncate(key, maxLength)}=${val}`)
      .join(', ');
  }
}

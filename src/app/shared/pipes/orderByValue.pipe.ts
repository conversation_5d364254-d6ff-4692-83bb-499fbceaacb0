import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'orderByValue'
})
export class OrderByValuePipe implements PipeTransform {

  transform(obj: any): any[] {
    // Convert object into an array of [key, value] arrays
    let items = Object.keys(obj).map(key => [key, obj[key]]);
    
    // Sort the array based on the value
    items.sort((a, b) => {
      return a[1] > b[1] ? 1 : (a[1] < b[1] ? -1 : 0);
    });
    
    return items;
  }

}
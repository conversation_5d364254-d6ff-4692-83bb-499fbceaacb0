import { Injectable } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { PageTitleService } from '../services/page-title.service';

@Injectable({
    providedIn: 'root'
})
export class TitleResolver implements Resolve<string> {

    constructor(
        private pageTitleService: PageTitleService
    ) { }

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
        // PageTitleService otomatik olarak başlığı ayarlayacak
        // Bu resolver artık sadece route değişikliklerini tetiklemek için kullanılıyor
        return of('title');
    }
}
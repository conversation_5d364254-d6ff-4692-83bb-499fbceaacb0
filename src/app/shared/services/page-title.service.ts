import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { TranslocoService } from '@ngneat/transloco';
import { BehaviorSubject, Observable, filter, map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PageTitleService {
  private titleSubject = new BehaviorSubject<string>('istabot');
  public title$ = this.titleSubject.asObservable();

  // Sayfa başlıkları için route-title eşleştirmeleri
  private routeTitleMap: { [key: string]: string } = {
    // Ana sayfalar
    '/': 'istabot',
    '/projects': 'project_list.my_projects',
    '/reports': 'report_list.title',
    '/settings': 'settings.settings', 
    '/datasets': 'datasets.my_datasets',

    // Auth sayfaları
    '/login': 'auth.login.login',
    '/signup': 'auth.signup.create_account',
    '/verification': 'istabot',
    '/verified': 'istabot',
    '/password/edit': 'istabot',
    '/password/reset': 'istabot',
    '/password-info': 'istabot',

    // Admin sayfaları
    '/admin/dashboard': 'tabs.admin.dashboard',
    '/admin/users': 'tabs.admin.users',
    '/admin/projects': 'tabs.admin.projects',
    '/admin/reports': 'tabs.admin.reports',
    '/admin/credit-card-transactions': 'tabs.admin.transactions',
    '/admin/settings': 'tabs.admin.settings',
    '/admin/eft-confirm': 'tabs.admin.eft-confirm',

    // Proje detay sayfaları
    '/projects/:pid/overview': 'tabs.project.overview',
    '/projects/:pid/dataset': 'tabs.project.dataset',
    '/projects/:pid/analysis': 'tabs.project.analysis',
    '/projects/:pid/settings': 'tabs.project.settings',
    '/projects/:pid/analysis/history': 'tabs.project.history',

    // Landing sayfaları
    '/changelog': 'landing.changelog.title',
    '/careers': 'careers.title',

    // Ödeme sayfaları
    '/payment/result': 'payment.result',

    // Hata sayfaları
    '/404': '404.wtfEvenIsThisRoute'
  };

  constructor(
    private titleService: Title,
    private router: Router,
    private transloco: TranslocoService
  ) {
    // Router olaylarını dinle - App component'te daha güçlü bir dinleme mekanizması var
    // Bu dinleme mekanizması sadece yedek olarak burada duruyor
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      filter(event => {
        const url = (event as NavigationEnd).url;
        // Proje detay sayfaları için özel işlem yapılıyor, onları atla
        return !url.includes('/projects/');
      }),
      map(() => this.getTitle())
    ).subscribe(title => {
      this.setTitle(title);
    });

    // Dil değişikliklerini dinle
    this.transloco.langChanges$.subscribe(() => {
      // Proje detay sayfasında değilsek başlığı güncelle
      if (!this.router.url.includes('/projects/')) {
        this.setTitle(this.getTitle());
      }
    });

    // Transloco hazır olduğunda başlığı ayarla
    this.transloco.events$.pipe(
      filter(event => event.type === 'translationLoadSuccess'),
    ).subscribe(() => {
      // Çeviriler yüklendiğinde başlığı güncelle
      // Proje detay sayfasında değilsek başlığı güncelle
      if (!this.router.url.includes('/projects/')) {
        this.setTitle(this.getTitle());
      }
    });

    // Başlangıçta mevcut URL için başlığı ayarla
    // Sayfa yenilemelerinde önemli
    this.initializeTitle();
  }

  /**
   * Başlangıçta sayfa başlığını ayarlar
   * Sayfa yenilemelerinde önemli
   */
  private initializeTitle(): void {
    // Çevirilerin yüklenmesini bekle
    let attempts = 0;
    const maxAttempts = 20; // Maksimum 20 deneme (20 * 100ms = 2 saniye)

    const checkTranslations = () => {
      attempts++;

      // Çevirilerin yüklenip yüklenmediğini kontrol et
      const activeLang = this.transloco.getActiveLang();
      const translations = this.transloco.getTranslation(activeLang);

      if (translations && Object.keys(translations).length > 0) {

        // Çeviriler yüklendi, başlığı ayarla
        // Proje detay sayfasında değilsek başlığı güncelle
        if (!this.router.url.includes('/projects/')) {
          // Birden fazla kez başlığı ayarla (zamanlama sorunlarını önlemek için)
          this.setTitle(this.getTitle());

          setTimeout(() => {
            this.setTitle(this.getTitle());
          }, 100);

          setTimeout(() => {
            this.setTitle(this.getTitle());
          }, 300);
        }
      } else {
        // Çeviriler henüz yüklenmedi, tekrar dene
        if (attempts < maxAttempts) {
        setTimeout(checkTranslations, 100); // 100ms bekle
        } else {
          console.error('Failed to load translations after', maxAttempts, 'attempts');
          // Yine de başlığı ayarlamaya çalış
          if (!this.router.url.includes('/projects/')) {
            this.setTitle('istabot');
          }
        }
      }
    };

    // Çevirilerin yüklenmesini bekle
    checkTranslations();

    // Transloco olaylarını da dinle
    this.transloco.events$.pipe(
      filter(event => event.type === 'translationLoadSuccess')
    ).subscribe(() => {
      // Çeviriler yüklendi, başlığı ayarla
      if (!this.router.url.includes('/projects/')) {
        this.setTitle(this.getTitle());
      }
    });
  }

  /**
   * Mevcut route'a göre başlığı belirler
   */
  private getTitle(): string {
    const url = this.router.url.split('?')[0]; // Query parametrelerini kaldır

    // Tam eşleşme kontrolü
    if (this.routeTitleMap[url]) {
      // Özel durum: Projeler listesi sayfası
      if (url === '/projects') {
        return this.transloco.translate('project_list.my_projects');
      }
      return this.transloco.translate(this.routeTitleMap[url]);
    }

    // Özel durumlar için kontroller
    if (url.startsWith('/projects/')) {
      // Proje detay sayfası - başlık ProjectDetailComponent içinde ayarlanıyor
      // Burada varsayılan bir başlık döndür
      if (url.includes('/overview')) {
        return this.transloco.translate('tabs.project.overview');
      } else if (url.includes('/dataset')) {
        return this.transloco.translate('tabs.project.dataset');
      } else if (url.includes('/analysis')) {
        if (url.includes('/history')) {
          return this.transloco.translate('tabs.project.history');
        } else {
          return this.transloco.translate('tabs.project.analysis');
        }
      } else if (url.includes('/settings')) {
        return this.transloco.translate('tabs.project.settings');
      }
    } else if (url.startsWith('/reports/')) {
      // Rapor detay sayfası - bu durumda başlık component içinde ayarlanacak
      return this.titleSubject.getValue();
    } else if (url.startsWith('/admin/')) {
      // Admin sayfaları için özel kontrol
      try {
        // Çevirilerin yüklenip yüklenmediğini kontrol et
        const activeLang = this.transloco.getActiveLang();
        const translations = this.transloco.getTranslation(activeLang);

        if (translations && Object.keys(translations).length > 0) {
          // Çeviriler yüklendi, doğru çeviriyi döndür
          if (url.includes('/dashboard')) {
            return this.transloco.translate('tabs.admin.dashboard');
          } else if (url.includes('/users')) {
            return this.transloco.translate('tabs.admin.users');
          } else if (url.includes('/projects')) {
            return this.transloco.translate('tabs.admin.projects');
          } else if (url.includes('/reports')) {
            return this.transloco.translate('tabs.admin.reports');
          } else if (url.includes('/credit-card-transactions')) {
            return this.transloco.translate('tabs.admin.transactions');
          } else if (url.includes('/settings')) {
            return this.transloco.translate('tabs.admin.settings');
          } else if (url.includes('/eft-confirm')) {
            return this.transloco.translate('tabs.admin.eft-confirm');
          } else {
            return this.transloco.translate('tabs.admin.dashboard');
          }
        } else {
          return 'Admin Panel';
        }
      } catch (error) {
        console.error('Error getting admin page title:', error);
        return 'Admin Panel';
      }
    } else if (url.startsWith('/settings')) {
      // Settings sayfaları için özel kontrol
      try {
        // Çevirilerin yüklenip yüklenmediğini kontrol et
        const activeLang = this.transloco.getActiveLang();
        const translations = this.transloco.getTranslation(activeLang);

        if (translations && Object.keys(translations).length > 0) {
          // Çeviriler yüklendi, doğru çeviriyi döndür
          return this.transloco.translate('settings.settings');
        } else {
          return 'Ayarlar';
        }
      } catch (error) {
        console.error('Error getting settings page title:', error);
        return 'Ayarlar';
      }
    } else if (url.startsWith('/corporate/')) {
      // Kurumsal panel sayfaları için özel kontrol
      try {
        // Çevirilerin yüklenip yüklenmediğini kontrol et
        const activeLang = this.transloco.getActiveLang();
        const translations = this.transloco.getTranslation(activeLang);

        if (translations && Object.keys(translations).length > 0) {
          // Çeviriler yüklendi, doğru çeviriyi döndür
          if (url.includes('/users')) {
            return this.transloco.translate('tabs.corporate.users');
          } else if (url.includes('/projects')) {
            return this.transloco.translate('tabs.corporate.projects');
          } else if (url.includes('/activities')) {
            return this.transloco.translate('tabs.corporate.activities');
          } else {
            return this.transloco.translate('tabs.corporate.users');
          }
        } else {
          return 'Kurumsal Yönetim';
        }
      } catch (error) {
        console.error('Error getting corporate page title:', error);
        return 'Kurumsal Yönetim';
      }
    }

    // Kısmi eşleşme kontrolü - en uzun eşleşmeyi bul
    let bestMatch = '';
    let bestMatchRoute = '';

    for (const route in this.routeTitleMap) {
      // Parametreli route'ları kontrol et (/:pid/ gibi)
      if (route.includes('/:')) {
        const routeParts = route.split('/');
        const urlParts = url.split('/');

        if (routeParts.length === urlParts.length) {
          let isMatch = true;

          for (let i = 0; i < routeParts.length; i++) {
            if (routeParts[i].startsWith(':')) {
              // Parametre, her değer kabul edilir
              continue;
            }

            if (routeParts[i] !== urlParts[i]) {
              isMatch = false;
              break;
            }
          }

          if (isMatch && route.length > bestMatch.length) {
            bestMatch = route;
            bestMatchRoute = this.routeTitleMap[route];
          }
        }
      }
      // Normal route'ları kontrol et
      else if (url.startsWith(route) && route !== '/' && route.length > bestMatch.length) {
        bestMatch = route;
        bestMatchRoute = this.routeTitleMap[route];
      }
    }

    if (bestMatchRoute) {
      return this.transloco.translate(bestMatchRoute);
    }

    // Varsayılan başlık
    return this.transloco.translate('istabot');
  }

  /**
   * Sayfa başlığını ayarlar
   * @param title Sayfa başlığı veya URL
   */
  public setTitle(title: string): void {
    if (!title) return;

    try {
      // Eğer URL verilmişse, başlığı getTitle metodunu kullanarak belirle
      if (title.startsWith('/')) {
        // URL verilmiş, başlığı getTitle metodunu kullanarak belirle
        const pageTitle = this.getTitle();

        // Çeviriler yüklenmemişse, varsayılan başlığı kullan
        if (!pageTitle || pageTitle === 'istabot' || pageTitle === 'Missing translation') {
          this.titleService.setTitle('istabot');
          this.titleSubject.next('istabot');
          return;
        }

        // "route adı | istabot" formatını uygula
        let formattedTitle: string;

        try {
          // Çevirmeyi dene
          const translatedTitle = this.transloco.translate(pageTitle);
          formattedTitle = pageTitle.includes('istabot') ? translatedTitle : `${translatedTitle} | istabot`;
        } catch (error) {
          // Çevirme hatası olursa doğrudan kullan
          formattedTitle = pageTitle.includes('istabot') ? pageTitle : `${pageTitle} | istabot`;
        }

        this.titleService.setTitle(formattedTitle);
        this.titleSubject.next(formattedTitle);
      } else {
        // Doğrudan başlık verilmiş
        // "route adı | istabot" formatını uygula
        const formattedTitle = title.includes('istabot') ? title : `${title} | istabot`;
        this.titleService.setTitle(formattedTitle);
        this.titleSubject.next(formattedTitle);
      }
    } catch (error) {
      // Herhangi bir hata olursa, varsayılan başlığı kullan
      console.error('Error setting page title:', error);
      this.titleService.setTitle('istabot');
      this.titleSubject.next('istabot');
    }
  }

  /**
   * Özel bir başlık ayarlar (component içinden çağrılabilir)
   * @param title Sayfa başlığı
   * @param translationKey Çeviri anahtarı (opsiyonel)
   */
  public setCustomTitle(title: string, translationKey?: string): void {
    if (translationKey) {
      this.setTitle(this.transloco.translate(translationKey));
    } else {
      this.setTitle(title);
    }
  }

  /**
   * Route-title eşleştirmesi ekler
   * @param route Route
   * @param titleKey Başlık çeviri anahtarı
   */
  public addRouteTitle(route: string, titleKey: string): void {
    this.routeTitleMap[route] = titleKey;
  }
}

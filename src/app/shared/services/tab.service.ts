import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TranslocoService } from '@ngneat/transloco';

@Injectable({
  providedIn: 'root'
})
export class TabService {
  private activeTabSubject = new BehaviorSubject<string>('');
  public activeTab$ = this.activeTabSubject.asObservable();

  constructor(private translocoService: TranslocoService) {}

  /**
   * Aktif tab'ı ayarlar
   * @param tabId Aktif tab ID'si
   */
  setActiveTab(tabId: string): void {
    this.activeTabSubject.next(tabId);
  }

  /**
   * Aktif tab'ı döndürür
   */
  getActiveTab(): string {
    return this.activeTabSubject.getValue();
  }

  /**
   * Tab başlığını çevirir
   * @param translationKey Çeviri anahtarı
   * @returns Çevrilmiş başlık
   */
  getTabTitle(translationKey: string): string {
    return this.translocoService.translate(translationKey);
  }

  /**
   * Tab'ın aktif olup olmadığını kontrol eder
   * @param tabId Tab ID'si
   * @returns Tab aktif mi?
   */
  isTabActive(tabId: string): boolean {
    return this.getActiveTab() === tabId;
  }

  /**
   * Tab'ın devre dışı olup olmadığını kontrol eder
   * @param tab Tab nesnesi
   * @returns Tab devre dışı mı?
   */
  isTabDisabled(tab: any): boolean {
    if (tab.disabled) {
      return true;
    }
    
    if (tab.disabledCondition) {
      return tab.disabledCondition();
    }
    
    return false;
  }
}

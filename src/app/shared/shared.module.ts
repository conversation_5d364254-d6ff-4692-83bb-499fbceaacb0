import { NgModule } from '@angular/core';
import { CommonModule, NgTemplateOutlet } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { PagenotfoundComponent } from './components/pagenotfound/pagenotfound.component';
import { translocoLoader } from '@app/transloco-loader';
import { NgScrollbarModule } from 'ngx-scrollbar';
import {
  TRANSLOCO_CONFIG,
  TranslocoConfig,
  TranslocoModule
} from "@ngneat/transloco";
import { ClickOutsideDirective } from './directives/click-outside.directive';
import { NgSelectModule } from '@ng-select/ng-select';
import { AutoCorrectDecimalDirective } from './directives/local-decimal.directive';
import { DatasetViewComponent } from './components/dataset-view/dataset-view.component';
import { MatDialogModule } from '@angular/material/dialog';
import { AgreementComponent } from './components/agreement/agreement.component';
import { MatButtonModule } from '@angular/material/button';

import { FilterPipe } from './pipes/filter.pipe';
import { OrderByValuePipe } from './pipes/orderByValue.pipe';
import { HighlightDirective } from './directives/highlight.directive';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { NgxIntlTelInputModule } from '@justin-s/ngx-intl-tel-input';
import { ConfirmComponent } from './components/confirm/confirm.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DateFnsModule } from 'ngx-date-fns';
import { MatIconModule } from '@angular/material/icon';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { ThousandSeparatorPipe } from './pipes/seperator.pipe';
import { MatSortModule } from '@angular/material/sort';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { LazyLoadDirective } from './directives/lazy-load.directive';
import { VideoEmbedComponent } from './components/video-embed/video-embed.component';
import { SafePipe } from './pipes/safe.pipe';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { icons } from './icons';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSelectModule } from '@angular/material/select';
import { HotTableModule } from '@handsontable/angular';
import { registerAllModules } from 'handsontable/registry';

import { CreateAnalysisComponent } from './components/create-analysis/create-analysis.component';
import { HelpCenterComponent } from './components/help-center/help-center.component';
registerAllModules();
import { CorporateManagementComponent } from './components/corporate-management/corporate-management.component';
import { ReferralComponent } from './components/referral/referral.component';

@NgModule({
  declarations: [
    //Directives
    AutoCorrectDecimalDirective,
    ClickOutsideDirective,
    HighlightDirective,
    LazyLoadDirective,
    //Pipes
    FilterPipe,
    OrderByValuePipe,
    ThousandSeparatorPipe,
    SafePipe,
    //Components
    PagenotfoundComponent,
    DatasetViewComponent,
    AgreementComponent,
    ConfirmComponent,
    VideoEmbedComponent,
    CreateAnalysisComponent,
    HelpCenterComponent,
    CorporateManagementComponent,
    ReferralComponent
  ],
  imports: [
    // Ng common
    NgTemplateOutlet,
    CommonModule,
    FormsModule,
    HttpClientModule,
    RouterModule,
    ReactiveFormsModule,
    // Additional
    HotTableModule,
    NgSelectModule,
    TranslocoModule,
    NgxIntlTelInputModule,
    // Material
    MatDialogModule,
    MatTabsModule,
    MatTableModule,
    NgIconsModule,
    MatInputModule,
    MatFormFieldModule,
    MatTooltipModule,
    MatSortModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatPaginatorModule,
    MatExpansionModule,
    MatSelectModule,
    DragDropModule
  ],
  exports: [
    // Modules
    CommonModule,
    MatSortModule,
    MatDatepickerModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    HttpClientModule,
    MatNativeDateModule,
    MatTableModule,
    MatTabsModule,
    HotTableModule,
    MatFormFieldModule,
    MatInputModule,
    TranslocoModule,
    NgScrollbarModule,
    MatIconModule,
    MatDialogModule,
    MatButtonModule,
    NgSelectModule,
    NgIconsModule,
    NgxIntlTelInputModule,
    MatTooltipModule,
    DateFnsModule,
    MatCardModule,
    MatMenuModule,
    MatPaginatorModule,
    MatExpansionModule,
    MatSelectModule,
    DragDropModule,
    // Directives
    LazyLoadDirective,
    ClickOutsideDirective,
    AutoCorrectDecimalDirective,
    HighlightDirective,
    // Pipes
    FilterPipe,
    SafePipe,
    OrderByValuePipe,
    ThousandSeparatorPipe,
    HelpCenterComponent,
    CorporateManagementComponent
  ],
  providers: [
    provideIcons(
      icons
    ),
    translocoLoader,
    {
      provide: TRANSLOCO_CONFIG,
      useValue: {
        availableLangs: ["en", "tr"],
        reRenderOnLangChange: true,
        fallbackLang: "tr",
        defaultLang: "en"
      } as TranslocoConfig
    }
  ],
})
export class SharedModule { }
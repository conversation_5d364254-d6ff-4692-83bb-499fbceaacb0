import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";

const apiKey = "405b49c5-519d-45b3-ac0e-ef2814b5bdfb";
const profileId = "smoggy-tahr-pJ0i8C";

// createSmitheryUrl fonksiyonu (Manual encoding - SDK broken)
function createSmitheryUrl(baseUrl, options) {
  const url = new URL(baseUrl + "/mcp");
  url.searchParams.set("profile", profileId);
  url.searchParams.set("api_key", options.apiKey);

  // Config'i base64 encode et (Smithery formatı)
  if (options.config) {
    const configString = JSON.stringify(options.config);
    const encodedConfig = btoa(configString); // base64 encode
    url.searchParams.set("config", encodedConfig);
    console.log("🔧 Original config:", JSON.stringify(options.config));
    console.log("🔧 Encoded config:", encodedConfig);
  }

  return url.toString();
}

// 🎯 HTML to Image - DOĞRU FORMAT
const servers = [
  {
    name: "Call For Papers",
    url: `https://server.smithery.ai/@alperenkocyigit/call-for-papers-mcp/mcp?profile=${profileId}&api_key=${apiKey}`,
  },
  {
    name: "HTML to Image (SMITHERY FORMAT)",
    url: createSmitheryUrl(
      "https://server.smithery.ai/@alperenkocyigit/html-to-image-mcp",
      {
        config: {
          cloudinaryCloudName: "dvf4za6kt",
          cloudinaryApiKey: "137893552457114",
          cloudinaryApiSecret: "26F_Jkq9NnZmeN2MUF6JLjACZEA",
        },
        apiKey: apiKey,
      },
    ),
  },
];

async function testServer(serverInfo) {
  console.log(`\n🚀 ${serverInfo.name} sunucusuna bağlanıyor...`);
  console.log("📡 Server URL:", serverInfo.url);

  // Config varsa doğru query parameter formatında gönder
  let transport;
  if (serverInfo.config) {
    console.log("🔧 Config ile bağlanmaya çalışıyor...");

    // Her config property'sini ayrı query parameter olarak ekle
    const configUrl = new URL(serverInfo.url);

    // Config objesi içindeki her key'i ayrı parameter yap
    Object.entries(serverInfo.config).forEach(([key, value]) => {
      configUrl.searchParams.set(key, value.toString());
    });

    transport = new StreamableHTTPClientTransport(configUrl.toString());
    console.log("📡 Config URL:", configUrl.toString());
  } else {
    transport = new StreamableHTTPClientTransport(serverInfo.url);
  }
  const client = new Client({
    name: `${serverInfo.name} client`,
    version: "1.0.0",
  });

  try {
    await client.connect(transport);
    console.log("✅ Bağlantı başarılı!");

    const tools = await client.listTools();
    const toolsList = tools.tools || tools;
    console.log(
      `🛠️ Mevcut araçlar: ${toolsList.map((t) => t.name).join(", ")}`,
    );

    // Araçların detaylarını göster
    if (toolsList && toolsList.length > 0) {
      console.log(`\n📋 ${serverInfo.name} araç detayları:`);
      toolsList.forEach((tool, index) => {
        console.log(
          `${index + 1}. ${tool.name}: ${tool.description || "Açıklama yok"}`,
        );
        console.log(`   Schema:`, JSON.stringify(tool.inputSchema, null, 2));
      });

      // Test araçları
      if (serverInfo.name === "Call For Papers") {
        console.log("\n🧪 get_events aracını test ediyoruz...");
        const result = await client.callTool({
          name: "get_events",
          arguments: {
            keywords: "AI conference",
            limit: 3,
          },
        });
        console.log(
          "🎯 AI Konferans Sonuçları:",
          JSON.stringify(result.content, null, 2),
        );
      }

      if (serverInfo.name === "HTML to Image (SMITHERY FORMAT)") {
        console.log("\n🧪 take_screenshot aracını test ediyoruz...");
        const result = await client.callTool({
          name: "take_screenshot",
          arguments: {
            url: "https://google.com",
            width: 1280,
            height: 720,
            fullPage: false,
          },
        });
        console.log(
          "🎯 Screenshot Sonuç:",
          JSON.stringify(result.content, null, 2),
        );
      }
    }

    await client.close();
  } catch (error) {
    console.error(`❌ ${serverInfo.name} Hatası:`, error.message);
  }
}

// Ana fonksiyon
async function main() {
  for (const server of servers) {
    await testServer(server);
  }
}

main().catch(console.error);
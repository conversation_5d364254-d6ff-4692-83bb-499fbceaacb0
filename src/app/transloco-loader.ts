import { inject, Injectable } from "@angular/core";
import { Translation,TRANSLOCO_LOADER, TranslocoLoader } from "@ngneat/transloco";
import { HttpClient } from "@angular/common/http";

@Injectable({ providedIn: 'root' })
export class TranslocoHttpLoader implements TranslocoLoader {
    private http = inject(HttpClient);

    getTranslation(lang: string) {
        return this.http.get<Translation>(`/assets/i18n/${lang}.json`);
    }
}
export const translocoLoader = { provide: TRANSLOCO_LOADER, useClass: TranslocoHttpLoader };

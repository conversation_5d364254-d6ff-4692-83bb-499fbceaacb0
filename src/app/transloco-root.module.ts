import {
  provideTransloco,
  TranslocoModule
} from '@ngneat/transloco';
import { isDevMode, NgModule } from '@angular/core';
import { TranslocoHttpLoader } from './transloco-loader';

/**
 * Get the initial language from localStorage or fallback to browser language
 * This ensures that the language preference is respected when the app starts
 */
export function getInitialLang(): string {
  const storedLang = localStorage.getItem('activeLang');
  if (storedLang && (storedLang === 'tr' || storedLang === 'en')) {
    return storedLang;
  }

  // Fallback to browser language or default to Turkish
  const browserLang = navigator.language.split('-')[0];
  return (browserLang === 'tr' || browserLang === 'en') ? browserLang : 'tr';
}

@NgModule({
  exports: [ TranslocoModule ],
  providers: [
      provideTransloco({
        config: {
          availableLangs: ['tr', 'en'],
          defaultLang: getInitialLang(),
          // Remove this option if your application doesn't support changing language in runtime.
          reRenderOnLangChange: true,
          prodMode: !isDevMode(),
          fallbackLang: 'tr'
        },
        loader: TranslocoHttpLoader
      }),
  ],
})
export class TranslocoRootModule {}

{"istabot": "istabot | New Era of Statistical Data Analysis", "landing": {"analysis": {"descriptive": "Descriptive", "independent": "Independent", "dependent": "Dependent", "correlation": "Correlation", "chisq": "Chi-Square", "multi": "Multi-Group"}, "cta": {"brand": "istabot", "description1": "Analysis has never been easier!", "description2": "Turn your data into insights", "description3": "Accelerate your research", "description4": "Improve your workflow", "description5": "The analysis process is now effortless!", "cta": "Start Now!"}, "howto": {"title": {"main": "How It Works", "sub": "Discover is<PERSON><PERSON>'s four-step statistical analysis approach."}, "step1": "Create Project and Upload Dataset", "step2": "Edit Dataset", "step3": "Perform Advanced Analyses", "step4": "Generate Comprehensive Reports"}, "how": {"step": "Step", "title": "Four Steps of Professional Analysis", "description": "Get results in minutes without technical knowledge", "steps": [{"id": 1, "icon": "lucideUpload", "title": "Upload Dataset", "description": "Drag and drop your Excel file or select it from your computer."}, {"id": 2, "icon": "lucideFileSpreadsheet", "title": "Select Analysis", "description": "Choose from analyses like Dependent, Multi-Group, Chi-Square, or Correlation."}, {"id": 3, "icon": "lucideBarcode", "title": "View Results", "description": "Review results as tables and comments."}, {"id": 4, "icon": "lucide<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Download Report", "description": "Download your ready-to-publish Word report in APA format with one click."}]}, "usecases": {"title": {"main": "Use Cases", "sub": "Discover how is<PERSON>bot can enhance your work in various fields."}, "case1": {"title": "Academic Research", "description": "Simplify your statistical data analysis for scientific articles in medicine, dentistry, health sciences, and other disciplines."}, "case2": {"title": "Graduate Studies", "description": "Effortlessly perform the necessary data analyses for your specialization, doctoral, and master's theses. Support your thesis work with istabot."}, "case3": {"title": "Professional Managers and Researchers", "description": "Powerful data analysis tools for professional managers and researchers. Make better decisions using statistical science."}, "case4": {"title": "Everyone", "description": "Whether you are an expert or a beginner, is<PERSON><PERSON>'s intuitive design makes powerful statistical analysis accessible."}}, "services": {"title": {"main": "Our Services", "sub": "Discover the key features that make istabot your indispensable analysis tool."}, "service1": {"title": "Seamless Data Integration", "description": "Easily upload your data and process it with Excel-like ease."}, "service2": {"title": "Smart Data Preprocessing", "description": "Prepare your datasets before analysis."}, "service3": {"title": "Basic Level Analyses", "analysis1": "Descriptive Statistics Analysis", "analysis2": "Independent Single Group Data Analysis", "analysis3": "Independent Multi-Group Data Analysis", "analysis4": "Dependent Data Analysis", "analysis5": "Correlation Analysis", "analysis6": "Chi-Square Analysis"}, "service4": {"title": "Professional Reporting", "description": "Generate reports in APA format, Word format, with multilingual support."}}, "footer": {"brand": "istabot", "description": "A New Era in Statistical Data Analysis", "developed_by": "Developed by E-İstatistik", "rights": "© 2024 All rights reserved", "hiring": "We're hiring!", "quick_links": {"title": "Quick Access", "links": [{"title": "Homepage", "href": "#homepage"}, {"title": "Services", "href": "#services"}, {"title": "How It Works", "href": "#howto"}, {"title": "Use Cases", "href": "#usecases"}, {"title": "Comparison", "href": "#comparison"}, {"title": "Contact", "href": "#contact"}]}, "contact": {"title": "Contact", "address": "Körfez Mahallesi 19 Mayıs Kümesi Küme Evleri No:188-14 Atakum Samsun", "phone1": "+90-(850)-885 12 56", "phone2": "WhatsApp: +90-(538)-615 0 444"}, "legal": {"privacy_policy": "Privacy Policy", "terms_of_use": "Terms of Use"}, "footer": {"copyright": "2025 İstabot. All rights reserved.", "quick_links": "Quick Links", "home": "Home", "support": "Support", "contact": "Contact", "privacy_policy": "Privacy Policy", "terms_of_use": "Terms of Use", "gdpr": "GDPR", "free_demo": "Free Demo", "tutorial_videos": "Tutorial Videos", "api_docs": "API Documentation", "support_request": "Support Request", "company_description": "İstabot is a professional statistical analysis platform that accelerates academic work and research.", "developed_by": "Developed by <PERSON><PERSON><PERSON><PERSON>.", "address": {"line1": "Körfez Mahallesi 19 Mayıs Kümesi Küme Evleri No:188-14", "line2": "<PERSON><PERSON><PERSON>"}, "faq": "Frequently Asked Questions"}, "back_to_top": "Back to Top"}, "navbar": {"brand": "istabot", "partner": "Developed by eistatistik.com", "link1": "Homepage", "link2": "Our Services", "link3": "How It Works?", "link4": "Use Cases", "link5": "Comparison", "link6": "Contact", "features": "Features", "how_it_works": "How It Works", "use_cases": "Use Cases", "comparison": "Comparison", "pricing": "Pricing", "faq": "FAQ", "buttons": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "dashboard": "Go to Portal"}, "start_now": "Try Now"}, "homepage": {"title": {"main": "In Statistical Data Analysis", "highlight": "A New Era!"}, "description": "Complete complex statistical analyses in seconds.", "cta": {"text": "Start Now"}, "demo": {"text": "Watch the Demo"}}, "comparison": {"brand": "istabot", "title": {"main": "Why Choose istabot", "sub": "See how istabot stands out among statistical analysis software."}, "table": {"header1": "No technical knowledge required, fast and easy analysis", "header2": "Ready-to-publish reports in APA format, Word export, and multilingual support", "header3": "Intuitive design for optimal user experience", "header4": "Comprehensive Analysis Tools", "header5": "Report Format"}}, "hero": {"badge1": "✨ 1-year free trial", "badge2": "💳 No credit card required", "highlight": "In Statistical Data Analysis", "description": "Leverage comprehensive statistical analyses and professional reporting systems to advance your academic work.", "start": "Start Now", "video": "Introduction Video", "title": {"line1": "Professional Statistical", "line2": "Analysis Platform"}, "buttons": {"demo_video": "Watch Demo", "start_analysis": "Start Analysis"}}, "features": {"title": "Features", "description": "Everything you need for professional analyses", "services": [{"title": "Fast Analysis", "description": "Upload your data and get results in seconds", "features": ["Instant data processing", "Quick results", "Automatic calculation"]}, {"title": "User-Friendly", "description": "No technical knowledge required with an Excel-like interface", "features": ["Drag-and-drop", "Easy selection", "Understandable interface"]}, {"title": "APA Format", "description": "Professional reports suitable for academic publications", "features": ["Word report", "APA standards", "Ready tables"]}, {"title": "Reliable Results", "description": "Proven <PERSON> libraries, reproducible results", "features": ["R-based", "Verified", "Up-to-date methods"]}], "analyses": {"title": "Supported Analyses", "types": ["Descriptive", "Independent", "Dependent", "Correlation", "Chi-Square", "Multiple Group"]}, "main_card": {"title": "Professional Statistical Analysis Platform", "description": "Leverage comprehensive statistical analyses and professional reporting systems to advance your academic work."}, "security_card": {"title": "Full GDPR Compliance", "description": "Maximum security protocols for your data, compliant with data protection standards."}, "report_card": {"title": "Ready Reports in APA Format EN/TR"}, "speed_card": {"title": "Faster analysis. More results. Better research."}, "r_based_card": {"title": "R-based reliable analysis results"}, "results_card": {"title": "Meaningful results, meaningless worries"}}, "cases": {"title": "Use Cases", "description": "Professional statistical analysis in every field", "usecases": {"0": {"title": "Academic Research", "description": "Article analyses in medicine, dentistry, health sciences, and other faculties", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-1.webp"}, "1": {"title": "Thesis Research", "description": "Analyses for specialization, doctoral, and master's theses", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-2.webp"}, "2": {"title": "Professional Research", "description": "Better decisions with statistical science for managers and researchers", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-3.webp"}, "3": {"title": "General Use", "description": "For anyone in need of statistical analysis", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-4.webp"}}, "academic_fields": {"title": "Academic Fields of Use", "fields": {"0": {"title": "Medical School", "description": "Clinical research and medical data"}, "1": {"title": "Dentistry", "description": "Dental research and analyses"}, "2": {"title": "Health Sciences", "description": "Health research and data analyses"}, "3": {"title": "Social Sciences", "description": "Social research and surveys"}, "4": {"title": "Educational Sciences", "description": "Educational research and student analyses"}, "5": {"title": "Private Sector", "description": "Market research and business analyses"}}}, "try_free": "Try Free Now!"}, "compare": {"title": "istabot vs Traditional Analysis Comparison", "subtitle": "Why should you choose istabot for your statistical analyses?", "features": {"time_saving": {"title": "Time Savings", "description": "85% faster analysis process compared to traditional methods. Complete tasks in seconds that would normally take hours."}, "ease_of_use": {"title": "Ease of Use", "description": "No statistics or programming knowledge required. Anyone can use it easily with the drag-and-drop interface."}, "professional_results": {"title": "Professional Results", "description": "Complete your work without wasting time with ready-made reports in APA format and tables suitable for academic publications."}}, "table": {"feature": "Feature", "traditional": "Traditional", "rows": {"analysis_time": {"title": "Analysis time", "istabot": "Seconds", "istabot_detail": "Results in minutes", "traditional": "Hours/Days", "traditional_detail": "Long preparation process"}, "technical_knowledge": {"title": "Technical knowledge requirement", "istabot": "Not required", "istabot_detail": "Excel-like interface", "traditional": "Advanced level", "traditional_detail": "Steep learning curve"}, "user_interface": {"title": "User interface", "istabot": "Easy interface", "istabot_detail": "Simple and intuitive", "traditional": "Complex commands", "traditional_detail": "Requires coding knowledge"}, "report_format": {"title": "Report format", "istabot": "APA format Word", "istabot_detail": "Ready with one click", "traditional": "Manual preparation", "traditional_detail": "Extra workload"}, "error_risk": {"title": "Error risk", "istabot": "Minimum", "istabot_detail": "Automatic checks", "traditional": "High", "traditional_detail": "Errors in manual operations"}, "cost": {"title": "Cost", "istabot": "Pay per analysis", "istabot_detail": "Economical and flexible", "traditional": "High license fee", "traditional_detail": "Fixed and expensive"}}}, "cost_comparison": {"title": "Cost Comparison", "calculator": {"title": "Calculator", "monthly_analyses": "Monthly number of analyses", "hours_per_analysis": "Average time per analysis (hours)", "hourly_rate": "Your hourly rate (₺)", "calculate_button": "Calculate"}, "results": {"metric": "Metric", "istabot": "istabot", "competitor": "Competitor", "difference": "Difference", "analysis_time": "Analysis Time", "cost_per_analysis": "Cost Per Analysis", "monthly_cost": "Total Monthly Cost", "efficiency_increase": "Efficiency Increase"}, "note": "* Calculations are based on average values. Analysis time with istabot is 85% faster than traditional methods."}, "tools": ["SPSS", "Python", "MINITAB"]}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Most common questions about istabot", "categories": {"about_platform": "About the Platform", "usage": "Usage", "pricing": "Pricing and Payments", "technical": "Technical Questions"}, "questions": {"what_is_istabot": {"question": "What is is<PERSON>bot?", "answer": "istabot is a web-based statistical analysis platform developed for academic studies and research. It allows you to perform professional analyses with a simple interface without requiring complex software and coding knowledge. It provides reliable and accurate results with R-based algorithms."}, "difference": {"question": "How is istabot different from other analysis programs?", "answer_intro": "Unlike traditional analysis programs, istabot:", "answer_items": ["Requires no technical knowledge, no need to know statistics or coding", "Is web-based, requires no installation", "Presents analysis results as ready-made reports in APA format", "Has no license cost with a pay-as-you-go model", "Has Turkish and English language support"]}, "supported_analyses": {"question": "What analyses does istabot support?", "answer_intro": "istabot supports various statistical analysis methods most commonly used in academic research:", "answer_items": ["Descriptive Statistics", "Frequency Analysis", "Independent Samples t-Test", "One-Way ANOVA", "Paired Samples t-Test", "Repeated Measures ANOVA", "<PERSON><PERSON><PERSON>", "Kruskal-Wallis H Test", "<PERSON><PERSON> Signed-Rank Test", "Friedman Test", "Pearson Correlation Analysis", "<PERSON><PERSON>man Correlation Analysis", "Point-Biserial Correlation", "Tetrachoric Correlation", "Pearson Chi-Square Test", "Fisher's Exact Test", "<PERSON>' Corrected Chi-Square Test", "Kolmogorov-<PERSON><PERSON>nov Test", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Runs Test"], "answer_conclusion": "Our range of analyses is expanding every day."}, "upload_data": {"question": "How can I upload my data?", "answer_intro": "You can easily upload your data to istabot in Excel format (.xlsx, .xls). To do this:", "answer_steps": ["Log in to your account", "Click on the \"Create Project\" button", "Drag and drop your Excel file or upload it using \"Select File\"", "Click on the \"Save Changes\" button at the bottom of the screen", "Your dataset will be ready on the platform"], "answer_conclusion": "The first row of your Excel file should contain variable names; you can download a sample dataset from the project creation screen. You can also manage your variables with the data editing tools on the online platform."}, "perform_analysis": {"question": "How can I perform my analyses?", "answer_intro": "After uploading your dataset, the analysis process is completed in just a few steps:", "answer_steps": ["Select the type of analysis (Descriptive statistics, Dependent data analysis, correlation, etc.)", "Select the variables to be used in the analysis", "Adjust analysis options (optional)", "Click on the \"Analyze\" button", "Results will appear on your screen within seconds"], "answer_conclusion": "You can download your analysis results in Word format from the reports section or store them on the platform."}, "download_reports": {"question": "How can I download my reports?", "answer_intro": "istabot presents your analysis results as professional reports in APA format. To download your reports:", "answer_steps": ["Click on the \"Download Report\" button on the analysis results page", "Select your preferred language (Turkish or English)", "Your report will be automatically downloaded in Word format"], "answer_conclusion": "Your reports include statistical test results, tables, graphs, and interpretations. You can add them directly to your academic work or edit them."}, "credit_system": {"question": "How does the credit system work?", "answer_intro": "istabot is built on a system that uses a certain number of credits for each analysis:", "answer_items": ["Each type of analysis requires a different amount of credits", "Credits you purchase are valid in your account for 2 years", "You can purchase additional credit packages at any time", "You can calculate the amount of credit you need with our \"Credit Calculator\" tool"]}, "payment_methods": {"question": "What payment methods do you accept?", "answer_intro": "istabot supports the following secure payment methods:", "answer_items": ["Credit card (Visa, MasterCard, etc.)", "Bank transfer / EFT"], "answer_conclusion": "All payments are processed through Akbank's secure payment infrastructure and your data is encrypted. Invoices and receipts related to your payments are automatically sent to your email address."}, "data_security": {"question": "Is my data secure?", "answer_intro": "istabot takes your data security very seriously. Our security measures:", "answer_items": ["All data transfers are protected with SSL/TLS encryption", "Our data storage systems are regularly backed up and undergo security audits", "We implement KVKK and GDPR compliant data storage policies", "Access to sensitive data is limited to authorized personnel only", "Optionally, you can permanently delete your data after completing your analyses"], "answer_conclusion": "For more information about our data security policies, you can review our Privacy Policy."}, "mobile_usage": {"question": "Can I use istabot on mobile devices?", "answer_intro": "Yes, istabot is fully mobile compatible and developed according to responsive design principles:", "answer_items": ["Can be used in all browsers (Chrome, Safari, Firefox, etc.)", "Provides full functionality on tablet devices", "Basic operations can be performed on smartphones, but desktop interface is recommended for complex data editing operations", "Tested and optimized on iOS and Android devices"], "answer_conclusion": "We recommend using a desktop or laptop computer for the best experience when working with large datasets."}}, "contact": {"title": "Your Data is Complex, Our Solution is Simple", "subtitle": "Our customer support team is ready to assist you", "whatsapp": "Contact Us Now"}}, "how_it_works": {"title": "Professional Analysis: In Four Steps", "subtitle": "Get results in minutes without technical knowledge", "steps": {"step1": {"title": "Upload Dataset", "drag_drop": "Drag your Excel file here", "or": "or", "browse": "browse files"}, "step2": {"title": "Data Preparation", "description": "Prepare your data for analysis with diagnostic operations.", "variable_calculation": "Variable Calculation", "missing_data": "Missing Data Analysis", "variable_coding": "Variable Coding"}, "step3": {"title": "Select Analysis", "description": "Choose the type of analysis you need to evaluate your data professionally.", "descriptive": "Descriptive Statistics", "single_group": "Single Group Independent Data Analysis", "multi_group": "Multi Group Independent Data Analysis", "dependent": "Dependent Data Analysis", "chi_square": "Chi-Square Analysis", "correlation": "Correlation Analysis", "click_for_details": "Click for details"}, "step4": {"title": "View and Download Report", "description": "Review results and download your ready-made report in APA format in Turkish or English.", "analysis_results": "Analysis Results", "analysis_interpretation": "Analysis Interpretation", "sample_interpretation": "A statistically significant difference was found between X Value and Y Value (p<0.05).", "download_report": "Download DOCX Report"}}, "cta": {"title": "Revolutionize Your Statistical Analyses!", "description": "Create professional analyses in seconds, save hours, and interpret your data with perfect confidence!", "try_free": "Try Free Now!", "features": {"unlimited_data": "Unlimited Data Upload", "apa_reporting": "APA Format Reporting", "auto_interpretation": "Automatic Interpretation", "support": "24/7 Technical Support"}}}, "pricing": {"title": "Pricing Plans", "description": "Flexible payment solutions with credit packages tailored to your needs", "buy_button": "Buy Now", "packages": {"mini": {"title": "Mini Package", "discount_label": "10% Off!", "original_price": "7.500 ₺", "discounted_price": "6.750 ₺", "credits": "100 Credits", "description": "Perfect for small research projects", "features": ["10% Discount!", "Valid for 2 years", "Turkish and English reports", "Unlimited report exports", "Renewable"]}, "standard": {"title": "Standard Package", "discount_label": "15% Off!", "original_price": "11.250 ₺", "discounted_price": "9.561 ₺", "credits": "150 Credits", "description": "Ideal for medium-sized studies", "features": ["15% Discount!", "Valid for 2 years", "Turkish and English reports", "Unlimited report exports", "Renewable"]}, "custom": {"title": "Custom Package", "price": "75 ₺", "per_credit": "/credit", "credits": "Choose your credit amount", "description": "Customizable package according to your needs", "features": ["Flexible credit amount", "Valid for 2 years", "Turkish and English reports", "Unlimited report exports", "Renewable"]}}, "corporate": {"title": "Corporate Pricing", "description": "Special prices for universities and research groups", "contact_button": "Contact Us"}}, "stats": {"title": "istabot in Statistics", "subtitle": "Data-driven decisions, time savings, and accurate results", "cards": {"speed": {"label": "Analysis Speed", "description": "Faster results"}, "users": {"label": "Number of Users", "description": "Academics and researchers"}, "analyses": {"label": "Total Analyses", "description": "Successfully completed"}, "accuracy": {"label": "Accuracy Rate", "description": "With correct data input"}}, "r_standard": "R is the globally accepted standard for statistical analysis in academic publications", "apa_reports": {"title": "Professional Reports in APA Format", "description_part1": "Save", "percentage": "92%", "description_part2": "time with one-click downloadable reports that meet academic standards."}, "try_free_button": "Try Free Now!"}, "changelog": {"title": "Changelog", "description": "Latest updates and improvements to istabot", "types": {"added": "Added", "improved": "Improved", "fixed": "Fixed"}, "releases": {"2.1.0": {"added": {"data_edit": {"title": "Data editing interface completely redesigned", "items": ["Missing value analysis and editing features", "New variable computation with existing variables", "Variable recoding functionality", "Data validation controls", "Unlimited data editing capability"]}}, "improved": {"ux": {"title": "User experience enhanced", "items": ["Issues and bugs resolved", "Performance optimizations"]}}}, "2.2.0": {"added": {"reporting": {"title": "Report contents improved, Acknowledgements and Warnings sections added", "items": ["Report headings and table contents improved", "Acknowledgements section added", "Warnings section added"]}, "coorporate": {"title": "Special features for corporate accounts", "items": ["Corporate management panel", "Special reporting features for corporate accounts"]}}}}, "back": "Go Back", "date_format": "longDate", "date_locale": "en-US"}}, "read-more": {"read-more": "Read More", "read-less": "Show Less"}, "create-analysis": {"title": "Create Analysis", "step_prefix": "Step", "cancel": "Cancel", "loading": "Loading...", "view_dataset": "View Dataset", "analyses": {"preview": "Preview", "preview_number": "Preview Number"}, "steps": {"title": "Steps", "variable_list": "Variable Selection", "split_list": "Split Variable", "factor_list": "Factor Selection", "dependent_list": "Dependent Variables", "covariate_list": "Covariate Selection", "row_list": "Row Selection", "column_list": "Column Selection", "define_list": "Definition", "define": "Definition", "review": "Review", "status_list": "Status Variable", "time_list": "Time Variable", "strata_list": "Strata Variable", "independent_list": "Independent Variables"}, "buttons": {"next": "Next", "previous": "Previous", "submit": "Submit Analysis", "close": "Close", "clear": "Clear", "add": "Add", "change": "Change"}, "warnings": {"step_incomplete": "You cannot proceed without completing this step.", "previous_steps": "Please complete the previous steps.", "all_steps": "Please complete all required steps.", "dependent_first": "Please complete the Dependent Variables step first.", "reference_required": "Reference values are required for all selected variables in single group analysis.", "invalid_reference_value": "Please enter valid numeric values for all reference values.", "default": "Please select at least one variable in the current step."}, "filters": {"search": "Search variables...", "display_mode": "Display:", "name": "Name", "header": "Header", "both": "Both", "type": "Type:", "scale": "Scale", "ordinal": "Ordinal", "nominal": "Nominal", "no_type": "No type found", "select_all": "Select All", "deselect_all": "Deselect All"}, "variables": {"in_use": "In Use", "reference": "Reference:", "value_labels": "Value Labels", "count": "{{ count }} items", "selected": "Selected Variables", "search": "Search variables...", "not_enough_labels": "Must have at least 2 labels", "ordinal_not_allowed": "Ordinal variable not allowed", "wrong_measure_type": "Wrong measure type for this step", "needs_exactly_two_labels": "Must have exactly 2 labels", "disabled": "Disabled"}, "empty_state": {"title": "No suitable variables found", "description": "There are no variables matching your filter criteria. Please change the filter options and try again."}, "error": {"missing_info": "Analysis information missing", "missing_info_desc": "Required analysis information not found. Please try again."}, "info": {"title": "Information", "variables_in_use": "Variables selected in other steps are disabled and cannot be selected. Each variable can only be used in one step.", "split_variable": "Split Variable", "split_description": "You can select a maximum of 1 variable in this step. The selected variable will be used to group the results. This step is optional, you can continue without selecting a variable.", "covariate": "Covariate Selection", "covariate_description": "You can select as many variables as you want in this step. The selected variables are used to control external factors that may affect the analysis result. This step is optional, you can continue without selecting a variable.", "single_group": "Single Group Analysis", "single_group_description": "You must enter a reference value for each variable you select. Analysis cannot be performed for variables without a reference value, and you cannot proceed to the next step."}, "help": {"title": "Help"}, "examples": {"title": "Examples"}, "dependent": {"title": "Dependent Data Definition", "description": "Define measurement times and variables for dependent data analysis.", "define_title": "Dependent Data Definition", "define_description": "Define measurement times and variables for dependent data analysis.", "time": "Time", "time_name_tr": "Time Name (Turkish)", "time_name_en": "Time Name (English)", "variable_for_time": "Variable for this time", "select_variable": "Select variable", "search_variable": "Search variable...", "no_variable_found": "No variable found", "already_selected": "Already selected", "group": {"title": "Variable Group", "name_tr": "Group Name (Turkish)", "name_en": "Group Name (English)", "placeholder_tr": "E.g.: Blood Values, Test Scores...", "placeholder_en": "E.g.: Blood Values, Test Scores..."}, "times": {"title": "Measurement Times and Variables", "copy": "Copy Times", "add": "Add Time", "min_warning": "You need to define at least 2 times.", "time": "Time {{ index }}", "name_tr": "Time Name (Turkish)", "name_en": "Time Name (English)", "placeholder_tr": "E.g.: Before, After, Week 1...", "placeholder_en": "E.g.: Before, After, Week 1...", "variable": "Variable for this time", "select_variable": "Select variable", "search": "Search variable..."}, "copy_times": {"title": "Copy Times", "description": "Select previously defined times:", "group": "Group {{ index }}"}, "defined_groups": {"title": "Defined Groups", "time": "Time (TR/EN)", "variable": "Variable", "variable_type": "Variable Type"}, "add_button": "Add", "all_times_required": "You must select a variable for all times."}, "split": {"title": "Split Variable", "description": "You can select a split variable to group results (optional)."}, "review": {"title": "Analysis Information", "description": "Review your selections", "analysis_info": "Analysis Information", "drag_to_order": "Drag variables to reorder", "no_definition": "No definition created", "no_variable_selected": "No variables selected in this step", "credit": {"title": "Credit Information", "required": "Required Credits:", "balance": "Current Balance:", "calculating": "Calculating credits...", "amount": "credits will be used", "insufficient": "Insufficient Credits", "insufficient_message": "You don't have enough credits to perform this analysis. You can still continue."}, "report": {"title": "Report Settings", "format": "Report Format:", "decimal": "Decimal Places:", "decimal_separator": "Decimal Separator", "comma": "Comma (,)", "dot": "Dot (.)", "decimal_places": "Decimal Places"}, "configuration": {"title": "Analysis Configuration", "separator": "Decimal Separator:", "precision": "Decimal Precision:", "created_at": "Created At:", "updated_at": "Updated At:"}, "report_type": {"title": "Report Type", "by_row": "By Row", "by_column": "By Column"}, "time_option": {"title": "Contains Time?", "has_time": "Contains Time", "no_time": "Does Not Contain Time"}, "selected_variables": {"title": "Selected Variables", "step": "Step {{ step }}: {{ name }}", "no_selection": "No variables selected in this step.", "count": "{{ count }} variables selected"}, "times": {"title": "Defined Times", "count": "{{ count }} items", "time": "Time {{ index }}", "tr": "Turkish:", "en": "English:", "variable": "Variable:"}}}, "analyses-process": {"create-analysis": {"title": "Create Analysis", "step_prefix": "Step", "steps": {"title": "Steps", "variable_list": "Variable Selection", "split_list": "Split Variable", "factor_list": "Factor Selection", "dependent_list": "Dependent Variables", "covariate_list": "Covariate Selection", "row_list": "Row Selection", "column_list": "Column Selection", "define_list": "Definition", "status_dependent_list": "Status Variable", "status_list": "Status Variable", "time_list": "Time Variable", "independent_list": "Independent Variables", "strata_list": "Strata Variable", "review": "Review"}, "buttons": {"next": "Next", "previous": "Previous", "submit": "Start Analysis", "close": "Close"}}}, "tabs": {"project": {"overview": "Overview", "dataset": "Dataset", "analysis": "Analysis", "settings": "Settings", "history": "History", "members": "Members"}, "settings": {"account": "Account", "purchase_history": "Purchase History", "language": "Language", "report_settings": "Report Settings", "gift_credit": "Gift Credit"}, "admin": {"dashboard": "Dashboard", "users": "Users", "projects": "Projects", "reports": "Reports", "eft-confirm": "EFT Confirm", "transactions": "Transactions", "settings": "Settings"}}, "sidebar": {"titles": {"istabot": "istabot", "workspace": "WORKSPACE", "social": "SOCIAL", "general": "GENERAL"}, "tooltip": {"projects": "Projects", "reports": "Reports", "gift_credits": "Gift Credits", "invite_friends": "Referral Program", "settings": "Settings", "help": "Help", "credit": "Your Credit Information", "buy_credits": "Buy Credits", "logout": "Logout", "profile_options": "Options", "analyses": "Analyses"}, "workspace": {"projects": "Projects", "analyses": "Analyses", "reports": "Reports"}, "social": {"gift_credits": "Gift Credits", "invite_friends": "Referral Program", "explore": "Explore"}, "general": {"settings": "Settings", "help": "Help"}, "balance": "Balance", "buy_credits": "Buy Credit"}, "auth": {"change_password": {"title": "Create New Password", "success_title": "Password Updated", "success_message": "You can now sign in with your new password.", "new_password": "New Password", "confirm_password": "Confirm Password", "new_password_placeholder": "At least 8 characters", "confirm_password_placeholder": "Re-enter your password", "reset_password_token_invalid": "Password reset link is invalid", "update_button": "Update Password", "login_button": "Sign In", "reset_pass": "Reset Password", "reset_pass_desc": "A reset link will be sent to your email to change your password", "error": {"password_short": "Password must be at least 8 characters", "password_mismatch": "Passwords do not match"}}, "login": {"error": "An error occurred", "success": "Success", "redirect": "Taking you to your dashboard...", "account_creating": "Creating your account...", "desc": "Welcome to the new era of statistical analysis", "email": "Email address", "name": "First name", "email_placeholder": "Enter your email address", "surname": "Last name", "password": "Password", "confirm_password": "Confirm password", "name_is_required": "Please enter your first name", "surname_is_required": "Please enter your last name", "email_is_required": "Please enter your email address", "email_is_invalid": "Please enter a valid email address", "phone_is_required": "Please enter your phone number", "phone_is_invalid": "Please enter a valid phone number", "password_is_required": "Please enter your password", "password_is_short": "Your password must be at least 6 characters long", "password_do_not_match": "Your passwords do not match", "all_fields_are_required": "All fields marked with * are required", "email_taken": "This email address is already in use", "phone_taken": "This phone number is already in use", "email_phone_taken": "This email address and phone number are already in use", "user_doesnt_exist": "We couldn't find an account with this email address", "invalid_email_or_password": "Incorrect email address or password", "login_failed": "Unable to sign in. Please check your credentials", "input_validation": "Please check your email address and password", "forgot_password": "Forgot password?", "login": "Sign in", "sign_up": "Create account", "dont_have_account": "New to istabot?", "already_have_account": "Already have an account?", "confirm_contact": "Yes, I'd like to receive updates about new features and special offers", "membership_agreement": "Terms of service", "privacy_policy": "Privacy policy", "and": "and", "agree_to_our": "I have read and agree to the", "you_must_agree": "Please accept our terms of service and privacy policy to continue", "is_istabot_project": "© istabot 2025. All rights reserved.", "welcome_stats": "Welcome to the World of Statistics!", "security_important": "Your Security is Important to Us", "first_step": "Take your first step for professional analysis", "create_strong_password": "Create a strong password to secure your account", "personal_info": "Personal Information", "account_security": "Account Security", "continue": "Continue", "go_back": "Go Back", "create_account": "Create Account", "password_security_tips": "Password Security Tips", "password_tip_length": "At least 6 characters long", "password_tip_case": "Include uppercase and lowercase letters", "password_tip_numbers": "Include numbers and special characters", "remember_me": "Remember me"}, "signup": {"success": "Success", "redirect": "Redirecting to homepage...", "account_creating": "Creating your account...", "welcome": "Welcome to the new era of statistical analysis!", "security_title": "Your Security is Important to Us", "first_step": "Take your first step towards professional analysis", "create_password": "Create a strong password to keep your account secure", "personal_info": "Personal Information", "security": "Account Security", "first_name": "First Name", "first_name_placeholder": "Enter your first name", "last_name": "Last Name", "last_name_placeholder": "Enter your last name", "email": "Email", "email_placeholder": "Enter your email address", "phone": "Phone", "password": "Password", "password_placeholder": "At least 8 characters", "confirm_password": "Confirm Password", "confirm_password_placeholder": "Re-enter your password", "first_name_required": "Please enter your first name", "last_name_required": "Please enter your last name", "email_required": "Please enter your email address", "email_invalid": "Please enter a valid email address", "phone_required": "Please enter your phone number", "phone_is_invalid": "Please enter a valid phone number", "password_required": "Please enter your password", "password_min_length": "Password must be at least 8 characters", "password_max_length": "Password cannot exceed 24 characters", "confirm_password_required": "Please confirm your password", "passwords_not_match": "Passwords do not match", "password_tips": "Password Security Tips", "password_tip_1": "Must be at least 8 characters long", "password_tip_2": "Include uppercase and lowercase letters", "password_tip_3": "Use numbers and special characters", "continue": "Continue", "back": "Go Back", "create_account": "Create My Account", "already_have_account": "Already have an account?", "login": "Sign in", "creating_account": "Creating account...", "error": "Error!", "errors": {"email_taken": "This email address is already in use", "phone_taken": "This phone number is already in use", "email_phone_taken": "This email and phone number are already in use"}}, "mail-input": {"title": "Reset Password", "desc": "Enter your email address to reset your password", "email": "Email address", "email_placeholder": "Enter your email address", "send_email": "Send reset link", "cancel": "Cancel", "email_required": "Please enter your email address", "email_invalid": "Please enter a valid email address", "error": "Error!", "email_not_found": "No account found with this email address", "email_sent": "Reset link sent", "password_reset_instructions_sent": "Check your email for reset instructions", "sending_email": "Sending reset link...", "email_blank": "Email could not be sent.", "sending": "Sending..."}, "verification": {"desc_title": "Check your email to verify your account", "pass_title": "Check your email to reset your password", "desc": "We sent you an email with instructions", "didnt_receive": "Didn't receive the email?", "resend": "Resend email", "back": "Back to sign in", "update": "Update email address", "resend_success": "Verification email sent", "resend_error": "Unable to send email", "email_already_confirmed": "Your email is already verified", "email_not_found": "Email address not found", "sending_email": "Sending email...", "success": "Success", "error": "Error!", "email_confirm_sent": "Email sent", "email_blank": "Email could not be sent"}, "verified": {"email_confirmed": "Your email has been verified successfully ✓", "email_already_confirmed": "Your email is already verified", "login": "Back to sign in", "check": "Verifying your email address...", "confirmation_token_invalid": "Email verification failed", "resend": "Resend verification email"}}, "project_list": {"compare": "Compare Project", "cant_compare": "These projects cannot be compared", "clone": "Clone Project", "cant_clone": "This project cannot be cloned", "recalculate": "Recalculate Project Reports", "cant_recalculate": "Reports for this project cannot be recalculated", "clear_search": "Clear search", "select_project": {"title": "Select target project for comparison", "select": "Select project", "cancel": "Cancel", "compare": "Compare"}, "my_projects": "My projects", "report_settings_title": "Report Settings", "title": "Projects", "total": "Total", "search_placeholder": "Search by project name or dataset...", "description": "Manage, track and organize all your projects in one place", "show_favorites": "Show favorites", "show_all": "Show all", "filters": {"title": "Filter", "reset": "Clear Filters", "date_range": {"label": "Date Range", "start": "Start", "end": "End"}, "sorting": {"label": "Sort", "options": {"date_desc": "Date (New-Old)", "date_asc": "Date (Old-New)", "name_asc": "Name (A-Z)", "name_desc": "Name (Z-A)", "position": "<PERSON><PERSON><PERSON>"}}, "status": {"label": "Status", "options": {"all": "All", "new": "New Project", "ready": "Ready for Analysis", "needs_review": "Needs Review", "needs_dataset": "Dataset Required"}}, "dataset": {"label": "Dataset", "options": {"all": "All", "with": "Has Dataset", "without": "No Dataset"}}}, "status": {"new_project": "New Project", "needs_dataset": "Needs Dataset", "ready_for_analysis": "Ready for Analysis", "needs_review": "Needs Review"}, "search_project": "Search projects", "create_project": "Create project", "delete": "Delete project", "cant_delete": "This project cannot be deleted", "project_not_found": "Let's create your first project", "create_project_null": "Create your first project", "analysis": "Analysis", "descriptive": "Descriptive analysis", "independent": "Independent data analysis", "dependent": "Dependent data analysis", "single": "Single group analysis", "multi": "Multi group analysis", "correlation": "Correlation analysis", "chisq": "Chi-square analysis", "dataset": "Dataset", "my_dataset": "My datasets", "no_dataset": "No dataset available", "dataset_not_ready": "Dataset not ready", "data_view": "Data view", "variable_view": "Variable view", "dataview_warning": "Numbers are rounded to 2 decimal places for display. Your original data remains unchanged", "past_reports": "Past reports", "report_created": "Report created", "report_not_found": "No reports found", "download_all_reports": "Download all reports", "report_settings": "Report settings", "start_analysis": "Start analysis", "continue_to_analysis": "Continue to analysis", "for_first_analysis": "Click start analysis to begin your first analysis", "analysis_not_found": "No analysis found", "separator": "Decimal separator", "comma": "Comma", "dot": "Dot", "precision": "Decimal precision", "precision_info": "Enter a value between 1 and 4", "example": "Example", "continue": "Continue", "save": "Save", "reset": "Reset", "show_balance": "Show Balance Detail", "buy_analysis": "Buy Analysis Credits", "analysis_management": "Analysis Management", "project": "Project", "go_to_project": "Go to Project", "edit": "Edit", "edit_dataset": "Edit Dataset", "add_dataset": "Add Dataset", "created_at": "Created At", "report_title": "Report Title", "dataset_required": "Please add a dataset to start the analysis", "clear": "Clear", "show": "Show", "view": "View", "download": "Download", "action": "Action", "go_to_eistatistik.com": "Visit eistatistik.com", "file_preparing": "Preparing file", "edit_title": "Edit project name", "loading": "Loading...", "my_analyses": "My analyses", "language": "Language", "add_favorite": "Add to favorites", "remove_favorite": "Remove from favorites", "credit_used": "credits used"}, "project_detail": {"header": {"last_updated": "Last Update:", "overview": "Overview", "dataset": "Dataset", "analysis": "Analysis", "settings": "Settings", "members": "Members", "credits_used": "credits used", "edit": "Edit", "no_description": "No description", "normal": "Normal Proje", "demo": "<PERSON><PERSON>", "demo_template": "<PERSON><PERSON>", "project_id": "Project ID", "used_credits": "Used Credits", "updated_at": "Last Updated", "admin_tools": {"menu": "<PERSON><PERSON>", "change_project_type": "Change Project Type", "clone": "Clone Project", "recalculate": "Recalculate Reports", "compare": "Compare Reports"}}, "overview": {"dataset_info": "Dataset Information", "edit_dataset": "Edit Dataset", "view_dataset": "View Dataset", "variables": "Variables", "missing_values": "Missing Values", "project_desc": "Project Description", "edit": "Edit", "save": "Save", "cancel": "Cancel", "no_description": "No description", "compare": "Compare Project", "cant_compare": "This project cannot be compared", "demo_info": "This is a demo project. Your demo credits will be used in the transactions you make.", "clone": "Clone Project", "cant_clone": "This project cannot be cloned", "show_all": "Show all", "recalculate": "Recalculate project reports", "cant_recalculate": "Reports for this project cannot be recalculated", "save_dataset": "Save Dataset", "credit_used": "credits used", "dataset_ready": {"title": "Your Dataset is Ready for Analysis!", "info": "Choose from our statistical analyses to gain insights from your data.", "button": "Start Analysis"}, "dataset_not_diagnosed": {"warning": "Dataset Review Required", "title": "Your Dataset Has Not Been Processed Yet!", "info": "Edit the dataset to check for missing values and errors.", "button": "Edit Dataset"}, "no_dataset": {"title": "Upload Your Dataset", "info": "Start by uploading your dataset in Excel format to begin your analysis journey.", "area_text": "Drag and drop your file here or,", "area_text_2": "browse files", "area_info": "Only Excel files are supported (.xlsx)."}, "last_reports": {"title": "Last Reports", "view_all": "View all reports for this project", "no_report": "No reports have been generated for this project yet."}}, "dataset": {"last_updated": "Last updated at:", "imported": "imported", "not_imported": "Not Imported", "variable_status": {"total": "Total", "valid": "<PERSON><PERSON>", "invalid": "Invalid"}, "placeholder": "Search variable...", "download": "Download", "clear_search": "Clear search", "edit_dataset": "Edit Dataset", "filter": {"title": "Filter", "type": "Type", "all": "All", "import_status": "Import Status", "imported": "Imported", "no_imported": "Not Imported", "reset_filter": "Reset Filter"}, "table": {"headers": {"import": "Import", "variable": "Variable Name", "type": "Type", "label": "Label", "variable_label": "Variable Labels"}, "imported": "Imported", "not_imported": "Not Imported", "missing_value": "Missing Value"}, "values": "Value"}, "analysis": {"header_analysis": "Select Analysis Type", "desc_analysis": "Select the appropriate analysis based on your research question", "header_history": "Analysis Reports", "desc_history": "View and manage your previous analysis reports", "select": "Select", "detail": "Detail", "show_history": "Show History", "show_analyses": "Show Analyses", "download_favorites": "Download Favorite Reports", "filter": {"title": "Filter", "sort_by": "Sort By", "date_range": "Date Range", "start": "Start", "end": "End", "reset": "Reset Filters"}, "compare_report": "Compare Reports", "select_reports_compare": "Select reports to compare", "compare": "Compare", "cancel": "Cancel", "new": "New", "analyses": {"descriptive": "Descriptive Statistics", "single": "Single Group Analysis", "multi": "Multiple Group Analysis", "dependent": "Dependent Data Analysis", "correlation": "Correlation Analysis", "chisq": "Chi-Square Analysis", "comean": "Mean Comparison", "logistic_cox": "Logistic/Cox Regression", "survival": "Survival Analysis", "roc": "ROC Analysis", "linear": "Linear Regression"}, "analysis_history": {"clear_search": "Clear", "filters": "Filters", "sort_by": "Sort by", "newest_first": "Newest First", "oldest_first": "Oldest First", "name_asc": "Name (A-Z)", "name_desc": "Name (Z-A)", "date_range": "Date Range", "start_date": "Start date", "end_date": "End date", "reset_filters": "Reset Filters", "compare_reports": "Compare Reports", "select_reports": "Select reports to compare", "cancel": "Cancel", "compare": "Compare", "search_placeholder": "Search analyses...", "no_reports": "No analysis reports found", "downloading": "Downloading favorite reports...", "credit_used": "credits used"}}, "settings": {"title": "Project Settings", "project_name": "Project Name", "edit": "Edit", "save_changes": "Save Changes", "cancel": "Cancel", "name_required": "Project name is required", "name_max_length": "Project name cannot exceed 64 characters", "dataset_name": "Dataset Name", "no_dataset": "No dataset found", "save_dataset": "Save Dataset", "add_dataset": "Add Dataset", "update_dataset": "Update Dataset", "dataset_locked": "Dataset locked", "dataset_locked_desc": "Changes have been made to the dataset.", "dataset_add_success": "Dataset added successfully", "dataset_add_error": "An error occurred while adding dataset", "dataset_update_success": "Dataset updated successfully", "dataset_update_error": "An error occurred while updating dataset", "delete_project": "Delete Project", "delete_project_warning": "When you delete your project, all your data will be permanently deleted. This action cannot be undone.", "report_settings_title": "Report Settings", "separator": "Decimal Separator", "dot": "Dot", "comma": "Comma", "precision": "Decimal Precision", "example": "Example", "update_success": "Project updated successfully", "update_error": "An error occurred while updating the project", "file_processing_error": "An error occurred while processing the file", "delete_success": "Project deleted successfully", "delete_error": "An error occurred while deleting the project"}, "members": {"title": "Project Members", "description": "View and manage all researchers who have access to this project", "loading": "Loading members...", "loading_researchers": "Loading researchers...", "add_member": "Add Researcher", "share_history": "History", "no_history": "No activities yet", "search_placeholder": "Search among members...", "clear_search": "Clear search", "show_history": "Show History", "hide_history": "Hide History", "total_members": "Total {{count}} members", "roles": {"owner": "Project Owner", "researcher": "Researcher"}, "owner_info": {"title": "Project Founder", "description": "Owner and administrator of this project"}, "researcher_info": {"title": "Researcher", "description": "Researcher with access to project analyses"}, "actions": {"transfer_ownership": "Transfer Ownership", "remove_member": "Remove Member", "add_first_researcher": "Add First Researcher"}, "empty_state": {"no_researchers": "No researchers yet", "no_researchers_description": "No researchers have been added to this project yet. Click the button to add the first researcher.", "no_search_results": "No search results found", "no_search_results_description": "Try different keywords"}, "history": {"title": "Share History", "no_history": "No activities have been performed yet", "no_history_description": "Project membership changes will appear here", "actions": {"add_researcher": "Researcher Added", "remove_researcher": "Researcher Removed", "transfer_ownership": "Ownership Transferred"}}, "confirmations": {"remove_member": {"title": "Remove Researcher", "content": "Are you sure you want to remove {{name}} from the project?", "confirm": "Remove", "cancel": "Cancel"}, "transfer_ownership": {"title": "Transfer Ownership", "content": "Are you sure you want to transfer project ownership to {{name}}? This action cannot be undone.", "confirm": "Transfer", "cancel": "Cancel"}}, "messages": {"member_added_success": "Researcher added successfully", "member_removed_success": "Researcher removed successfully", "ownership_transferred_success": "Project ownership transferred successfully", "user_not_found": "User not found", "member_already_exists": "Researcher already exists or is project owner", "no_permission": "You don't have permission for this action", "add_member_error": "Could not add researcher", "remove_member_error": "Could not remove researcher", "transfer_ownership_error": "Could not transfer ownership", "load_researchers_error": "Could not load researchers", "load_history_error": "Could not load history"}}, "add_member": {"title": "Add Researcher", "email_label": "Email Address", "email_required": "Email address is required", "email_invalid": "Please enter a valid email address", "email_placeholder": "<EMAIL>", "email_placeholder_focused": "example: <EMAIL>", "info_title": "Info:", "info_description": "The user to be added must be registered in the system. After adding the researcher, they will be able to access project analyses.", "cancel": "Cancel", "submit": "Add Researcher", "submitting": "Adding...", "form_validation": {"email_required": "Email address is required", "email_format": "Please enter a valid email address"}}, "type_update": {"unauthorized": "Unauthorized", "success": "Project type updated successfully", "error": "An error occurred while updating the project type"}}, "report_list": {"title": "Reports", "create_report": "Create New Report", "project_count": "total reports from", "total_report": "projects", "search_placeholder": "Search by report title or project...", "download_report": "Download Report", "by_projects": "Projects", "all_reports": "All Reports", "select_report_prompt": "Select a report to copy", "select_report_desc": "Choose the report you want to copy from the list", "credit_used": "credits used", "clear_search": "Clear search", "common": {"cancel": "Cancel"}, "filters": {"title": "Filter", "reset": "Clear Filters", "date_range": {"label": "Date Range", "start": "Start", "end": "End"}, "sorting": {"label": "Sort", "options": {"date_desc": "Date (New-Old)", "date_asc": "Date (Old-New)", "name_asc": "Name (A-Z)", "name_desc": "Name (Z-A)", "position": "<PERSON><PERSON><PERSON>"}}, "status": {"label": "Status", "options": {"all": "All", "new": "New Project", "ready": "Ready for Analysis", "needs_review": "Needs Review", "needs_dataset": "Dataset Required"}}, "dataset": {"label": "Dataset", "options": {"all": "All", "with": "Has Dataset", "without": "No Dataset"}}}}, "create_report": {"title": "Create Report", "new_analysis": "New Analysis", "new_analysis_desc": "Create report by starting a new analysis", "from_existing": "From Existing", "from_existing_desc": "Copy from an existing report", "select_project": "Select Project", "select_project_placeholder": "Choose a project", "invalid_dataset": "Project can not use", "select_analysis_type": "Select Analysis Type", "new": "New", "common": {"cancel": "Cancel", "continue": "Continue", "back": "Back"}, "no_valid_projects": "No projects with uploaded datasets found. Please upload a dataset to a project first.", "no_projects_available": "No eligible projects available"}, "reports": {"title": "Reports"}, "analyses_list": {"my_analyses": "My Analyses", "continue": "Continue", "dataset": "Dataset", "project": "Project"}, "dataset_list": {"my_datasets": "My Datasets", "show": "Show", "project": "Project", "variable_view": "Variable View", "data_view": "Data View", "download_dataset": "Download Dataset"}, "analyses": {"descriptive": {"title": "Descriptive Statistics", "description": "Provides summary statistics for variables in your dataset", "help_text": "Descriptive statistics summarize the central tendency, dispersion, and shape of a dataset's distribution.", "examples": {"1": "Examining age distribution in a sample", "2": "Calculating mean and median of income data", "3": "Finding minimum, maximum, and range values of variables"}}, "single": {"title": "Single Group Analysis", "description": "Tests whether the mean of a variable differs from a specific test value", "help_text": "Single group t-test allows you to compare the mean of a variable with a theoretical value.", "examples": {"1": "Testing if a product's weight differs from a standard value", "2": "Determining if a group's average score differs from a norm", "3": "Testing if a mean value is above or below a certain threshold"}}, "multi": {"title": "Multiple Group Analysis", "description": "Tests mean differences between more than two groups (ANOVA)", "help_text": "ANOVA (Analysis of Variance) tests whether there are significant differences between the means of different groups.", "examples": {"1": "Testing if there are differences in income means between groups with different education levels", "2": "Comparing results of different treatment groups", "3": "Comparing responses of different age groups to a test"}}, "dependent": {"title": "Dependent Data Analysis", "description": "Tests the difference between two measurements from the same sample", "help_text": "Dependent samples t-test tests the difference between values measured in two different situations or times from the same sample.", "examples": {"1": "Comparing test scores before and after a training program", "2": "Comparing blood values before and after using a medication", "3": "Comparing performances of the same participants in two different conditions"}, "incomplete_define": {"title": "Incomplete Definition", "content": "You have an incomplete definition. If you continue, this definition will not be included in the analysis. You need to click the add button first.", "confirm": "Continue", "cancel": "Cancel"}}, "correlation": {"title": "Correlation Analysis", "description": "Measures the direction and strength of the relationship between two variables", "help_text": "Correlation analysis allows you to determine the strength and direction of the relationship between two variables.", "examples": {"1": "Examining the relationship between age and income", "2": "Measuring the relationship between education duration and salary", "3": "Analyzing the relationship between two different test scores"}, "min_variables_warning": "You must select at least 2 variables for correlation analysis."}, "chisq": {"title": "Chi-Square Analysis", "description": "Tests the relationship between categorical variables", "help_text": "Chi-square test tests whether there is a relationship between categorical variables.", "examples": {"1": "Testing if there is a relationship between gender and occupation preference", "2": "Testing if there is a relationship between education level and political view", "3": "Performing independence test between two nominal variables"}}, "comean": {"title": "Mean Comparison", "description": "Tests mean differences between two or more groups", "help_text": "Mean comparison analysis tests whether there are significant differences between the means of different groups.", "examples": {"1": "Comparing results of two different treatment groups", "2": "Comparing responses of different age groups to a test", "3": "Analyzing income differences by education level"}}, "logistic_cox": {"title": "Logistic/Cox Regression", "description": "Performs probability modeling and survival analysis for binary outcome variables", "help_text": "Logistic/Cox regression allows you to perform probability modeling and survival analysis for binary outcome variables.", "examples": {"1": "Predicting disease risk based on patient characteristics", "2": "Analyzing the time until a specific event occurs", "3": "Evaluating the impact of risk factors"}}, "survival": {"title": "Survival Analysis", "description": "Analyzes the time until a specific event occurs", "help_text": "Survival analysis allows you to examine the time until a specific event occurs and the factors that influence it.", "examples": {"1": "Analyzing the survival time of patients after treatment", "2": "Comparing the impact of different treatments on survival", "3": "Assessing the effect of risk factors on survival duration"}}, "roc": {"title": "ROC Analysis", "description": "Evaluates the accuracy of diagnostic tests and optimizes threshold values", "help_text": "ROC analysis allows you to evaluate the accuracy of diagnostic tests and determine optimal threshold values.", "examples": {"1": "Assessing the sensitivity and specificity of a diagnostic test", "2": "Comparing the performance of different tests", "3": "Determining the optimal cutoff point"}}, "linear": {"title": "Linear Regression", "description": "Models the relationship between a dependent variable and one or more independent variables to make predictions", "help_text": "Linear regression allows you to model the relationship between a dependent variable and one or more independent variables to make predictions.", "examples": {"1": "Predicting house prices using factors like area and location", "2": "Identifying factors that affect sales figures", "3": "Analyzing variables affecting academic performance"}}, "insufficient_credits": {"title": "Insufficient Credits", "content": "You don't have enough credits to perform this analysis. If you still want to continue, you will need to make a payment to see the analysis results.", "confirm": "Continue", "cancel": "Buy Credits"}, "go_to_project": "Back to project", "go_back_to_analysis_list": "Back to analyses", "go_back_to_reports": "Back to reports", "go_to_my_analyses": "View my analyses", "create_analyse": "Create analysis", "create_new_analysis": "Create new analysis", "start_analysis": "Start analysis", "press_enter_to_save": "Press Enter to save", "enter_report_title": "Enter report title", "cancel": "Cancel", "select_your_analyze_type": "Select analysis type", "hover_analyses_for_info": "Hover over each type for details", "analysis_not_found": "Create your first analysis", "report_created": "Report created", "report_not_found": "No report found", "report_is_creating": "Creating your report...", "report_is_ready": "Your report is ready", "save_report": "Update current report", "save_as_report": "Save as new report", "clone_report": "Clone report variables", "update_existing_report": "Update existing report", "create_new_report": "Create new report", "clone_report_question": "You came from cloning report variables. What would you like to do?", "delete_report": "Delete report", "maximize_report": "Show full report", "select_variable": "Select variable", "variable_not_found": "No variable found", "selected_variable_not_found": "Selected variable not found", "no_variable_of_this_type_found": "No variables of this type", "at_least_one_variable": "Select at least one variable and enter reference value if needed", "at_least_one_defined": "Define at least one variable", "is_involve_time": "Include time variable?", "at_most_one": "Select only one time variable", "add_time": "Add time", "time_table": "Time table", "at_least_two_times": "Enter at least 2 time points", "use_earlier": "Use previous times", "enter_time": "Enter time", "list": {"selected_variable_list": "Selected variables", "column_list": "Column variables", "row_list": "Row variables", "factor_list": "Factors/groups", "variable_list": "Variables", "split_list": "Split variable", "pair_list": "Paired variables", "define_list": "Definitions", "dependent_list": "Dependent variables", "covariate_list": "Covariates"}, "definition": "Definition", "en_definition": "English definition", "tr_definition": "Turkish definition", "add_definition": "Add definition", "no_definition_added": "No definitions added", "use_define_times": "Select time definition", "report_settings": "Report settings", "reporting_options": "Report options", "separator": "Decimal separator", "comma": "Comma", "dot": "Dot", "precision": "Decimal places", "preview": "Preview", "preview_number": "Example number in selected format", "data_is_preparing": "Preparing data...", "file_preparing": "Preparing file...", "fetching_data": "Loading data...", "coming_soon": "Coming soon", "clear": "Clear", "reset": "Clear selection", "undo": "Undo changes", "edit_report_title": "Edit report title", "add_remove_favorites": "Add or remove report from favorites", "edit_report": {"title": "Edit Project", "label": "Project name", "description": "Description", "description_placeholder": "Project description", "optional": "Optional", "update": "Update"}, "remove": "Remove", "remove_defined": "Remove definition", "show": "Show", "hide": "<PERSON>de", "close": "Close", "save": "Save", "select": "Select", "select_all": "Select all", "back": "Back", "forward": "Continue", "change": "Change", "review": "Review", "filter": "Filter", "split": "Split", "show_balance": "Show balance details", "balance": "Balance", "buy_analysis": "Buy analysis credits", "credit_used": "credits used", "options": "Options", "yes": "Yes", "no": "No", "language": "Language", "reports": "Reports", "download": "Download", "download_all_reports": "Download all", "search_analyses": "Search analyses", "search_variable": "Search variables", "variable": "Variable", "variables": "Variables", "type": "Type", "reference": "Reference", "reference_value": "Reference value", "selected": "Selected", "timer": "Timer", "times": "Times", "all_fields_are_required": "All fields are required", "no_value_label": "No value label", "example": "Example", "show_dataset": "View dataset", "definitions": "Variable definitions", "analysis_reports": "Analysis reports", "according_to_row": "Group by row", "according_to_column": "Group by column", "time": "Time point", "use_times": "Use time points", "new": "New", "summary": {"title": "Analysis Summary", "description": "Selected variables and analysis settings", "selected_variables": "Selected Variables", "credit_info": "Credit Information", "required_credits": "Required Credits", "available_credits": "Available Credits", "credits": "credits", "insufficient_credits": "You don't have enough credits. Please add more credits.", "report_settings": "Report Settings", "decimal_separator": "Decimal Separator", "comma": "Comma", "dot": "Dot", "decimal_precision": "Decimal Precision", "decimal_places": "decimal places", "example": "Example", "analysis_info": "Analysis Info", "outputs": "Outputs", "output_report": "Word format report", "output_data": "Excel data table", "output_charts": "Automatic charts", "credit_calculation": "Credit Calculation", "download_sample": "Download Sample Report"}, "navigation": {"back": "Back", "run_analysis": "Run Analysis"}, "titles": {"descriptive": "Descriptive Statistics", "single": "Single Group Analysis", "multi": "Multiple Group Analysis", "dependent": "Dependent Sample Analysis", "correlation": "Correlation Analysis", "chisq": "Chi-Square Analysis", "comean": "Mean Comparison", "logistic_cox": "Logistic/Cox Regression", "survival": "Survival Analysis", "roc": "ROC Analysis", "linear": "Linear Regression", "unknown": "Analysis"}, "descriptions": {"descriptive": "calculates basic statistics like mean, median, and standard deviation for your selected variables.", "single": "tests whether the mean of a variable is different from a specific value.", "multi": "compares means across multiple groups (ANOVA).", "dependent": "compares two measurements from the same sample.", "correlation": "measures the relationship between two variables.", "chisq": "tests the relationship between categorical variables.", "comean": "compares means between different groups.", "logistic_cox": "models probability of binary outcomes and survival analysis.", "survival": "analyzes time until an event occurs.", "roc": "evaluates diagnostic test accuracy and optimizes thresholds.", "linear": "models relationship between dependent variable and one or more independent variables.", "unknown": "performs statistical calculations on your data."}, "no_description": "No description"}, "analyses_type_list": {"descriptive": "Descriptive Statistics", "descriptive_info": "Analyze the distribution characteristics and central tendencies of your variables", "independent": "Independent Data Analysis", "single": "Independent Data Analysis", "single_info": "Compare your measured values with known reference or literature values", "single_i": "Single Group", "multi": "Multiple Group Data Analysis", "multi_info": "Compare quantitative data between two or more independent groups", "multi_i": "Multiple Groups", "correlation": "Correlation Analysis", "correlation_info": "Explore relationships and patterns between your variables", "chisq": "Chi-square Analysis", "chisq_info": "Analyze relationships between categorical variables", "dependent": "Dependent Data Analysis", "dependent_info": "Compare repeated measurements over time within the same group", "comean": "Mean Comparison", "comean_info": "Analyze mean differences between two or more independent groups", "logistic_cox": "Logistic/Cox Regression", "logistic_cox_info": "Perform probability modeling and survival analysis for binary outcome variables", "survival": "Survival Analysis", "survival_info": "Analyze the time until a specific event occurs", "roc": "ROC Analysis", "roc_info": "Evaluate the accuracy of diagnostic tests and optimize threshold values", "linear": "Linear Regression", "linear_info": "Model the relationship between a dependent variable and one or more independent variables", "r_s_r": "Regression/Survival/ROC", "create_analyse": "Create Analysis", "select": "Select", "close": "Close", "requirements": "Requirements"}, "dataset_view": {"rows": "rows", "variables": "variables", "export": "Export", "close": "Close", "try_again": "Try Again", "error_title": "An error occurred while loading the dataset.", "error_description": "Please try again to view the dataset or contact support.", "data_view": "Data View", "variable_view": "Variable View", "search_placeholder": "Search...", "search_variable_placeholder": "Search variables...", "filter": "Filter", "filter_options": {"all_variables": "All Variables", "scale": "Scale Variables", "nominal": "Nominal Variables", "ordinal": "Ordinal Variables"}, "search_results": {"showing": "Showing {{filtered}} / {{total}} rows", "not_found_title": "No Results Found", "not_found_description": "No variables match your search criteria. Please try a different search term or clear filters."}, "clear_filters": "Clear all filters", "search": "Search:", "filter_column": "Filter:", "variable": {"label_tr": "Label (TR)", "label_en": "Label (EN)", "value_labels": "Value Labels", "value": "Value", "missing": "missing"}}, "settings": {"invoice": "Invoice History", "credit": "Credit Balance", "settings": "Settings", "language": "Language Preferences", "logout": "Sign Out", "product": "Analysis Type", "dataset": "Dataset Name", "status": "Status", "date": "Transaction Date", "price": "Amount", "canceled": "Canceled", "approved": "Completed", "pending": "In Progress", "single": "Single Group Analysis", "multi": "Multiple Group Analysis", "correlation": "Correlation Analysis", "chisq": "Chi-Square Analysis", "descriptive": "Descriptive Statistics", "dependent": "Dependent Analysis", "logistic_cox": "Logistic/Cox Regression", "survival": "Survival Analysis", "roc": "ROC Analysis", "linear": "Linear Regression", "purchase_history": "Order History", "no_purchase": "No orders found", "more": "View Details", "cookie": "<PERSON><PERSON>", "membership_agreement": "Terms of Service", "privacy_policy": "Privacy Policy", "agreement_text": "By using this service, you agree to our terms and conditions"}, "profile": {"account": "My Account", "delete_account": "Delete My Account", "account_title": "Account <PERSON><PERSON>", "purchase_history_title": "Order History", "language_title": "Language Settings", "title": "Welcome Back 👋", "settings": "Account <PERSON><PERSON>", "language": "Language Settings", "logout": "Sign Out", "purchase_history": "Order History", "admin_panel": "Admin Dashboard", "corporate": "Corporate Management", "language_updated_success": "Language preference updated", "language_updated_error": "Unable to update language preference", "personal_info": "Personal Information", "name": "Full Name", "email": "Email", "phone": "Phone", "member_since": "Member Since", "gift_credit": "Gift Credit Preference", "gift_credit_desc": "Choose whether other users can send you gift credits.", "gift_pref_ok": "Gift credit receiving is active.", "gift_pref_no": "Gift credit receiving is disabled.", "change_pref_ok": "Enable gift receiving", "change_pref_no": "Disable gift receiving", "academic_info": "Academic Information", "university": "University", "university_placeholder": "Enter your university name", "faculty": "Faculty", "faculty_placeholder": "Enter your faculty name", "department": "Department", "department_placeholder": "Enter your department name", "interests": "Interests", "interests_placeholder": "Enter your interests separated by commas (e.g: data science, machine learning, artificial intelligence)", "profile_save_success": "Profile information saved successfully", "profile_save_error": "An error occurred while saving profile information"}, "notification": {"auth": {"login": {"success": {"title": "Welcome Back!", "message": "Sign in successful"}, "info": {"title": "Note", "already_logged_in": "You're already signed in"}, "error": {"title": "Sign In Failed", "message": {"email": "Please enter your email", "password": "Please enter your password", "invalid": "Incorrect email or password", "unauthorized": "Access denied", "forbidden": "Account restricted", "notfound": "Account not found", "unauthenticated": "Please sign in", "switched_user_login_successful": "User switch successful", "target_user_not_found": "Selected user not found", "email_taken": "Email already registered", "phone_taken": "Phone number already registered", "email_phone_taken": "Email and phone already registered", "user_doesnt_exist": "No account found", "invalid_email_or_password": "Incorrect email or password", "account_deleted": "Account no longer active"}}}, "logout": {"success": {"title": "See You Soon!", "message": "Sign out successful"}, "error": {"title": "Sign Out Failed", "message": "Unable to sign out. Redirecting to home"}}}, "analysis": {"report_position": {"title": "Success!", "success": "The order of your report has changed"}, "report_favorite": {"title": "Success!", "added": "Report added to favorites", "removed": "Report removed from favorites", "error": {"title": "Error!", "message": "An error occurred."}}, "report_conf": {"success": {"title": "Report Settings Updated"}, "error": {"title": "Settings Update Failed", "message": "Unable to save report settings"}}, "report_delete": {"success": {"title": "Report Removed", "message": "Report successfully removed"}, "error": {"title": "Removal Failed", "message": "Unable to remove report"}}, "report_update": {"success": {"title": "Report Updated"}, "error": {"title": "Update Failed", "message": "Unable to update report"}}, "submit": {"success": {"title": "Analysis Completed!", "message": "Analysis successfully completed."}, "error": {"title": "Error!", "message": "An error occurred while completing the analysis."}, "request_failed": {"title": "Error!", "message": "An error occurred while creating the analysis request."}}, "general": {"error": {"title": "Error!", "message": "An error occurred during the analysis."}}}, "analyses_process": {"success": {"title": "Analysis Complete", "message": "Analysis successfully completed"}, "error": {"title": "Analysis Failed", "message": "Unable to complete analysis"}}, "project": {"favorite": {"position": {"title": "Success!", "success": "The order of your project has changed"}, "title": "Success!", "error_title": "Error!", "added": "Project added to favorites", "removed": "Project removed from favorites", "error": "An error occurred"}, "update_description": {"success": {"title": "Success!", "message": "Description updated."}, "error": {"title": "Error!", "message": "An error occurred while updating the description."}}, "update_name": {"success": {"title": "Project Updated", "message": "Project name updated"}, "error": {"title": "Update Failed", "message": "Unable to update project"}}, "clone": {"success": {"title": "Success!", "message": "Project cloned successfully."}, "error": {"title": "Error!", "message": "An error occurred while cloning the project."}}, "recalculate": {"success": {"title": "Success!", "message": "Project reports recalculated successfully."}, "error": {"title": "Error!", "message": "An error occurred while recalculating project reports."}}, "compare": {"success": {"title": "Success!", "message": "Project compared successfully."}, "error": {"title": "Error!", "message": "An error occurred while comparing the project."}}, "delete": {"success": {"title": "Project Removed", "message": "Project successfully removed"}, "error": {"title": "Removal Failed", "message": "Unable to remove project"}}}, "settings": {"delete_account": {"success": {"title": "Account Removed", "message": "Your account has been removed"}, "error": {"title": "Removal Failed", "message": "Unable to remove account"}}}, "payment": {"success": {"title": "Payment Complete", "message": "Payment processed successfully"}, "error": {"title": "Payment Failed", "message": "Unable to process payment"}, "rejected": {"title": "Payment Declined", "message": "Payment was declined"}, "canceled": {"title": "Payment Canceled", "message": "Payment has been canceled"}, "eft": {"success": {"title": "Transfer Complete", "message": "Bank transfer successful"}, "error": {"title": "Transfer Failed", "message": "Unable to complete transfer"}}, "coupon": {"success": {"title": "Coupon Applied", "message": "Discount code applied"}, "remove": {"title": "Coupon Removed", "message": "Discount code removed"}, "error": {"title": "Coupon Invalid", "message": "Unable to apply discount code"}}, "address_detail": {"success": {"title": "Address Saved", "message": "New address saved"}, "error": {"title": "Address Error", "message": "This address label already exists"}}, "address_select": {"success": {"title": "Address Selected", "message": "Billing address updated"}}, "delete_address": {"success": {"title": "Address Removed", "message": "Address successfully removed"}, "error": {"title": "Removal Failed", "message": "Unable to remove address"}}, "update_address": {"success": {"title": "Address Updated", "message": "Address details updated"}, "error": {"title": "Update Failed", "message": "Unable to update address"}}, "contact_us": {"title": "Message Sent", "message": "Thank you for your inquiry. We'll respond shortly"}}, "dataset": {"download": {"error": {"title": "Download Failed", "message": "Unable to download dataset"}}, "view": {"error": {"title": "View Failed", "message": "Unable to load dataset"}}}, "report": {"download": {"error": {"title": "Download Failed", "message": "Unable to download report"}}, "favorite": {"title": "Success!", "added": "Report added to favorites", "removed": "Report removed from favorites"}}}, "404": {"pageNotFound": "The page you are looking for seems to be lost in our data.", "goBack": "Go back to istabot"}, "shared": {"danger_zone": "Danger Zone", "edit": "Edit", "cancel": "Cancel", "save": "Save", "credit_card_result": {"approved": "Your payment has been successfully processed.", "declined": "Your payment has been declined.", "info": "You can close this field.", "close": "Close"}, "confirm": {"delete_project": {"title": "Delete this project?", "content": "This will permanently delete the project and all its data", "confirm": "Delete Project", "cancel": "Keep Project"}, "changes": {"title": "Discard changes?", "content": "You have unsaved changes that will be lost", "confirm": "Discard Changes", "cancel": "Keep Editing"}, "diagnose": {"title": "Finalize dataset?", "content": "This will prepare your dataset for analysis in istabot. This cannot be undone", "confirm": "Finalize Dataset", "cancel": "Keep Editing"}, "reset": {"title": "Reset all changes?", "content": " ", "confirm": "Reset All", "cancel": "Keep Changes"}, "close_payment": {"title": "Leave checkout?", "content": "Items in your cart will be saved for later", "confirm": "Leave Checkout", "cancel": "Continue Checkout"}, "credit_info": {"title": "{{credits}} credits needed for this analysis"}, "analyses_process": {"title": "Insufficient Credits", "content": "You can run this analysis now but won't see results until credits are added. Credits will be deducted once payment is complete", "confirm": "Run Analysis", "cancel": "Add Credits First"}, "delete_account": {"title": "Delete your account?", "content": "This will permanently delete your account and all data", "confirm": "Delete Account", "cancel": "Keep Account"}, "create_project": {"title": "Leave without saving?", "content": "Your changes will be lost", "confirm": "Leave", "cancel": "Keep Editing"}, "zero_eft_payment": {"title": "Payment Notice", "content": "Cart total is 0. Bank transfers will be verified before processing. Contact <EMAIL> with any issues", "cancel": "Got It"}, "report_delete": {"title": "Delete this report?", "content": "This will permanently delete the report", "confirm": "Delete Report", "cancel": "Keep Report"}, "close_eft": {"title": "Leave transfer?", "content": "You've uploaded a receipt but haven't completed the transfer", "confirm": "Leave", "cancel": "Complete Transfer"}, "delete_address": {"title": "Delete this address?", "content": "This will permanently remove the address", "confirm": "Delete Address", "cancel": "Keep Address"}, "edit_address": {"title": "Edit this address?", "content": "Address changes cannot be undone", "confirm": "Edit Address", "cancel": "Keep Current"}, "remove_variable": {"title": "Remove {{variable}} variable? You can add it back later", "confirm": "Remove Variable", "cancel": "Keep Variable"}, "clone_analysis_cancel": {"title": "Cancel analysis clone?", "content": "Clone progress will be lost", "confirm": "Stop Cloning", "cancel": "Continue Cloning"}, "clear_cart": {"title": "Empty your cart?", "content": "This will remove all items", "confirm": "Empty Cart", "cancel": "Keep Items"}, "clear_cart_item": {"title": "Remove this item?", "content": "This item will be removed from your cart", "confirm": "Remove Item", "cancel": "Keep Item"}, "project_detail": {"settings": {"delete_project": {"title": "Delete this project?", "content": "This will permanently delete the project and all its data", "confirm": "Delete Project", "cancel": "Keep Project"}}}, "back_with_coupon": {"title": "Coupon will be lost", "content": "If you go back, your applied coupon will be lost and you need to apply again later. Are you sure you want to continue?", "confirm": "Yes, go back", "cancel": "No, stay here"}, "close_analysis": {"title": "Close analysis?", "content": "You have unsaved changes. If you close now, your changes will be lost.", "confirm": "Close anyway", "cancel": "Continue editing"}}, "create_project": {"new_project": "Create new project", "help_video": "Watch Tutorial", "update_project": "Update project", "name": "Project name", "name_is_required": "Please enter a project name", "dataset": "Dataset", "optional": "Optional", "upload_dataset": "Upload dataset", "upload_success": "Dataset uploaded successfully", "try_again": "Upload failed, please try again", "table_example": "Example dataset", "name_placeholder": "Enter project name", "or_drag_drop": "or drag and drop", "creating": "Creating...", "updating": "Updating...", "excel_only": "Excel files only", "how_to_create": "How to create a dataset", "example_info": "Download this example dataset as a starting point for your analysis", "download": "Download", "file_processing_error": "An error occurred while processing the file.", "update": "Update", "create": "Create", "success": "Success", "error": "Upload failed", "error_message": "Please try again", "create_message": "Project created", "diagnose_message": "Preparing dataset for analysis...", "update_message": "Project updated", "project_creating": "Creating project...", "errors": {"string_values": "This column contains text values where numbers are expected", "separator": "This column contains multiple decimal separators", "server_side": "Unable to process request. Please try again", "remove_columns": "Unable to remove unimported columns", "s3_upload": "Unable to upload file. Please try again"}}, "value-label": {"value_labels": "Value labels", "value": "Value", "labels": "Labels", "revert": "Undo changes", "save": "Save changes", "show_labels": "Show labels", "saved_labels": "Saved labels", "empty_error": "Label field cannot be empty", "use": "Use saved labels"}, "balance": {"titles": "Credit Balance", "cells": {"total": "Total Credits", "available": "Available", "used": "Used", "active_packs": "Active Packages"}, "tabs": {"active_packages": "Active Packages", "usage_history": "Usage History"}, "credits": "Credits", "available": "Available:", "expire": "Expires:", "buy_credits": "Buy More Credits"}, "payment": {"helper_video": "Watch Help Video", "title": "Buy Analysis Credits", "coupon_placeholder": "Enter Coupon Code", "complete_payment": "Complete Payment", "to_online_payment": "Continue to Online Payment", "to_eft": "View Bank Transfer Details", "defined_coupons": "Available Coupons", "accept_payment": "Report Payment", "continue_payment": "Go to Payment", "continue_to_payment": "Continue to payment", "step_descriptions": {"step1": "Choose from our credit packages for your analyses.", "step2": "Select billing information or add a new address.", "step3": "Review your order, confirm and proceed to payment."}, "steps": {"step1": "Package Selection", "step2": "Billing Details", "step3": "Summary"}, "buttons": {"continue": "Next", "back": "Back", "complete_free_order": "Complete Free Order", "continue_to_payment": "Continue to Payment", "view_eft_details": "View Bank Transfer Details"}, "package_selection": {"packages": "Packages", "flexible": "Flexible", "credits": "Credits", "credit": "credit", "credits_label": "credits", "corporate_custom": "Custom for Your Organization", "discount": "Discount", "credit_amount": "Credit Amount", "credit_placeholder": "Min: 1, Max: 1000", "min_credit_error": "Minimum 1 credit required", "max_credit_error": "Total cart cannot exceed 1000 credits (Max: {{max}} credits)", "total": "Total", "details": "Details", "add": "Add", "contact_us": "Contact Us", "credit_calculator": "How many credits will my analysis need?", "cart_summary": "<PERSON>t <PERSON>mma<PERSON>", "selected_packages": "Selected Packages", "piece": "piece", "total_credits": "Total Credits"}, "billing_info": {"billing_addresses": "Billing Addresses", "add_new_address": "Add New Address", "individual": "Individual", "corporate": "Corporate", "default": "<PERSON><PERSON><PERSON>", "tax_no": "Tax Number", "tax_office": "Tax Office", "cart_summary": "<PERSON>t <PERSON>mma<PERSON>", "selected_packages": "Selected Packages", "credits": "credits", "credits_label": "Credits", "piece": "piece", "total_credits": "Total Credits", "total": "Total", "confirm_delete_address": "Are you sure you want to delete this address?"}, "package_details": {"details": "Details", "credit_amount": "Credit Amount", "credits": "Credits", "min_credit_error": "Minimum 1 credit required", "max_credit_error": "Total cart cannot exceed 1000 credits (Max: {max})", "price": "Price", "validity_period_title": "Validity Period", "validity_period_desc": "Valid for 2 years from purchase date", "report_languages_title": "Report Languages", "report_languages_desc": "Get reports in Turkish and English", "report_export_title": "Report Export", "report_export_desc": "Unlimited report downloads", "repurchase_title": "Repurchase", "repurchase_desc": "Buy the same package again anytime", "corporate_support_title": "Corporate Support", "corporate_support_desc": "Priority support and dedicated consulting", "close": "Close", "add_to_cart": "Add to Cart", "contact_us": "Contact Us"}, "payment_summary": {"selected_packages": "Selected Packages", "credits": "credits", "products_total": "Products Total", "discount": "Discount", "total": "Total", "billing_address": "Billing Address", "individual": "Individual", "corporate": "Corporate", "tax_no": "Tax Number", "coupon_code": "Coupon Code", "coupon_placeholder": "Your coupon code", "apply": "Apply", "remove": "Remove", "payment_method": "Payment Method", "credit_card": "Credit Card", "bank_transfer": "Bank Transfer", "credit_card_info": "You will be redirected to Akbank secure payment page after confirming your order.", "bank_transfer_info": "You will see bank transfer details after confirming your order. Credits will be added after payment confirmation.", "payment_info": "Payment Information", "free_order": "Free Order", "free_order_info": "No payment required as your order total is 0₺ thanks to your coupon code.", "agreement_text": "I accept the Distance Sales Agreement and Terms of Use."}, "address_detail": {"update_title": "Edit Address", "new_title": "New Address", "desc": "This address will be used for billing purposes. Please fill all fields correctly and completely.", "address_type": "Address Type", "individual": "Individual", "company_name": "Company/Organization Name", "company_name_placeholder": "Enter Company/Organization Name", "company_name_required": "Company/Organization Name is required.", "corporate": "Corporate", "address_type_required": "Address Type is required.", "select_address_type": "Select Address Type", "address_title": "Address Title", "address_title_placeholder": "Home, Work, Other", "address_title_required": "Address Title is required.", "full_name": "Full Name", "full_name_placeholder": "Enter Full Name", "full_name_required": "Full Name is required.", "city": "City", "city_placeholder": "Enter City", "city_required": "City is required.", "district": "District", "district_placeholder": "Enter District", "district_required": "District is required.", "address": "Address", "address_placeholder": "Enter Address", "address_required": "Address is required.", "tax_office": "Tax Office", "tax_office_placeholder": "Enter Tax Office", "tax_office_required": "Tax Office is required.", "tax_number": "Tax ID Number", "tax_number_placeholder": "Enter Tax ID Number", "tax_number_required": "Tax Number is required.", "close": "Cancel", "save": "Save"}, "eft_detail": {"title": "Bank Transfer Details", "eft_info": "Below are the account details and transfer description for bank transfer payment.", "eft_subinfo": "After making your bank transfer payment, please upload your receipt image in the section below.", "price": "Amount to Transfer", "account_name": "Account Name", "bank_name": "Bank Name", "branch_value": "19 Mayis University Branch (01389)", "branch": "Branch", "img_info": "Click to change", "upload_receipt": "Upload Receipt", "account_number": "Account Number", "iban": "IBAN", "transfer_description": "Leave description field empty.", "send": "Send Payment Notification", "click_to_change": "Click to change", "sending": "Sending Payment Notification"}, "contact": {"title": "Contact Us", "description": "Please fill out the form below to learn more about our corporate package.", "name": "Full Name", "name_input": "Enter Full Name", "name_required": "Full Name is required.", "email": "Email", "email_input": "<PERSON><PERSON>", "email_required": "Email is required.", "phone": "Phone Number (Optional)", "phone_input": "Enter Phone Number", "company": "Company Name", "company_input": "Enter Company Name", "company_required": "Company Name is required.", "message": "Your Message", "message_input": "Describe your needs or questions", "send": "Request Information"}, "calculate_credit": {"title": "Calculate Analysis Credit Amount", "descriptive": "Descriptive Statistics", "independent": "Independent Data Analysis", "dependent": "Dependent Data Analysis", "single": "Single Group", "multi": "Multi Group", "correlation": "Correlation Analysis", "chisq": "Chi-Square Analysis", "variable_count_input": "Enter number of variables", "factor_count_input": "Enter number of factors/groups", "row_variable_count_input": "Enter number of row variables", "col_variable_count_input": "Enter number of column variables", "use_split_variable": "I will use a split variable", "split_label_count": "Enter number of split variable labels", "clear": "Clear", "total_credit": "Total Credits", "save": "Save", "clear_all": "Clear All"}}, "validation_messages": {"dataset_empty": "Dataset must contain at least one variable", "insufficient_rows": "Dataset must contain at least 2 rows of data", "invalid_labels": "Variable labels (EN/TR) and value labels must be non-numeric and not contain & character", "invalid_value_labels": "Value labels must be complete for both EN/TR and not contain & character", "invalid_scale_data": "Scale variables must contain only numeric values", "missing_values": "Variable contains missing values. Please enter a valid value to include missing values in the analysis", "duplicate_values": "Variable contains duplicate values", "inconsistent_format": "Variable contains inconsistent data formats", "unique_value_labels": "Value labels must be unique", "non_numeric_data": "Variable contains non-numeric data. Please recode your data appropriately.", "no_issues_found": "No Issues Found", "validation_details": {"title": "Validation Details", "warnings": "Warning", "errors": "Error", "issue_explanation": "Explanation", "how_to_fix": "How to Fix", "affected_variables": "Affected Variables", "show_details": "Show Details", "hide_details": "Hide Details", "edit": "Edit", "no_issues_title": "All Variables Look Good!", "no_issues_description": "Your dataset has no validation issues. You're ready to proceed with your analysis.", "data_quality_tips": "Data Quality Tips", "tip_consistent_format": "Keep data formats consistent within variables", "tip_descriptive_labels": "Use clear, descriptive labels for variables and values", "tip_handle_missing": "Consider how to handle missing values before analysis", "tip_variable_types": "Ensure variable types (Scale, Nominal, Ordinal) match your data"}, "issue_explanations": {"dataset_empty": "Your dataset does not contain any variables. A valid dataset must include at least one variable.", "insufficient_rows": "Your dataset must contain at least 2 rows of data to be properly analyzed. Since the first row is used for headers, at least one additional row must contain actual data.", "invalid_labels": "Variable labels must not be numeric and should not contain special characters like '&'. Labels are used to provide a human-readable description of your variables.", "invalid_value_labels": "For categorical variables (Nominal/Ordinal), value labels must be present and properly formatted for both English and Turkish. Each value must have a corresponding label, and labels must not contain the '&' character.", "unique_value_labels": "Value labels within a variable must be unique. You have duplicate labels for different values, which can cause confusion in analysis and reporting.", "invalid_scale_data": "Scale variables must contain only numeric values. Text, dates, or other non-numeric data cannot be used in numeric calculations.", "non_numeric_data": "This variable contains non-numeric data but needs to contain numeric values for analysis.", "missing_values": "This variable contains empty cells (missing values). Missing values can affect analysis results if not properly handled.", "inconsistent_format": "This variable contains data in inconsistent formats (e.g., a mix of text and numbers), which can cause issues during analysis.", "missing_value_labels": "This categorical variable (Nominal/Ordinal) is missing value labels. Each value in categorical variables must have a corresponding label."}, "issue_recommendations": {"dataset_empty": "Add variables to your dataset or upload a new dataset containing at least one variable and data.", "insufficient_rows": "Add more rows of data to your dataset. Each analysis requires at least one row of actual data in addition to the header row.", "invalid_labels": "Edit variable labels in the Variable View. Ensure labels are descriptive text (not just numbers) and avoid using the '&' character.", "invalid_value_labels": "In Variable View, click on the Value Labels cell for this variable and add appropriate labels for each value. Ensure both English and Turkish labels are provided for each value.", "unique_value_labels": "Check your value labels in Variable View and ensure each label is unique. If multiple values represent the same concept, consider recoding them into a single value.", "invalid_scale_data": "Either change the variable type from Scale to Nominal/Ordinal or correct the data to ensure all values are numeric. You can use the Value Recoding tool to convert text values into numbers.", "non_numeric_data": "For statistical analysis, convert these values into numbers using the Value Recoding tool, or change the variable type to Nominal/Ordinal if the values represent categories.", "missing_values": "You can handle missing values in the following ways: 1) Use the 'Missing Value Analysis' tool to fill them with appropriate values, 2) Filter out rows with missing values, or 3) Leave them as is, but be aware that they may be excluded from some analyses.", "inconsistent_format": "Review the data and ensure all values follow the same format. Use the Data View to identify inconsistent values and correct them manually or with the Value Recoding tool.", "missing_value_labels": "Add value labels by clicking on the Value Labels cell in Variable View. Each unique value in your data must have a corresponding label in both English and Turkish.", "generic_missing": "Consider using the 'Missing Value Analysis' tool to address missing values in your dataset. You can fill values using methods like mean, median, or mode imputation.", "generic_invalid": "Review your data in Data View and make corrections as needed. Use the Value Recoding tool to fix formatting issues or convert between data types.", "generic": "Use the Data Management tools in the menu to resolve this issue. If the problem persists, you may need to modify your source data and re-upload the dataset."}}, "validation_details": {"title": "Validation Details", "valid_variables": "<PERSON><PERSON>", "warnings": "Warning", "errors": "Error", "filters": {"all_issues": "All Issues", "errors_only": "Errors Only", "warnings_only": "Warnings Only"}, "no_issues_found": "No Issues Found", "empty_states": {"all": "There are no validation issues to display.", "errors": "No errors found in the current dataset.", "warnings": "No warnings found in the current dataset."}, "show_all_issues": "Show All Issues", "status": {"error": "Error", "warning": "Warning"}, "total_variables": "Total Variables", "total_valid_variables": "Total Valid Variables", "not_imported": "Not Imported", "invalid_variables": "Invalid Variables", "not_imported_variables": "Not Imported Variables", "warning_message": "Invalid variables will not be imported.", "old_warning_message": {"line1": "Invalid variables will be excluded from import process.", "line2": "Do you want to continue with valid variables only?"}, "actions": {"cancel": "Cancel", "continue": "Continue"}}, "value_label": {"title": "Value Labels", "available_labels": "Available Labels", "use_labels": "Use These Labels", "value": "Value", "label_en": "Label (EN)", "label_tr": "Label (TR)", "cancel": "Cancel", "save": "Save", "no_labels": "No available labels found", "messages": {"validation_error": "Please check value labels"}}, "edit_project": {"title": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON> adı", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "optional": "İsteğe bağlı", "update": "<PERSON><PERSON><PERSON><PERSON>", "updating": "Updating", "description_placeholder": "<PERSON><PERSON>"}, "diagnose": {"title": "Edit Dataset", "help_video": "Watch Tutorial", "data_management": "Data Management", "validation_details": "Validation Details", "valid": "<PERSON><PERSON>", "warnings": "Warning", "import_variable": "Toggle variable import", "errors": "Error", "data_view": "Data View", "variable_view": "Variable View", "search_placeholder": "Search variables...", "analyze_missing_values": "Missing Value Analysis", "missing_value_info": "Analyse missing values", "create_computed_variable": "Compute Variable", "compute_variable_info": "Computing variables", "auto_recode_values": "Recode Values", "auto_recode_info": "Auto recoding values", "download_dataset": "Download Dataset", "filters_list": "Filters", "computed": "Computed", "recoded": "Recoded", "imputed": "Imputed", "imputed_with": "This variable imputed with {{method}} method", "recoded_with": "This variable recoded with {{method}} method", "clear_search": "Clear search", "methods": {"mean": "Mean Imputation", "median": "Median Imputation", "mode": "Mode Imputation", "sequential": "Sequential Recoding", "range": "Range Recoding", "missing_values": "Missing Values"}, "variable": "Variable", "missing_warning": "contains missing values.", "measure_type": "Measure Type", "header": "<PERSON>umn <PERSON>", "show_header": "Header", "show_label": "Label", "total": "Total", "select_all": "Select all", "deselect_all": "Deselect all", "no_variables_found": "No variable found", "try_different_filters": "Try different filters", "filters": {"import_status": "Import Status", "measure_type": "Measure Type", "validation_status": "Validation Status", "missing_values": "Missing Values Status", "import": {"all": "All Import Status", "imported": "Imported", "not_imported": "Not Imported"}, "measure": {"all": "All Types", "scale": "Scale", "ordinal": "Ordinal", "nominal": "Nominal"}, "validation": {"all": "All Validation", "valid": "<PERSON><PERSON>", "warning": "With Warnings", "error": "With Errors"}, "missing": {"all": "All Missing Status", "with_missing": "With Missing", "no_missing": "No Missing"}, "reset": "Reset Filters"}, "saving": {"title": "Saving Changes", "message": "Please wait while your changes are being saved...", "progress": "Saving dataset...", "success_title": "Success", "error_title": "Error", "save_error": "An error occurred while saving changes"}, "table": {"import": "IMPORT", "name": "NAME", "type": "TYPE", "label_en": "LABEL (EN)", "label_tr": "LABEL (TR)", "values": "VALUES", "missing": "MISSING", "status": "STATUS"}, "error": {"title": "Error", "invalid_value": "Invalid value"}, "import": {"title": "Import Dataset", "content": "This will replace your current dataset. Are you sure you want to continue?", "confirm": "Import", "cancel": "Cancel", "success": "Dataset imported successfully", "error": "Error importing dataset", "with_validation": "Dataset imported with validation errors. Please check validation details.", "view_details": "View Details"}, "value_labels": {"add_values": "Add values", "values_count": "{{ count }} values", "value": "Value", "unsaved_changes": "Unsaved Changes", "no_available_labels": "No available value labels found", "label_en": "Label (EN)", "label_tr": "Label (TR)", "use_existing": "Use Existing Labels", "save_close": "Save & Close", "updated": "Value labels updated successfully"}, "missing": {"count": "{{count}} missing", "zero": "No missing values", "high": "High missing count"}, "status": {"valid": "<PERSON><PERSON>", "warning": "Warning", "error": "Error"}, "actions": {"import_excel": "Import from Excel", "export_excel": "Export to Excel", "reset_changes": "Reset Changes", "save_all": "Save All Changes", "close": "Close"}, "messages": {"changes_saved": "Changes have been saved successfully", "save_error": "An error occurred while saving changes", "measure_updated": "Measure type updated successfully", "invalid_labels": "Invalid labels. Please check the values", "invalid_value_labels": "Invalid value labels. Please check the values", "validation_warning_imported": "{{count}} variables have validation errors", "export_success": "Dataset exported successfully", "export_error": "Error exporting dataset", "data_updated": "Data updated successfully", "validation_issues": "Validation issues found", "all_selected": "All variables selected", "all_deselected": "All variables deselected"}, "confirm": {"changes": {"title": "Unsaved Changes", "content": "You have unsaved changes. Are you sure you want to continue?", "confirm": "Close it anyway", "cancel": "Cancel"}, "reset": {"title": "Reset Changes", "content": "This will reset all changes. Are you sure?", "confirm": "Reset", "cancel": "Cancel"}, "unsaved_labels": {"title": "Unsaved Changes", "content": "You have unsaved changes to value labels. Do you want to save them?", "save": "Save", "discard": "Discard"}}, "context_menu": {"compute_options": "Compute Options", "auto_update": "Auto Update", "source_variables": "Source Variables", "remove_row": "Remove Row", "read_only": "Read Only"}, "dropdown_menu": {"read_only": "Read Only", "compute_options": "Compute Options", "auto_update": "Auto Update", "source_variables": "Source Variables"}}, "agreement": {"title": "Terms and Conditions", "content": " ", "read_agreement": "I have read and accept the Pre-Information Terms and Distance Sales Agreement", "accept": "Accept"}, "auto_recoding": {"no_variables_available": "No variables available", "name_output_variable": "Name your output variable", "title": "Recode Values", "source_variables": "Source Variables", "used_in_variables": "Used in Variables", "select_variables": "Select variables...", "search_variables": "Search variables...", "select_all": "Select All", "deselect_all": "Deselect All", "variables_selected": "Variables Selected", "output_variables": "Output Variables", "variable_name": "Name:", "select_variables_first": "Please select variables first", "turkish_label": "Turkish Label:", "english_label": "English Label:", "recoding_options": "Recoding Options", "sequential_recoding": "Sequential Recoding", "sequential_recoding_desc": "Assign numbers to values sequentially, ascending or descending.", "start_from": "Start From", "sequential": "Sequential", "ascending": "Ascending", "descending": "Descending", "range_recoding": "Range Recoding", "recode_range": "Recode Range", "from": "From", "to": "To", "start_value": "Start Value", "end_value": "End Value", "as": "As", "equals": "Equals", "values_above": "Values Above", "values_below": "Values Below", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "search_value_mapping": "Search value...", "new_value": "New value", "missing": "Missing", "missing_values": "Missing Values", "enter_variable_name": "Enter variable name", "enter_turkish_label": "Enter Turkish label", "enter_english_label": "Enter English label", "preserve_original_missing_values": "Preserve original missing values", "preserve_original_missing_values_description": "Instead of changing the missing value, protect the original missing values", "system_missing_value": "System Missing Value", "apply_to_missing_values": "Apply to Missing Values", "value_mapping_preview": "Preview", "total": "Total", "apply": "Apply Changes", "search": "Search value...", "old_value": "Old Value", "footer": {"info": "Changes will be saved as a new variable", "reset_all": "Reset All", "apply": "Apply Changes"}, "warnings": {"enter_system_missing": "Please enter a value", "enter_through_values": "Please enter all through values", "start_less_than_end": "Start value must be less than or equal to end value", "enter_threshold_new_value": "Please enter both threshold and new value", "enter_missing_value": "Please enter a missing value", "value_already_missing": "This value is already marked as missing"}, "errors": {"variable_name_exists": "Variable name already exists"}, "success": {"changes_reset": "All changes have been reset", "changes_applied": "Changes have been applied successfully"}, "changes_reset": "All changes have been reset", "create_variable": "Create Variable", "next": "Next", "back": "Back", "range_recoding_desc": "Encode values by dividing them into specific ranges.", "missing_recoding_desc": "Analyze missing values.", "select_variables_auto_recode": "Select the variable(s) you want to use in the recoding process.", "select_option_auto_recode": "Select the encoding method you want to apply.", "selected_variables": "Selected Variables", "and": "and", "more": "more", "close": "Close", "name_output_variables": "Name your output variables", "change_missing_value": "Change missing values", "change_missing_value_description": "Update the missing values ​​with the new value", "select_variables_to_group": "Select the variables you want to group", "grouping_options": "Grouping Options", "values": "Values", "group_variables": "Group Variables", "apply_grouping": "Apply Grouping", "edit": "Edit", "equal_to": "equal to the value", "values_between": "values between", "greater": "values greater than", "less": "values less than"}, "compute_variable": {"title": "Compute Variable", "source_variable": "Source Variable", "select_variable": "Select variable...", "search_variables": "Search variables...", "output_variable": "Output Variable", "variable_name": "Name:", "turkish_label": "Turkish Label:", "english_label": "English Label:", "operation_select": "Select Operation", "select_operation": "Select an operation...", "available_variables": "Available Variables", "enter_variable_name": "Enter variable name", "enter_turkish_label": "Enter Turkish label", "enter_english_label": "Enter English label", "no_operation": "Please select an operation first", "no_compatible": "No compatible variables for this operation", "no_results": "No variables found matching search", "compute_explanation": "Selected Variables", "preview": {"title": "Preview", "showing": "Showing first 5 rows", "row": "Row", "original": "Original Value", "result": "Result", "no_data": "No data available for preview", "calculation_error": "An error occurred while calculating preview"}, "footer": {"info": "Changes will be saved as a new variable", "preview": "Preview", "create": "Create Variable"}, "operations": {"add": {"name": "Addition", "description": "Adds the values of selected variables"}, "subtract": {"name": "Subtraction", "description": "Subtracts the values of selected variables in order"}, "multiply": {"name": "Multiplication", "description": "Multiplies the values of selected variables"}, "divide": {"name": "Division", "description": "Divides the selected variables in order"}, "mean": {"name": "Mean", "description": "Calculates the average of selected variables"}, "log": {"name": "Logarithm", "description": "Calculates base-10 logarithm"}, "ln": {"name": "Natural Logarithm", "description": "Calculates natural logarithm (base e)"}, "square": {"name": "Square", "description": "Calculates the square of the value"}, "sqrt": {"name": "Square Root", "description": "Calculates the square root of the value"}, "concat": {"name": "Text Concatenation", "description": "Combines the text values of selected variables"}}, "messages": {"select_variables": "Please select at least one variable", "max_variables": "Maximum {{count}} variables can be selected for this operation", "same_type": "Selected variables must be of the same type", "variable_name_required": "Variable name is required", "variable_exists": "A variable with this name already exists", "add": "Add values of variables: {{variables}}", "subtract": "Subtract in order: {{variables}}", "multiply": "Multiply values of variables: {{variables}}", "divide": "Divide {{variable1}} by {{variable2}}", "mean": "Calculate the average of variables: {{variables}}", "log": "Calculate base-10 logarithm of {{variables}}", "ln": "Calculate natural logarithm of {{variables}}", "square": "Calculate square of {{variables}}", "sqrt": "Calculate square root of {{variables}}", "concat": "Combine text values of variables: {{variables}}", "cannot_select_all": "Cannot select all variables due to operation constraints", "division_operation_limit": "Division operation can only use 2 variables. Please select variables again.", "maxVariables": "A maximum of {{max}} variables can be selected for this operation", "sameType": "Selected variables must be of the same type", "variable_created": "New variable created successfully", "compute_error": "Error computing new variable", "invalid_variable_type": "This variable type is not compatible with the selected operation", "requires_numeric": "This operation requires numeric variables", "contains_text_values": "This variable contains text values and cannot be used for numeric operations", "contains_mixed_values": "This variable contains both numeric and text values"}, "validations": {"select_operation": "Please select an operation", "select_variables": "Please select at least one variable", "division_two_variables": "Division operation requires exactly two variables", "provide_name": "Please provide a name for the new variable", "name_exists": "A variable with this name already exists", "provide_all_names": "Please provide names for all transformation variables", "unique_names": "Variable names must be unique"}, "success": {"variable_created": "New variable created successfully"}, "errors": {"creation_failed": "Failed to create variable", "compute_error": "Computation error", "unsupported_operation": "Unsupported operation", "computation_failed": "An error occurred while computing the new variable", "variable_creation_failed": "An error occurred while creating the variable"}, "clear_selection": "Clear Selection", "new_name": "New variable name", "computed_badge": "Computed", "transformation_names": {"title": "New Variables", "new_name": "New name for {{variable}}"}, "select_variable_for_compute": "Select the variable you want to compute", "select_option": "Select the variables you want to perform the operation on", "name_output_variable": "Name your output variable", "and": "and", "more": "more", "enter_necessary_infos": "Enter the necessary information for the variable that will be created as a result of the operation.", "variables_selected": "Selected Variables", "close": "Close"}, "missing_value": {"header": "Missing Data Analysis", "total_variables": "Total Variables", "variables_with_missing": "Variables with Missing", "overall_missing_rate": "Overall Missing Rate", "variable": "Variable", "type": "Type", "missing_count": "Missing Count", "missing_percentage": "Missing %", "pattern": "Pattern", "actions": "Actions", "fix_missing_values": "Fix Missing Values", "no_missing_values": "No missing values found.", "close": "Close", "back": "Back"}, "imputation_modal": {"header": "Fix Missing Values", "variable": "Variable", "type": "Type", "missing_values": "Missing Values", "variable_name": "Variable Name", "variable_name_placeholder": "Enter new variable name", "label_en": "Label (EN)", "label_tr": "Label (TR)", "preview": "Preview", "showing_first_5": "Showing first 5 changes", "row": "Row", "original_value": "Original Value", "new_value": "New Value", "missing": "Missing", "preview_button": "Preview", "create_variable": "Create Variable", "select_method_warning": "Please select a method.", "form_invalid_error": "Please fill out the form correctly.", "imputation_success": "Missing values imputed successfully.", "imputation_error": "An error occurred while imputing missing values.", "no_applicable_methods_title": "No Available Imputation Methods", "no_applicable_methods_description": "We couldn't find any suitable imputation methods for the variable '{{variable}}' ({{type}}).", "no_applicable_methods_hint_1": "For numeric data, make sure all non-missing values can be converted to numbers", "no_applicable_methods_hint_2": "Check if the variable type (Scale/Nominal/Ordinal) matches your data", "methods": {"mean": {"name": "Mean Imputation", "description": "Replace missing values with the mean of the variable"}, "median": {"name": "Median Imputation", "description": "Replace missing values with the median value"}, "mode": {"name": "Mode Imputation", "description": "Replace missing values with the most frequent value"}}, "next": "Next", "back": "Back"}, "referral": {"title": "Referral Program", "referral_program": "Referral Program", "your_referral_code": "Your Referral Code", "copy": "Copy", "share_referral_code": "Share Referral Code", "share_via_whatsapp": "Share via Whatsapp", "share_via_email": "Share via Email", "total_referrals": "Total Referrals", "earned_credit": "Earned Credit", "use_referral_code": "Use Referral Code", "referral_code_used": "Referral Code Used", "user_used_referral_code": "You used the referral code of this user.", "bonus_info": "The referral bonus will be activated when you complete your first purchase. Since you haven't made your first purchase yet, you can change or remove the referral code if you wish.", "bonus_terms_info": "What are the bonus credit earning terms?", "use_different_code": "Use a different code", "remove_code": "Remove Code", "enter_referral_code": "Enter referral code", "change_code": "Change Code", "apply_code": "Apply Code", "cancel": "Cancel", "self_referral_error": "You cannot use your own referral code", "invalid_referral_code_error": "Invalid referral code. Please enter a valid code.", "copied": "<PERSON>pied", "referral_code_usage_info": "You can use a referral code only once. When the code is used, you earn bonus credit on your first purchase.", "previous_purchase_info": "You cannot use a referral code because you have made a purchase before.", "referral_history": "Referral History", "referrals": "referrals", "user": "User", "date": "Date", "earned_credits": "Earned Credits", "no_referrals_yet": "You don't have any referrals yet", "share_to_earn": "Share your code to start earning credits!", "bonus_terms_title": "Bonus Credit Earning Terms", "referral_bonus_info": "When you invite your friends using your referral code, both you and your friend earn bonus credits.", "credit_earning_table": "Credit Earning Table", "purchased_credit": "Purchased Credit", "referrer_earnings": "<PERSON><PERSON><PERSON>s", "referred_earnings": "Referred User Earnings", "table_note": "The values in the table vary according to the amount of credit purchased by the referred user in their first purchase.", "referrer_title": "Referring User (You)", "referred_title": "Referred User (Your Friend)", "referrer_info_1": "You earn bonuses based on the amount of credit your referred user purchases.", "referrer_info_2": "Your friend needs to complete their first purchase.", "referrer_info_3": "Earned credits are automatically loaded into your account.", "referrer_info_4": "They won't be visible until your friend completes their purchase.", "referred_info_1": "If your friend uses your referral code before making their first purchase, they earn a bonus based on the amount of credit they purchase.", "referred_info_2": "Credits are automatically loaded into their account after the first purchase.", "referred_info_3": "The minimum purchase amount to earn bonus credit under the Referral Program is 100 credits.", "additional_info": "Additional Information", "additional_info_1": "To use someone else's referral code, the account must not have made any purchases before.", "additional_info_2": "A person can use only one referral code.", "additional_info_3": "If the person using the referral code has not yet made their first purchase, they can change the code.", "understood": "I Understand"}, "title": "istabot | New Era of Statistical Data Analysis"}, "cookie": {"text": "We use essential cookies to improve your experience and analyze website usage. Your data helps us provide better service.", "learn_more": "Privacy Policy", "accept": "Accept"}, "validation": {"file_type": "Only {{allowed}} file types are accepted", "file_size": "File size cannot exceed {{maxSize}}", "empty_dataset": "Dataset cannot be empty", "max_rows": "Number of rows ({{current}}) exceeds maximum limit ({{max}})", "min_rows": "Dataset must contain at least 3 rows (including header)", "max_columns": "Number of columns ({{current}}) exceeds maximum limit ({{max}})", "min_columns": "Dataset must contain at least one column", "invalid_header": "Invalid header row", "empty_headers": "Column headers cannot be empty", "duplicate_headers": "Duplicate column headers are not allowed", "inconsistent_rows": "{{count}} rows have inconsistent column counts"}, "navigation": {"home": "Home", "projects": "Projects", "projects_short": "Projects", "reports": "Reports", "reports_short": "Reports", "members": "Members", "project": {"overview": "Overview", "overview_short": "Overview", "dataset": "Dataset", "dataset_short": "Dataset", "analysis": "Analysis", "analysis_short": "Analysis", "settings": "Settings", "settings_short": "Settings", "history": "Analysis History", "history_short": "History", "members_short": "Members"}}, "corporate-management": {"pagination": {"showing": "Showing", "of": "of"}, "unit_type": {"consultancy": "Consultancy", "academic": "Academic", "goverment": "Government", "journal": "Journal", "survey": "Survey", "healthcare": "Healthcare", "other": "Other"}, "unit_selection": {"title": "Unit Selection", "subtitle": "Select a unit for corporate management operations", "change_unit": "Change Unit", "users": "Users", "managers": "Managers", "select": "Select", "no_units": "No units found", "no_units_desc": "Contact your administrator to add units"}, "title": "Corporate Management", "company_name": "{{ name }} Company", "tabs": {"users": "Users", "projects": "Projects", "activities": "Activities"}, "credit_info": {"title": "Corporate Credit Information", "total": "Total Credit", "used": "Used Credit", "remaining": "Remaining Credit", "credits": "Credit", "transferred": "Transferred", "unused": "Unused", "help_text": "You can view credit information for this unit"}, "projects": {"title": "Projects", "search_placeholder": "Search projects...", "filter_label": "Status:", "clear_filters": "Clear Filters", "no_results": "No projects found matching the search criteria.", "unknown_owner": "Unknown User", "table": {"name": "Project Name", "owner": "Owner", "status": "Status", "actions": "Actions"}, "status": {"all": "All Projects", "paid": "Paid", "unpaid": "Unpaid"}, "actions": {"mark_paid": "<PERSON> as <PERSON><PERSON>", "mark_unpaid": "<PERSON> as Unpaid"}}, "activities": {"title": "Activities", "search_placeholder": "Search activities...", "filter_type_label": "Type:", "filter_status_label": "Status:", "clear_filters": "Clear Filters", "no_results": "No activities found matching the search criteria.", "loading": "Loading...", "by_manager": "Manager", "table": {"date": "Date", "action": "Action", "status": "Status", "user": "User", "credits": "Credits", "reason": "Reason"}, "type": {"all": "All Types", "credit_send": "Credit Send", "credit_refund": "Credit Refund", "user_add": "User Add", "user_remove": "User Remove"}, "status": {"all": "All Statuses", "done": "Completed", "cancelled": "Cancelled", "pending": "Pending"}}, "search": {"placeholder": "Search by email...", "filter_label": "Status:", "clear_filters": "Clear Filters", "no_results": "No users found matching the search criteria."}, "user_list": {"title": "Corporate Users", "add_button": "Add User", "import_button": "Import from Excel"}, "table": {"email": "Email", "status": "Status", "role": "Role", "credit_info": "Credit Information", "actions": "Actions"}, "credit_details": {"total": "Total:", "used": "Used:", "remaining": "Remaining:", "not_registered_message": "Credit information can be viewed after the user registers.", "not_confirmed_message": "Credit information can be viewed after the user confirms their account."}, "actions": {"add_credit": "Give Credit", "remove_credit": "Take Credit", "make_admin": "Make Admin", "make_user": "Make User", "delete_user": "Delete User", "inactive_credit_tooltip": "Credit operations cannot be performed for inactive users", "inactive_role_tooltip": "Role cannot be changed for inactive users", "cannot_delete_user_with_credits": "Users with credits cannot be deleted", "inactive_user_credit": "Active registration is required for credit operations", "no_unit_credits": "The department has no credits to assign", "no_user_credits": "The user has no credits to take", "give_credit": "Give credit to user"}, "status": {"registered": "Account Active", "not_registered": "Not Registered", "not_confirmed": "Account Not Confirmed", "all": "All Users", "active": "Active Accounts", "user_registered": "Registered User", "user_not_registered": "Unregistered User", "user_not_confirmed": "Unconfirmed User", "user_removed": "User Removed"}, "role": {"admin": "Admin", "user": "User"}, "import_excel": {"title": "Import Users from Excel", "description": "Upload an Excel file to add multiple users.", "drag_drop": "Drag and drop file or browse", "file_types": "Excel files (.xlsx, .xls) or CSV", "browse_files": "Select File", "download_template": "Download Template", "preview": "Preview", "email": "Email", "reason": "Reason", "status": "Status", "valid": "<PERSON><PERSON>", "invalid_file_type": "Invalid file type. Please upload an Excel or CSV file.", "missing_email": "Missing email address", "invalid_email": "Invalid email address", "email_exists": "Email address already exists", "duplicate_email": "Duplicate email in upload list", "total_records": "Total Records", "valid_records": "Valid Records", "invalid_records": "Invalid Records", "fix_errors": "Please fix errors and try again", "no_valid_users": "No valid users to import", "importing": "Importing...", "insufficient_credits": "Insufficient credits. You have {{available}} credits, requires {{required}} credits", "import": "Start Import", "cancel": "Cancel", "close": "Close", "total": "Total", "success": "Success", "failed": "Failed", "results": "Import Results", "import_complete": "Import completed. Total: {{total}}, Success: {{success}}, Failed: {{failed}}"}, "add_user": {"title": "Add User", "email_label": "Email Address:", "email_placeholder": "<EMAIL>", "initial_credit_label": "Initial Credit", "info_text": "An invitation will be sent to the user.", "cancel": "Cancel", "add": "Add", "reason_label": "Reason:", "reason_placeholder": "Specify a reason for adding the user"}, "credit_dialog": {"give_title": "Give Credit", "take_title": "Take Credit", "user_prompt": "Specify amount for user {{ email }}:", "amount_label": "Credit Amount:", "remaining_credits": "User's remaining credit:", "take_all": "Take All Credits ({{ amount }})", "cancel": "Cancel", "confirm": "Confirm", "reason_label": "Reason:", "reason_placeholder": "Specify a reason for the credit operation"}, "notifications": {"invalid_email": "Please enter a valid email address", "email_exists": "This email address is already registered", "user_added": "User {{ email }} successfully added", "credit_given": "{{ amount }} credits given to user {{ email }}", "credit_taken": "{{ amount }} credits taken from user {{ email }}", "insufficient_credits": "User does not have sufficient credits. Available credit: {{ amount }}", "user_deleted": "User {{ email }} deleted", "role_changed": "User {{ email }}'s role changed to \"{{ role }}\"", "inactive_user": "Credit operation cannot be performed as user {{ email }} has not activated their account.", "inactive_user_role": "Role change cannot be performed as user {{ email }} has not activated their account.", "cannot_delete_with_credits": "User {{ email }} cannot be deleted. {{ credits }} credits assigned to the user.", "cannot_delete_admin": "Admin user {{email}} cannot be deleted. Please demote the user to a regular user role first."}, "confirmations": {"add_user": "Are you sure you want to add user {{ email }}?", "add_user_content": "The user will be created with 0 credits initially. You can add credits later.", "delete_user": "Are you sure you want to delete user {{ email }}?", "delete_user_content": "This action cannot be undone.", "give_credit": "Are you sure you want to give {{ amount }} credits to user {{ email }}?", "take_credit": "Are you sure you want to take {{ amount }} credits from user {{ email }}?", "change_role_admin": "Are you sure you want to change the role of user {{ email }} to \"admin\"?", "change_role_admin_content": "Admin role provides access to the corporate management panel.", "change_role_user": "Are you sure you want to change the role of user {{ email }} to \"user\"?", "yes": "Yes", "no": "No", "delete": "Delete", "cancel": "Cancel", "add": "Add"}}, "careers": {"title": "Join Our Team", "subtitle": "We're looking for talented individuals to help us build the future of statistical analysis tools.", "back_to_home": "Back to Home", "open_positions": "Open Positions", "positions_subtitle": "Check out our current job openings and find the perfect fit for your skills and interests.", "view_details": "View Details", "hide_details": "Hide Details", "apply_now": "Apply Now", "description": "Description", "responsibilities": "Responsibilities", "requirements": "Requirements", "apply_for_position": "Apply for this Position", "no_match_title": "Don't see a position that matches your skills?", "no_match_description": "We're always looking for talented people to join our team. Send us your resume and we'll keep it on file for future opportunities.", "contact_us": "Contact Our Hiring Team", "jobs": {"angular_frontend": {"id": "angular-frontend", "title": "Angular Frontend Developer", "location": "Atakum / Samsun", "type": "Full-time", "description": "Join our dynamic team as an Angular Frontend Developer and help build cutting-edge statistical analysis tools for researchers worldwide.", "responsibilities": ["Develop responsive and interactive UIs using Angular", "Collaborate with UX designers to implement user-friendly interfaces", "Work with RESTful APIs and backend services", "Participate in code reviews and maintain code quality", "Implement new features and improve existing ones", "Optimize applications for maximum speed and scalability"], "requirements": ["2+ years of experience with <PERSON><PERSON> (version 10+)", "Strong knowledge of TypeScript, HTML5, and CSS3/SCSS", "Experience with responsive design and cross-browser compatibility", "Familiarity with state management solutions (NgRx, RxJS)", "Understanding of RESTful APIs and HTTP protocols", "Knowledge of version control systems (Git)", "Good problem-solving skills and attention to detail", "Ability to work in a team environment", "Nice-to-have: Experience with UI libraries like Angular Material, TailwindCSS", "Nice-to-have: Knowledge of testing frameworks (<PERSON>, <PERSON><PERSON>)"]}, "ruby_on_rails": {"id": "ruby-on-rails", "title": "Ruby on Rails Developer", "location": "Atakum / Samsun", "type": "Full-time", "description": "We are seeking a Ruby on Rails developer to join our team, supporting ongoing projects through improving existing codebases and developing new products. You will contribute directly to the growth and enhancement of our platform, focusing specifically on API-only backend services.", "responsibilities": ["Develop, test, and maintain robust, efficient, and scalable Ruby on Rails applications", "Collaborate effectively with the product team to implement new backend features", "Ensure high code quality through best practices, performance optimization, and code reviews"], "requirements": ["Minimum 1 year of experience in Ruby on Rails development", "Strong understanding of Ruby programming language and best practices", "Familiarity with Ruby Gems and related Rails libraries", "Solid foundation in Object-Oriented Programming (OOP), data structures, and algorithms", "Practical knowledge of PostgreSQL databases", "Good understanding of RESTful API principles and overall API architecture", "Experience with Git version control systems (e.g., Git, GitHub, GitLens, Gist)", "Ability to closely collaborate with product management and other development team members", "Commitment to writing maintainable, optimized, and high-quality code", "Nice-to-have: Good communication skills in English", "Nice-to-have: Experience with Docker, AWS, and CI/CD pipelines"]}, "r_junior": {"id": "r-junior", "title": "<PERSON> <PERSON>", "location": "Atakum / Samsun", "type": "Full-time", "description": "We are looking for a dedicated R developer to join our data science team. The ideal candidate will play a critical role in developing statistical analysis algorithms and maintaining robust analytical solutions for our platform.", "responsibilities": ["Collaborate effectively with the product team to implement new statistical features", "Develop, test, and maintain statistical analysis modules using R", "Ensure code quality through rigorous performance optimization and best coding practices"], "requirements": ["Bachelor's degree in Statistics, Mathematics, Computer Science, or related field", "Proficiency in R programming language and statistical analysis", "Understanding of statistical concepts and methodologies", "Experience with data manipulation and visualization in R", "Familiarity with R packages for statistical analysis (e.g., dplyr, ggplot2, tidyr)", "Basic knowledge of version control systems (Git)", "Ability to work in a collaborative team environment", "Strong problem-solving skills and attention to detail", "Nice-to-have: Experience with <PERSON> Markdown or <PERSON><PERSON>", "Nice-to-have: Knowledge of other programming languages (Python, SQL)"]}}}, "explore": {"title": "Explore", "beta": "Beta", "publish": "Publish", "loading": "Loading...", "tabs": {"callForPapers": "Call for Papers", "featured": "Featured", "recent": "Recent Publications", "researchers": "Researchers", "interdisciplinary": "Interdisciplinary", "openAccess": "Open Access"}, "search": {"placeholder": "Type what you want to search...", "filters": "Filters", "advanced": {"discipline": {"label": "Discipline", "all": "All Disciplines"}, "year": {"label": "Year", "all": "All Years"}, "language": {"label": "Language", "all": "All Languages"}, "access": {"label": "Access Type", "all": "All Types"}, "contentType": {"label": "Content Type", "all": "All Content"}, "sort": {"label": "Sort by", "relevance": "Relevance", "date": "Date", "citations": "Citations", "impact": "Impact"}, "clearFilters": "Clear Filters"}}, "urgentCfp": {"title": "<PERSON>rgent Call for Papers", "days": "days", "viewAll": "View All", "dismiss": "<PERSON><PERSON><PERSON>"}, "cfp": {"submissionDeadline": "Submission Deadline", "notificationDue": "Notification Due", "daysLeft": "days left", "wikiCfp": "Wiki CFP", "eventWebsite": "Event Website"}, "content": {"addToCollection": "Add to Collection", "addToFavorites": "Add to Favorites", "otherAuthors": "other authors", "citations": "citations", "downloads": "downloads", "impactFactor": "Impact Factor", "openAccess": "Open Access"}, "empty": {"title": "No results found", "description": "No results found for your search. Please try different keywords.", "clearFilters": "Clear Filters"}, "disciplines": {"computerScience": "Computer Science", "engineering": "Engineering", "medicine": "Medicine", "physics": "Physics", "chemistry": "Chemistry", "biology": "Biology", "mathematics": "Mathematics", "psychology": "Psychology", "sociology": "Sociology", "economics": "Economics"}, "languages": {"turkish": "Turkish", "english": "English", "german": "German", "french": "French", "spanish": "Spanish"}, "accessTypes": {"openAccess": "Open Access", "subscription": "Subscription", "hybrid": "Hybrid"}, "contentTypes": {"researchPaper": "Research Paper", "researcher": "Researcher", "institution": "Institution", "project": "Project", "dataset": "Dataset"}}}
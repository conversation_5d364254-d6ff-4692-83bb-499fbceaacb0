{"istabot": "istabot | İstatistiksel Veri Analizinin Yeni Çağı", "landing": {"analysis": {"descriptive": "Tanımlayıcı", "independent": "Bağımsız", "dependent": "Bağımlı", "correlation": "Korelasyon", "chisq": "<PERSON><PERSON><PERSON><PERSON>", "multi": "Çok Grup"}, "cta": {"brand": "istabot", "description1": "<PERSON><PERSON><PERSON><PERSON> hiç bu kadar kolay olmamıştı!", "description2": "Verilerinizi içgörülere dönüştürün", "description3": "Araştırmanızı hızlandırın", "description4": "İş akışınızı geliştirin", "description5": "<PERSON><PERSON>z süreci artık zahmetsiz!", "cta": "Şimdi Başla!"}, "howto": {"title": {"main": "Nasıl <PERSON>alışır", "sub": "istabot'un dört adımda istatistiksel analiz yaklaşımını keşfedin."}, "step1": "<PERSON>je <PERSON> ve Veri <PERSON>ini <PERSON>", "step2": "<PERSON><PERSON>", "step3": "Gelişmiş <PERSON>zle<PERSON>", "step4": "Kapsamlı Raporlar Oluşturun"}, "how": {"step": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>ımda Profesyonel Analiz", "description": "Teknik bilgi g<PERSON>, dakikalar içinde sonuca ul<PERSON>n", "steps": [{"id": 1, "icon": "lucideUpload", "title": "<PERSON><PERSON>", "description": "Excel dosyanızı sürükle-bırak yapın veya bilgisayarınızdan seçin."}, {"id": 2, "icon": "lucideFileSpreadsheet", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Korelasyon gibi analizlerinden birini seçin."}, {"id": 3, "icon": "lucideBarcode", "title": "Sonuçları Gör", "description": "Sonuçları tablolar ve yorumlar olarak inceleyin. \n"}, {"id": 4, "icon": "lucide<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "description": "APA formatında hazır Word raporunuzu tek tıkla indirin."}]}, "usecases": {"title": {"main": "Kullanım Senaryoları", "sub": "istabot'un çeşitli alanlardaki çalışmalarınızı nasıl geliştirebileceğini keşfedin."}, "case1": {"title": "Akademik Araştırma", "description": "<PERSON><PERSON><PERSON>, di<PERSON> hekimliği, sağlık bilimleri ve diğer disiplinlerdeki bilimsel makaleler için istatistiksel veri analizinizi kolaylaştırın."}, "case2": {"title": "Lisansüstü Çalışmalar", "description": "Uzman<PERSON><PERSON>k tezi, doktora tezi ve yüksek lisans tezi için gerekli veri analizlerini zahmetsizce gerçekleştirin. istabot ile tez çalışmalarınızı destekleyin."}, "case3": {"title": "Profesyonel Yöneticiler ve Araştırmacılar", "description": "Profesyonel yöneticiler ve araştırmacılar için güçlü veri analizi araçları. İstatistik bilimini kullanarak daha iyi kararlar alın."}, "case4": {"title": "<PERSON><PERSON>", "description": "İster uzman ister yeni ba<PERSON> olun, istabot'un sezgisel tasarımı güçlü istatistiksel analizi erişilebilir kılar."}}, "services": {"title": {"main": "Hizmetlerimiz", "sub": "istabot'u vazgeçilmez analiz aracınız yapan temel özellikleri keşfedin."}, "service1": {"title": "Sorun<PERSON>z Veri Entegrasyonu", "description": "Verilerinizi kolayca yükleyin ve Excel kolaylığında işleyin."}, "service2": {"title": "Akıllı Veri Ön İşleme", "description": "Veri setlerinizi analiz öncesi hazırlayın."}, "service3": {"title": "<PERSON><PERSON> Düze<PERSON>", "analysis1": "Tanımlayıcı İstatistik Analizi", "analysis2": "Bağımsız Tek Grup Veri Analizi", "analysis3": "Bağımsız Çok Grup Veri Analizi", "analysis4": "Bağımlı Veri Analizi", "analysis5": "Korelas<PERSON>", "analysis6": "<PERSON><PERSON><PERSON><PERSON>"}, "service4": {"title": "Profes<PERSON><PERSON>", "description": "APA formatında, Word formatında, çok dilli destekle raporlar oluşturun."}}, "footer": {"brand": "istabot", "description": "İstatistiksel Veri Analizinin Yeni Çağı", "developed_by": "E-İstatistik tarafından geliştirilmiştir", "rights": "© istabot 2024. Tüm hakları saklıdır.", "hiring": "<PERSON><PERSON><PERSON>", "quick_links": {"title": "Hızlı Erişim", "links": [{"title": "<PERSON><PERSON><PERSON>", "href": "#homepage"}, {"title": "<PERSON><PERSON><PERSON>", "href": "#services"}, {"title": "Nasıl <PERSON>alışır", "href": "#howto"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "href": "#usecases"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "#comparison"}, {"title": "İletişim", "href": "#contact"}]}, "contact": {"title": "İletişim", "address": "Körfez Mahallesi 19 Mayıs Kümesi Küme Evleri No:188-14 Atakum Samsun", "phone1": "+90-(850)-885 12 56", "phone2": "WhatsApp: +90-(538)-615 0 444"}, "legal": {"privacy_policy": "Gizlilik Politikası", "terms_of_use": "Kullanım Şartları"}, "footer": {"copyright": "2025 İstabot. Tüm hakları saklıdır.", "quick_links": "Hızlı Bağlantılar", "home": "<PERSON>", "support": "Destek", "contact": "İletişim", "privacy_policy": "Gizlilik Politikası", "terms_of_use": "Kullanım Koşulları", "gdpr": "KVKK", "free_demo": "Ücretsiz Demo", "tutorial_videos": "Eğitim Videoları", "api_docs": "API Dokümantasyonu", "support_request": "<PERSON><PERSON><PERSON>", "company_description": "<PERSON><PERSON><PERSON>, akademik çalışmaları ve araştırmaları hızlandıran profesyonel bir istatistik analiz platformudur.", "developed_by": "eistatistik tarafından geliştirilmiştir.", "address": {"line1": "Körfez Mahallesi 19 Mayıs Kümesi Küme Evleri No:188-14", "line2": "<PERSON><PERSON><PERSON>"}, "faq": "Sık Sorulan Sorular"}, "back_to_top": "Yukarı Çık"}, "navbar": {"brand": "istabot", "partner": "eistatistik.com tarafından geliştirilmiştir.", "link1": "<PERSON><PERSON><PERSON>", "link2": "Hizmetlerimiz", "link3": "Nasıl İşliyor?", "link4": "<PERSON><PERSON><PERSON><PERSON>", "link5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link6": "İletişim", "features": "<PERSON><PERSON><PERSON><PERSON>", "how_it_works": "Nasıl <PERSON>alışır", "use_cases": "<PERSON><PERSON><PERSON><PERSON>", "comparison": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pricing": "Fiya<PERSON><PERSON>rma", "faq": "SSS", "buttons": {"login": "<PERSON><PERSON><PERSON>", "signup": "<PERSON><PERSON><PERSON>", "dashboard": "Portala Git"}, "start_now": "<PERSON><PERSON>"}, "homepage": {"title": {"highlight": "İstatistiksel Veri Analizinin", "main": "Yeni Çağı"}, "description": "Karmaşık istatistiksel analizleri saniyeler içinde tamamlayın.", "cta": {"text": "<PERSON><PERSON>"}, "demo": {"text": "<PERSON><PERSON><PERSON>"}}, "comparison": {"brand": "istabot", "title": {"main": "Neden istabot'u Seçmelisiniz", "sub": "istabot'un istatistiksel analiz yazılımları arasında nasıl öne çıktığını görün."}, "table": {"header1": "Teknik bilgi gerektirmez, hızlı ve kolay analiz", "header2": "APA formatında, Word dışa aktarma ve çok dilli destekle yayına hazır raporlar", "header3": "Optimal kullanıcı deneyimi için sezgisel tasarım", "header4": "Kapsamlı Analiz <PERSON>", "header5": "<PERSON>or <PERSON>atı"}}, "hero": {"description": "Akademik çalışmalarınızı daha ileriye taşımak için kapsamlı istatistiksel analizler ve profesyonel raporlama sistemlerinden faydalanın.", "start": "<PERSON><PERSON>", "video": "Tanıtım Videosu", "title": {"line1": "Profesyonel İstatistiksel", "line2": "<PERSON><PERSON><PERSON>"}, "buttons": {"demo_video": "Tanıtım Videosu", "start_analysis": "<PERSON><PERSON><PERSON>"}}, "features": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Profesyonel analizler için i<PERSON>ı<PERSON>ız olan <PERSON>ey", "services": [{"title": "Hızlı Analiz", "description": "Verilerinizi yükleyin ve sonuçları saniyeler içinde alın", "features": ["Anlık veri işleme", "Hızlı sonuç", "Otomatik <PERSON>a"]}, {"title": "Kullanıcı Dostu", "description": "Excel benzeri arayüz ile teknik bilgi gerektirmez", "features": ["Sürükle-bırak", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "APA Format", "description": "Akademik yayınlar için uygun profesyonel raporlar", "features": ["Word raporu", "APA standartları", "<PERSON><PERSON><PERSON>r tablolar"]}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kanıtlanmış R kütüphaneleri, tekrarlanabilir sonuçlar", "features": ["R tabanlı", "Doğrulanmış", "<PERSON><PERSON><PERSON><PERSON>"]}], "analyses": {"title": "Desteklenen Analizler", "types": ["Descriptive", "Independent", "Dependent", "Correlation", "Chi-Square", "Multiple Group"]}, "main_card": {"title": "Profesyonel İstatistiksel Analiz Platformu", "description": "Akademik çalışmalarınızı daha ileriye taşımak için kapsamlı istatistiksel analizler ve profesyonel raporlama sistemlerinden faydalanın."}, "security_card": {"title": "KVKK İle Tam Uyum", "description": "KVKK standartlarına uygun, verileriniz için maksimum güvenlik protokolleri."}, "report_card": {"title": "APA Formatında TR/EN Hazır Raporlar"}, "speed_card": {"title": "Daha hızlı analiz. Daha fazla sonuç. Daha iyi araştırma."}, "r_based_card": {"title": "R tabanlı güvenilir analiz sonuçları"}, "results_card": {"title": "An<PERSON><PERSON><PERSON> sonuçlar, anlamsız endişeler"}}, "cases": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Her alanda profesyonel istatistik analizi", "usecases": {"0": {"title": "Akademik Araştırmalar", "description": "<PERSON><PERSON><PERSON>, diş hekimliği, sağlık bilimleri ve diğer fakültelerde makale analizleri", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-1.webp"}, "1": {"title": "Tez Araştırmaları", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, doktora ve yüksek lisans tezleri için analizler", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-2.webp"}, "2": {"title": "Profesyonel <PERSON>ştırmalar", "description": "Yönetici ve araştırmacılar için istatistik bilimi ile daha iyi kararlar", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-3.webp"}, "3": {"title": "<PERSON><PERSON>", "description": "İstatistiksel analize ihtiyaç duyan herkes için", "image": "https://istabot-test.s3.eu-central-1.amazonaws.com/usecase-4.webp"}}, "academic_fields": {"title": "Akademik Kullanım Alanları", "fields": {"0": {"title": "<PERSON><PERSON><PERSON>", "description": "Klinik araştırmalar ve tıbbi veriler"}, "1": {"title": "Diş Hekimliği", "description": "Dental araştırmalar ve analizler"}, "2": {"title": "Sağlık Bilimleri", "description": "Sağlık araştırmaları ve veri analizleri"}, "3": {"title": "<PERSON><PERSON><PERSON>", "description": "Toplumsal araştırmalar ve anketler"}, "4": {"title": "Eğitim Bilimleri", "description": "Eğitim araştırmaları ve öğrenci analizleri"}, "5": {"title": "<PERSON><PERSON>", "description": "Pazar araştırmaları ve iş analizleri"}}}, "try_free": "<PERSON><PERSON>!"}, "compare": {"title": "İstabot ile Geleneksel Analiz <PERSON>ırması", "subtitle": "Neden istatistik analizleriniz için istabot'u seçmelisiniz?", "features": {"time_saving": {"title": "Zaman <PERSON>", "description": "Geleneksel yöntemlere göre %85 daha hızlı analiz süreci. Saatler süren işlemleri saniyeler içinde tamamlayın."}, "ease_of_use": {"title": "Kullanım Kolaylığı", "description": "İstatistik veya programlama bilgisi gerektirmez. Sürükle-bırak arayüzü ile herkes kolayca kullanabilir."}, "professional_results": {"title": "Profesyonel <PERSON>", "description": "APA formatında hazır raporlar ve akademik yayınlara uygun tablolar ile zaman kaybetmeden çalışmanızı tamamlayın."}}, "table": {"feature": "Özellik", "traditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rows": {"analysis_time": {"title": "<PERSON><PERSON><PERSON>", "istabot": "<PERSON><PERSON><PERSON><PERSON>", "istabot_detail": "Dakikalar içinde sonuç", "traditional": "Saatler/Günler", "traditional_detail": "Uzun hazırlık süreci"}, "technical_knowledge": {"title": "Teknik bilgi gereksinimi", "istabot": "Gerekmez", "istabot_detail": "Excel benzeri arayüz", "traditional": "İleri seviye", "traditional_detail": "<PERSON><PERSON>n öğrenme <PERSON>"}, "user_interface": {"title": "Kullanıcı arayüzü", "istabot": "<PERSON><PERSON>", "istabot_detail": "Sade ve anlaşılır", "traditional": "Karmaşık komutlar", "traditional_detail": "Kod bilgisi gerektirir"}, "report_format": {"title": "Rapor formatı", "istabot": "APA formatında Word", "istabot_detail": "Tek tıkla hazır", "traditional": "<PERSON>", "traditional_detail": "Ekstra iş yükü"}, "error_risk": {"title": "<PERSON><PERSON> riski", "istabot": "Minimum", "istabot_detail": "Otomatik kontroller", "traditional": "<PERSON><PERSON><PERSON><PERSON>", "traditional_detail": "<PERSON>"}, "cost": {"title": "Maliyet", "istabot": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "istabot_detail": "Ekonomik ve esnek", "traditional": "Yüksek lisans ücreti", "traditional_detail": "Sabit ve pahalı"}}}, "cost_comparison": {"title": "Maliyet Karşılaştırması", "calculator": {"title": "Hesaplayıcı", "monthly_analyses": "Aylık analiz sayısı", "hours_per_analysis": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON>na ortal<PERSON> süre (saat)", "hourly_rate": "Saatlik <PERSON>ğ<PERSON>z (₺)", "calculate_button": "<PERSON><PERSON><PERSON><PERSON>"}, "results": {"metric": "<PERSON><PERSON>", "istabot": "İstabot", "competitor": "Rakip", "difference": "Fark", "analysis_time": "<PERSON><PERSON><PERSON>", "cost_per_analysis": "<PERSON><PERSON><PERSON>", "monthly_cost": "Aylık Toplam Maliyet", "efficiency_increase": "Verimlilik Artışı"}, "note": "* Hesaplamalar ortalama değerler üzerinden yapılmıştır. İstabot ile analiz süresi geleneksel yöntemlere göre %85 daha hızlıdır."}, "tools": ["SPSS", "Python", "MINITAB"]}, "faq": {"title": "Sık Sorulan Sorular", "subtitle": "İstabot hakkında en çok merak edilenler", "categories": {"about_platform": "Platform Hakkında", "usage": "Kullanım", "pricing": "Fiyatlandırma ve Ödemeler", "technical": "Teknik Sorular"}, "questions": {"what_is_istabot": {"question": "<PERSON><PERSON><PERSON>dir?", "answer": "<PERSON><PERSON><PERSON>, akademik çalışmalar ve araştırmalar için geliştirilen web tabanlı bir istatistik analiz platformudur. Karmaşık yazılımlar ve kodlama bilgisi gerektirmeden, basit bir arayüz ile profesyonel analizler yapmanızı sağlar. R tabanlı algoritmalar ile güvenilir ve doğru sonuçlar sunar."}, "difference": {"question": "İstabot'un diğer analiz programlarından farkı nedir?", "answer_intro": "<PERSON><PERSON><PERSON>, geleneksel analiz programlarından farklı olarak:", "answer_items": ["Teknik bilgi gerektir<PERSON>z, istatistik veya kodlama bilmenize gerek yoktur", "<PERSON> ta<PERSON><PERSON><PERSON>, herhangi bir kurulum gerektirmez", "Analizlerin sonuçlarını APA formatında hazır raporlar olarak sunar", "Kullandıkça öde modeliyle lisans maliyeti yoktur", "Türkçe ve İngilizce dil desteği vardır"]}, "supported_analyses": {"question": "İstabot hangi analizleri destekliyor?", "answer_intro": "<PERSON><PERSON><PERSON>, akademik araştırmalarda en sık kullanılan çeşitli istatistiksel analiz yöntemlerini desteklemektedir:", "answer_items": ["Tanımlayıcı İstatistikler", "Freka<PERSON>", "Bağımsız Örneklem t-Testi", "Tek Yönlü ANOVA", "Eşleştirilmiş Örneklem t-Testi", "Tekrarlı Ölçümler ANOVA", "<PERSON><PERSON><PERSON>", "Kruskal-Wallis <PERSON>", "Wilcoxon İşaretli Sıralar Testi", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Point-Biserial Korelasyon", "Tetrachoric Korelasyon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Kolmogorov-S<PERSON>nov Testi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Runs Testi"], "answer_conclusion": "Her geçen gün analiz çeşitlerimiz artmaktadır."}, "upload_data": {"question": "Verilerimi nasıl yükleyebilirim?", "answer_intro": "İstabot'a verilerinizi Excel formatında (.xlsx, .xls) kolayca yükleyebilirsiniz. Bunun için:", "answer_steps": ["Hesabınıza giriş yapın", "\"Proje Oluştur\" butonuna tıklayın", "Excel dosyanızı sürükleyip bırakın veya \"<PERSON><PERSON><PERSON>\" ile yü<PERSON>in", "Çıkan ekranın altındaki \"Değişiklikleri Kaydet\" butonuna tıklayın", "<PERSON>eri setiniz platformda hazır olacaktır"], "answer_conclusion": "Excel dosyanızın ilk satırı değişken isimlerini içermelidir, örnek veri seti için proje oluşturma ekranından indirebilirsiniz. Çevrimiçi platformdaki veri düzenleme araçlarıyla da değişkenlerinizi yönetebilirsiniz."}, "perform_analysis": {"question": "<PERSON><PERSON><PERSON><PERSON><PERSON> nasıl yapa<PERSON>irim?", "answer_intro": "Veri setinizi yükledikten sonra analiz işlemi sadece birkaç adımda tamamlanır:", "answer_steps": ["<PERSON><PERSON><PERSON> (Tanımlayıcı istatistik, Bağımlı veri analizi, korelasyon vb.)", "Analizde kullanılacak değişkenleri seçin", "<PERSON><PERSON><PERSON> (isteğe bağlı)", "\"Analiz Et\" butonuna tıklayın", "Sonuçlar saniyeler içinde ekranınızda görüntülenecektir"], "answer_conclusion": "<PERSON><PERSON>z <PERSON>çlarınızı raporlar bölümünden Word formatında indirebilir veya platformda saklayabilirsiniz."}, "download_reports": {"question": "Raporlarımı nasıl indirebilirim?", "answer_intro": "<PERSON><PERSON><PERSON>, analiz sonuçlarınızı APA formatında profesyonel raporlar olarak sunar. Raporlarınızı indirmek için:", "answer_steps": ["<PERSON><PERSON><PERSON>ı sayfasında \"Raporu İndir\" butonuna tıklayın", "İstediğiniz <PERSON> (Türkçe veya İngilizce)", "Word formatında raporunuz otomatik olarak indirilecektir"], "answer_conclusion": "Raporlarınız istatistiksel test sonuçlarını, tab<PERSON>lar<PERSON>n<PERSON>, grafiklerini ve yorumlarını içerir. Doğrudan akademik çalışmanıza ekleyebilir veya düzenleyebilirsiniz."}, "credit_system": {"question": "<PERSON><PERSON><PERSON> sistemi nasıl çalışır?", "answer_intro": "<PERSON><PERSON><PERSON>, her analiz i<PERSON>in belirli sayıda kredi kullanan bir sistem üzerine kuruludur:", "answer_items": ["Her analiz türü farklı kredi miktarı gerektirir", "Satın aldığınız krediler 2 yıl boyunca hesabınızda geçerlidir", "İstediğiniz zaman ek kredi paketleri satın alabilirsiniz", "\"Kredi Hesaplayıcı\" aracımızla ihtiyacınız olan kredi miktarını hesaplayabilirsiniz"]}, "payment_methods": {"question": "Hangi ödeme yöntemlerini kabul ediyorsunuz?", "answer_intro": "İ<PERSON>bot, aşağıdaki güvenli ödeme yöntemlerini desteklemektedir:", "answer_items": ["<PERSON><PERSON><PERSON> ka<PERSON> (Visa, MasterCard vb.)", "Banka havalesi / EFT"], "answer_conclusion": "Tüm ödemeler Akbank güvenli ödeme altyapısı üzerinden gerçekleştirilir ve verileriniz şifrelenir. Ödemelerinizle ilgili fatura ve makbuzlar otomatik olarak e-posta adresinize gönderilir."}, "data_security": {"question": "Verilerim güvende mi?", "answer_intro": "<PERSON><PERSON><PERSON>, veri güvenliğinizi çok ciddiye almaktadır. Güvenlik önlemlerimiz:", "answer_items": ["Tüm veri transferleri SSL/TLS şifreleme ile korunur", "Veri depolama sistemlerimiz düzenli olarak yedeklenir ve güvenlik denetimlerinden geçer", "KVKK ve GDPR uyumlu veri saklama politikaları uygularız", "<PERSON><PERSON><PERSON>, sad<PERSON>e <PERSON><PERSON><PERSON> personelle sınırlandırılmıştır", "İsteğe ba<PERSON><PERSON><PERSON> o<PERSON>, analizlerinizi tamamladıktan sonra verilerinizi kalıcı olarak silebilirsiniz"], "answer_conclusion": "Veri güvenliği politikalarımız hakkında daha fazla bilgi için Gizlilik Politikamızı inceleyebilirsiniz."}, "mobile_usage": {"question": "İstabot'u mobil cihazlarda kullanabilir miyim?", "answer_intro": "<PERSON><PERSON>, İstabot tamamen mobil uyumludur ve responsive tasarım prensiplerine göre geliştirilmiştir:", "answer_items": ["<PERSON><PERSON><PERSON>ılarda kullanılabilir (Chrome, Safari, Firefox vb.)", "Tablet cihazlarda tam fonksiyonellik sunmaktadır", "Akıllı telefonlarda temel işlemler yapılabilir, ancak karmaşık veri düzenleme işlemleri için masaüstü arayüzü önerilir", "iOS ve Android cihazlarda test edilmiş ve optimize edilmiştir"], "answer_conclusion": "Büyük veri setleriyle çalışırken en iyi deneyim için masaüstü veya dizüstü bilgisayar kullanmanızı öneririz."}}, "contact": {"title": "Verileriniz Karmaşık Çözümümüz Basit", "subtitle": "Müşteri destek ekibimiz size yardımcı olmak için hazır", "whatsapp": "<PERSON><PERSON>eçin"}}, "how_it_works": {"title": "Profesyonel Analizin Dört Adımı", "subtitle": "Teknik bilgi gerektirmeden dakikalar içinde sonuç alın", "steps": {"step1": {"title": "<PERSON><PERSON> <PERSON>", "drag_drop": "Excel dosyanızı buraya sürükleyin", "or": "veya", "browse": "<PERSON><PERSON>"}, "step2": {"title": "<PERSON><PERSON>", "description": "Diagnose işlemleri ile verilerinizi analize hazırlayın.", "variable_calculation": "Değişken Hesaplama", "missing_data": "Eks<PERSON>", "variable_coding": "Değişken Kodlama"}, "step3": {"title": "<PERSON><PERSON><PERSON>", "description": "İhtiyaç duyduğunuz analiz türünü seçerek verilerinizi profesyonel şekilde <PERSON>ğ<PERSON>dirin.", "descriptive": "Tanımlayıcı İstatistik", "single_group": "Tek Grup Bağımsız Veri Analizi", "multi_group": "Çok Grup Bağımsız Veri Analizi", "dependent": "Bağımlı Veri Analizi", "chi_square": "<PERSON>", "correlation": "Korelas<PERSON>", "click_for_details": "Detaylı bilgi için tıkla"}, "step4": {"title": "<PERSON><PERSON><PERSON>", "description": "Sonuçları inceleyip APA formatında hazır raporunuzu Türkçe veya İngilizce olarak indirin.", "analysis_results": "<PERSON><PERSON><PERSON>", "analysis_interpretation": "<PERSON><PERSON><PERSON>", "sample_interpretation": "X Value ve Y Value arasında istatistiksel olarak anlamlı bir fark bulunmuştur (p<0.05).", "download_report": "DOCX Raporu İndir"}}, "cta": {"title": "İstatistik Analizlerinizde Devrim Yaratın!", "description": "Saniyeler içinde profesyonel analizler oluşturun, saatler kazanın ve verilerinizi kusursuz bir güvenle yorumlayın!", "try_free": "<PERSON><PERSON>!", "features": {"unlimited_data": "Sınırsız Veri Yükleme", "apa_reporting": "APA Format Raporlama", "auto_interpretation": "Otomatik Yorum", "support": "7/24 Teknik Destek"}}}, "pricing": {"title": "Fiyatlandırma <PERSON>", "description": "İhtiyacınıza uygun kredi paketleri ile esnek ödeme çözümleri", "buy_button": "Satın Al", "packages": {"mini": {"title": "Mini Paket", "discount_label": "%10 İndirim!", "original_price": "7.500 ₺", "discounted_price": "6.750 ₺", "credits": "100 Kredi", "description": "Küçük araştırma projeleri için mü<PERSON>mmel", "features": ["%10 İndirim!", "2 yıl geçerli", "Türkçe ve İngilizce raporlar", "Sınırs<PERSON>z rapor dışa aktarma", "Yenilenebilir"]}, "standard": {"title": "<PERSON><PERSON>", "discount_label": "%15 İndirim!", "original_price": "11.250 ₺", "discounted_price": "9.561 ₺", "credits": "150 Kredi", "description": "Orta ölçekli çalışmalar için ideal", "features": ["%15 İndirim!", "2 yıl geçerli", "Türkçe ve İngilizce raporlar", "Sınırs<PERSON>z rapor dışa aktarma", "Yenilenebilir"]}, "custom": {"title": "<PERSON><PERSON>", "price": "75 ₺", "per_credit": "/kredi", "credits": "Kredi miktarınızı seçin", "description": "İhtiyacınıza göre özelleştirilebilir paket", "features": ["Esnek kredi miktarı", "2 yıl geçerli", "Türkçe ve İngilizce raporlar", "Sınırs<PERSON>z rapor dışa aktarma", "Yenilenebilir"]}}, "corporate": {"title": "Kurumsal Fiyatlandırma", "description": "Üniversiteler ve araştırma grupları için özel fiyatlar", "contact_button": "Bizimle İletişime Geçin"}}, "stats": {"title": "İstatist<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> ka<PERSON>, zamandan tasarruf ve doğru sonuçlar", "cards": {"speed": {"label": "<PERSON><PERSON><PERSON>", "description": "Daha hızlı sonuçlar"}, "users": {"label": "Kullanıcı Sayısı", "description": "Akademisyen ve araştırmacı"}, "analyses": {"label": "Toplam Analiz", "description": "Başarıyla <PERSON>"}, "accuracy": {"label": "Doğruluk Oranı", "description": "<PERSON><PERSON><PERSON> veri girişi ile"}}, "r_standard": "R, dünya çapında akademik yayınlarda kabul gören istatistiksel analiz standardıdır", "apa_reports": {"title": "APA Formatında Profesyonel Raporlar", "description_part1": "Tek tıkla indirilebilen akademik standartlara uygun raporlarla", "percentage": "%92", "description_part2": "zaman tasarrufu <PERSON>ın."}, "try_free_button": "<PERSON><PERSON>!"}, "changelog": {"title": "Değişiklik Günlüğü", "description": "istabot'un en son gü<PERSON>llemeleri ve geliştirmeleri", "types": {"added": "<PERSON><PERSON><PERSON><PERSON>", "improved": "İyileştirilenler", "fixed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "releases": {"1.1.0": {"added": {"data_edit": {"title": "<PERSON><PERSON> düzenleme a<PERSON>ü<PERSON>ü baştan sona yeniden tasarlandı", "items": ["Eksik veri analizi ve eksik verileri düzenleme işlemleri", "Mevcut değişkenler ile yeni değişken hesaplama özelliği", "Değişkenleri yeniden kodlama özelliği", "<PERSON><PERSON> doğrulama kontrolleri", "Sınırsız veri düzenleme imkanı"]}}, "improved": {"ux": {"title": "Kullanıcı deneyimi geliştirildi", "items": ["<PERSON><PERSON><PERSON> ve hatalar g<PERSON>di", "Performans optimizasyonları"]}}}, "1.2.0": {"added": {"reporting": {"title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve Uyarılar bölümleri eklendi", "items": ["Rapor başlıkları ve tablo içerikleri iyileştirildi", "Teşekkür bölümü eklendi", "Uyarılar bölümü eklendi"]}, "coorporate": {"title": "Kurumsal hesaplar için ö<PERSON> özellikler", "items": ["<PERSON><PERSON><PERSON>i", "Kurumsal hesaplar i<PERSON>in ö<PERSON> raporlama özellikleri"]}}}}, "back": "<PERSON><PERSON>", "date_format": "longDate", "date_locale": "tr-TR"}}, "read-more": {"read-more": "Devamını Oku", "read-less": "<PERSON><PERSON>"}, "create-analysis": {"title": "<PERSON><PERSON><PERSON>", "step_prefix": "<PERSON><PERSON><PERSON>", "loading": "Yükleniyor...", "view_dataset": "<PERSON><PERSON>", "cancel": "İptal", "analyses": {"preview": "<PERSON><PERSON><PERSON><PERSON>", "preview_number": "Önizleme Sayısı"}, "steps": {"title": "<PERSON><PERSON><PERSON>lar", "variable_list": "Değişken Seçimi", "split_list": "<PERSON><PERSON><PERSON><PERSON>", "factor_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dependent_list": "Bağımlı Değişkenler", "covariate_list": "<PERSON><PERSON><PERSON>", "row_list": "<PERSON><PERSON><PERSON><PERSON>", "column_list": "<PERSON><PERSON><PERSON>", "define_list": "<PERSON><PERSON><PERSON>lama", "define": "<PERSON><PERSON><PERSON>lama", "review": "İnceleme", "status_list": "Durum değişkeni", "time_list": "<PERSON><PERSON>", "strata_list": "<PERSON><PERSON>", "independent_list": "<PERSON>ğ<PERSON><PERSON><PERSON><PERSON>"}, "buttons": {"next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "warnings": {"step_incomplete": "Bu adımı tamamlamadan ilerleyemezsiniz.", "previous_steps": "Lütfen önceki adımları tamamlayın.", "all_steps": "Lütfen tüm zorunlu adımları tamamlayın.", "dependent_first": "Lütfen önce Bağımlı Değişkenler adımını tamamlayın.", "reference_required": "Tek grup analizinde seçilen tüm değişkenler için referans değeri girmeniz zorunludur.", "invalid_reference_value": "Lütfen tüm referans değerleri için geçerli sayısal değerler girin.", "default": "Lütfen mevcut adımda en az bir değişken seçin."}, "filters": {"search": "Değişkenleri ara...", "display_mode": "Görünüm:", "name": "İsim", "header": "Başlık", "both": "Her ikisi", "type": "Tip:", "scale": "Scale", "ordinal": "Ordinal", "nominal": "Nominal", "no_type": "Tip bulunamadı", "select_all": "Tümünü Seç", "deselect_all": "Tümünü <PERSON>ldı<PERSON>"}, "variables": {"in_use": "Kullanımda", "reference": "Referans:", "value_labels": "<PERSON><PERSON><PERSON>", "count": "{{ count }} adet", "selected": "<PERSON><PERSON><PERSON><PERSON>", "search": "Değişkenleri ara...", "not_enough_labels": "En az 2 etikete sahip olmalı", "ordinal_not_allowed": "Ordinal değişken kullanılamaz", "wrong_measure_type": "Bu adım için yanlış ölçüm türü", "needs_exactly_two_labels": "Tam olarak 2 etikete sahip olmalı", "disabled": "Kullanılamaz"}, "empty_state": {"title": "Uygun değişken bulunamadı", "description": "Filtreleme kriterlerinize uygun değişken bulunmuyor. Lütfen filtreleme seçeneklerini değiştirin ve tekrar deneyin."}, "error": {"missing_info": "<PERSON><PERSON>z bilgileri eksik", "missing_info_desc": "Gerekli analiz bilgileri bulunamadı. Lütfen tekrar deneyin."}, "info": {"title": "<PERSON><PERSON><PERSON>", "variables_in_use": "<PERSON><PERSON><PERSON> adımlarda seçilmiş değişkenler devre dışı bırakılmıştır ve seçilemez. Her değişken yalnızca bir adımda kullanılabilir.", "split_variable": "<PERSON><PERSON><PERSON><PERSON>", "split_description": "Bu adımda en fazla 1 değişken seçebilirsiniz. Seçtiğ<PERSON>z değ<PERSON>şken, sonuçların gruplandırılması için kullanılacaktır. Bu adım isteğ<PERSON> b<PERSON><PERSON>, değişken seçmeden de devam edebilirsiniz.", "covariate": "<PERSON><PERSON><PERSON>", "covariate_description": "Bu adımda istediğiniz kadar değişken seçebilirsiniz. Seçtiğ<PERSON><PERSON>, analiz sonucunu etkileyebilecek dış faktörleri kontrol etmek için kullanılır. Bu adım isteğe b<PERSON>ı<PERSON>, değişken seçmeden de devam edebilirsiniz.", "single_group": "Tek Grup Analizi", "single_group_description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> her değişken için bir referans değeri girmeniz zorunludur. Referans değeri girilmeyen değişkenler için analiz yapılamaz ve bir sonraki adıma geçilemez."}, "help": {"title": "Yardım"}, "examples": {"title": "Örnekler"}, "dependent": {"title": "Bağımlı Veri Tanımlama", "description": "Bağımlı veri analizi için ölçüm zamanlarını ve değişkenleri tanımlayın.", "define_title": "Bağımlı Veri Tanımlama", "define_description": "Bağımlı veri analizi için ölçüm zamanlarını ve değişkenleri tanımlayın.", "time": "Zaman", "time_name_tr": "<PERSON>aman Adı (Türkçe)", "time_name_en": "Zaman Adı (İngilizce)", "variable_for_time": "<PERSON><PERSON> zaman i<PERSON>", "select_variable": "Değişken seçin", "search_variable": "Değişken ara...", "no_variable_found": "Değişken bulunamadı", "already_selected": "Zaten seçildi", "group": {"title": "Değişken Grubu", "name_tr": "Grup Adı (Türkçe)", "name_en": "Grup Adı (İngilizce)", "placeholder_tr": "Örn: <PERSON><PERSON>, <PERSON>...", "placeholder_en": "Örn: Blood Values, Test Scores..."}, "times": {"title": "Ölçüm Zamanları ve Değişkenler", "copy": "Zamanları Kopyala", "add": "<PERSON><PERSON>", "min_warning": "En az 2 zaman tanımlamanız gerekmektedir.", "time": "<PERSON>aman {{ index }}", "name_tr": "<PERSON>aman Adı (Türkçe)", "name_en": "Zaman Adı (İngilizce)", "placeholder_tr": "Örn: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 1. <PERSON><PERSON><PERSON>...", "placeholder_en": "Örn: Before, After, Week 1...", "variable": "<PERSON><PERSON> zaman i<PERSON>", "select_variable": "Değişken seçin", "search": "Değişken ara..."}, "copy_times": {"title": "Zamanları Kopyala", "description": "Önceden tanımlanmış zamanları seçin:", "group": "Grup {{ index }}"}, "defined_groups": {"title": "Tanımlanan Gruplar", "time": "Zaman (TR/EN)", "variable": "Değişken", "variable_type": "Değişken Tipi"}, "add_button": "<PERSON><PERSON>", "all_times_required": "<PERSON><PERSON>m zamanlar için <PERSON>ken seçmeniz zorunludur."}, "split": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sonuçları gruplamak için bir ayırma değişkeni seçebilirsiniz (isteğe bağlı)."}, "review": {"title": "<PERSON><PERSON><PERSON>", "description": "Seçimlerinizi gözden geçirin", "analysis_info": "<PERSON><PERSON><PERSON>", "drag_to_order": "Değişkenleri sürükleyerek sıralayın", "no_definition": "<PERSON><PERSON><PERSON><PERSON>a ya<PERSON>ı<PERSON>dı", "no_variable_selected": "<PERSON>u adımda değişken seçilmedi", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "time_option": {"title": "Zaman İçeriyor mu?", "has_time": "Zaman İçeriyor", "no_time": "Zaman İçermiyor"}, "credit": {"title": "<PERSON><PERSON><PERSON>", "required": "Gerekli K<PERSON>i:", "balance": "Mevcut Bakiye:", "calculating": "<PERSON><PERSON><PERSON> he<PERSON>lanıyor...", "amount": "kredi k<PERSON>ı<PERSON>k", "insufficient": "<PERSON><PERSON><PERSON>", "insufficient_message": "Bu analizi gerçekleştirmek için yeterli krediniz bulunmuyor. <PERSON><PERSON> de devam edebilirsiniz."}, "configuration": {"title": "<PERSON><PERSON><PERSON>", "separator": "Ondalık Ayırıcı:", "precision": "Ondalık <PERSON>:", "created_at": "Oluşturulma Tarihi:", "updated_at": "<PERSON><PERSON><PERSON><PERSON><PERSON>:"}, "report": {"title": "<PERSON><PERSON>", "format": "Rapor Formatı:", "decimal": "Ondalık Basamak:", "decimal_separator": "Ondalık Ayracı", "comma": "<PERSON><PERSON><PERSON><PERSON><PERSON> (,)", "dot": "<PERSON><PERSON><PERSON> (.)", "decimal_places": "Ondalık Basamak Sayısı"}, "report_type": {"title": "<PERSON><PERSON>", "by_row": "Satıra Göre", "by_column": "Sütuna Göre"}, "selected_variables": {"title": "<PERSON><PERSON><PERSON><PERSON>", "step": "Adım {{ step }}: {{ name }}", "no_selection": "Bu adımda değişken seçilmedi.", "count": "{{ count }} değişken seçildi"}, "times": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "count": "{{ count }} adet", "time": "<PERSON>aman {{ index }}", "tr": "Türkçe:", "en": "English:", "variable": "Değişken:"}}}, "tabs": {"project": {"overview": "Genel Bakış", "dataset": "<PERSON><PERSON>", "analysis": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "history": "Geçmiş", "members": "<PERSON><PERSON><PERSON>"}, "settings": {"account": "<PERSON><PERSON><PERSON>", "purchase_history": "Satın Alım Geçmişi", "language": "Dil", "report_settings": "<PERSON><PERSON>", "gift_credit": "Kredi Hediye Et"}, "admin": {"dashboard": "<PERSON><PERSON><PERSON><PERSON>i", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON>", "eft-confirm": "Eft / Havale Yönetimi", "reports": "<PERSON><PERSON><PERSON>", "transactions": "İşlemler", "settings": "<PERSON><PERSON><PERSON>"}}, "sidebar": {"titles": {"istabot": "istabot", "workspace": "ÇALIŞMA ALANI", "social": "SOSYAL", "general": "GENEL"}, "tooltip": {"projects": "<PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "gift_credits": "<PERSON><PERSON><PERSON>", "invite_friends": "Referans Programı", "settings": "<PERSON><PERSON><PERSON>", "help": "Yardım", "credit": "Kredi bilgi<PERSON>z", "buy_credits": "Kredi Satın Al", "logout": "Çıkış Yap", "profile_options": "Seçenekler", "explore": "Keşfet", "analyses": "<PERSON><PERSON><PERSON><PERSON>"}, "workspace": {"projects": "<PERSON><PERSON><PERSON>", "analyses": "<PERSON><PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>"}, "social": {"gift_credits": "Kredi Hediye Et", "invite_friends": "Referans Programı", "explore": "Keşfet"}, "general": {"settings": "<PERSON><PERSON><PERSON>", "help": "Yardım", "admin_panel": "<PERSON><PERSON>"}, "balance": "Bakiye", "buy_credits": "Kredi Satın Al"}, "auth": {"change_password": {"title": "<PERSON><PERSON>", "success_title": "<PERSON><PERSON><PERSON><PERSON>", "success_message": "<PERSON>ni şifreniz ile giriş yapabilirsiniz.", "new_password": "<PERSON><PERSON>", "confirm_password": "<PERSON><PERSON><PERSON>", "new_password_placeholder": "En az 8 karakter", "confirm_password_placeholder": "Şifrenizi tekrar girin", "reset_password_token_invalid": "Şifre sıfırlama bağlantısı geçersiz", "update_button": "<PERSON><PERSON><PERSON><PERSON>", "login_button": "<PERSON><PERSON><PERSON>", "reset_pass": "<PERSON><PERSON><PERSON>", "reset_pass_desc": "Şifrenizi değiştirmek için e-postanıza sıfırlama bağlantısı gönderilir", "error": {"password_short": "Şifre en az 8 karakter olmalıdır", "password_mismatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor"}}, "mail-input": {"title": "Şifre <PERSON>ırl<PERSON>", "desc": "Şifrenizi sıfırlamak için e-posta adresinizi girin", "email": "E-posta adresi", "email_placeholder": "E-posta adresinizi girin", "send_email": "Sıfırlama bağlantısı gönder", "cancel": "İptal", "email_required": "Lütfen e-posta adresinizi girin", "email_invalid": "Lütfen geçerli bir e-posta adresi girin", "error": "Hata!", "email_not_found": "Bu e-posta adresiyle kayıtlı bir hesap bulunamadı", "email_sent": "Sıfırlama bağlantısı gönderildi", "password_reset_instructions_sent": "Sıfırlama talimatları için e-postanızı kontrol edin", "sending_email": "Sıfırlama bağlantısı gönderiliyor...", "email_blank": "E-posta g<PERSON>medi.", "sending": "Gönderiliyor..."}, "login": {"error": "<PERSON>ir hata o<PERSON>", "success": "Başarılı", "redirect": "Ana sayfaya yönlendiriliyorsunuz...", "account_creating": "Hesabınız oluşturuluyor...", "desc": "İstatistiksel analizin yeni çağına hoş geldiniz", "email": "E-posta adresi", "name": "Ad", "email_placeholder": "E-posta adresinizi girin", "surname": "Soyad", "password": "Şifre", "confirm_password": "<PERSON><PERSON><PERSON>", "email_is_required": "Lütfen e-posta adresinizi girin", "email_is_invalid": "Lütfen geçerli bir e-posta adresi girin", "password_is_required": "Lütfen şifrenizi girin", "password_is_short": "Şifreniz en az 6 karakter olmalıdır", "all_fields_are_required": "* ile i<PERSON><PERSON><PERSON> tüm alanlar zorunludur", "email_taken": "Bu e-posta adresi zaten kullanımda", "phone_taken": "Bu telefon numarası zaten kullanımda", "email_phone_taken": "Bu e-posta adresi ve telefon numarası zaten kullanımda", "user_doesnt_exist": "Bu e-posta adresiyle kayıtlı bir hesap bulunamadı", "invalid_email_or_password": "E-posta adresi veya şifre hatalı", "login_failed": "G<PERSON>ş yapılamadı. Lütfen bilgilerinizi kontrol edin", "input_validation": "Lütfen e-posta adresinizi ve şifrenizi kontrol edin", "forgot_password": "<PERSON><PERSON><PERSON><PERSON> unuttum", "login": "<PERSON><PERSON><PERSON> yap", "sign_up": "<PERSON><PERSON><PERSON>", "dont_have_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "already_have_account": "Zaten hesabınız var mı?", "confirm_contact": "<PERSON><PERSON>, yeni <PERSON> ve özel teklifler hakkında bilgi almak istiyorum", "membership_agreement": "Kullanım koşulları", "privacy_policy": "Gizlilik politikası", "and": "ve", "agree_to_our": "Okudum ve kabul ediyorum:", "you_must_agree": "Devam etmek için kullanım koşullarını ve gizlilik politikasını kabul etmelisiniz", "is_istabot_project": "© istabot 2025. Tüm hakları saklıdır.", "welcome_stats": "İstatistik Dünyasına Hoş Geldiniz!", "security_important": "Güvenliğiniz Bizim İçin <PERSON>", "first_step": "Profesyonel analiz için ilk adımınızı atın", "create_strong_password": "Hesabınızı güvende tutmak için güçlü bir şifre oluşturun", "personal_info": "<PERSON><PERSON><PERSON><PERSON>", "account_security": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON>", "go_back": "<PERSON><PERSON>", "create_account": "<PERSON><PERSON><PERSON>", "remember_me": "<PERSON><PERSON>"}, "signup": {"success": "Başarılı", "redirect": "Ana sayfaya yönlendiriliyorsunuz...", "account_creating": "Hesabınız oluşturuluyor...", "welcome": "İstatistiksel analizin yeni çağına hoş geldiniz!", "security_title": "Güvenliğiniz Bizim İçin <PERSON>", "first_step": "Profesyonel analizler için ilk adımınızı atın", "create_password": "Hesabınızı güvende tutmak için güçlü bir şifre oluşturun", "personal_info": "<PERSON><PERSON><PERSON><PERSON>", "security": "<PERSON><PERSON><PERSON>", "first_name": "Ad", "first_name_placeholder": "Adınızı girin", "last_name": "Soyad", "last_name_placeholder": "Soyadınızı girin", "email": "E-posta", "email_placeholder": "E-posta adresinizi girin", "phone": "Telefon", "password": "Şifre", "password_placeholder": "En az 8 karakter", "confirm_password": "<PERSON><PERSON><PERSON>", "confirm_password_placeholder": "Şifrenizi tekrar girin", "first_name_required": "Lütfen adınızı girin", "last_name_required": "Lütfen soyadınızı girin", "email_required": "Lütfen e-posta adresinizi girin", "email_invalid": "Lütfen geçerli bir e-posta adresi girin", "phone_required": "Lütfen telefon numaranızı girin", "phone_is_invalid": "Lütfen geçerli bir telefon numarası girin", "password_required": "Lütfen şifrenizi girin", "password_min_length": "Şifreniz en az 8 karakter olmalıdır", "password_max_length": "Şifreniz en fazla 24 karakter olmalıdır", "confirm_password_required": "Lütfen şifrenizi tekrar girin", "passwords_not_match": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "password_tips": "Güvenli Şifre İpuçları", "password_tip_1": "En az 8 karakter uzunluğunda olmalı", "password_tip_2": "Büyük ve küçük harf içermeli", "password_tip_3": "<PERSON><PERSON> ve özel karakter kullanın", "continue": "<PERSON><PERSON>", "back": "<PERSON><PERSON>", "create_account": "Hesabımı Oluştur", "already_have_account": "Zaten hesabınız var mı?", "login": "<PERSON><PERSON><PERSON>", "creating_account": "<PERSON><PERSON><PERSON>...", "error": "Hata!", "errors": {"email_taken": "Bu e-posta adresi zaten kullanımda", "phone_taken": "Bu telefon numarası zaten kullanımda", "email_phone_taken": "Bu e-posta ve telefon numarası zaten kullanımda"}}, "verification": {"desc_title": "Hesabınızı doğrulamak için e-postanızı kontrol edin", "pass_title": "Şifrenizi sıfırlamak için e-postanızı kontrol edin", "desc": "Size talimatları içeren bir e-posta gönderdik", "didnt_receive": "E-posta almadınız mı?", "resend": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON><PERSON>", "update": "E-posta adresini <PERSON>", "resend_success": "Doğrulama e-postası gönderildi", "email_already_confirmed": "E-postanız zaten doğrulanmış", "email_not_found": "E-posta adresi bulunamadı", "sending_email": "E-posta gönderiliyor...", "success": "Başarılı", "error": "Hata!", "email_confirm_sent": "E-posta gö<PERSON>ildi", "email_blank": "E-posta <PERSON>"}, "verified": {"email_confirmed": "E-posta adresiniz başarıyla doğrulandı ✓", "email_already_confirmed": "E-posta adresiniz zaten doğrulanmış", "login": "<PERSON><PERSON><PERSON>", "check": "E-posta adresiniz doğrulanıyor...", "confirmation_token_invalid": "E-posta doğrulama başarısız", "resend": "Doğrulama e-postasını tekrar gönder"}}, "project_list": {"compare": "<PERSON><PERSON><PERSON>ı<PERSON>", "cant_compare": "Bu projeyi karşılaştıramazsınız", "clone": "<PERSON><PERSON><PERSON>", "cant_clone": "Bu proje klonlanamaz", "recalculate": "<PERSON>je rap<PERSON>larını yeniden hesapla", "cant_recalculate": "Bu projenin raporları yeniden hesaplanamaz", "clear_search": "Aramayı temizle", "select_project": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yapmak için hedef proje seçin", "select": "<PERSON><PERSON>", "cancel": "İptal", "compare": "Karş<PERSON>laştır"}, "show_all": "Tümünü <PERSON>ö<PERSON>", "show_favorites": "Favorileri göster", "my_projects": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "total": "Toplam", "search_placeholder": "Proje adı veya veri seti ile ara...", "description": "Tüm projelerinizi tek bir yerden y<PERSON>, takip edin ve organize edin", "filters": {"title": "Filtrele", "reset": "<PERSON><PERSON><PERSON><PERSON>", "date_range": {"label": "<PERSON><PERSON><PERSON>", "start": "Başlangıç", "end": "Bitiş"}, "sorting": {"label": "Sıralama", "options": {"date_desc": "<PERSON><PERSON><PERSON> (Yeni-Eski)", "date_asc": "<PERSON><PERSON><PERSON> (Eski-Yeni)", "name_asc": "İsim (A-Z)", "name_desc": "İsim (Z-A)", "position": "Varsayılan"}}, "status": {"label": "Durum", "options": {"all": "Tümü", "new": "<PERSON><PERSON>", "ready": "<PERSON><PERSON><PERSON>", "needs_review": "İnceleme Gerekiyor", "needs_dataset": "Veri Seti Gerekiyor"}}, "dataset": {"label": "<PERSON><PERSON>", "options": {"all": "Tümü", "with": "<PERSON><PERSON>", "without": "<PERSON><PERSON> Set<PERSON>"}}}, "status": {"new_project": "<PERSON><PERSON>", "needs_dataset": "Veri Seti Gerekiyor", "ready_for_analysis": "<PERSON><PERSON><PERSON>", "needs_review": "İnceleme Gerekiyor"}, "search_project": "<PERSON>je ara", "create_project": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON> sil", "cant_delete": "<PERSON>u proje silinemez", "project_not_found": "İlk projenizi oluşturalım", "create_project_null": "İlk projenizi oluşturun", "edit_title": "<PERSON><PERSON> adı<PERSON> düzen<PERSON>", "analysis": "<PERSON><PERSON><PERSON>", "descriptive": "Tanımlayıcı analiz", "independent": "Bağımsız veri analizi", "dependent_analysis": "Bağımlı veri analizi", "single": "Tek grup analizi", "multi": "Çoklu grup analizi", "loading": "Yükleniyor...", "correlation": "Korelasyon analizi", "chisq": "<PERSON><PERSON><PERSON><PERSON> analizi", "dataset": "<PERSON><PERSON> seti", "my_dataset": "<PERSON><PERSON>", "no_dataset": "Veri seti bulunamadı", "dataset_required": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON> için lütfen veri seti e<PERSON>in", "dataset_not_ready": "<PERSON>eri seti hazı<PERSON>", "data_view": "<PERSON><PERSON>", "variable_view": "Değişken görünümü", "dataview_warning": "Sayılar görüntüleme için 2 ondalık basamağa yuvarlanmıştır. Orijinal verileriniz değişmemiştir", "past_reports": "Geçmiş raporlar", "report_created": "<PERSON><PERSON>", "report_not_found": "<PERSON><PERSON>", "download_all_reports": "<PERSON><PERSON>m raporları indir", "report_settings": "<PERSON><PERSON>", "start_analysis": "<PERSON><PERSON><PERSON> ba<PERSON>", "continue_to_analysis": "Analize devam et", "for_first_analysis": "İlk analizinize başlamak için 'Analize başla'ya tıklayın", "analysis_not_found": "<PERSON><PERSON><PERSON>", "separator": "Ondalık ayracı", "comma": "Virg<PERSON><PERSON>", "dot": "Nokta", "precision": "Ondalık <PERSON>", "precision_info": "1 ile 4 arasında bir değer girin", "example": "Örnek", "continue": "<PERSON><PERSON> et", "save": "<PERSON><PERSON>", "reset": "Sıfırla", "show_balance": "Bakiye Detayını Görüntüle", "buy_analysis": "Analiz Kredisi Satın Al", "analysis_management": "<PERSON><PERSON><PERSON>", "project": "<PERSON><PERSON>", "go_to_project": "Projeye Git", "edit": "<PERSON><PERSON><PERSON><PERSON>", "edit_dataset": "<PERSON><PERSON>", "add_dataset": "<PERSON><PERSON> <PERSON>", "clear": "<PERSON><PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "report_title": "<PERSON><PERSON> b<PERSON>ı<PERSON>ı", "action": "İşlem", "go_to_eistatistik.com": "eistatistik.com'u ziyaret et", "file_preparing": "<PERSON><PERSON><PERSON>", "my_analyses": "<PERSON><PERSON><PERSON><PERSON>", "language": "Dil", "add_favorite": "<PERSON>av<PERSON><PERSON>e ekle", "remove_favorite": "Favorilerden kaldır", "report_settings_title": "<PERSON><PERSON>", "credit_used": "kredi kull<PERSON>ı<PERSON>ı"}, "project_detail": {"header": {"last_updated": "<PERSON> günce<PERSON><PERSON>:", "overview": "Genel Bakış", "dataset": "<PERSON><PERSON>", "analysis": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "members": "<PERSON><PERSON><PERSON>", "credits_used": "kredi kull<PERSON>ı<PERSON>ı", "edit": "<PERSON><PERSON><PERSON><PERSON>", "no_description": "Açıklama yok", "normal": "Normal Proje", "demo": "<PERSON><PERSON>", "demo_template": "<PERSON><PERSON>", "project_id": "Proje <PERSON>", "used_credits": "Kullanılan Kredi", "updated_at": "<PERSON>", "admin_tools": {"menu": "<PERSON><PERSON>", "change_project_type": "<PERSON><PERSON>", "clone": "<PERSON><PERSON><PERSON>", "recalculate": "Raporları Yeniden Hesapla", "compare": "Raporları Karşılaştır"}}, "overview": {"dataset_info": "Veri seti bilgisi", "edit_dataset": "<PERSON><PERSON> d<PERSON>", "view_dataset": "<PERSON><PERSON><PERSON>", "variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "missing_values": "<PERSON><PERSON><PERSON>", "project_desc": "<PERSON>je <PERSON>", "notes_placeholder": "Projenizle ilgili notlar eklemek için düzenle düğmesine tıklayın. <PERSON>ne<PERSON><PERSON> de<PERSON>, araştırma sorularını veya analiz hedeflerini burada belgeleyebilirsiniz..", "edit": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "cancel": "İptal", "no_description": "Açıklama yok", "compare": "<PERSON><PERSON><PERSON>ı<PERSON>", "cant_compare": "Bu projeyi karşılaştıramazsınız", "clone": "<PERSON><PERSON><PERSON>", "cant_clone": "Bu proje klonlanamaz", "show_all": "Tümünü <PERSON>ö<PERSON>", "recalculate": "<PERSON>je rap<PERSON>larını yeniden hesapla", "cant_recalculate": "Bu projenin raporları yeniden hesaplanamaz", "save_dataset": "<PERSON><PERSON>", "credit_used": "kredi kull<PERSON>ı<PERSON>ı", "demo_info": "Bu bir demo projesidir. Yapacağınız işlemlerde demo kredileriniz kullanılacaktır.", "dataset_ready": {"title": "Veriniz analiz için hazır!", "info": "Verilerinizden içgörüler elde etmek için istatistiksel analizlerimizden seçin.", "button": "<PERSON><PERSON><PERSON>"}, "dataset_not_diagnosed": {"warning": "<PERSON>eri setinin incelenmesi gerekiyor", "title": "Veri setiniz henüz düzenlenmedi!", "info": "Eksik değerleri ve hataları kontrol etmek için veri setini düzenleyin.", "button": "<PERSON><PERSON> d<PERSON>"}, "no_dataset": {"title": "<PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> başlamak için veri setinizi Excel formatında yükleyin", "area_text": "dosyanızı buraya sürükleyip bırakın veya,", "area_text_2": "dosyalara göz atın", "area_info": "Sadece Excel dosyaları desteklenir (.xlsx)"}, "last_reports": {"title": "<PERSON>", "view_all": "Projeye ait tüm raporları görüntüle", "no_report": "<PERSON>u projede hen<PERSON>z rapor <PERSON>."}}, "dataset": {"last_updated": "<PERSON> günce<PERSON><PERSON>:", "imported": "İçe aktarıldı", "not_imported": "İçe aktarılmadı", "variable_status": {"total": "Toplam", "valid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalid": "Geçersiz"}, "placeholder": "Değişken ara...", "download": "<PERSON><PERSON><PERSON>", "clear_search": "Aramayı temizle", "edit_dataset": "<PERSON><PERSON> d<PERSON>", "filter": {"title": "Filtrele", "type": "<PERSON><PERSON><PERSON>", "all": "Tümü", "import_status": "İçe Aktarma Durumu", "imported": "İçe Aktarılan", "no_imported": "İçe Aktarılmayan", "reset_filter": "<PERSON><PERSON><PERSON>i <PERSON>ırl<PERSON>"}, "table": {"headers": {"import": "İçe Aktar", "variable": "Değişken Adı", "type": "<PERSON><PERSON><PERSON>", "label": "Etiket", "variable_label": "Değişken Etiketleri"}, "imported": "İçe Aktarıldı", "not_imported": "İçe Aktarılmadı", "missing_value": "<PERSON><PERSON><PERSON>"}, "values": "Değişken"}, "analysis": {"header_analysis": "<PERSON><PERSON><PERSON>", "desc_analysis": "Araştırma sorunuza uygun analizi seçin", "header_history": "<PERSON><PERSON><PERSON>", "desc_history": "Önceki analiz raporlarınızı görüntüleyin ve yönetin", "select": "Seç", "detail": "Detay", "show_history": "Geçmişi Göster", "show_analyses": "<PERSON><PERSON><PERSON><PERSON>", "download_favorites": "<PERSON><PERSON><PERSON>ı İndir", "filter": {"title": "Filtre", "sort_by": "Sıralama Kriteri", "date_range": "<PERSON><PERSON><PERSON>", "start": "Başlangıç", "end": "Bitiş", "reset": "Filtreleri Sıfırla"}, "compare_report": "Raporları Karşılaştır", "select_reports_compare": "Karşılaştırılacak raporları seçin", "compare": "Karş<PERSON>laştır", "cancel": "İptal", "new": "<PERSON><PERSON>", "analyses": {"descriptive": "Tanımlayıcı İstatistikler", "single": "Tek Grup Analizi", "multi": "Çoklu Grup Analizi", "dependent": "Bağımlı Veri Analizi", "correlation": "Korelas<PERSON>", "chisq": "<PERSON><PERSON><PERSON><PERSON>", "comean": "Ortalama <PERSON>şılaştırma", "logistic_cox": "Lojistik/Cox Regresyon", "survival": "Sağ<PERSON><PERSON><PERSON>", "roc": "ROC Analizi", "linear": "Doğrusal Regresyon"}, "analysis_history": {"clear_search": "Aramayı Temizle", "filters": "<PERSON><PERSON><PERSON><PERSON>", "sort_by": "S<PERSON>rala", "newest_first": "En Yeni İlk", "oldest_first": "En Eski İlk", "name_asc": "İsim (A-Z)", "name_desc": "İsim (Z-A)", "date_range": "<PERSON><PERSON><PERSON>", "start_date": "Başlangıç <PERSON>", "end_date": "Bitiş Tarihi", "reset_filters": "Filtreleri Sıfırla", "compare_reports": "Raporları Karşılaştır", "select_reports": "Karşılaştırılacak raporları seçin", "cancel": "İptal", "compare": "Karş<PERSON>laştır", "search_placeholder": "Ana<PERSON>z ara...", "no_reports": "<PERSON><PERSON><PERSON> bulu<PERSON>", "downloading": "Favori raporlar indiriliyor...", "credit_used": "kredi kull<PERSON>ı<PERSON>ı"}}, "settings": {"title": "<PERSON><PERSON>", "project_name": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "save_changes": "Değişiklikleri Kaydet", "cancel": "İptal", "name_required": "Proje adı zorunludur", "name_max_length": "Proje adı en fazla 64 karakter olabilir", "dataset_name": "Veri Seti Adı", "no_dataset": "Veri seti bulunamadı", "save_dataset": "<PERSON><PERSON>", "add_dataset": "<PERSON><PERSON> <PERSON>", "update_dataset": "<PERSON><PERSON> <PERSON>", "dataset_locked": "<PERSON>eri seti kilitli", "dataset_locked_desc": "Veri seti ile ilgili değişiklik yapılmış.", "dataset_add_success": "Veri seti başarıyla eklendi", "dataset_add_error": "Veri seti eklenirken bir hata o<PERSON>", "dataset_update_success": "<PERSON>eri seti başar<PERSON><PERSON> g<PERSON>", "dataset_update_error": "Veri seti güncellenirken bir hata o<PERSON>", "delete_project": "<PERSON><PERSON><PERSON>", "delete_project_warning": "Projenizi silmek is<PERSON>, tüm verileriniz kalıcı olarak silinecektir. Bu işlem geri alınamaz. ", "delete_project_reports_warning": "Projede oluşturduğunuz raporlar olduğu için bu proje silinemez.", "report_settings_title": "<PERSON><PERSON>", "separator": "Ondalık Ayırıcı", "dot": "Nokta", "comma": "Virg<PERSON><PERSON>", "precision": "Ondalık <PERSON>", "example": "Örnek", "update_success": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "update_error": "<PERSON><PERSON> g<PERSON> bir hata o<PERSON>", "file_processing_error": "<PERSON><PERSON>a i<PERSON>lenirk<PERSON> bir hata o<PERSON>", "delete_success": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete_error": "<PERSON><PERSON> si<PERSON> bir hata o<PERSON>"}, "members": {"title": "<PERSON><PERSON>", "description": "Bu projeye erişimi olan tüm araştırmacıları görüntüleyin ve yönetin", "loading": "Üyeler yükleniyor...", "loading_researchers": "Araştırmacılar yükleniyor...", "add_member": "Araştırmacı Ekle", "share_history": "Geçmiş", "no_history": "Henüz işlem yok", "search_placeholder": "Üyeler arasında ara...", "clear_search": "Aramayı temizle", "show_history": "Geçmişi Göster", "hide_history": "Geçmişi Gizle", "total_members": "Toplam {{count}} üye", "roles": {"owner": "<PERSON><PERSON>", "researcher": "Araştırmacı"}, "owner_info": {"title": "<PERSON><PERSON>", "description": "<PERSON>u projenin sahibi ve yönet<PERSON>si"}, "researcher_info": {"title": "Araştırmacı", "description": "Proje analizlerine erişimi olan araştırmacı"}, "actions": {"transfer_ownership": "Sahipliği Transfer Et", "remove_member": "<PERSON><PERSON><PERSON>", "add_first_researcher": "İlk Araştırmacıyı Ekle"}, "empty_state": {"no_researchers": "Henüz araştırmacı yok", "no_researchers_description": "Bu projeye henüz araştırmacı eklenmemiş. İlk araştırmacıyı eklemek için butona tıklayın.", "no_search_results": "<PERSON><PERSON> son<PERSON>u bulu<PERSON>ı", "no_search_results_description": "Farklı anahtar kelimeler deneyin"}, "history": {"title": "Paylaşım Geçmişi", "no_history": "Henüz hiçbir işlem yapılmamış", "no_history_description": "Proje ü<PERSON>liği değişiklikleri burada görünecek", "actions": {"add_researcher": "Araştırmacı Eklendi", "remove_researcher": "Araştırmacı Çıkarıldı", "transfer_ownership": "Sahiplik Transfer Edildi"}}, "confirmations": {"remove_member": {"title": "Araştırmacı Çıkar", "content": "{{name}} is<PERSON><PERSON>ırmacıyı projeden çıkarmak istediğinizden emin misiniz?", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "İptal"}, "transfer_ownership": {"title": "Sahipliği Transfer Et", "content": "<PERSON><PERSON> {{name}} is<PERSON>li kullanıcıya transfer etmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "confirm": "Transfer Et", "cancel": "İptal"}}, "messages": {"member_added_success": "Araştırmacı başarıyla eklendi", "member_removed_success": "Araştırmacı başarıyla çıkarıldı", "ownership_transferred_success": "Proje <PERSON>ği başarıyla transfer edildi", "user_not_found": "Kullanıcı bulunamadı", "member_already_exists": "Araştırmacı zaten var veya proje sahibi", "no_permission": "Bu iş<PERSON> için <PERSON> bulunmuyor", "add_member_error": "Araştırmacı eklenemedi", "remove_member_error": "Araştırmacı çıkarılamadı", "transfer_ownership_error": "Sahiplik transfer edilemedi", "load_researchers_error": "Araştırmacılar yüklenemedi", "load_history_error": "Geçmiş yüklenemedi"}}, "add_member": {"title": "Araştırmacı Ekle", "email_label": "E-posta Adresi", "email_required": "E-posta adresi gereklidir", "email_invalid": "Geçerli bir e-posta adresi girin", "email_placeholder": "<EMAIL>", "email_placeholder_focused": "örnek: <EMAIL>", "info_title": "Bilgi:", "info_description": "Eklenecek kullanıcının sistemde kayıtlı olması gerekmektedir. Araştırmacı eklendikten sonra proje analizlerine erişim sağlayabilecektir.", "cancel": "İptal", "submit": "Araştırmacı Ekle", "submitting": "Ekleniyor...", "form_validation": {"email_required": "E-posta adresi gereklidir", "email_format": "Geçerli bir e-posta adresi girin"}}, "type_update": {"unauthorized": "Yetkisiz kullanıcı.", "success": "<PERSON><PERSON> tü<PERSON>ü başar<PERSON><PERSON> güncellendi.", "error": "<PERSON><PERSON> tü<PERSON> gü<PERSON>llenemedi."}}, "report_list": {"title": "<PERSON><PERSON><PERSON>", "create_report": "<PERSON><PERSON>", "project_count": "projeden <PERSON>", "total_report": "rapor", "search_placeholder": "<PERSON><PERSON> başlığı veya proje ile ara...", "download_report": "<PERSON><PERSON><PERSON>", "by_projects": "<PERSON><PERSON><PERSON>", "all_reports": "<PERSON><PERSON><PERSON>", "select_report_prompt": "Kopyalamak istediğiniz raporu <PERSON>", "select_report_desc": "Kopyalamak istediğiniz raporu listeden seçin", "show_favorites": "Favorileri göster", "show_all": "Tümünü <PERSON>ö<PERSON>", "credit_used": "kredi kull<PERSON>ı<PERSON>ı", "clear_search": "Aramayı temizle", "common": {"cancel": "İptal"}, "filters": {"title": "Filtrele", "reset": "<PERSON><PERSON><PERSON><PERSON>", "date_range": {"label": "<PERSON><PERSON><PERSON>", "start": "Başlangıç", "end": "Bitiş"}, "sorting": {"label": "Sıralama", "options": {"date_desc": "<PERSON><PERSON><PERSON> (Yeni-Eski)", "date_asc": "<PERSON><PERSON><PERSON> (Eski-Yeni)", "name_asc": "İsim (A-Z)", "name_desc": "İsim (Z-A)", "position": "Varsayılan"}}, "status": {"label": "Durum", "options": {"all": "Tümü", "new": "<PERSON><PERSON>", "ready": "<PERSON><PERSON><PERSON>", "needs_review": "İnceleme Gerekiyor", "needs_dataset": "Veri Seti Gerekiyor"}}, "dataset": {"label": "<PERSON><PERSON>", "options": {"all": "Tümü", "with": "<PERSON><PERSON>", "without": "<PERSON><PERSON> Set<PERSON>"}}}}, "create_report": {"title": "<PERSON><PERSON>", "new_analysis": "<PERSON><PERSON>", "new_analysis_desc": "Yeni bir analiz başlatarak rapor oluş<PERSON>", "from_existing": "<PERSON><PERSON><PERSON>", "from_existing_desc": "<PERSON>ar olan bir rap<PERSON>an k<PERSON>", "select_project": "<PERSON><PERSON>", "select_project_placeholder": "<PERSON><PERSON>", "invalid_dataset": "<PERSON><PERSON>", "select_analysis_type": "<PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "common": {"cancel": "İptal", "continue": "<PERSON><PERSON>", "back": "<PERSON><PERSON>"}, "no_valid_projects": "Veri seti yüklenmiş proje bulunamadı. Lütfen önce bir projeye veri seti yükleyin.", "no_projects_available": "<PERSON>ygun proje bulunmuyor"}, "reports": {"title": "<PERSON><PERSON><PERSON>"}, "analyses_list": {"my_analyses": "<PERSON><PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON>", "dataset": "<PERSON><PERSON>", "project": "<PERSON><PERSON>"}, "dataset_list": {"my_datasets": "<PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON>", "project": "<PERSON><PERSON>", "variable_view": "Değişken Görünümü", "data_view": "<PERSON><PERSON>", "download_dataset": "<PERSON><PERSON>"}, "analyses": {"titles": {"descriptive": "Tanımlayıcı İstatistikler", "single": "Tek Grup Analizi", "multi": "Çoklu Grup Analizi", "dependent": "Bağımlı Veri Analizi", "correlation": "Korelas<PERSON>", "chisq": "<PERSON><PERSON><PERSON><PERSON>", "comean": "Ortalama <PERSON>şılaştırma", "logistic_cox": "Lojistik/Cox Regresyon", "survival": "Sağ<PERSON><PERSON><PERSON>", "roc": "ROC Analizi", "linear": "Doğrusal Regresyon", "unknown": "<PERSON><PERSON><PERSON>"}, "descriptions": {"descriptive": "<PERSON><PERSON><PERSON>şkenlerinizin ortalama, medyan ve standart sapma gibi temel istatistiklerini hesaplar.", "single": "bir de<PERSON><PERSON><PERSON><PERSON><PERSON> ortal<PERSON>ının belirli bir de<PERSON><PERSON>en farklı olup olmadığını test eder.", "multi": "birden fazla grup arasındaki ortalamaları karşılaştırır (ANOVA).", "dependent": "aynı örneklemden alınan iki ölçümü karşılaştırır.", "correlation": "iki değişken arasındaki ilişkiyi ölçer.", "chisq": "kategorik değişkenler arasındaki ilişkiyi test eder.", "comean": "farklı gruplar arasındaki ortalamaları karşılaştırır.", "logistic_cox": "ikili sonuç değişkenleri için olasılık modellemesi ve sağkalım analizi yapar.", "survival": "belirli bir o<PERSON>ın gerçekleşme süresini analiz eder.", "roc": "tanı testlerinin doğruluğunu değerlendirir ve eşik değerlerini optimize eder.", "linear": "bağımlı değişken ile bir veya daha fazla bağımsız değişken arasındaki ilişkiyi modelleyerek tahmin yapar.", "unknown": "verileriniz üzerinde istatistiksel hesaplamalar yapar."}, "descriptive": {"title": "Tanımlayıcı İstatistikler", "description": "Değişkenler için özet istatistikler sağlar", "help_text": "Tanımlayıcı istatistikler, bir veri setinin merkez<PERSON> e<PERSON>, da<PERSON><PERSON>lı<PERSON> ve dağılım şekli gibi ana özelliklerini özetler.", "examples": {"1": "Bir örneklemdeki yaş dağılımını inceleme", "2": "<PERSON><PERSON><PERSON> veril<PERSON>nin ortalama ve medyanını hesaplama", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> minimum, maks<PERSON><PERSON> ve aralık değ<PERSON><PERSON><PERSON> bulma"}}, "single": {"title": "Tek Grup Analizi", "description": "Bir değişkenin ortalamasının belirli bir test değerinden farklı olup olmadığını test eder", "help_text": "Tek grup t-testi, bir <PERSON><PERSON><PERSON><PERSON><PERSON>in ortalamasını teorik bir değerle karşılaştırmanıza olanak tanır.", "examples": {"1": "Bir ürünün ağırlığının standart bir değerden farklı olup olmadığını test etme", "2": "Bir grubun ortalama puanının bir normdan farklı olup o<PERSON>dığını belirleme", "3": "Bir ortalama değerin belirli bir eşiğin üstünde veya altında olup olmadığını test etme"}}, "multi": {"title": "Çoklu Grup Analizi", "description": "Birden fazla grup arasındaki ortalamaları karşılaştırır", "help_text": "Çoklu grup analizi, anlamlı farklılıklar olup olmadığını belirlemek için farklı gruplar arasındaki ortalamaları karşılaştırmanıza olanak tanır.", "examples": {"1": "Farklı okullar arasındaki test puanlarını karşılaştırma", "2": "Farklı bölgelerdeki ürün performansını analiz etme", "3": "Birden fazla hasta grubu arasındaki tedavi etkilerini değerlendirme"}}, "dependent": {"title": "Bağımlı Analiz", "description": "Değişkenlerin zaman veya koşullar üzerindeki değişimlerini analiz eder", "help_text": "Bağ<PERSON><PERSON><PERSON><PERSON> analiz, değişkenlerin bir müdahale öncesi ve sonrası gibi ilişkili ölçümler arasında nasıl değiştiğini inceler.", "examples": {"1": "Bir diyet programı öncesi ve sonrası kilo kaybını ölçme", "2": "Eğitim sonrası performans iyileştirmelerini takip etme", "3": "<PERSON><PERSON> ve sonrası semptomlardaki değişiklikleri analiz etme"}, "incomplete_define": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON>rım kalan bir tanı<PERSON>lamanız var. Devam ederseniz bu tanım analize dahil edilmeyecektir. Önce Ekle butonuna basmanız gerekir.", "confirm": "<PERSON><PERSON>", "cancel": "İptal"}}, "correlation": {"title": "Korelas<PERSON>", "description": "Değişkenler arasındaki ilişkilerin gücünü ve yönünü ölçer", "help_text": "Korelasyon analizi, nedensellik ima etmeden değişken çiftlerinin ne kadar güçlü ilişkili olduğunu ölçer.", "examples": {"1": "Çalışma süresi ve sınav puanları arasındaki ilişkiyi inceleme", "2": "G<PERSON>r ve harcama alışkanlıkları arasındaki bağlantıyı analiz etme", "3": "Yaş ve kan basıncı arasındaki ilişkiyi araştırma"}, "min_variables_warning": "Korelasyon analizinde en az 2 değişken seçilmelidir."}, "chisq": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kategorik değişkenler arasındaki ilişkileri test eder", "help_text": "<PERSON><PERSON><PERSON><PERSON> anal<PERSON>, kate<PERSON><PERSON> değişkenler arasında anlamlı bir ilişki olup olmadığını belirler.", "examples": {"1": "Cinsiyetin ürün tercihiyle ilişkili olup olmadığını test etme", "2": "Eğitim düzeyinin oy verme davranışıyla ilişkili olup olmadığını analiz etme", "3": "<PERSON>avi tü<PERSON>ü<PERSON>ün iyileşme oranlarıyla ilişkili olup olmadığını belirleme"}}, "comean": {"title": "Ortalama <PERSON>ş<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>şkenleri kontrol ederken ortalamaları karşılaştırır", "help_text": "Ortalama karşı<PERSON><PERSON><PERSON><PERSON><PERSON> analizi, <PERSON><PERSON><PERSON>lerin etkisini hesaba katarken grup ortalamalarını karşılaştırmanıza olanak tanır.", "examples": {"1": "Yaşı kontrol ederken tedavi etkilerini karşılaştırma", "2": "Deneyimi hesaba katarken maaş farklılıklarını analiz etme", "3": "Başlangıç yeteneğini kontrol ederken performans farklılıklarını değerlendirme"}}, "logistic_cox": {"title": "Lojistik/Cox Regresyon", "description": "İkili sonuç değişkenleri için olasılık modellemesi ve sağkalım analizi yapar", "help_text": "Lojistik/Cox reg<PERSON>yon, i<PERSON><PERSON>kenleri için olasılık modellemesi ve sağkalım analizi yapmanıza olanak tanır.", "examples": {"1": "<PERSON><PERSON> göre hastalık riskini tahmin etme", "2": "Belirli bir o<PERSON>ın gerçekleşme süresini analiz etme", "3": "Risk faktörlerinin et<PERSON>ini değ<PERSON>lendirme"}}, "survival": {"title": "Sağ<PERSON><PERSON><PERSON>", "description": "Belirli bir o<PERSON>ın gerçekleşme süresini analiz eder", "help_text": "Sağkalım analizi, belirli bir olayın gerçekleşme süresini ve buna etki eden faktörleri incelemenize olanak tanır.", "examples": {"1": "<PERSON><PERSON> hastaların yaşam süresini analiz etme", "2": "Farklı tedavi yöntemlerinin sağkalım üzerindeki etkisini karşılaştırma", "3": "Risk faktörlerinin sağkalım süresine etkisini değerlendirme"}}, "roc": {"title": "ROC Analizi", "description": "Tanı testlerinin doğruluğunu değerlendirir ve eşik değerlerini optimize eder", "help_text": "ROC analizi, tanı testlerinin doğruluğunu değerlendirmenize ve optimal eşik değerlerini belirlemenize olanak tanır.", "examples": {"1": "Tanı testinin duyarlılık ve özgüllüğünü değerlendirme", "2": "Farklı testlerin performansını karşılaştırma", "3": "Optimal kesim noktasını belirleme"}}, "linear": {"title": "Doğrusal Regresyon", "description": "Bağımlı değişken ile bir veya daha fazla bağımsız değişken arasındaki ilişkiyi modelleyerek tahmin yapar", "help_text": "Doğrusal regresyon, bağımlı değişken ile bir veya daha fazla bağımsız değişken arasındaki ilişkiyi modelleyerek tahmin yapmanıza olanak tanır.", "examples": {"1": "Ev fiyatlarını tahmin etmek için yüzölçümü ve konum gibi faktörleri kullanma", "2": "Satış rakamlarını etkileyen faktörleri belirleme", "3": "Akademik başarıyı etkileyen değişkenleri analiz etme"}}, "insufficient_credits": {"title": "<PERSON><PERSON><PERSON>", "content": "Bu analizi gerçekleştirmek için yeterli krediniz bulunmuyor. <PERSON><PERSON> de de<PERSON>m etmek isterseniz, analiz sonuçlarını görmek için ödeme yapmanız gerekecektir.", "confirm": "<PERSON><PERSON>", "cancel": "Kredi Satın Al"}, "go_to_project": "<PERSON><PERSON><PERSON>", "go_back_to_analysis_list": "<PERSON><PERSON><PERSON><PERSON>", "go_back_to_reports": "<PERSON><PERSON><PERSON><PERSON>", "press_enter_to_save": "Kaydetmek için <PERSON>'a basın", "enter_report_title": "<PERSON><PERSON> b<PERSON><PERSON>lığını girin", "go_to_my_analyses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create_analyse": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "create_new_analysis": "Yeni analiz oluştur", "start_analysis": "<PERSON><PERSON><PERSON> ba<PERSON>", "select_your_analyze_type": "<PERSON><PERSON><PERSON>ü<PERSON>", "hover_analyses_for_info": "Detaylar için analiz türlerinin üzerine gelin", "analysis_not_found": "İlk analizinizi oluşturun", "report_created": "<PERSON><PERSON>", "report_not_found": "<PERSON><PERSON>", "report_is_creating": "Raporunuz oluşturuluyor...", "report_is_ready": "Raporunuz hazır", "save_report": "<PERSON><PERSON><PERSON> rapor<PERSON>", "save_as_report": "<PERSON><PERSON> rapor o<PERSON>ak ka<PERSON>et", "clone_report": "<PERSON><PERSON>ini klonla", "update_existing_report": "<PERSON><PERSON><PERSON> rapor<PERSON>", "create_new_report": "<PERSON><PERSON> rap<PERSON>", "clone_report_question": "<PERSON>or değişkenlerini klonlayarak geldiniz. Ne yapmak istersiniz?", "delete_report": "<PERSON><PERSON><PERSON> sil", "maximize_report": "<PERSON>", "select_variable": "Değişken seç", "variable_not_found": "Değişken bulunamadı", "selected_variable_not_found": "Seçili değişken bulunamadı", "no_variable_of_this_type_found": "Bu türde değişken bulunamadı", "at_least_one_variable": "En az bir değişken seçin ve gerekirse referans değeri girin", "at_least_one_defined": "En az bir değişken tanımlayın", "is_involve_time": "Zaman değişkeni içeriyor mu?", "at_most_one": "<PERSON><PERSON><PERSON> bir zaman değişkeni <PERSON>", "add_time": "<PERSON><PERSON> <PERSON>", "time_table": "Zaman tablosu", "at_least_two_times": "En az 2 zaman noktası girin", "use_earlier": "Önceki zamanları kullan", "enter_time": "<PERSON><PERSON> girin", "list": {"selected_variable_list": "<PERSON><PERSON><PERSON>", "column_list": "<PERSON><PERSON><PERSON>", "row_list": "<PERSON><PERSON><PERSON><PERSON>", "factor_list": "Faktörler/gruplar", "variable_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "split_list": "<PERSON><PERSON><PERSON><PERSON>", "pair_list": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "define_list": "Tan<PERSON><PERSON>lamalar", "dependent_list": "Bağımlı değişkenler", "covariate_list": "Kovaryatlar", "status_dependent_list": "Durum değişkeni", "status_list": "Durum değişkeni", "time_list": "<PERSON><PERSON>", "independent_list": "<PERSON>ğ<PERSON><PERSON><PERSON><PERSON>", "strata_list": "<PERSON><PERSON>"}, "definition": "Tanım", "en_definition": "İngilizce tanım", "tr_definition": "Türkçe tanım", "add_definition": "<PERSON><PERSON><PERSON>", "no_definition_added": "<PERSON><PERSON><PERSON>", "use_define_times": "Zaman tanımını seç", "report_settings": "<PERSON><PERSON>", "reporting_options": "<PERSON><PERSON>", "separator": "Ondalık ayracı", "comma": "Virg<PERSON><PERSON>", "dot": "Nokta", "precision": "Ondalık basamak", "preview": "<PERSON><PERSON><PERSON><PERSON>", "preview_number": "Seçili formatta örnek sayı", "data_is_preparing": "Veriler hazırlanıyor...", "file_preparing": "<PERSON><PERSON>a <PERSON>ıyo<PERSON>...", "fetching_data": "Veriler yükleniyor...", "coming_soon": "Yakında", "clear": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "undo": "Değişiklikleri geri al", "remove": "Kaldır", "remove_defined": "Tanımı kaldır", "show": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "save": "<PERSON><PERSON>", "select": "Seç", "select_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "back": "<PERSON><PERSON>", "forward": "İleri", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "review": "<PERSON><PERSON><PERSON>", "filter": "Filtrele", "split": "<PERSON><PERSON>ır", "show_balance": "Bakiye detaylarını göster", "balance": "Bakiye", "buy_analysis": "<PERSON><PERSON><PERSON> k<PERSON>n al", "credit_used": "kredi kull<PERSON>ı<PERSON>ı", "options": "Seçenekler", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "language": "Dil", "reports": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "download_all_reports": "Tümünü indir", "search_analyses": "<PERSON><PERSON><PERSON> ara", "search_variable": "Değişken ara", "variable": "Değişken", "variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "reference": "Referans", "reference_value": "<PERSON><PERSON><PERSON>", "selected": "Seçildi", "timer": "Zamanlayıcı", "times": "Zamanlar", "all_fields_are_required": "<PERSON><PERSON><PERSON> al<PERSON>", "no_value_label": "<PERSON><PERSON><PERSON> et<PERSON> yok", "example": "Örnek", "show_dataset": "<PERSON><PERSON><PERSON>", "definitions": "Değişken tanımları", "analysis_reports": "<PERSON><PERSON><PERSON>", "according_to_row": "Satıra göre grupla", "according_to_column": "Sütuna göre grupla", "time": "Zaman noktası", "use_times": "Zaman noktalarını kullan", "delete": "<PERSON><PERSON><PERSON>", "edit_report_title": "<PERSON>or Başlığını Düzenle", "new": "<PERSON><PERSON>", "add_remove_favorites": "<PERSON><PERSON><PERSON> ekle veya kaldır", "edit_report": {"title": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> adı", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "description_placeholder": "<PERSON><PERSON>", "optional": "İsteğe bağlı", "update": "<PERSON><PERSON><PERSON><PERSON>"}, "no_description": "Açıklama yok"}, "analyses_type_list": {"descriptive": "Tanımlayıcı İstatistikler", "descriptive_info": "Değişkenlerinizin dağılım özelliklerini ve merkezi eğilimlerini analiz edin", "independent": "Bağımsız Veri Analizi", "single": "Bağımsız Veri Analizi", "single_info": "Ölçülen değerlerinizi bilinen referans veya literatür değerleriyle ka<PERSON>şılaştırın", "single_i": "Tek Grup", "multi": "Çoklu Grup Veri Analizi", "multi_info": "İki veya daha fazla bağımsız grup arasındaki nicel verileri karşılaştırın", "multi_i": "Çoklu Grup", "correlation": "Korelas<PERSON>", "correlation_info": "Değişkenleriniz arasındaki ilişkileri ve örüntüleri keşfedin", "chisq": "<PERSON><PERSON><PERSON><PERSON>", "chisq_info": "Kategorik değişkenler arasındaki ilişkileri analiz edin", "dependent": "Bağımlı Veri Analizi", "dependent_info": "Aynı grubun zaman içindeki tekrarlı ölçümlerini karşılaştırın", "comean": "Ortalama <PERSON>şılaştırma", "comean_info": "İki veya daha fazla bağımsız grup arasındaki ortalama farklılıkları analiz edin", "logistic_cox": "Lojistik/Cox Regresyon", "logistic_cox_info": "İkili sonuç değişkenleri için olasılık modellemesi ve sağkalım analizi yapın", "survival": "Sağ<PERSON><PERSON><PERSON>", "survival_info": "Belirli bir olayın gerçekleşme süresini analiz edin", "roc": "ROC Analizi", "roc_info": "Tanı testlerinin doğruluğunu değerlendirin ve eşik değerlerini optimize edin", "linear": "Doğrusal Regresyon", "linear_info": "Bağımlı değişken ile bir veya daha fazla bağımsız değişken arasındaki ilişkiyi modelleyin", "r_s_r": "Regresyon/sağkalım/ROC", "create_analyse": "<PERSON><PERSON><PERSON>", "select": "Seç", "close": "Ka<PERSON><PERSON>", "requirements": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dataset_view": {"rows": "<PERSON>ır", "variables": "değişken", "export": "Dışa Aktar", "close": "Ka<PERSON><PERSON>", "try_again": "<PERSON><PERSON><PERSON>", "error_title": "Veri seti yüklenir<PERSON> bir hata o<PERSON>.", "error_description": "<PERSON><PERSON> setini görüntülemek için tekrar deneyin veya destek ekibiyle iletişime geçin.", "data_view": "<PERSON><PERSON>", "variable_view": "Değişken Görünümü", "search_placeholder": "Ara...", "search_variable_placeholder": "Değişken ara...", "filter": "Filtrele", "filter_options": {"all_variables": "<PERSON><PERSON><PERSON>", "scale": "Scale Değişkenler", "nominal": "Nominal Değişkenler", "ordinal": "Ordinal Deği<PERSON>ler"}, "search_results": {"showing": "Gösteriliyor {{filtered}} / {{total}} satır", "not_found_title": "<PERSON><PERSON>ç <PERSON>namadı", "not_found_description": "Arama kriterlerinize uygun değişken bulunamadı. Lütfen farklı bir arama terimi deneyin veya filtreleri temizleyin."}, "clear_filters": "<PERSON><PERSON><PERSON> filtrel<PERSON> te<PERSON>", "search": "Arama:", "filter_column": "Filtre:", "variable": {"label_tr": "Etiket (TR)", "label_en": "Etiket (EN)", "value_labels": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "missing": "kayıp"}}, "settings": {"invoice": "<PERSON><PERSON>", "credit": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON>", "logout": "Çıkış Yap", "product": "<PERSON><PERSON><PERSON><PERSON>", "dataset": "<PERSON><PERSON>", "status": "Durum", "date": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "canceled": "İptal Edildi", "approved": "Onaylandı", "pending": "<PERSON><PERSON>", "single": "Tek Grup", "multi": "Çok Grup", "correlation": "Korelas<PERSON>", "chisq": "<PERSON><PERSON><PERSON><PERSON>", "descriptive": "Tanımlayıcı İstatistik", "dependent": "Bağımlı Veri Analizi", "logistic_cox": "Lojistik/Cox Regresyon", "survival": "Sağ<PERSON><PERSON><PERSON>", "roc": "ROC Analizi", "linear": "Doğrusal Regresyon", "no_purchase": "Henüz Satın Alım <PERSON>adınız.", "more": "daha", "cookie": "Çerez Politikası", "membership_agreement": "Üyelik Sözleşmesi", "privacy_policy": "Gizlilik Politikası", "purchase_history": "Satın Alma Geçmişi", "agreement_text": "Belirtilen kurallar çerçevesinde hizmet sunulmaktadır."}, "profile": {"delete_account": "Hesabı Sil", "account": "<PERSON><PERSON><PERSON>", "account_title": "<PERSON><PERSON><PERSON>", "purchase_history_title": "Satın Alma Geçmişi", "language_title": "<PERSON><PERSON>", "title": "<PERSON><PERSON> 👋", "settings": "<PERSON><PERSON><PERSON>", "language": "Dil", "logout": "Çıkış Yap", "purchase_history": "Satın Alma Geçmişi", "admin_panel": "<PERSON><PERSON>", "corporate": "<PERSON><PERSON><PERSON>", "language_updated_success": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "language_updated_error": "<PERSON><PERSON> güncellenirken bir hata o<PERSON>", "personal_info": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ad Soyad", "email": "E-posta", "phone": "Telefon", "member_since": "<PERSON><PERSON><PERSON>", "gift_credit": "Hediye <PERSON>i", "gift_credit_pref": "Hediye Kredi Alma Tercihi", "gift_credit_desc": "<PERSON><PERSON>er kullanıcıların size hediye kredi gönderip gönderemeyeceğini seçin.", "gift_pref_ok": "Hediye kredi alma aktif.", "gift_pref_no": "Hediye kredi alma kapalı.", "change_pref_ok": "Hediye almayı aç", "change_pref_no": "Hediye almayı kapat", "academic_info": "Akademik Bilgiler", "university": "Üniversite", "university_placeholder": "Üniversite adını giriniz", "faculty": "Fakülte", "faculty_placeholder": "Fakülte adını giriniz", "department": "Bölüm", "department_placeholder": "Bölüm adını giriniz", "interests": "<PERSON><PERSON><PERSON>", "interests_placeholder": "<PERSON><PERSON><PERSON>ınızı virgülle ayırarak yazınız (örn: veri bi<PERSON>, makine <PERSON>, ya<PERSON>y <PERSON>)", "profile_save_success": "Profil bilgileri başarıyla kaydedildi", "profile_save_error": "Profil bilgileri kaydedilirken bir hata o<PERSON>"}, "notification": {"auth": {"login": {"success": {"title": "Hoşgeldiniz!", "message": "Giriş Başarılı"}, "info": {"title": "<PERSON><PERSON><PERSON>", "already_logged_in": "Zaten giriş yapmışsınız."}, "error": {"title": "Hata!", "message": {"account_deleted": "<PERSON><PERSON><PERSON>.", "email": "E-posta adresi bo<PERSON> o<PERSON>.", "password": "<PERSON><PERSON><PERSON> boş o<PERSON>az.", "invalid": "Geçersiz e-posta veya şifre.", "unauthorized": "Yetkisiz kullanıcı.", "forbidden": "Yasaklanmış kullanıcı.", "notfound": "Kullanıcı bulunamadı.", "unauthenticated": "<PERSON><PERSON><PERSON>.", "switched_user_login_successful": "Kullanıcı değiştirme girişi başarılı.", "target_user_not_found": "<PERSON><PERSON><PERSON> bulunamadı.", "email_taken": "E-posta zaten alınmış.", "phone_taken": "Telefon numarası zaten alınmış.", "email_phone_taken": "E-posta ve telefon numarası zaten alınmış.", "user_doesnt_exist": "Kullanıcı Bulunamadı.", "invalid_email_or_password": "Geçersiz e-posta adresi veya şifre.", "banned": "Hesabınıza er<PERSON><PERSON><PERSON> k<PERSON>ı<PERSON>mıştır. Daha fazla bilgi için <EMAIL> ile iletişime geçebilirsiniz."}}}, "logout": {"success": {"title": "Hoşçakal!", "message": "Çıkış başarılı."}, "error": {"title": "Çıkış Hatası!", "message": "<PERSON>ık<PERSON>ş yapılırken hata. Ana sayfaya yönlendiriliyorsunuz."}}}, "analyses_process": {"success": {"title": "<PERSON><PERSON>z <PERSON>amlandı!", "message": "<PERSON><PERSON><PERSON> başarıyla tamamlandı."}, "error": {"title": "<PERSON><PERSON><PERSON>ı!", "message": "<PERSON><PERSON><PERSON> sı<PERSON>ında bir hata oluştu."}}, "analysis": {"report_position": {"title": "Başarılı!", "success": "Raporunuzun sırası değişti"}, "report_favorite": {"title": "Başarılı!", "added": "<PERSON><PERSON>", "removed": "<PERSON><PERSON> kaldırıldı", "error": {"title": "Hata!", "message": "<PERSON>ir hata o<PERSON>."}}, "report_conf": {"success": {"title": "Rapor Ayarları Kaydedildi!"}, "error": {"title": "Rapor Ayarları Hatası!", "message": "<PERSON><PERSON> a<PERSON>ları kaydedilirken bir hata oluştu."}}, "report_delete": {"success": {"title": "<PERSON><PERSON>!", "message": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON> bir hata <PERSON>."}}, "report_update": {"success": {"title": "<PERSON><PERSON>!"}, "error": {"title": "Hata!", "message": "<PERSON><PERSON> bir hata <PERSON>."}}, "submit": {"success": {"title": "<PERSON><PERSON><PERSON>!", "message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>ı."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON><PERSON> bir hata oluş<PERSON>."}, "request_failed": {"title": "Hata!", "message": "<PERSON><PERSON><PERSON> oluşturulurken bir hata oluştu."}}, "general": {"error": {"title": "Hata!", "message": "<PERSON><PERSON><PERSON> bir hata oluş<PERSON>."}}}, "project": {"position": {"title": "Başarılı!", "success": "Projenizin sırası değişti"}, "favorite": {"title": "Başarılı!", "error_title": "Hata!", "added": "<PERSON><PERSON>", "removed": "Proje <PERSON>den kaldırıldı", "error": "<PERSON>ir hata o<PERSON>"}, "update_description": {"success": {"title": "Başarılı!", "message": "Açıklama güncellendi."}, "error": {"title": "Hata!", "message": "Açıklama güncellenirken bir hata oluştu."}}, "update_name": {"success": {"title": "Başarılı!", "message": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON> g<PERSON>en bir hata o<PERSON>."}}, "clone": {"success": {"title": "Başarılı!", "message": "<PERSON>je başarıyla klonlandı."}, "error": {"title": "Hata!", "message": "<PERSON>je k<PERSON>lanırken bir hata oluştu."}}, "recalculate": {"success": {"title": "Başarılı!", "message": "<PERSON>je rap<PERSON> başarıyla yeniden hesaplandı."}, "error": {"title": "Hata!", "message": "<PERSON>je rap<PERSON>ı yeniden hesaplanırken bir hata oluştu."}}, "compare": {"success": {"title": "Başarılı!", "message": "<PERSON>je ba<PERSON><PERSON><PERSON><PERSON> karşılaştırıldı."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON>ştırılırken bir hata oluştu."}}, "delete": {"success": {"title": "Başarılı!", "message": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON> si<PERSON>en bir hata <PERSON>."}}}, "settings": {"delete_account": {"success": {"title": "Başarılı!", "message": "Hesab<PERSON><PERSON><PERSON>z ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON><PERSON> silme i<PERSON><PERSON>i sı<PERSON>ında bir hata oluş<PERSON>."}}}, "payment": {"success": {"title": "Başarılı!", "message": "Ödeme işlemi başarılı."}, "error": {"title": "Hata!", "message": "Ödeme işlemi sırasında bir hata oluştu."}, "rejected": {"title": "Uyarı!", "message": "Ödeme işlemi reddedildi."}, "canceled": {"title": "Uyarı!", "message": "Ödeme işlemi iptal edildi."}, "eft": {"success": {"title": "Başarılı!", "message": "Havale / EFT işlemi başarılı."}, "error": {"title": "Hata!", "message": "Havale / EFT işlemi sırasında bir hata oluştu."}}, "coupon": {"success": {"title": "Başarılı!", "message": "Kupon kodu başarıyla uygulandı."}, "remove": {"title": "Başarılı!", "message": "Ku<PERSON>n kodu başarıyla kaldırıldı."}, "error": {"title": "Hata!", "message": "Kupon kodu uygulanırken bir hata oluştu."}}, "address_detail": {"success": {"title": "Başarılı!", "message": "<PERSON><PERSON> b<PERSON>şarıyla eklendi."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON> başlığı zaten kullanımda"}}, "address_select": {"success": {"title": "Başarılı!", "message": "<PERSON><PERSON> başarıyla seçildi."}}, "delete_address": {"success": {"title": "Başarılı!", "message": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON> si<PERSON>en bir hata <PERSON>."}}, "update_address": {"success": {"title": "Başarılı!", "message": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> g<PERSON>."}, "error": {"title": "Hata!", "message": "<PERSON><PERSON> gü<PERSON>llenirken bir hata o<PERSON>."}}, "contact_us": {"title": "Başarılı!", "message": "Talebiliniz alındı. En kısa sürede size dönüş yapılacaktır."}}, "dataset": {"download": {"error": {"title": "<PERSON><PERSON>", "message": "<PERSON>eri seti indirilemedi"}}, "view": {"error": {"title": "Görüntü<PERSON><PERSON>", "message": "Veri seti yüklenir<PERSON> bir hata o<PERSON>"}}}, "report": {"download": {"error": {"title": "<PERSON><PERSON>", "message": "Raporlar indirilirken bir hata o<PERSON>"}}, "favorite": {"title": "Başarılı!", "added": "<PERSON><PERSON>", "removed": "<PERSON><PERSON> kaldırıldı"}}, "diagnose": {"error": {"title": "<PERSON><PERSON>", "message": "Veri seti analiz edilemedi"}}}, "404": {"pageNotFound": "Aradığınız sayfa verilerimiz arasında kaybolmuş gibi görünüyor.", "goBack": "ista<PERSON>'a dön"}, "shared": {"danger_zone": "Tehlikeli Bölge", "edit": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal", "save": "<PERSON><PERSON>", "credit_card_result": {"approved": "Ödeme işleminiz başarıyla gerçekleşmiştir.", "declined": "Ödeme işleminiz başarısız olmuştur.", "info": "Bu alanı kapatabilirsiniz.", "close": "Tamamlandı"}, "confirm": {"delete_project": {"title": "<PERSON>jeyi silmek istediğinize emin mi<PERSON>?", "content": "Proje silinecek. Bu işlem geri alınamaz.", "confirm": "Sil", "cancel": "İptal"}, "changes": {"title": "Veri düzenlemeyi kapatmak istediğinize emin misiniz?", "content": "Yaptığınız değişiklikler kaydedilmemiş olacak. Devam etmek istediğinize emin misiniz?", "confirm": "<PERSON><PERSON>", "cancel": "İptal"}, "diagnose": {"title": "Değişiklikleri sonlandırmak istediğinizden emin misiniz?", "content": "Veri setinizi istabot için hazır hale getirmek için sonlandırabilirsiniz. Bu işlem geri alınamaz.", "confirm": "Sonlandır", "cancel": "İptal"}, "reset": {"title": "Yaptığı<PERSON><PERSON>z değişiklikleri sıfırlamak istediğinize emin misiniz?", "content": " ", "confirm": "Sıfırla", "cancel": "İptal"}, "close_payment": {"title": "Sepetinizde ürün bulunmaktadır.", "content": "Satın alma işlemini tamamlamadan çıkış yapmak istediğinize emin misiniz?", "confirm": "Çıkış", "cancel": "İptal"}, "credit_info": {"title": "Bu analizi oluşturmak için {{credits}} kredi gereklidir."}, "analyses_process": {"title": "Yetersiz Bakiye!", "content": "Bu analizi yapmak için yeterli bakiyen bulunamadı. Analizin yapılacak ancak raporu ödeme yapılana kadar göremeyeceksin, ödeme yapılınca da bakiyenden düşülecek. Devam etmek istediğine emin misin?", "confirm": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>"}, "delete_account": {"title": "Hesabınızı silmek istediğinize emin misiniz?", "content": "Hesabınızı silmek istediğinizde, tüm verileriniz ve projeleriniz kalıcı olarak silinecektir. Bu işlem geri alınamaz.", "confirm": "Hesabı Sil", "cancel": "İptal"}, "create_project": {"title": "Kapatmak istediğinize emin misin<PERSON>?", "content": "Yaptığınız değişiklikler kaydedilmemiş olacak. Devam etmek istediğinize emin misiniz?", "confirm": "<PERSON><PERSON>", "cancel": "İptal"}, "zero_eft_payment": {"title": "Uyarı!", "content": "Sepet tutarınız 0 TL olduğu için ödeme işlemi gerçekleştirilemeyecektir. EFT işlemi yaptıysanız gerekli kontroller yapılarak geri dönüş sağlanacaktır. Eğer bir sorunla karşılaşırsanız lütfen bizimle iletişime geçiniz.(<EMAIL>)", "cancel": "<PERSON><PERSON><PERSON><PERSON>"}, "report_delete": {"title": "<PERSON><PERSON><PERSON> silm<PERSON> istediğinize emin misin<PERSON>?", "content": "Rapor silinecek. Bu işlem geri alınamaz.", "confirm": "Sil", "cancel": "İptal"}, "close_eft": {"title": "Uyarı!", "content": "Yüklediğiniz bir dekont var. EFT işleminizi tamamlamadan çıkış yapmak istediğinize emin misiniz?", "confirm": "Çıkış", "cancel": "İptal"}, "delete_address": {"title": "<PERSON><PERSON><PERSON> silmek istediğinize emin misin<PERSON>?", "content": "Adres silinecek. Bu işlem geri alınamaz.", "confirm": "Sil", "cancel": "İptal"}, "edit_address": {"title": "Adresi düzenlemek istediğinize emin misiniz?", "content": "Adres düzenlenecek. Bu işlem geri alınamaz.", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal"}, "remove_variable": {"title": " {{variable}} Değişkeni kaldırmak istediğinize emin misiniz? Daha sonra tekrar ekleyebilirsiniz.", "confirm": "Kaldır", "cancel": "İptal"}, "clone_analysis_cancel": {"title": "<PERSON><PERSON><PERSON> işlemini iptal etmek istediğinize emin misin<PERSON>?", "content": "Kopyalanan bilgiler kaybolacak. Bu işlem geri alınamaz.", "confirm": "İptal Et", "cancel": "İptal"}, "clear_cart": {"title": "Sepeti temizlemek istediğinize emin misiniz?", "content": "Sepetinizdeki tüm ürünler silinecek. Bu işlem geri alınamaz.", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "İptal"}, "clear_cart_item": {"title": "Ürünü sepetten kaldırmak istediğinize emin misiniz?", "content": "Ürün sepetten kaldırılacak. Bu işlem geri alınamaz.", "confirm": "Kaldır", "cancel": "İptal"}, "project_detail": {"settings": {"delete_project": {"title": "<PERSON><PERSON><PERSON> silmek istiyor musunuz?", "content": "Bu işlem projeyi ve tüm verilerini kalıcı olarak silecektir", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}}}, "back_with_coupon": {"title": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON>, uygulanmış kuponunuz kaybolacak ve tekrar girmeniz gerekecek. Devam etmek istediğinizden emin misiniz?", "confirm": "<PERSON><PERSON>, geri dön", "cancel": "Hay<PERSON>r, burada kal"}, "close_analysis": {"title": "<PERSON><PERSON><PERSON> kapat<PERSON>k istiyor musunuz?", "content": "Kaydedilmemiş değişiklikleriniz var. Şimdi ka<PERSON>ı<PERSON>nız, değişiklikleriniz kaybolacak.", "confirm": "<PERSON><PERSON>", "cancel": "Düzenlemeye devam et"}}, "create_project": {"new_project": "Yeni proje oluştur", "update_project": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON> adı", "name_placeholder": "<PERSON><PERSON> adını girin", "name_is_required": "Lütfen bir proje adı girin", "dataset": "<PERSON><PERSON> seti", "optional": "İsteğe bağlı", "upload_dataset": "<PERSON><PERSON> set<PERSON>", "or_drag_drop": "veya s<PERSON><PERSON><PERSON><PERSON> bırak", "excel_only": "Sadece Excel dosyaları", "creating": "Oluşturuluyor...", "updating": "Güncelleniyor...", "how_to_create": "Proje Na<PERSON>ıl Oluşturulur?", "upload_success": "Veri seti başar<PERSON><PERSON> yü<PERSON>", "try_again": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lütfen tekrar deneyin", "table_example": "Örnek veri seti", "example_info": "Analizinize başlamak için bu örnek veri setini indirebilirsiniz", "download": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "file_processing_error": "<PERSON><PERSON>a i<PERSON>lenirk<PERSON> bir hata o<PERSON>", "create": "Oluştur", "success": "Başarılı", "error": "Yükleme başarısız", "error_message": "Lütfen tekrar deneyin", "create_message": "<PERSON><PERSON>", "diagnose_message": "Veri seti analiz için hazırlanıyor...", "update_message": "<PERSON><PERSON>", "project_creating": "<PERSON>je oluşturuluyor...", "errors": {"string_values": "Bu sütun sayı<PERSON> değ<PERSON>ler beklenirken metin değerleri içeriyor", "separator": "Bu sütun birden fazla ondalık ayracı içeriyor", "server_side": "İşlem yapılamadı. Lütfen tekrar deneyin", "remove_columns": "İçe aktarılmayan sütunlar kaldırılamadı", "s3_upload": "Dosya <PERSON>üklenemedi. Lütfen tekrar deneyin"}}, "value-label": {"value_labels": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "labels": "<PERSON><PERSON><PERSON><PERSON>", "revert": "Değişiklikleri geri al", "save": "Değişiklikleri kaydet", "show_labels": "Etiketleri göster", "saved_labels": "Kaydedilmiş etiketler", "empty_error": "Etiket alanı boş bırakılamaz", "use": "Kaydedilmiş etiketleri kullan"}, "balance": {"titles": "<PERSON><PERSON><PERSON>", "cells": {"total": "Toplam Krediler", "available": "Kullanılabilir", "used": "Kullanılan", "active_packs": "<PERSON><PERSON><PERSON>"}, "tabs": {"active_packages": "<PERSON><PERSON><PERSON>", "usage_history": "Geçmiş Kullanımlar"}, "credits": "<PERSON><PERSON><PERSON>", "available": "Kullanılabilir:", "expire": "<PERSON>:", "buy_credits": "Kredi Satın Al"}, "payment": {"helper_video": "Yardım Videosu İzle", "title": "Analiz Kredisi Satın Al", "coupon_placeholder": "Kupon Kodu Gir", "complete_payment": "<PERSON><PERSON><PERSON><PERSON>", "to_online_payment": "Online Ödemeye Devam Et", "to_eft": "Havale/EFT Bilgilerini Görüntüle", "defined_coupons": "Tanımlı Kuponlar", "accept_payment": "Ödemeyi Bildir", "continue_payment": "Ödeme Ekranına Git", "continue_to_payment": "Ödemeye devam et", "step_descriptions": {"step1": "Analizleriniz için kredi paketlerimizden dilediğinizi seçebilirsiniz.", "step2": "Fatura bilgilerinizi seçin veya yeni bir adres e<PERSON>in.", "step3": "Siparişinizi inceleyip onaylayın ve ödeme adımına geçin."}, "steps": {"step1": "<PERSON><PERSON>", "step2": "<PERSON><PERSON>", "step3": "Özet"}, "buttons": {"continue": "İleri", "back": "<PERSON><PERSON>", "complete_free_order": "Ücretsiz Siparişi Tamamla", "continue_to_payment": "Ödemeye Devam Et", "view_eft_details": "EFT Bilgilerini Görüntüle"}, "package_selection": {"packages": "<PERSON><PERSON><PERSON>", "flexible": "Esnek", "credits": "<PERSON><PERSON><PERSON>", "credit": "kredi", "credits_label": "kredi", "corporate_custom": "<PERSON><PERSON><PERSON><PERSON>", "discount": "İndirim", "credit_amount": "<PERSON><PERSON><PERSON>", "credit_placeholder": "Min: 1, Max: 1000", "min_credit_error": "Minimum 1 kredi seçmelisiniz", "max_credit_error": "Toplam sepet 1000 krediyi aşamaz (Maks: {{max}} kredi seçebilirsiniz)", "total": "Toplam", "details": "Detaylar", "add": "<PERSON><PERSON>", "contact_us": "İletişime Geç", "credit_calculator": "Analizim kaç krediye ihtiyaç duyacak?", "cart_summary": "<PERSON><PERSON> Ö<PERSON>ti", "selected_packages": "<PERSON><PERSON><PERSON><PERSON>", "piece": "adet", "total_credits": "Toplam Kredi"}, "billing_info": {"billing_addresses": "<PERSON><PERSON>", "add_new_address": "<PERSON><PERSON>", "individual": "<PERSON><PERSON><PERSON><PERSON>", "corporate": "<PERSON><PERSON><PERSON>", "default": "Varsayılan", "tax_no": "Vergi No", "tax_office": "V.D.", "cart_summary": "<PERSON><PERSON> Ö<PERSON>ti", "selected_packages": "<PERSON><PERSON><PERSON><PERSON>", "credits": "kredi", "credits_label": "<PERSON><PERSON><PERSON>", "piece": "adet", "total_credits": "Toplam Kredi", "total": "Toplam", "confirm_delete_address": "Bu adresi silmek istediğinizden emin misiniz?"}, "package_details": {"details": "Detayları", "credit_amount": "<PERSON><PERSON><PERSON>", "credits": "<PERSON><PERSON><PERSON>", "min_credit_error": "Minimum 1 kredi seçmelisiniz", "max_credit_error": "Toplam sepet 1000 krediyi aşamaz (Maks: {max} kredi)", "price": "<PERSON><PERSON><PERSON>", "validity_period_title": "<PERSON><PERSON><PERSON>m Süresi", "validity_period_desc": "Satın alım tarihinden itibaren 2 yıl geçerlidir", "report_languages_title": "<PERSON><PERSON>", "report_languages_desc": "Türkçe ve İngilizce dillerinde rapor alabilirsiniz", "report_export_title": "<PERSON><PERSON>", "report_export_desc": "Sın<PERSON><PERSON><PERSON><PERSON> sayıda rapor indirme hakkı", "repurchase_title": "Tekrar <PERSON>ı<PERSON>", "repurchase_desc": "Dilediğiniz zaman aynı paketten tekrar satın alabilirsiniz", "corporate_support_title": "<PERSON><PERSON><PERSON>", "corporate_support_desc": "Öncelikli destek ve özel danışmanlık hizmeti", "close": "Ka<PERSON><PERSON>", "add_to_cart": "Sepete Ekle", "contact_us": "İletişime Geç"}, "payment_summary": {"selected_packages": "<PERSON><PERSON><PERSON><PERSON>", "credits": "kredi", "products_total": "Ürünler Toplamı", "discount": "İndirim", "total": "Toplam", "billing_address": "<PERSON><PERSON>", "individual": "<PERSON><PERSON><PERSON><PERSON>", "corporate": "<PERSON><PERSON><PERSON>", "tax_no": "Vergi No", "coupon_code": "<PERSON><PERSON><PERSON>", "coupon_placeholder": "Kupon kodunuz", "apply": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Kaldır", "payment_method": "<PERSON><PERSON><PERSON>", "credit_card": "<PERSON><PERSON><PERSON>", "bank_transfer": "EFT/Havale", "credit_card_info": "Siparişi onayladıktan sonra Akbank güvenli ödeme sayfasına yönlendirileceksiniz.", "bank_transfer_info": "Siparişi onayladıktan sonra havale/EFT bilgilerini göreceksiniz. Ödeme onaylandıktan sonra kredileriniz tanımlanacaktır.", "payment_info": "<PERSON><PERSON><PERSON>", "free_order": "Ücretsiz Sipariş", "free_order_info": "Kupon kodunuz sayesinde sipariş tutarınız 0₺ olduğu için ödeme işlemi gerekmemektedir.", "agreement_text": "Mesafeli Satış Sözleşmesi'ni ve Kullanım Koşulları'nı kabul ediyorum."}, "address_detail": {"update_title": "<PERSON><PERSON><PERSON>", "new_title": "<PERSON><PERSON>", "desc": "<PERSON><PERSON> <PERSON>, fatura düzenleme işlemleri için kullanılacaktır. Lütfen tüm alanlar doğru ve eksiksiz giriniz.", "address_type": "<PERSON><PERSON>", "individual": "<PERSON><PERSON><PERSON><PERSON>", "company_name": "Şirket/Kurum Adı", "company_name_placeholder": "Şirket/Kurum Adı Giriniz", "company_name_required": "Şirket/Kurum Adı alanı boş bırakılamaz.", "corporate": "<PERSON><PERSON><PERSON>", "address_type_required": "Adres Türü alanı boş bırakılamaz.", "select_address_type": "<PERSON>res Türü <PERSON>", "address_title": "<PERSON><PERSON> Başlığı", "address_title_placeholder": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "address_title_required": "Adres Başlığı alanı boş bırakılamaz.", "full_name": "Ad Soyad", "full_name_placeholder": "Ad Soyad G<PERSON>", "full_name_required": "Ad Soyad alanı boş bırakılamaz.", "city": "İl", "city_placeholder": "<PERSON>l Giri<PERSON>z", "city_required": "İl alanı boş bırakılamaz.", "district": "İlçe", "district_placeholder": "İlçe Giriniz", "district_required": "İlçe alanı boş bırakılamaz.", "address": "<PERSON><PERSON>", "address_placeholder": "<PERSON><PERSON>", "address_required": "<PERSON><PERSON> alanı boş bırakılamaz.", "tax_office": "<PERSON><PERSON><PERSON>", "tax_office_placeholder": "<PERSON><PERSON><PERSON>", "tax_office_required": "Vergi Dairesi alanı boş bırakılamaz.", "tax_number": "<PERSON><PERSON>gi <PERSON>", "tax_number_placeholder": "Vergi Kimlik Numarası Giriniz", "tax_number_required": "Vergi Numarası alanı boş bırakılamaz.", "close": "Vazgeç", "save": "<PERSON><PERSON>"}, "eft_detail": {"title": "Havale / EFT Detayları", "eft_info": "Havale / EFT yöntemi ile ödeme yapılacak hesap bilgisi ve transfer açıklaması aşağıda verilmiştir.", "eft_subinfo": "Havale / EFT yöntemi ile ödemenizi yaptıktan sonra dekontunuzun resmini aşağı<PERSON> b<PERSON><PERSON><PERSON>me yükleyiniz.", "price": "Aktarılacak Tutar", "account_name": "<PERSON><PERSON><PERSON>", "bank_name": "Banka Adı", "branch_value": "19 Mayıs Üniversitesi Şubesi (01389)", "branch": "Şube", "img_info": "Değiştirmek için tıklayın", "upload_receipt": "<PERSON>kon<PERSON> Yü<PERSON>", "account_number": "<PERSON><PERSON><PERSON>", "iban": "IBAN", "transfer_description": "Açıklama bölümünü boş bırakınız.", "send": "Ödeme Bildirimini <PERSON>", "click_to_change": "Değiştirmek için tıklayın", "sending": "Ödeme Bildiriliyor"}, "contact": {"title": "Bizimle İletişime Geçin", "description": " Kurumsal paketimiz hakkında daha fazla bilgi almak için lütfen aşağıdaki formu doldurun.", "name": "Ad Soyad", "name_input": "Ad Soyad G<PERSON>", "name_required": "Ad Soyad alanı boş bırakılamaz.", "email": "E-posta", "email_input": "E-posta Giri<PERSON>z", "email_required": "E-posta alanı boş bırakılamaz.", "phone": "Telefon Numarası (Opsiyonel)", "phone_input": "Telefon Numarası Giriniz", "company": "Şirket Adı", "company_input": "Şirket Adı Giriniz", "company_required": "Şirket Adı alanı boş bırakılamaz.", "message": "Mesajınız", "message_input": "İhtiyaçlarınızı veya sorularınızı belirtin", "send": "<PERSON><PERSON><PERSON>"}, "calculate_credit": {"title": "<PERSON><PERSON>z Kredi Miktarını Hesapla", "descriptive": "Tanımlayıcı İstatistik", "independent": "Bağımsız Veri Analizi", "dependent": "Bağımlı Veri Analizi", "single": "Tek Grup", "multi": "Çok Grup", "correlation": "Korelas<PERSON>", "chisq": "<PERSON>", "variable_count_input": "Değişken sayısı giriniz", "factor_count_input": "Faktör/Grup sayısı giriniz", "row_variable_count_input": "Satır değişken sayısı giriniz", "col_variable_count_input": "Sütun değişken sayısı giriniz", "use_split_variable": "Split değişkeni kullanacağım", "split_label_count": "Split değişkeni etiket sayısı giriniz", "clear": "<PERSON><PERSON><PERSON>", "total_credit": " Toplam Kredi", "save": "<PERSON><PERSON>", "clear_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "validation_messages": {"dataset_empty": "Veri seti en az bir değişken içermelidir", "insufficient_rows": "Veri seti en az 2 satır veri içermelidir", "invalid_labels": "Değişken etiketleri (EN/TR) ve Değer etiketleri sayısal olmamalı ve ¨&¨ karakteri içermemelidir", "invalid_value_labels": "Değer etiketleri hem EN hem TR için dolu olmalı ve ¨&¨ karakteri içermemelidir", "invalid_scale_data": "Ölçek değişkenleri sadece sayısal değerler içermelidir", "missing_values": "Değişken(ler) eksik değerler içeriyor. Eksik değerlerin analize dahil edilmesi için geçerli bir değer giriniz", "duplicate_values": "<PERSON><PERSON><PERSON><PERSON><PERSON>(ler) tekrar eden değerler içeriyor", "inconsistent_format": "Değişken(ler) tutarsız veri formatları içeriyor", "unique_value_labels": "Değer etiketleri benzersiz olmalıdır", "non_numeric_data": "Değişken(ler) sayısal olmayan veriler içeriyor. Lütfen verilerinizi uygun şekilde yeniden kodlayın.", "no_issues_found": "<PERSON><PERSON>", "validation_details": {"title": "Doğrulama Detayları", "warnings": "Uyarı", "errors": "<PERSON><PERSON>", "issue_explanation": "<PERSON><PERSON>ı<PERSON><PERSON>", "how_to_fix": "Nasıl Düzeltilir", "affected_variables": "Etkilenen Değişkenler", "show_details": "Detayları Göster", "hide_details": "Detayları Gizle", "edit": "<PERSON><PERSON><PERSON><PERSON>", "no_issues_title": "Tüm Değişkenler İyi Görünüyor!", "no_issues_description": "Veri setinizde doğrulama sorunu yok. Analizinize devam etmeye hazırsınız.", "data_quality_tips": "Veri Kalitesi <PERSON>ı", "tip_consistent_format": "Değişkenler içinde veri formatlarını tutarlı tutun", "tip_descriptive_labels": "Değişkenler ve değerler için net, açıklayıcı etiketler kullanın", "tip_handle_missing": "Analizden önce eksik değerlerin nasıl ele alınacağını düşünün", "tip_variable_types": "Değişken türlerinin (Ölçek, Nominal, Sıralı) verilerinizle eşleştiğinden emin olun"}, "issue_explanations": {"dataset_empty": "Veri setiniz herhangi bir değişken içermiyor. Geçerli bir veri setinde en az bir değişken bulunmalıdır.", "insufficient_rows": "Veri setinizin düzgün şekilde analiz edilebilmesi için en az 2 satır veri içermesi gerekir. İlk satır başlıklar için kullanıldığından, en az bir ek satır gerçek veri içermelidir.", "invalid_labels": "Değişken etiketleri sayısal olmamalı ve '&' gibi özel karakterler içermemelidir. <PERSON><PERSON><PERSON><PERSON>, değişkenlerinizin insan tarafından okunabilir bir açıklamasını sağlamak için kullanılır.", "invalid_value_labels": "Kategorik <PERSON>ler (Nominal/Ordinal) için değer etiketleri hem İngilizce hem de Türkçe için boş olmamalı ve doğru biçimlendirilmiş olmalıdır. Her değerin karşılık gelen bir etiketi olmalı ve etiketler '&' karakterini içermemelidir. Değer etiketleri sayısal olamaz.", "unique_value_labels": "Bir değişken içindeki değer etiketleri benzersiz olmalıdır. Farklı değerler için tekrarlanan etiketleriniz var, bu da analiz ve raporlamada karışıklığa neden olabilir.", "invalid_scale_data": "Ölçek değişkenleri yalnızca sayısal değerler içermelidir. <PERSON><PERSON>, tarihler veya diğer sayısal olmayan veriler sayısal hesaplamalarda kullanılamaz.", "non_numeric_data": "Bu değişken sayısal olmayan veriler içeriyor ancak analiz için sayısal değerler içermesi gerekiyor.", "missing_values": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>in verilerinde b<PERSON><PERSON> (eksik <PERSON>) var. <PERSON><PERSON><PERSON>, düzg<PERSON>n şekilde ele alınmazsa analiz sonuçlarını etkileyebilir.", "inconsistent_format": "Bu değişken tutarsız <PERSON>a (<PERSON><PERSON><PERSON><PERSON>, metin ve sayıların karışımı) veriler içeriyor, bu da analiz sorunlarına neden olabilir.", "missing_value_labels": "Bu kategorik değişkende (Nominal/Ordinal) değer etiketleri eksik. Kategorik değiş<PERSON>lerdeki her değ<PERSON>n karşılık gelen bir etiketi olması gerekir."}, "issue_recommendations": {"dataset_empty": "Veri setinize değişkenler ekleyin veya en az bir değişken ve veri içeren yeni bir veri seti yükleyin.", "insufficient_rows": "Veri setinize daha fazla veri satırı ekleyin. Her analiz, ba<PERSON><PERSON><PERSON>k satırına ek olarak en az bir gerçek veri satırı gerektirir.", "invalid_labels": "Değişken Görünümü'nde değişken etiketlerini düzenleyin. Etiketlerin açıklayıcı metin (sadece sayı değil) olduğundan emin olun ve '&' karakteri kullanmaktan kaçının.", "invalid_value_labels": "Değişken Görünümü'nde, bu değ<PERSON><PERSON><PERSON> için Değer Etiketleri hücresine tıklayın ve her değer için uygun etiketler ekleyin. Her değer için hem İngilizce hem de Türkçe etiketlerin sağlandığından emin olun.", "unique_value_labels": "Değişken Görünümü'ndeki değer etiketlerinizi kontrol edin ve her etiketin benzersiz olduğundan emin olun. Birden fazla değer aynı kavramı temsil ediyorsa, bunları tek bir değere yeniden kodlamayı düşünün.", "invalid_scale_data": "Ya değişken türünü Ölçek'ten Nominal/Ordinal'e değiştirin ya da tüm değerlerin sayısal olduğundan emin olmak için verileri düzeltin. Metin değ<PERSON><PERSON>ini sayılara dönüştürmek için Değer Yeniden Kodlama aracını kullanabilirsiniz.", "non_numeric_data": "İstatistiksel analiz i<PERSON>, bu değerleri Değer Yeniden Kodlama aracını kullanarak sayılara dönüştürün veya değerler kategorileri temsil ediyorsa değişken türünü Nominal/Ordinal olarak değiştirin.", "missing_values": "Eksik değerleri şu şekillerde ele alabilirsiniz: 1) 'Eksik Değer Analizi' aracını kullanarak uygun değerlerle doldurma, 2) Eksik değerler içeren satırları filtreleme veya 3) Oldukları gibi bı<PERSON>, ancak bazı analizlerden hariç tutulabileceklerinin farkında olun.", "inconsistent_format": "Verileri gözden geçirin ve tüm değerlerin aynı formatı izlediğinden emin olun. Tutarsız değerleri belirlemek için Veri Görünümü'nü kullanın ve bunları manuel olarak veya Değer Yeniden Kodlama aracıyla düzeltin.", "missing_value_labels": "Değişken Görünümü'nde Değer Etiketleri hücresine tıklayarak değer etiketleri ekleyin. Verilerinizdeki her benzersiz değerin hem İngilizce hem de Türkçe karşılık gelen bir etiketi olmalıdır.", "generic_missing": "Veri setinizdeki eksik değerleri ele almak için 'Eksik Değer Analizi' aracını kullanmayı düşünün. <PERSON><PERSON><PERSON>, medyan veya mod doldurma gibi yöntemleri kullanarak değerleri doldurabilirsiniz.", "generic_invalid": "Verilerinizi Veri Görünümü'nde inceleyin ve gerektiği gibi düzeltmeler yapın. Biçimlendirme sorunlarını düzeltmek veya veri türleri arasında dönüşüm yapmak için Değer Yeniden Kodlama aracını kullanabilirsiniz.", "generic": "Bu sorunu çözmek için menüdeki Veri Yönetimi araçlarını kullanın. <PERSON><PERSON> devam ederse, kaynak verilerinizi değiştirmeniz ve veri setini yeniden yüklemeniz gerekebilir."}}, "value_label": {"title": "<PERSON><PERSON><PERSON>", "available_labels": "<PERSON><PERSON><PERSON>", "use_labels": "<PERSON><PERSON>tiket<PERSON>", "value": "<PERSON><PERSON><PERSON>", "label_en": "Etiket (EN)", "label_tr": "Etiket (TR)", "cancel": "İptal", "save": "<PERSON><PERSON>", "no_labels": "Kullanılabilir etiket bulunamadı", "messages": {"validation_error": "Lütfen değer etiketlerini kontrol ediniz"}}, "validation_details": {"title": "Doğrulama Detayları", "valid_variables": "<PERSON>eç<PERSON><PERSON>", "warnings": "Uyarı", "errors": "<PERSON><PERSON>", "filters": {"all_issues": "<PERSON><PERSON><PERSON>", "errors_only": "<PERSON><PERSON><PERSON>", "warnings_only": "<PERSON><PERSON><PERSON>"}, "no_issues_found": "<PERSON><PERSON>", "empty_states": {"all": "Görüntülenecek doğrulama sorunu bulunamadı.", "errors": "Mevcut veri setinde hata bulunamadı.", "warnings": "Mevcut veri setinde uyarı bulunamadı."}, "show_all_issues": "<PERSON><PERSON>m Sorunları Göster", "status": {"error": "<PERSON><PERSON>", "warning": "Uyarı"}, "total_variables": "Toplam Değişken", "total_valid_variables": "Toplam Geçerli Değişken", "not_imported": "İçe Aktarılmayan", "invalid_variables": "Geçersiz <PERSON>", "not_imported_variables": "İçe Aktarılmayan <PERSON>", "warning_message": "Geçersiz değişkenler içe aktarılmayacaktır.", "old_warning_message": {"line1": "Geçersiz değişkenler içe aktarma işleminden hariç tutulacak.", "line2": "Yalnızca geçerli değişkenlerle devam etmek istiyor musunuz?"}, "actions": {"cancel": "İptal", "continue": "<PERSON><PERSON>"}}, "diagnose": {"title": "<PERSON><PERSON>", "help_video": "Yardım Videosu İzle", "data_management": "<PERSON><PERSON>", "validation_details": "Doğrulama Detayları", "valid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warnings": "Uyarı", "errors": "<PERSON><PERSON>", "import_variable": "Değişkeni içe aktar", "data_view": "<PERSON><PERSON>", "variable_view": "Değişken Görünümü", "search_placeholder": "Değişken ara...", "analyze_missing_values": "Eks<PERSON>", "missing_value_info": "Eksik verilerinizi analiz edin.", "create_computed_variable": "Değişken Hesapla", "compute_variable_info": "Değişkenlerinizi he<PERSON>.", "auto_recode_values": "Değişken Değiştirme", "auto_recode_info": "Değişkenlerinizi değiştirin", "download_dataset": "<PERSON><PERSON>", "filters_list": "<PERSON><PERSON><PERSON><PERSON>", "computed": "Hesaplanmış", "recoded": "Yeniden Kodlanmış", "imputed": "Doldurulmuş", "imputed_with": "<PERSON><PERSON> {{method}} yöntemi ile dolduruldu", "recoded_with": "<PERSON>u deği<PERSON>ken {{method}} yöntemi ile yeniden kodlandı", "clear_search": "Aramayı temizle", "methods": {"mean": "Ortalama İmputasyonu", "median": "<PERSON><PERSON><PERSON>", "mode": "Mod İmputasyonu", "sequential": "Sıralı Kodlama", "range": "Aralık <PERSON>", "missing_values": "<PERSON><PERSON><PERSON>"}, "variable": "Değişken", "missing_warning": "adet eksik değer içeriyor.", "measure_type": "Ölçüm Türü", "header": "Sütun Başlığı", "show_header": "Başlık", "show_header_desc": "Değişken başlıkları gösteriliyor", "show_label": "Etiket", "show_label_desc": "Değişken etiketleri gösteriliyor", "total": "Toplam", "select_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "deselect_all": "Tümünü kaldır", "no_variables_found": "Değişken bulunamadı", "try_different_filters": "Farklı bir filtre ile aramayı deneyin", "filters": {"import_status": "İçe Aktarma Durumu", "measure_type": "Ölçüm Türü", "validation_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "missing_values": "<PERSON><PERSON><PERSON>", "import": {"all": "<PERSON><PERSON><PERSON> du<PERSON>", "imported": "İçe Aktarılan", "not_imported": "İçe Aktarılmayan"}, "measure": {"all": "<PERSON><PERSON><PERSON>", "scale": "Scale", "ordinal": "Ordinal", "nominal": "Nominal"}, "validation": {"all": "<PERSON><PERSON><PERSON>", "valid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warning": "Uyarı İçeren", "error": "Hata İçeren"}, "missing": {"all": "<PERSON><PERSON><PERSON> Eksik <PERSON>", "with_missing": "Eksik İçeren", "no_missing": "Eksik İçermeyen"}, "reset": "Filtreleri Sıfırla"}, "error": {"title": "<PERSON><PERSON>", "invalid_value": "Geçers<PERSON>"}, "saving": {"title": "Değişiklikler Kaydediliyor", "message": "Değişiklikleriniz kaydedilirken lütfen bekleyin...", "progress": "Veri seti kaydediliyor...", "success_title": "Başarılı", "error_title": "<PERSON><PERSON>", "save_error": "Değişiklikler kaydedilirken bir hata oluştu"}, "table": {"import": "İÇE AKTAR", "name": "İSİM", "type": "TÜR", "label_en": "ETİKET (EN)", "label_tr": "ETİKET (TR)", "values": "DEĞERLER", "missing": "EKSİK", "status": "DURUM"}, "import": {"title": "Veri Seti İçe Aktar", "content": "Bu işlem mevcut veri setinizi değiştirecektir. Devam etmek istediğinize emin misiniz?", "confirm": "İçe Aktar", "cancel": "İptal", "success": "Veri seti başarıyla içe aktarıldı", "error": "Veri seti içe aktarılırken hata oluştu", "with_validation": "Veri seti doğrulama hatalarıyla içe aktarıldı. Lütfen detayları kontrol edin.", "view_details": "Detayları Görüntüle"}, "value_labels": {"add_values": "<PERSON><PERSON><PERSON>", "values_count": "{{ count }} <PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "label_en": "Etiket (EN)", "label_tr": "Etiket (TR)", "unsaved_changes": "Kaydedilmemiş değ<PERSON>şiklikler", "use_existing": "<PERSON><PERSON><PERSON>", "save_close": "<PERSON><PERSON>", "updated": "<PERSON><PERSON>er etiketleri başarıyla güncellendi", "no_available_labels": "Kullanılabilir etiket bulunamadı"}, "missing": {"count": "{{count}} eksik", "zero": "<PERSON><PERSON><PERSON> yok", "high": "Yüksek eksik sayısı"}, "status": {"valid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warning": "Uyarı", "error": "<PERSON><PERSON>"}, "actions": {"import_excel": "Excel'den İçe Aktar", "export_excel": "Excel'e Aktar", "reset_changes": "Değişiklikleri Sıfırla", "save_all": "<PERSON><PERSON><PERSON>ğişiklikleri Kaydet", "close": "Ka<PERSON><PERSON>"}, "messages": {"changes_saved": "Değişiklikler başarıyla kaydedildi", "save_error": "Değişiklikler kaydedilirken bir hata oluştu", "measure_updated": "Ölçüm türü başarıyla güncellendi", "invalid_labels": "Geçersiz etiketler. Lütfen değerleri kontrol edin", "invalid_value_labels": "Geçersiz değer etiketleri. Lütfen değerleri kontrol edin", "validation_warning_imported": "{{count}} değişkende doğrulama hatası bulundu", "export_success": "Veri seti başarıyla dışa aktarıldı", "export_error": "Veri seti dışa aktarılırken hata oluştu", "data_updated": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "validation_issues": "Doğrulama sorunları bulundu", "all_selected": "Değişkenlerin tümü seçildi", "all_deselected": "Değişkenlerin tümü kaldırıldı"}, "confirm": {"changes": {"title": "Kay<PERSON>il<PERSON><PERSON><PERSON>şiklikler", "content": "Kaydedilmemiş değişiklikleriniz var. Kaydetmeden çıkmak istediğinize emin misiniz?", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Vazgeç"}, "unsaved_labels": {"title": "Kay<PERSON>il<PERSON><PERSON><PERSON>şiklikler", "content": "Değer etiketlerindeki değişiklikleriniz kaydedilmedi. Kaydetmek ister misiniz?", "save": "<PERSON><PERSON>", "discard": "Vazgeç"}, "reset": {"title": "Değişiklikleri Sıfırla", "content": "Bu işlem tüm değişiklikleri sıfırlayacak. <PERSON><PERSON> misiniz?", "confirm": "Sıfırla", "cancel": "İptal"}}, "context_menu": {"compute_options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "auto_update": "Otomatik Güncelle", "source_variables": "<PERSON><PERSON><PERSON>", "remove_row": "Satırı Sil", "read_only": "Salt Okunur"}, "dropdown_menu": {"read_only": "Salt Okunur", "compute_options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "auto_update": "Otomatik Güncelle", "source_variables": "<PERSON><PERSON><PERSON>"}}, "agreement": {"title": "S<PERSON>zleşme", "content": " ", "read_agreement": " Ön Bilgilendirme Koşullarını ve Mesafeli Satış Sözleşmesi'ni okudum.", "accept": "Kabul Ediyorum"}, "auto_recoding": {"no_variables_available": "Değişken bulunamadı", "name_output_variable": "Çıktı değişkeninizi isimlendirin", "title": "Değişken Değiştirme", "source_variables": "<PERSON><PERSON><PERSON>", "used_in_variables": "<PERSON><PERSON><PERSON><PERSON>", "select_variables": "Değişken seç...", "search_variables": "Değişken ara...", "variables_selected": "<PERSON><PERSON><PERSON><PERSON>", "output_variables": "Çıktı Değişkenleri", "variable_name": "İsim:", "select_variables_first": "Önce bir değişken seçin", "turkish_label": "Türkçe Etiket:", "select_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "deselect_all": "Tümünü kaldır", "english_label": "İngilizce Etiket:", "recoding_options": "Kodlama <PERSON>", "sequential_recoding": "Sıralı Kodlama", "sequential_recoding_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, artan veya azalan ş<PERSON>.", "start_from": "Başlangıç", "sequential": "Sıralı", "ascending": "<PERSON><PERSON>", "descending": "<PERSON><PERSON><PERSON>", "range_recoding": "Aralık <PERSON>", "recode_range": "Aralık Yeniden Kodlama", "start_value": "Başlangıç <PERSON>", "end_value": "<PERSON><PERSON>ş <PERSON>", "from": "Başlangıç", "to": "ile", "as": "arası", "equals": "<PERSON><PERSON><PERSON><PERSON>", "missing": "<PERSON><PERSON><PERSON>", "search_value_mapping": "<PERSON><PERSON><PERSON> ara...", "values_above": "Üstündeki Değerler", "values_below": "Altındaki <PERSON>ğ<PERSON>", "threshold": "<PERSON><PERSON><PERSON>", "new_value": "<PERSON><PERSON>", "missing_values": "<PERSON><PERSON><PERSON>", "enter_variable_name": "Değ<PERSON>ş<PERSON> ismi giriniz", "enter_turkish_label": "Türkçe etiket giriniz", "enter_english_label": "İngilizce etiket giriniz", "preserve_original_missing_values": "Orijinal eksik değerleri koru", "preserve_original_missing_values_description": "Eksik değeri değiştirmek yerine orijinal eksik değerleri koru", "system_missing_value": "Sistem Eksik <PERSON>ğeri", "apply_to_missing_values": "<PERSON><PERSON><PERSON>ğerlere Uygula", "value_mapping_preview": "<PERSON><PERSON><PERSON><PERSON>", "total": "Toplam", "apply": "Değişiklikleri Uygula", "search": "<PERSON><PERSON><PERSON> ara...", "old_value": "<PERSON><PERSON>", "footer": {"info": "Değişiklikler yeni bir değişken olarak kaydedilecek", "reset_all": "Tümünü Sıfırla", "apply": "Değişiklikleri Uygula"}, "warnings": {"enter_system_missing": "Lütfen bir değer giriniz", "enter_through_values": "Lütfen tüm aralık değerlerini giriniz", "start_less_than_end": "Başlangıç değeri bitiş değerinden küçük veya eşit olmalıdır", "enter_threshold_new_value": "Lütfen hem eşik hem de yeni değeri giriniz", "enter_missing_value": "Lütfen eksik değeri giriniz", "value_already_missing": "Bu değer zaten eksik olarak işaretlenmiş"}, "errors": {"variable_name_exists": "Bu isimde bir değişken zaten var"}, "success": {"changes_reset": "<PERSON><PERSON><PERSON>ğ<PERSON>şiklikler sıfırlandı", "changes_applied": "Değişiklikler başarıyla uygulandı"}, "create_variable": "Değişken Oluştur", "next": "<PERSON><PERSON> et", "back": "<PERSON><PERSON>", "range_recoding_desc": "Değerleri belirli aralıklara ayırarak kodlayın.", "missing_recoding_desc": "Eksik değerleri analiz edin.", "select_variables_auto_recode": "Değişken değiştirme işleminde kullanacağınız değişkeni/değişkenleri seçiniz", "select_option_auto_recode": "Kodlamak istediğiniz yöntemi seçiniz", "selected_variables": "<PERSON><PERSON><PERSON><PERSON>", "and": "ve", "more": "tane daha", "close": "Ka<PERSON><PERSON>", "name_output_variables": "Çıktı değişkenlerinizi isimlendirin", "change_missing_value": "Eksik değerleri değiştir", "change_missing_value_description": "Eksik değerleri kendi yazacağınız yeni değer ile güncelleyin", "changes_reset": "<PERSON><PERSON><PERSON>ğ<PERSON>şiklikler sıfırlandı", "changes_applied": "Değişiklikler başarıyla uygulandı", "select_variables_to_group": "Gruplamak istediğiniz değişkenleri seçin", "grouping_options": "<PERSON><PERSON><PERSON><PERSON>", "values": "<PERSON><PERSON><PERSON>", "group_variables": "Değişkenleri Grupla", "apply_grouping": "G<PERSON>lama <PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "equal_to": "<PERSON><PERSON><PERSON><PERSON><PERSON> eşittir", "values_between": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "greater": "sayısından büyük olan değerler", "less": "sayısından küçük olan değerler"}, "compute_variable": {"title": "Değişken Hesapla", "source_variable": "Kaynak <PERSON>", "select_variable": "Değişken seç...", "search_variables": "Değişken ara...", "output_variable": "Çıktı Değişkeni", "variable_name": "İsim:", "turkish_label": "Türkçe Etiket:", "english_label": "İngilizce Etiket:", "operation_select": "İşlem Seç", "select_operation": "İşlem seçiniz...", "available_variables": "Kullanılabil<PERSON>", "no_operation": "Lütfen önce bir işlem seçin", "no_compatible": "Bu işlem için uygun değişken bulunamadı", "no_results": "Aranan değişken bulunamadı", "compute_explanation": "<PERSON><PERSON><PERSON><PERSON>", "enter_variable_name": "Değ<PERSON>ş<PERSON> ismi giriniz", "enter_turkish_label": "Türkçe etiket giriniz", "enter_english_label": "İngilizce etiket giriniz", "preview": {"title": "<PERSON><PERSON><PERSON><PERSON>", "showing": "İlk 5 satır gösteriliyor", "row": "Satır", "original": "Orijinal Değer", "result": "<PERSON><PERSON><PERSON>", "no_data": "Önizleme için veri bulunamadı", "calculation_error": "<PERSON><PERSON><PERSON>me he<PERSON>planırken bir hata oluştu"}, "footer": {"info": "Değişiklikler yeni bir değişken olarak kaydedilecek", "preview": "<PERSON><PERSON><PERSON>", "create": "Değişken Oluştur"}, "operations": {"add": {"name": "Toplama", "description": "Seçili değişkenlerin değerlerini toplar"}, "subtract": {"name": "Çıkarma ", "description": "Seçili değişkenlerin değerlerini sırayla çıkarır"}, "multiply": {"name": "Çarpma", "description": "Seçili değişkenlerin değerlerini çarpar"}, "divide": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>kenleri sırayla böler"}, "mean": {"name": "Ortalama", "description": "Seçili değişkenlerin ortalamasını hesaplar"}, "log": {"name": "Logarit<PERSON>", "description": "10 tabanında logaritma hesaplar"}, "ln": {"name": "<PERSON><PERSON><PERSON>", "description": "Doğal logaritma (e tabanında) hesaplar"}, "square": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "sqrt": {"name": "Karekök", "description": "<PERSON><PERSON><PERSON>n karekökünü hesaplar"}, "concat": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>in metin değerlerini birleştirir"}}, "messages": {"select_variables": "En az bir değişken seçmelisiniz", "max_variables": "Bu işlem için en fazla {{count}} değişken seçilebilir", "same_type": "Seçili değişkenler aynı tipte olmalıdır", "variable_name_required": "Değişken adı gereklidir", "variable_exists": "Bu isimde bir değişken zaten var", "add": "Değ<PERSON>şkenler toplamı: {{variables}}", "subtract": "<PERSON><PERSON><PERSON><PERSON>: {{variables}}", "multiply": "Değişkenler çarpımı: {{variables}}", "divide": "{{variable1}} de<PERSON><PERSON><PERSON>keninin {{variable2}} de<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "mean": "Değişkenlerin ortalaması: {{variables}}", "log": "10 tabanında logaritma: {{variables}}", "ln": "Doğal logaritma: {{variables}}", "square": "<PERSON><PERSON><PERSON> alma: {{variables}}", "sqrt": "Karekök alma: {{variables}}", "concat": "<PERSON><PERSON> birleştirme: {{variables}}", "cannot_select_all": "T<PERSON>m değişkenler işlem kısıtlamaları nedeniyle seçilemez", "division_operation_limit": "Bölme işlemi yalnızca 2 değişken kullanabilir. Lütfen değişkenleri yeniden seçin.", "maxVariables": "Bu işlem için en fazla {{max}} değişken seçilebilir", "sameType": "Seçili değişkenler aynı tipte olmalıdır", "variable_created": "Yeni de<PERSON> başarıyla oluşturuldu", "compute_error": "<PERSON>ni değişken hesaplanırken hata oluştu", "invalid_variable_type": "Bu değişken tipi seçili işlem ile uyumlu değil", "requires_numeric": "Değişken sayısal olmayan veriler içeriyor ve sayısal işlemler için kullanılamaz", "contains_text_values": "Bu değişken metin değerleri içeriyor ve sayısal işlemler için kull<PERSON>ı<PERSON>az", "contains_mixed_values": "Bu değişken hem sayısal hem metin değerler içeriyor"}, "validations": {"select_operation": "Lütfen bir işlem seçin", "select_variables": "Lütfen en az bir değişken seçin", "division_two_variables": "<PERSON><PERSON>lm<PERSON> işlemi için tam olarak iki değişken seçilmelidir", "provide_name": "Lütfen yeni değ<PERSON>şken için bir isim girin", "name_exists": "Bu isimde bir değişken zaten mevcut", "provide_all_names": "Lütfen tüm dönüşüm değişkenleri için isim girin", "unique_names": "Değişken isimleri benzersiz olmalıdır"}, "success": {"variable_created": "Yeni de<PERSON> başarıyla oluşturuldu"}, "errors": {"creation_failed": "Değişken oluşturma başarısız", "compute_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> hatası", "invalid_operation": "Geçersiz işlem tipi", "unsupported_operation": "Desteklenmeyen işlem tipi", "computation_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> sı<PERSON> bir hata oluş<PERSON>", "variable_creation_failed": "Değişken oluşturulurken bir hata oluştu"}, "clear_selection": "<PERSON><PERSON><PERSON><PERSON>", "computed_badge": "Hesaplanmış", "new_name": "<PERSON>ni <PERSON> adı", "transformation_names": {"title": "<PERSON><PERSON>", "new_name": "{{variable}} i<PERSON><PERSON> yeni isim"}, "select_variable_for_compute": "Hesaplamak istediğiniz değişken için işlem seçiniz", "select_option": "işlemi yapacağınız değişkenleri seçiniz", "name_output_variable": "Çıktı değişkeninizi isimlendirin", "and": "ve", "more": "tane daha", "enter_necessary_infos": "sonucunda oluşacak değişken için gerekli bilgileri giriniz.", "variables_selected": "<PERSON><PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>"}, "missing_value": {"header": "Eks<PERSON>", "total_variables": "Toplam Değişken", "variables_with_missing": "Eksik Değeri Olan <PERSON>", "overall_missing_rate": "Genel Eksik Oranı", "variable": "Değişken", "type": "<PERSON><PERSON><PERSON>", "missing_count": "<PERSON><PERSON><PERSON>", "missing_percentage": "Eksik %", "pattern": "<PERSON><PERSON>", "actions": "İşlemler", "fix_missing_values": "Eksik Değerleri Düzelt", "close": "Ka<PERSON><PERSON>", "no_missing_values": "<PERSON><PERSON><PERSON> bulunmamaktadır.", "back": "<PERSON><PERSON>"}, "imputation_modal": {"header": "Eksik Değerleri Düzelt", "variable": "Değişken", "type": "<PERSON><PERSON><PERSON>", "missing_values": "<PERSON><PERSON><PERSON>", "variable_name": "Değişken Adı", "variable_name_placeholder": "Yeni değişken adını girin", "label_en": "Etiket (EN)", "label_tr": "Etiket (TR)", "preview": "<PERSON><PERSON><PERSON><PERSON>", "showing_first_5": "İlk 5 değişiklik gösteriliyor", "row": "Satır", "original_value": "Orijinal Değer", "new_value": "<PERSON><PERSON>", "missing": "<PERSON><PERSON><PERSON>", "preview_button": "<PERSON><PERSON><PERSON>", "create_variable": "Değişken Oluştur", "select_method_warning": "Lütfen bir yöntem seçin.", "form_invalid_error": "Lütfen formu doğru şekilde dold<PERSON>n.", "imputation_success": "<PERSON><PERSON><PERSON> başarıyla dolduruldu.", "imputation_error": "<PERSON><PERSON><PERSON>ler doldurulurken bir hata oluştu.", "no_applicable_methods_title": "Uygulanabilir İmputasyon Yöntemi Yok", "no_applicable_methods_description": "'{{variable}}' ({{type}}) değişkeni için uygun imputasyon yöntemi bulunamadı.", "no_applicable_methods_hint_1": "<PERSON><PERSON><PERSON> ve<PERSON>, e<PERSON><PERSON> o<PERSON> tüm değ<PERSON><PERSON>in sayılara dönüştürülebilir olduğundan emin olun", "no_applicable_methods_hint_2": "Değişken türünün (Scale/Nominal/Ordinal) verilerinizle eşleştiğini kontrol edin", "methods": {"mean": {"name": "Ortalama İmputasyonu", "description": "Eksik değerleri değişkenin ortalaması ile değiştirin"}, "median": {"name": "<PERSON><PERSON><PERSON>", "description": "Eksik değerleri medyan değeri ile değiştirin"}, "mode": {"name": "Mod İmputasyonu", "description": "Eksik değerleri en sık görülen değer ile değiştirin"}}, "next": "<PERSON><PERSON> et", "back": "<PERSON><PERSON>"}, "edit_project": {"title": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON> adı", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "optional": "İsteğe bağlı", "update": "<PERSON><PERSON><PERSON><PERSON>", "description_placeholder": "<PERSON><PERSON>"}, "referral": {"title": "Referans Programı", "referral_program": "Referans Programı", "your_referral_code": "<PERSON><PERSON><PERSON>", "copy": "Kopyala", "share_referral_code": "QR Kodunu <PERSON>ş", "share_via_whatsapp": "Whatsapp ile pay<PERSON>", "share_via_email": "E-posta ile <PERSON>", "total_referrals": "Toplam Referans", "earned_credit": "Kazanılan Kredi", "use_referral_code": "Referans <PERSON>", "referral_code_used": "Referans Kodu Kullanıldı", "user_used_referral_code": "adlı kullanıcının referans kodunu kullandın.", "bonus_info": "İlk harcamanı tamamladığında referans bonusu aktif olacak. Henüz ilk harcamanı yapmadığın için istersen referans kodunu değiştirebilir veya kaldırabilirsin.", "bonus_terms_info": "Bonus kredi kazanma şartları nelerdir?", "use_different_code": "Farklı bir kod kullan", "remove_code": "Kodu Kaldır", "enter_referral_code": "Referans kodunu gir", "change_code": "<PERSON><PERSON>", "apply_code": "Kodu Uygula", "cancel": "İptal", "self_referral_error": "Kendi referans kodunuzu kullanamazsınız", "invalid_referral_code_error": "Geçersiz referans kodu. Lütfen geçerli bir kod girin.", "copied": "Kopyalandı", "referral_code_usage_info": "Yalnızca bir kez referans kodu kullanabilirsin. Kod kullanıldığında ilk satın alımda bonus kredi kazanırsın.", "previous_purchase_info": "Daha önce satın alım yaptığın için referans kodu kullanamazsın.", "referral_history": "Referans Geçmişi", "referrals": "referans", "user": "Kullanıcı", "date": "<PERSON><PERSON><PERSON>", "earned_credits": "Kazanılan Kredi", "no_referrals_yet": "Hen<PERSON>z referans kaydın bulunmu<PERSON>r", "share_to_earn": "<PERSON><PERSON><PERSON> kredi kazanmaya başla!", "bonus_terms_title": "Bonus Kredi <PERSON>ma Şartları", "referral_bonus_info": "Referans kodunu kullanarak arkadaşlarını davet ettiğinde hem sen hem de arkadaşın bonus kredi kazanırsınız.", "credit_earning_table": "<PERSON><PERSON><PERSON>", "purchased_credit": "Alınan Kredi", "referrer_earnings": "Davetçi Kazancı", "referred_earnings": "<PERSON><PERSON>", "table_note": "<PERSON><PERSON><PERSON><PERSON><PERSON>, davet edilen kull<PERSON>ının ilk satın alımında aldığı kredi miktarına göre değişir.", "referrer_title": "<PERSON><PERSON> (Sen)", "referred_title": "<PERSON><PERSON> (Arkadaşın)", "referrer_info_1": "<PERSON>t et<PERSON>ğin k<PERSON>ı<PERSON>ın aldığı kredi miktarına göre bonus kazanırsın.", "referrer_info_2": "Arkadaşının ilk satın alımını tamamlaması gerekir.", "referrer_info_3": "Kazanılan krediler hesabına otomatik olarak yüklenir.", "referrer_info_4": "Arkadaşının satın alımı tamamlanana kadar görünmez.", "referred_info_1": "Arkadaşın ilk satın alımını yapmadan önce senin referans kodunu kull<PERSON>, aldığı kredi miktarına göre bonus kazanır.", "referred_info_2": "<PERSON><PERSON><PERSON>, ilk satın alımdan sonra otomatik olarak hesabına <PERSON>.", "referred_info_3": "Referans Programı kapsamında bonus kredi kazanabilmek için minimum satın alım miktarı 100 kredi olmalıdır.", "additional_info": "<PERSON><PERSON>", "additional_info_1": "Başkasının referans kodunu kullanmak için hesapta daha önce hiç satın alım yapılmamış olması gerekir.", "additional_info_2": "Bir kişi yalnızca bir referans kodu kullanabilir.", "additional_info_3": "Referans kodunu kullanan kişi henüz ilk alışverişini yapmadıysa, kodu değiştirebilir.", "understood": "<PERSON><PERSON><PERSON><PERSON>"}, "title": "istabot | Analizin En Sade Hali"}, "cookie": {"text": "Web sitemizde en iyi deneyimi yaşamanızı sağlamak için çerezler kullanıyoruz.", "learn_more": " Daha fazla bilgi edinin", "accept": "Kabul Ediyorum"}, "validation": {"file_type": "<PERSON><PERSON><PERSON> {{allowed}} dosya türleri kabul edilmektedir", "file_size": "<PERSON><PERSON><PERSON> boy<PERSON>u {{maxSize}}'ı geçemez", "empty_dataset": "<PERSON><PERSON> seti bo<PERSON> o<PERSON>az", "max_rows": "<PERSON><PERSON><PERSON><PERSON> say<PERSON> ({{current}}) maksimum limiti ({{max}}) aşıyor", "min_rows": "Veri seti en az 3 satır içermelidir (başlık satırı dahil)", "max_columns": "<PERSON><PERSON><PERSON> ({{current}}) maksimum limiti ({{max}}) aşıyor", "min_columns": "Veri seti en az bir sütun içermelidir", "invalid_header": "Geçersiz başlık satırı", "empty_headers": "Sütun başlıkları boş olamaz", "duplicate_headers": "Te<PERSON>r eden sütun başlıkları bulunamaz", "inconsistent_rows": "{{count}} satırda tutarsız sütun sayısı var"}, "navigation": {"home": "<PERSON>", "projects": "<PERSON><PERSON><PERSON>", "projects_short": "<PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "reports_short": "<PERSON><PERSON><PERSON>", "members": "<PERSON><PERSON><PERSON>", "project": {"overview": "Genel Bakış", "overview_short": "<PERSON><PERSON>", "dataset": "<PERSON><PERSON>", "dataset_short": "<PERSON><PERSON>", "analysis": "<PERSON><PERSON><PERSON>", "analysis_short": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "settings_short": "<PERSON><PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON>", "history_short": "Geçmiş", "members_short": "<PERSON><PERSON><PERSON>"}}, "loading": "İşleminiz gerçekleştiriliyor, lütfen bekleyiniz...", "corporate-management": {"pagination": {"showing": "Gösteriliyor", "of": "/"}, "unit_type": {"consultancy": "Danışmanlık", "academic": "Akademik", "goverment": "<PERSON><PERSON>", "journal": "<PERSON><PERSON>", "survey": "Anket", "healthcare": "Sağlık", "other": "<PERSON><PERSON><PERSON>"}, "unit_selection": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Kurumsal yönetim işlemleri için birim seçin", "change_unit": "<PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "managers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "Seç", "no_units": "<PERSON><PERSON><PERSON> b<PERSON>", "no_units_desc": "Birim eklemek için yöneticinizle iletişime geçin"}, "title": "<PERSON><PERSON><PERSON>", "company_name": "{{ name }} Şirketi", "tabs": {"users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON>", "activities": "Aktiviteler"}, "credit_info": {"title": "<PERSON>rum Kredi Bilgis<PERSON>", "total": "Toplam Kredi", "used": "Kullanılan Kredi", "remaining": "<PERSON><PERSON>", "credits": "<PERSON><PERSON><PERSON>", "transferred": "Transfer Edilen", "unused": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "help_text": "Bu birime ait kredi bilgilerini görüntüleyebilirsiniz"}, "projects": {"title": "<PERSON><PERSON><PERSON>", "search_placeholder": "Proje ara...", "filter_label": "Durum:", "clear_filters": "<PERSON><PERSON><PERSON><PERSON>", "no_results": "<PERSON><PERSON> kriterlerine uygun proje bulunamadı.", "unknown_owner": "Bilinmeyen Kullanıcı", "table": {"name": "<PERSON><PERSON>", "owner": "<PERSON><PERSON>", "status": "Durum", "actions": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"all": "<PERSON><PERSON><PERSON>", "paid": "Ödendi", "unpaid": "Ödenmedi"}, "actions": {"mark_paid": "Ödendi olarak işaretle", "mark_unpaid": "Ödenmedi olarak işaretle"}}, "activities": {"title": "Aktiviteler", "search_placeholder": "Aktivite ara...", "filter_type_label": "Tip:", "filter_status_label": "Durum:", "clear_filters": "<PERSON><PERSON><PERSON><PERSON>", "no_results": "Arama kriterlerine uygun aktivite bulunamadı.", "loading": "Yükleniyor...", "by_manager": "Yönetici", "table": {"date": "<PERSON><PERSON><PERSON>", "action": "Aksiyon", "status": "Durum", "user": "Kullanıcı", "credits": "<PERSON><PERSON><PERSON>", "reason": "<PERSON><PERSON>"}, "type": {"all": "<PERSON><PERSON><PERSON>", "credit_send": "<PERSON><PERSON><PERSON>", "credit_refund": "<PERSON><PERSON><PERSON>", "user_add": "Kullanıcı Ekleme", "user_remove": "Kullanıcı Silme"}, "status": {"all": "<PERSON><PERSON><PERSON>", "done": "Tamamlandı", "cancelled": "İptal Edildi", "pending": "Beklemede"}}, "search": {"placeholder": "E-posta ile ara...", "filter_label": "Durum:", "clear_filters": "<PERSON><PERSON><PERSON><PERSON>", "no_results": "Arama kriterlerine uygun kullanıcı bulunamadı."}, "user_list": {"title": "<PERSON><PERSON><PERSON>ı<PERSON>ı<PERSON>", "add_button": "Kullanıcı Ekle", "import_button": "Excel'den İçe Aktar"}, "table": {"email": "E-posta", "status": "Durum", "role": "Rol", "credit_info": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "credit_details": {"total": "Toplam:", "used": "Kullanılan:", "remaining": "Kalan:", "not_registered_message": "Kullanıcı kayıt olduktan sonra kredi bilgileri görüntülenebilir.", "not_confirmed_message": "Kullanıcı hesabını onayladıktan sonra kredi bilgileri görüntülenebilir."}, "actions": {"add_credit": "<PERSON><PERSON><PERSON>", "remove_credit": "Kredi Al", "make_admin": "Yönetici Ya<PERSON>", "make_user": "Kullanıcı Yap", "delete_user": "Kullanıcıyı Sil", "inactive_credit_tooltip": "<PERSON><PERSON><PERSON> k<PERSON>ılara kredi işlemi yapılamaz", "inactive_role_tooltip": "<PERSON><PERSON><PERSON> k<PERSON>ının rolü değiştirilemez", "cannot_delete_user_with_credits": "Kredisi olan kullanıcı silinemez", "inactive_user_credit": "Kredi işlemleri için aktif kayıt gereklidir", "no_unit_credits": "Departmanın atanacak kredisi bulunmamaktadır", "no_user_credits": "Kullanıcının alınacak kredisi bulunmamaktadır", "give_credit": "Kullanıcıya kredi ver"}, "status": {"registered": "Hesap Aktif", "not_registered": "<PERSON><PERSON><PERSON>", "not_confirmed": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON><PERSON>"}, "role": {"admin": "Yönetici", "user": "Kullanıcı"}, "import_excel": {"title": "Excel'den Kullanıcı Aktarımı", "description": "Toplu kullanıcı eklemek için Excel dosyası yükleyin.", "drag_drop": "Dosyayı sürükleyip bırakın veya göz atın", "file_types": "Excel dosyaları (.xlsx, .xls) veya CSV", "browse_files": "<PERSON><PERSON><PERSON>", "download_template": "Şablonu <PERSON>r", "preview": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-posta", "credit": "<PERSON><PERSON><PERSON>", "reason": "<PERSON><PERSON>", "status": "Durum", "valid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalid_file_type": "Geçersiz dosya türü. Lütfen Excel veya CSV dosyası yükleyin.", "missing_email": "E-posta adresi eksik", "invalid_email": "Geçersiz e-posta adresi", "email_exists": "E-posta adresi zaten var", "duplicate_email": "Yüklenen listede tekrarlanan e-posta", "insufficient_credits": "Yetersiz kredi. Birimde {{available}} kredi var, {{required}} kredi gerekiyor", "total_records": "Toplam Kayıt", "valid_records": "Geçerli <PERSON>", "invalid_records": "Geçersiz <PERSON>", "fix_errors": "Lütfen hataları düzeltip tekrar deneyin", "no_valid_users": "Aktarılacak geçerli kullanıcı yok", "importing": "Aktarılıyor...", "import": "Aktarımı Başlat", "cancel": "İptal", "close": "Ka<PERSON><PERSON>", "total": "Toplam", "success": "Başarılı", "failed": "Başarısız", "results": "<PERSON>ktar<PERSON>m <PERSON>ı", "import_complete": "Aktarım tamamlandı. Toplam: {{total}}, Başarılı: {{success}}, Başarısız: {{failed}}"}, "add_user": {"title": "Kullanıcı Ekle", "email_label": "E-posta Adresi:", "email_placeholder": "<EMAIL>", "info_text": "Kullanıcıya davet gönderilecektir.", "initial_credit_label": "Başlangıç Kredisi", "cancel": "İptal", "add": "<PERSON><PERSON>", "reason_label": "Neden:", "reason_placeholder": "Kullanıcıyı eklemek için bir neden belirtin"}, "credit_dialog": {"give_title": "<PERSON><PERSON><PERSON>", "take_title": "Kredi Al", "user_prompt": "{{ email }} kullanıcısı için miktar belirleyin:", "amount_label": "<PERSON><PERSON><PERSON>:", "remaining_credits": "Kullanıcının kalan kredisi:", "take_all": "<PERSON><PERSON><PERSON> ({{ amount }})", "cancel": "İptal", "confirm": "<PERSON><PERSON><PERSON>", "reason_label": "Neden:", "reason_placeholder": "<PERSON><PERSON><PERSON> işlemi i<PERSON> bir neden belirtin"}, "notifications": {"invalid_email": "Lütfen geçerli bir e-posta adresi girin", "email_exists": "Bu e-posta adresi zaten kayıtlı", "user_added": "{{ email }} kullanıcısı başarıyla eklendi", "credit_given": "{{ email }} kullanıcısına {{ amount }} kredi verildi", "credit_taken": "{{ email }} kullanıcısından {{ amount }} kredi alı<PERSON>ı", "insufficient_credits": "Kullanıcının yeterli kredisi bulunmamaktadır. Mevcut kredi: {{ amount }}", "user_deleted": "{{ email }} kullanıcısı silindi", "role_changed": "{{ email }} k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rol<PERSON> \"{{ role }}\" o<PERSON><PERSON>tirildi", "inactive_user": "{{ email }} kullanıcısı hesabını aktifleştirmediği için kredi işlemi yapılamaz.", "inactive_user_role": "{{ email }} kullanıcısı hesabını aktifleştirmediği için rol değişikliği yapılamaz.", "cannot_delete_with_credits": "{{ email }} kullanıcısı silinemez. Kullanıcıya {{ credits }} kredi atanmış.", "cannot_delete_admin": "Admin kullan<PERSON>c<PERSON> {{email}} silinemez. Lütfen önce kullanıcıyı normal kullanıcı rolüne düşürün."}, "confirmations": {"add_user": "{{ email }} kullanıcısını eklemek istediğinize emin misiniz?", "add_user_content": "Kullanıcı başlangıçta 0 kredi ile oluşturulacak. Daha sonra kredi ekleyebilirsiniz.", "delete_user": "{{ email }} kullanıcısını silmek istediğinize emin misiniz?", "delete_user_content": "Bu işlem geri alınamaz.", "give_credit": "{{ email }} kullanıcısına {{ amount }} kredi vermek istediğinize emin misiniz?", "take_credit": "{{ email }} kullanıcısından {{ amount }} kredi almak istediğinize emin misiniz?", "change_role_admin": "{{ email }} kullanıcısının r<PERSON> \"yönetici\" olarak değiştirmek istediğinize emin misiniz?", "change_role_admin_content": "Yönetici rolü kurumsal yönetim paneline eri<PERSON><PERSON>.", "change_role_user": "{{ email }} kullanıcısının rolünü \"kullanıcı\" olarak değiştirmek istediğinize emin misiniz?", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "delete": "Sil", "cancel": "İptal", "add": "<PERSON><PERSON>"}}, "careers": {"title": "Ekibimize Katılın", "subtitle": "İstatistiksel analiz araçlarının geleceğini inşa etmemize yardımcı olacak yetenekli bireyler arıyoruz.", "back_to_home": "<PERSON><PERSON>", "open_positions": "Açık Pozi<PERSON>lar", "positions_subtitle": "Mevcut iş ilanlarımızı inceleyin ve becerileriniz ve ilgi alanlarınız için mükemmel pozisyonu bulun.", "view_details": "Detayları Gör", "hide_details": "Detayları Gizle", "apply_now": "Şimdi Başvur", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "responsibilities": "Sorumluluklar", "requirements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply_for_position": "Bu Pozisyona Başvur", "no_match_title": "Becerilerinizle eşleşen bir pozisyon görmüyor musunuz?", "no_match_description": "Her zaman ekibimize katılacak yetenekli insanlar arıyoruz. Özgeçmişinizi bize gö<PERSON>, gelecekteki fırsatlar için dosyada tutalım.", "contact_us": "İşe Alım Ekibimizle İletişime Geçin", "jobs": {"angular_frontend": {"id": "angular-frontend", "title": "Angular Frontend Geliştirici", "location": "Atakum / Samsun", "type": "<PERSON>", "description": "Dinamik ekibimize Angular Frontend Geliştirici olarak katılın ve dünya çapındaki araştırmacılar için en son teknoloji istatistiksel analiz araçları oluşturmamıza yardımcı olun.", "responsibilities": ["Angular kullanarak duyarlı ve etkileşimli kullanıcı arayüzleri geliştirmek", "Kullanıcı dostu arayüzler uygulamak için UX tasarımcılarıyla işbirliği yapmak", "RESTful API'ler ve backend servisleriyle çalışmak", "Kod incelemelerine katılmak ve kod kalitesini korumak", "Yeni özellikler uygulamak ve mevcut özellikleri geliştirmek", "Maks<PERSON>um hız ve ölçeklenebilirlik için uygulamaları optimize etmek"], "requirements": ["Angular (versiyon 10+) ile 2+ yıl deneyim", "TypeScript, HTML5 ve CSS3/SCSS konusunda güçlü bilgi", "Duyarlı tasarım ve tarayıcılar arası uyumluluk konusunda deneyim", "Durum yönetimi <PERSON> (NgRx, RxJS) ile tanışıklık", "RESTful API'ler ve HTTP protokolleri hakkında anlayış", "Sürüm kontrol sistemleri (Git) bilgisi", "İyi problem çözme becerileri ve detaylara dikkat", "Takım ortamında çalışabilme yeteneği", "<PERSON><PERSON><PERSON>: Angular Material, TailwindCSS gibi UI kütüphaneleri ile deneyim", "<PERSON>rcih <PERSON>: <PERSON> (<PERSON>, <PERSON><PERSON>) bilgisi"]}, "ruby_on_rails": {"id": "ruby-on-rails", "title": "Ruby on Rails Geliştirici", "location": "Atakum / Samsun", "type": "<PERSON>", "description": "Ekibimize katılacak, mevcut kod tabanlarını iyileştirerek ve yeni ürünler geliştirerek devam eden projeleri destekleyecek bir Ruby on Rails geliştirici arıyoruz. Platformumuzun büyümesine ve geliştirilmesine doğrudan katkıda bulunacak, özellikle yalnızca API backend hizmetlerine odaklanacaksınız.", "responsibilities": ["<PERSON><PERSON><PERSON>, verimli ve ölçeklenebilir Ruby on Rails uygulamaları geliştirmek, test etmek ve sürdürmek", "Yeni backend özellikleri uygulamak için ürün ekibiyle etkili bir şekilde işbirliği yapmak", "En iyi u<PERSON>ala<PERSON>, performans optimizasyonu ve kod incelemeleri yoluyla yüksek kod kalitesini sağlamak"], "requirements": ["Ruby on Rails geliştirmede en az 1 yıl deneyim", "Ruby programlama dili ve en iyi uygulamaları hakkında güçlü anlayış", "Ruby Gems ve ilgili Rails kütüphaneleri ile tanışıklık", "Nesne Yönel<PERSON> (OOP), veri ya<PERSON><PERSON><PERSON><PERSON> ve algoritmalar konusunda sağlam temel", "PostgreSQL veritabanları hakkında pratik bilgi", "RESTful API prensipleri ve genel API mimarisi hakkında iyi anlayış", "<PERSON>it sürüm kontrol sistemleri ile deneyim (örn<PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Gist)", "Ürün yönetimi ve diğer geliştirme ekibi üyeleriyle yakın işbirliği yapabilme yeteneği", "Bakımı kolay, optimize edilmiş ve yüksek kaliteli kod yazma taahhüdü", "Tercih <PERSON>bi: İngilizce iyi iletişim becerileri", "<PERSON><PERSON>ih se<PERSON>bi: <PERSON><PERSON>, AWS ve CI/CD pipeline'ları ile deneyim"]}, "r_junior": {"id": "r-junior", "title": "<PERSON> <PERSON>", "location": "Atakum / Samsun", "type": "<PERSON>", "description": "Veri bilimi ekibimize katılacak adanmış bir R geliştirici arıyoruz. İdeal aday, istatistiksel analiz algoritmaları geliştirmede ve platformumuz için sağlam analitik çözümler sürdürmede kritik bir rol oynayacaktır.", "responsibilities": ["Yeni istatistiksel özellikler uygulamak için ürün ekibiyle etkili bir şekilde işbirliği yapmak", "R kullanarak istatistiksel analiz modülleri geliştirmek, test etmek ve sürdürmek", "Titiz performans optimizasyonu ve en iyi kodlama uygulamaları yoluyla kod kalitesini sağlamak"], "requirements": ["<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> veya ilgili alanda lisans derecesi", "R programlama dili ve istatistiksel analiz konusunda yetkinlik", "İstatistiksel kavramlar ve metodolojiler hakkında anlayış", "R'de veri manipü<PERSON>yonu ve görselleştirme deneyimi", "İstatistiksel analiz için R paketleri ile tanışıklık (örn. dplyr, ggplot2, tidyr)", "Sürüm kontrol sistemleri (Git) hakkında temel bilgi", "İşbirlikçi bir takım ortamında çalışabilme yeteneği", "Güçlü problem çözme becerileri ve detaylara dikkat", "<PERSON><PERSON><PERSON> se<PERSON>bi: <PERSON> <PERSON><PERSON> veya Shiny ile deneyim", "<PERSON><PERSON><PERSON> se<PERSON>: <PERSON><PERSON><PERSON> (Python, SQL) bilgisi"]}}}, "explore": {"title": "Keşfet", "beta": "Beta", "publish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading": "Yükleniyor...", "tabs": {"callForPapers": "Bildiri Çağrıları", "featured": "<PERSON><PERSON>", "recent": "<PERSON><PERSON>", "researchers": "Araştırmacılar", "interdisciplinary": "Disiplinler Arası", "openAccess": "Açık <PERSON>"}, "search": {"placeholder": "Aramak istediğiniz içeriği yazın...", "filters": "<PERSON><PERSON><PERSON><PERSON>", "advanced": {"discipline": {"label": "Disi<PERSON>lin", "all": "<PERSON><PERSON><PERSON>"}, "year": {"label": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>"}, "language": {"label": "Dil", "all": "<PERSON><PERSON><PERSON>"}, "access": {"label": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>"}, "contentType": {"label": "İçerik Türü", "all": "<PERSON><PERSON><PERSON>"}, "sort": {"label": "Sıralama", "relevance": "İlgililik", "date": "<PERSON><PERSON><PERSON>", "citations": "Atıflar", "impact": "<PERSON><PERSON><PERSON>"}, "clearFilters": "<PERSON><PERSON><PERSON><PERSON>"}}, "urgentCfp": {"title": "Acil Bildiri Çağrıları", "days": "g<PERSON>n", "viewAll": "Tümünü <PERSON>", "dismiss": "Ka<PERSON><PERSON>"}, "cfp": {"submissionDeadline": "<PERSON>", "notificationDue": "<PERSON><PERSON><PERSON><PERSON>", "daysLeft": "gün kaldı", "wikiCfp": "Wiki CFP", "eventWebsite": "Etkinlik Sitesi"}, "content": {"addToCollection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addToFavorites": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otherAuthors": "<PERSON><PERSON><PERSON>", "citations": "at<PERSON>f", "downloads": "indirme", "impactFactor": "<PERSON><PERSON><PERSON>", "openAccess": "Açık <PERSON>"}, "empty": {"title": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "description": "Aramanız için herhangi bir sonuç bulunamadı. Lütfen farklı anahtar kelimeler deneyin.", "clearFilters": "<PERSON><PERSON><PERSON><PERSON>"}, "disciplines": {"computerScience": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineering": "Mühendislik", "medicine": "T<PERSON>p", "physics": "<PERSON><PERSON><PERSON>", "chemistry": "<PERSON><PERSON>", "biology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mathematics": "Matematik", "psychology": "Psikoloji", "sociology": "<PERSON><PERSON><PERSON><PERSON>", "economics": "Ekonomi"}, "languages": {"turkish": "Türkçe", "english": "İngilizce", "german": "Almanca", "french": "Fransızca", "spanish": "İspanyolca"}, "accessTypes": {"openAccess": "Açık <PERSON>", "subscription": "Abonelik", "hybrid": "Hibrit"}, "contentTypes": {"researchPaper": "Araş<PERSON><PERSON><PERSON>", "researcher": "Araştırmacı", "institution": "<PERSON><PERSON>", "project": "<PERSON><PERSON>", "dataset": "<PERSON><PERSON>"}}}
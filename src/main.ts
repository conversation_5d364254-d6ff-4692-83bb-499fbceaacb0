import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { enableProdMode, isDevMode } from '@angular/core';
import { environment } from './environment/environment';
// enableProdMode();// ...
import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from '@app/app.component';
import { provideRouter } from '@angular/router';

if (environment.production) {
  enableProdMode();
  // console.log('Production mode');
} else {
  // console.log('Development mode');
}

platformBrowserDynamic()
  .bootstrapModule(AppModule)
  .catch((err) => console.error(err));

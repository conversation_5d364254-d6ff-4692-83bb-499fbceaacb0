/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,ts}"],
  theme: {
    extend: {
      colors: {
        "brand-blue": {
          50: "#EBF2FF",
          100: "#F5F9FF", // En açık ton - Secondary Blue Linear first
          200: "#DBEAFE", // Bg Blue
          250: "#93C5FD", // Sidebar Menu Name Blue
          300: "#CAD6F2", // Secondary Blue Borde
          400: "#1D44B1", // hover
          500: "#003CBD", // Ana mavi - Primary Blue
          600: "#0047DB", // koyu mavi
          700: "#1E3A8A", // Layout Blue - Dark Blue
          800: "#0D2877", // Dark Blue
        },

        "brand-green": {
          50: "#EBF7F2",
          100: "#F5FBF8", // En açık ton - Secondary Green Linear
          200: "#FEFBF2", // Bg Green
          300: "#CAE5DB", // Secondary Green Border
          400: "#A8E3C1", // hover
          500: "#19C480", // Ana yeşil - <PERSON> Green
          600: "#0D9F6E", // koyu yeşil
          700: "#0A7C5C", // Layout Green - Dark Green
          800: "#055C48", // Dark Green
        },

        neutral: {
          100: "#F9FAFB", // Sayfa bg
          150: "#E1E3E8", // Sayfa border
          200: "#E5E7EB", // Input border
          400: "#818892", // hover
          600: "#4B5563", // Breadcrumb text
        },

        data: {
          // Ordinal
          purple: {
            100: "#F3E8FF",
            500: "#A855F7",
          },
          // Scale
          blue: {
            100: "#DBEAFE",
            500: "#3B82F6",
          },
          // Nominal
          orange: {
            100: "#FCF3CC",
            500: "#FE7D20",
          },
        },


        status: {
          error: {
            50: "#FFF5F5",
            100: "#FEF1F2",
            200: "#FED7D8",
            300: "#FEADB0",
            400: "#FE6568",
            500: "#FD2C30",
            600: "#E01619",
            700: "#B80D10",
          },
          warning: {
            25: "#FFFFF5",
            50: "#FFFCEB",
            75: "#F2F0CA",
            100: "#FEF3C7",
            200: "#FEE4A0",
            300: "#FED479",
            400: "#FFB43D",
            500: "#FF9500",
            600: "#E07A00",
            700: "#B86200",
          },
          info: {
            100: "#DBEAFE",
            200: "#B4D1FE",
            300: "#8DB8FE",
            400: "#4785E6",
            500: "#003CBD",
            600: "#002E94",
            700: "#00246B",
          },
          success: {
            100: "#EEF6F2",
            200: "#D2EBDE",
            300: "#8DDDB1",
            400: "#4DD197",
            500: "#19C480",
            600: "#14A066",
            700: "#0F7C4D",
          },
        },
        christi: {
          50: "#f4fbea",
          100: "#e6f5d2",
          200: "#ceecaa",
          300: "#afde78",
          400: "#90cd4e",
          500: "#69a52c",
          600: "#568e22",
          700: "#436d1e",
          800: "#38571d",
          900: "#304a1d",
          950: "#17280b",
        },
        "prussian-blue": {
          50: "#f0f9ff",
          100: "#e0f1fe",
          200: "#bae4fd",
          300: "#7dd0fc",
          400: "#38b8f8",
          500: "#0ea0e9",
          600: "#027fc7",
          700: "#0365a1",
          800: "#075685",
          900: "#0c486e",
          950: "#093352",
        },
        alabaster: {
          50: "#f9f9f9",
          100: "#efefef",
          200: "#dcdcdc",
          300: "#bdbdbd",
          400: "#989898",
          500: "#7c7c7c",
          600: "#656565",
          700: "#525252",
          800: "#464646",
          900: "#3d3d3d",
          950: "#292929",
        },
        whisper: {
          50: "#faf9fb",
          100: "#f1edf3",
          200: "#ece6ee",
          300: "#dcd2e0",
          400: "#c5b4cc",
          500: "#ac96b6",
          600: "#967ca1",
          700: "#7f6789",
          800: "#6b5772",
          900: "#57475c",
          950: "#3b2c3f",
        },
        midnight: {
          50: "#ecf9ff",
          100: "#d4f1ff",
          200: "#b3e8ff",
          300: "#7fdbff",
          400: "#43c4ff",
          500: "#18a4ff",
          600: "#0083ff",
          700: "#006bfb",
          800: "#0356ca",
          900: "#0a4b9e",
          950: "#071c3a",
        },
        bilbao: {
          50: "#f0faeb",
          100: "#e0f3d4",
          200: "#c2e9ad",
          300: "#9bd97d",
          400: "#78c754",
          500: "#59ac36",
          600: "#438828",
          700: "#397125",
          800: "#2d5420",
          900: "#28481f",
          950: "#12270c",
        },
      },
      gridTemplateColumns: {
        // Simple 16 column grid
        100: "repeat(100, minmax(0, 1fr))",
        50: "repeat(50, minmax(0, 1fr))",
        25: "repeat(25, minmax(0, 1fr))",
      },

      rotate: {
        "-25": "-25deg",
        "-30": "-30deg",
        "-35": "-35deg",
        "-180": "-180deg",
        15: "15deg",
        135: "135deg",
        225: "225deg",
        315: "315deg",
        690: "690deg",
      },
      translate: {
        66: "16.5rem",
        70: "17.5rem",
        120: "30rem",
        128: "32rem",
        144: "36rem",
        160: "40rem",
        176: "44rem",
        192: "48rem",
        208: "52rem",
        224: "56rem",
        240: "60rem",
        256: "64rem",
        512: "128rem",
      },
      height: {
        18: "4.5rem",
        78: "19.5rem",
        128: "32rem",
        144: "36rem",
        152: "38rem",
        156: "39rem",
        158: "39.5rem",
        160: "40rem",
        176: "44rem",
        192: "48rem",
        208: "52rem",
        224: "56rem",
        240: "60rem",
        256: "64rem",
      },
      width: {
        26: "6.5rem",
        27: "6.75rem",
        27.5: "6.875rem",
        30: "7.5rem",
        128: "32rem",
        144: "36rem",
        160: "40rem",
        176: "44rem",
        192: "48rem",
        208: "52rem",
        224: "56rem",
        240: "60rem",
        256: "64rem",
      },

      maxHeight: {
        108: "27rem",
        128: "32rem",
        144: "36rem",
        160: "40rem",
        176: "44rem",
        192: "48rem",
        208: "52rem",
        224: "56rem",
      },
      minWidth: {
        96: "24rem",
        108: "27rem",
        128: "32rem",
        144: "36rem",
        160: "40rem",
        176: "44rem",
        192: "48rem",
        208: "52rem",
        224: "56rem",
        240: "60rem",
        256: "64rem",
      },
      maxWidth: {
        "3/4": "75%",
        9: "2.25rem",
        10: "2.5rem",
        36: "9rem",
        64: "16rem",
        72: "18rem",
        80: "20rem",
        92: "23rem",
        94: "23.5rem",
        95: "23.75rem",
        96: "24rem",
        128: "32rem",
        144: "36rem",
        160: "40rem",
        176: "44rem",
        192: "48rem",
        208: "52rem",
        224: "56rem",
      },
      transitionDuration: {
        2000: "2000ms",
      },
      transitionDelay: {
        2000: "2000ms",
      },
      transitionProperty: {
        height: ["responsive", "height"],
        opacity: "opacity",
      },
      fontFamily: {
      baloo: ['"Baloo 2"', 'cursive'],
      },
      animation: {
        "spin-slow": "spin 6s linear infinite ",
        scalePulse: "scalePulse 2s cubic-bezier(0.4, 0, 0.6, 1) isnfinite",
        gradient: "gradient 6s linear infinite",
        flowrightToTop: "flowMerightToTop  3s ease-in infinite alternate",
        flowrightToBottom: "flowMerightBottom 3s ease-in infinite alternate",
        flowleftToTop: "flowMeleftToTop   3s ease-in infinite alternate",
        flowleftToBottom: "flowMeleftBottom  3s ease-in infinite alternate",
        scaleLittle: "scaleLittle 6s cubic-bezier(0.6, 0.4, 0.6, 1) infinite",
        scaleAnimate: "scaleAnima1 2s ease infinite ",
        scaleAnim1: "scaleAnima1 2s ease-in-out infinite ",
        scaleAnim2: "scaleAnima2 6s cubic-bezier(0,0,0, 1) infinite ",
        scaleAnim3: "scaleAnima3 6s cubic-bezier(0,0,0, 1) infinite ",
        scaleAnim4: "scaleAnima4 6s cubic-bezier(0,0,0, 1) infinite ",
        scaleAnim5: "scaleAnima5 6s cubic-bezier(0,0,0, 1) infinite ",
        textScale: "textScale 6s cubic-bezier(0,1,0, 1) infinite ",
        littleBounce: "littleBounce 2s ease-in-out infinite  ",
        shimmer: "shimmer 2s linear infinite",
      },
      keyframes: {
        gradient: {
          to: { "background-position": "-200% center" },
        },
        shimmer: {
          "0%": {
            transform: "translateX(-100%)",
          },
          "100%": {
            transform: "translateX(100%)",
          },
        },
        scalePulse: {
          "0%, 100%": { transform: "scale(1.3)" },
          "50%": { transform: "scale(1)" },
        },
        scaleLittle: {
          "0%, 100%": { transform: "scale(0.8)" },
          "50%": { transform: "scale(1)" },
        },

        animatedgradient: {
          "0%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
          "100%": { backgroundPosition: "0% 50%" },
        },

        flowMerightToTop: {
          "0%": { transform: "translateX(40px) translateY(10%)  " },
          "100%": {
            transform:
              "translateX(10px) translateY(-30%) skewX(20deg) scale(0.6)",
            opacity: 0.8,
          },
        },
        flowMerightBottom: {
          "0%": { transform: "translateX(40px) translateY(10%)" },
          "100%": {
            transform:
              "translateX(10px) translateY(30%) skewX(-12deg) scale(0.6)",
            opacity: 0.8,
          },
        },
        flowMeleftToTop: {
          "0%": { transform: "translateX(10px) translateY(-30%)  " },
          "100%": {
            transform:
              "translateX(40px) translateY(10%) skewX(20deg) scale(0.6)",
            opacity: 0.8,
          },
        },
        flowMeleftBottom: {
          "0%": { transform: "translateX(10px) translateY(30%)  " },
          "100%": {
            transform:
              "translateX(40px) translateY(10%) skewX(-12deg) scale(0.6) ",
            opacity: 0.8,
          },
        },

        littleBounce: {
          "0%, 100%": {
            transform: "translateY(-10%)",
          },
          "50%": {
            transform: "translateY(0)",
          },
        },
        scaleAnima1: {
          "0%, 100%": { transform: "scale(1.04)" },
          "50%": { transform: "scale(1)" },
        },

        scaleAnima2: {
          "0%, 100%": { transform: "scale(0.65)" },
          "50%": { transform: "scale(0.60)" },
        },

        scaleAnima3: {
          "0%, 100%": { transform: "scale(0.45)" },
          "50%": { transform: "scale(0.40)" },
        },

        scaleAnima4: {
          "0%, 100%": { transform: "scale(0.25)" },
          "50%": { transform: "scale(0.20)" },
        },
        scaleAnima5: {
          "0%, 100%": { transform: "scale(0.15)" },
          "50%": { transform: "scale(0.10)" },
        },
        textScale: {
          "0%, 100%": { transform: "scale(1) " },
          "50%": { transform: "scale(0.8)" },
        },
      },
      backgroundSize: {
        "size-200": "200% 200%",
        "300%": "300%",
      },
      backgroundPosition: {
        "pos-0": "0% 0%",
        "pos-100": "100% 100%",
      },
      cursor: {},
    },
  },
  plugins: [require("@tailwindcss/forms"), require("autoprefixer")],
};


